// 反馈管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 获取真实的用户ID
async function getUserId(openid) {
  try {
    console.log('🔍 反馈管理-获取用户ID:', { openid })

    // 首先查询用户表获取真实的_id
    const userQuery = await db.collection('users')
      .where({
        openid: openid
      })
      .limit(1)
      .get()

    if (userQuery.data.length > 0) {
      const realUserId = userQuery.data[0]._id
      console.log('✅ 找到用户真实ID:', { openid, realUserId })

      // 检查是否需要迁移旧数据（使用openid的反馈记录）
      const oldFeedbacks = await db.collection('feedbacks')
        .where({
          userId: openid
        })
        .get()

      if (oldFeedbacks.data.length > 0) {
        console.log(`⚠️ 发现${oldFeedbacks.data.length}条使用openid的旧反馈记录，需要迁移`)

        // 逐个更新旧记录的userId（避免使用batch）
        let migratedCount = 0
        for (const feedback of oldFeedbacks.data) {
          try {
            await db.collection('feedbacks').doc(feedback._id).update({
              data: {
                userId: realUserId,
                migratedAt: new Date(),
                oldUserId: openid
              }
            })
            migratedCount++
          } catch (updateError) {
            console.error(`❌ 更新反馈记录${feedback._id}失败:`, updateError)
          }
        }

        console.log(`✅ 旧反馈记录迁移完成，成功迁移${migratedCount}/${oldFeedbacks.data.length}条记录`)
      }

      return realUserId
    } else {
      console.log('⚠️ 未找到用户记录，使用openid作为后备')
      return openid
    }
  } catch (error) {
    console.error('❌ 获取用户ID失败:', error)
    return openid // 出错时返回openid作为后备
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action, data } = event

  try {
    switch (action) {
      case 'submitFeedback':
        // 提交反馈时使用前端传递的用户ID
        if (!data.userId) {
          throw new Error('用户ID缺失')
        }
        return await submitFeedback(data.userId, data)
      case 'getFeedbackHistory':
        // 获取历史时需要通过openid获取真实用户ID
        const userId = await getUserId(wxContext.OPENID)
        return await getFeedbackHistory(userId)
      case 'updateFeedbackStatus':
        return await updateFeedbackStatus(data.feedbackId, data.status, data.reply)
      case 'getAllFeedbacks':
        return await getAllFeedbacks(data.page, data.limit)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('反馈管理云函数错误:', error)
    return { success: false, error: error.message }
  }
}

// 提交反馈
async function submitFeedback(userId, feedbackData) {
  try {
    console.log('提交反馈:', {
      userId,
      feedbackType: feedbackData.type,
      contentLength: feedbackData.content?.length
    })

    // 验证用户ID格式（真实用户ID通常是24位的ObjectId）
    if (!userId || userId.length < 10) {
      throw new Error('无效的用户ID')
    }

    const feedback = {
      userId: userId,  // 使用前端传递的真实用户ID
      type: feedbackData.type || 'other',
      content: feedbackData.content,
      contactInfo: feedbackData.contactInfo || '',
      status: 'pending', // pending, processing, resolved, closed
      statusText: '待处理',
      createTime: new Date(),
      updateTime: new Date(),
      reply: '',
      adminId: '',
      priority: feedbackData.priority || 'normal', // low, normal, high, urgent
      tags: feedbackData.tags || [],
      attachments: feedbackData.attachments || [],
      userAgent: feedbackData.userAgent || '',
      version: feedbackData.version || '2.0.0'
    }

    const result = await db.collection('feedbacks').add({
      data: feedback
    })

    console.log('反馈提交成功:', result._id)
    
    return {
      success: true,
      feedbackId: result._id,
      message: '反馈提交成功'
    }
  } catch (error) {
    console.error('提交反馈失败:', error)
    throw error
  }
}

// 获取用户反馈历史
async function getFeedbackHistory(userId, page = 1, limit = 10) {
  try {
    const skip = (page - 1) * limit

    console.log('查询用户反馈历史:', { userId, page, limit })

    const result = await db.collection('feedbacks')
      .where({
        userId: userId
      })
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get()

    console.log('查询到反馈记录数量:', result.data.length)

    // 格式化数据以兼容前端显示
    const feedbacks = result.data.map(item => ({
      id: item._id,
      type: item.type,
      typeText: getFeedbackTypeText(item.type),
      content: item.content,
      contactInfo: item.contactInfo,
      date: formatDate(item.createTime),
      status: item.status,
      statusText: item.statusText,
      reply: item.reply,
      createTime: item.createTime,
      updateTime: item.updateTime
    }))

    return {
      success: true,
      data: feedbacks,
      total: result.data.length,
      page: page,
      hasMore: result.data.length === limit
    }
  } catch (error) {
    console.error('获取反馈历史失败:', error)
    throw error
  }
}

// 更新反馈状态（管理员功能）
async function updateFeedbackStatus(feedbackId, status, reply = '') {
  try {
    const updateData = {
      status: status,
      statusText: getStatusText(status),
      updateTime: new Date()
    }

    if (reply) {
      updateData.reply = reply
    }

    const result = await db.collection('feedbacks')
      .doc(feedbackId)
      .update({
        data: updateData
      })

    return {
      success: true,
      message: '反馈状态更新成功'
    }
  } catch (error) {
    console.error('更新反馈状态失败:', error)
    throw error
  }
}

// 获取所有反馈（管理员功能）
async function getAllFeedbacks(page = 1, limit = 20) {
  try {
    const skip = (page - 1) * limit
    
    const result = await db.collection('feedbacks')
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get()

    return {
      success: true,
      data: result.data,
      total: result.data.length,
      page: page,
      hasMore: result.data.length === limit
    }
  } catch (error) {
    console.error('获取所有反馈失败:', error)
    throw error
  }
}

// 辅助函数：获取反馈类型文本
function getFeedbackTypeText(type) {
  const typeMap = {
    'bug': '🐛 Bug反馈',
    'feature': '💡 功能建议',
    'other': '💬 其他'
  }
  return typeMap[type] || '💬 其他'
}

// 辅助函数：获取状态文本
function getStatusText(status) {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'resolved': '已解决',
    'closed': '已关闭'
  }
  return statusMap[status] || '待处理'
}

// 辅助函数：格式化日期
function formatDate(date) {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}`
}
