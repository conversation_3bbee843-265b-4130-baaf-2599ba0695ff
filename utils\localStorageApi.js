// 本地存储API - 云开发的后备方案
class LocalStorageApi {
  // 生成唯一ID
  static generateId() {
    return 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  // 获取当前用户ID（模拟openid）
  static getUserId() {
    let userId = wx.getStorageSync('kaoba_user_id')
    if (!userId) {
      userId = this.generateId()
      wx.setStorageSync('kaoba_user_id', userId)
    }
    return userId
  }

  // 任务管理相关API
  static async getTasks(filter = {}, limit = 20, skip = 0) {
    try {
      const userId = this.getUserId()
      const allTasks = wx.getStorageSync(`kaoba_tasks_${userId}`) || []
      
      let filteredTasks = allTasks
      
      // 应用过滤条件
      if (filter.completed !== undefined) {
        filteredTasks = filteredTasks.filter(task => task.completed === filter.completed)
      }
      
      if (filter.priority) {
        filteredTasks = filteredTasks.filter(task => task.priority === filter.priority)
      }
      
      if (filter.subject) {
        filteredTasks = filteredTasks.filter(task => task.subject === filter.subject)
      }
      
      if (filter.dateRange) {
        const { start, end } = filter.dateRange
        filteredTasks = filteredTasks.filter(task => {
          const taskDate = new Date(task.dueDate)
          return taskDate >= new Date(start) && taskDate <= new Date(end)
        })
      }
      
      // 排序
      filteredTasks.sort((a, b) => {
        const dateCompare = new Date(a.dueDate) - new Date(b.dueDate)
        if (dateCompare !== 0) return dateCompare
        
        const priorityOrder = { high: 3, medium: 2, low: 1 }
        return (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0)
      })
      
      // 分页
      const paginatedTasks = filteredTasks.slice(skip, skip + limit)
      
      return { success: true, data: paginatedTasks, total: filteredTasks.length }
    } catch (error) {
      console.error('获取本地任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async addTask(taskData) {
    try {
      const userId = this.getUserId()
      const tasks = wx.getStorageSync(`kaoba_tasks_${userId}`) || []
      
      const newTask = {
        _id: this.generateId(),
        ...taskData,
        completed: false,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
      
      tasks.push(newTask)
      wx.setStorageSync(`kaoba_tasks_${userId}`, tasks)
      
      return { success: true, data: newTask }
    } catch (error) {
      console.error('添加本地任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async updateTask(taskId, updates) {
    try {
      const userId = this.getUserId()
      const tasks = wx.getStorageSync(`kaoba_tasks_${userId}`) || []

      const taskIndex = tasks.findIndex(task => task._id === taskId)
      if (taskIndex === -1) {
        return { success: false, error: '任务不存在' }
      }

      tasks[taskIndex] = {
        ...tasks[taskIndex],
        ...updates,
        updateTime: new Date().toISOString()
      }

      wx.setStorageSync(`kaoba_tasks_${userId}`, tasks)
      
      return { success: true, data: tasks[taskIndex] }
    } catch (error) {
      console.error('更新本地任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async deleteTask(taskId) {
    try {
      const userId = this.getUserId()
      const tasks = wx.getStorageSync(`kaoba_tasks_${userId}`) || []

      const filteredTasks = tasks.filter(task => task._id !== taskId)
      wx.setStorageSync(`kaoba_tasks_${userId}`, filteredTasks)
      
      return { success: true }
    } catch (error) {
      console.error('删除本地任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async completeTask(taskId, completed = true) {
    const updates = { completed }
    if (completed) {
      updates.completedTime = new Date().toISOString()
    }
    return await this.updateTask(taskId, updates)
  }

  static async getTaskStats(dateRange = null) {
    try {
      const userId = this.getUserId()
      const allTasks = wx.getStorageSync(`kaoba_tasks_${userId}`) || []
      
      let tasks = allTasks
      if (dateRange) {
        const { start, end } = dateRange
        tasks = allTasks.filter(task => {
          const taskDate = new Date(task.createTime)
          return taskDate >= new Date(start) && taskDate <= new Date(end)
        })
      }
      
      const stats = {
        total: tasks.length,
        completed: tasks.filter(t => t.completed).length,
        pending: tasks.filter(t => !t.completed).length,
        overdue: tasks.filter(t => !t.completed && new Date(t.dueDate) < new Date()).length,
        byPriority: {
          high: tasks.filter(t => t.priority === 'high').length,
          medium: tasks.filter(t => t.priority === 'medium').length,
          low: tasks.filter(t => t.priority === 'low').length
        },
        bySubject: {}
      }
      
      // 按科目统计
      tasks.forEach(task => {
        if (task.subject) {
          stats.bySubject[task.subject] = (stats.bySubject[task.subject] || 0) + 1
        }
      })
      
      return { success: true, data: stats }
    } catch (error) {
      console.error('获取本地任务统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 考试管理相关API
  static async getExams(filter = {}, limit = 20, skip = 0) {
    try {
      const userId = this.getUserId()
      const allExams = wx.getStorageSync(`kaoba_exams_${userId}`) || []
      
      let filteredExams = allExams
      
      if (filter.subject) {
        filteredExams = filteredExams.filter(exam => exam.subject === filter.subject)
      }
      
      if (filter.dateRange) {
        const { start, end } = filter.dateRange
        filteredExams = filteredExams.filter(exam => {
          const examDate = new Date(exam.examDate)
          return examDate >= new Date(start) && examDate <= new Date(end)
        })
      }
      
      if (filter.status === 'upcoming') {
        filteredExams = filteredExams.filter(exam => new Date(exam.examDate) >= new Date())
      } else if (filter.status === 'past') {
        filteredExams = filteredExams.filter(exam => new Date(exam.examDate) < new Date())
      }
      
      // 排序
      filteredExams.sort((a, b) => new Date(a.examDate) - new Date(b.examDate))
      
      // 分页
      const paginatedExams = filteredExams.slice(skip, skip + limit)
      
      return { success: true, data: paginatedExams, total: filteredExams.length }
    } catch (error) {
      console.error('获取本地考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getUpcomingExams(days = 7, limit = 10) {
    const now = new Date()
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + days)
    
    return await this.getExams({
      dateRange: {
        start: now.toISOString(),
        end: futureDate.toISOString()
      }
    }, limit)
  }

  // 复习统计相关API
  static async getStudyStats(dateRange = null) {
    try {
      const userId = this.getUserId()
      const allSessions = wx.getStorageSync(`kaoba_study_sessions_${userId}`) || []
      
      let sessions = allSessions
      if (dateRange) {
        const { start, end } = dateRange
        sessions = allSessions.filter(session => {
          const sessionDate = new Date(session.startTime)
          return sessionDate >= new Date(start) && sessionDate <= new Date(end)
        })
      }
      
      const completedSessions = sessions.filter(s => s.status === 'completed')
      
      const stats = {
        totalSessions: completedSessions.length,
        totalDuration: completedSessions.reduce((sum, s) => sum + (s.totalDuration || 0), 0),
        averageDuration: completedSessions.length > 0 ? 
          completedSessions.reduce((sum, s) => sum + (s.totalDuration || 0), 0) / completedSessions.length : 0,
        bySubject: {},
        byDate: {}
      }
      
      return { success: true, data: stats }
    } catch (error) {
      console.error('获取本地复习统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getPomodoroStats(dateRange = null) {
    try {
      const userId = this.getUserId()
      const allPomodoros = wx.getStorageSync(`beikao_pomodoro_sessions_${userId}`) || []
      
      let pomodoros = allPomodoros
      if (dateRange) {
        const { start, end } = dateRange
        pomodoros = allPomodoros.filter(pomodoro => {
          const pomodoroDate = new Date(pomodoro.createTime)
          return pomodoroDate >= new Date(start) && pomodoroDate <= new Date(end)
        })
      }
      
      const stats = {
        totalSessions: pomodoros.length,
        completedSessions: pomodoros.filter(p => p.completed).length,
        totalFocusTime: pomodoros.filter(p => p.completed).reduce((sum, p) => sum + (p.duration || 25), 0),
        bySubject: {}
      }
      
      return { success: true, data: stats }
    } catch (error) {
      console.error('获取本地番茄钟统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 初始化示例数据
  static initSampleData() {
    const userId = this.getUserId()
    
    // 检查是否已有数据
    const existingTasks = wx.getStorageSync(`kaoba_tasks_${userId}`)
    if (existingTasks && existingTasks.length > 0) {
      console.log('本地数据已存在，跳过初始化')
      return { success: true, message: '本地数据已存在' }
    }
    
    // 创建示例任务
    const sampleTasks = [
      {
        _id: this.generateId(),
        title: '数学高数第一章复习',
        subject: '数学',
        priority: 'high',
        estimatedTime: 120,
        dueDate: new Date().toISOString(),
        description: '复习极限与连续性相关内容',
        completed: false,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        _id: this.generateId(),
        title: '英语单词背诵',
        subject: '英语',
        priority: 'medium',
        estimatedTime: 30,
        dueDate: new Date().toISOString(),
        description: '背诵考研核心词汇100个',
        completed: true,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        completedTime: new Date().toISOString()
      }
    ]
    
    wx.setStorageSync(`beikao_tasks_${userId}`, sampleTasks)
    
    // 创建示例考试
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + 30)
    
    const sampleExams = [
      {
        _id: this.generateId(),
        title: '高等数学期末考试',
        subject: '数学',
        examDate: futureDate.toISOString(),
        examTime: '09:00',
        location: '教学楼A101',
        description: '涵盖微积分、线性代数等内容',
        reminderDays: 3,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
    ]
    
    wx.setStorageSync(`beikao_exams_${userId}`, sampleExams)
    
    console.log('本地示例数据初始化完成')
    return { success: true, message: '本地示例数据初始化完成' }
  }

  // 添加番茄钟会话
  static async addPomodoroSession(pomodoroData) {
    try {
      const userId = this.getUserId()
      const sessions = wx.getStorageSync(`kaoba_pomodoro_sessions_${userId}`) || []

      const newSession = {
        _id: this.generateId(),
        ...pomodoroData,
        createTime: new Date().toISOString()
      }

      sessions.push(newSession)
      wx.setStorageSync(`kaoba_pomodoro_sessions_${userId}`, sessions)

      return { success: true, data: newSession }
    } catch (error) {
      console.error('添加本地番茄钟会话失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 获取番茄钟统计
  static async getPomodoroStats(dateRange = null) {
    try {
      const userId = this.getUserId()
      const sessions = wx.getStorageSync(`kaoba_pomodoro_sessions_${userId}`) || []

      let filteredSessions = sessions
      if (dateRange && dateRange.start && dateRange.end) {
        const startDate = new Date(dateRange.start)
        const endDate = new Date(dateRange.end)
        filteredSessions = sessions.filter(session => {
          const sessionDate = new Date(session.createTime)
          return sessionDate >= startDate && sessionDate <= endDate
        })
      }

      const stats = {
        totalSessions: filteredSessions.length,
        totalMinutes: filteredSessions.reduce((sum, session) => sum + (session.duration || 0), 0),
        completedSessions: filteredSessions.filter(session => session.completed).length,
        averageEfficiency: filteredSessions.length > 0 ?
          filteredSessions.reduce((sum, session) => sum + (session.efficiency || 0), 0) / filteredSessions.length : 0
      }

      return { success: true, data: stats }
    } catch (error) {
      console.error('获取本地番茄钟统计失败:', error)
      return { success: false, error: error.message }
    }
  }
}

module.exports = LocalStorageApi
