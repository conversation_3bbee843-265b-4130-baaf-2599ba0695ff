// pages/study-session-detail/index.js
Page({
  data: {
    filterType: 'all',
    sortIndex: 0,
    sortOptions: [
      { key: 'date', name: '按时间排序' },
      { key: 'duration', name: '按时长排序' },
      { key: 'efficiency', name: '按效率排序' },
      { key: 'type', name: '按类型排序' }
    ],

    sessions: [],
    filteredSessions: [],

    overview: {
      totalSessions: 0,
      totalTime: '0h',
      avgEfficiency: 0,
      completionRate: 0
    },

    showSessionModal: false,
    selectedSession: null
  },

  onLoad(options) {
    this.loadSessions()
  },

  onShow() {
    this.loadSessions()
  },

  // 加载复习会话数据
  loadSessions() {
    try {
      // 从本地存储获取会话数据
      const sessions = wx.getStorageSync('studySessions') || []

      // 如果没有数据，生成示例数据
      if (sessions.length === 0) {
        this.generateSampleSessions()
        return
      }

      // 处理会话数据
      const processedSessions = this.processSessions(sessions)

      this.setData({ sessions: processedSessions })
      this.filterSessions()
      this.calculateOverview()

    } catch (error) {
      console.error('加载会话数据失败:', error)
      this.generateSampleSessions()
    }
  },

  // 生成示例会话数据
  generateSampleSessions() {
    const sampleSessions = [
      {
        id: 'session_001',
        type: 'pomodoro',
        typeIcon: '🍅',
        typeName: '番茄钟',
        title: '数学复习',
        taskName: '高等数学第三章',
        subject: '数学',
        examName: '期末考试',
        startTime: '09:00',
        endTime: '09:25',
        date: this.formatDate(new Date()),
        duration: '25分钟',
        plannedDuration: '25分钟',
        status: 'completed',
        statusText: '已完成',
        efficiency: 92,
        focusTime: '23分钟',
        interruptions: 1,
        backgroundSound: '雨声',
        suggestions: [
          '专注度很高，继续保持',
          '建议减少中断次数',
          '可以尝试更长的专注时间'
        ]
      },
      {
        id: 'session_002',
        type: 'break',
        typeIcon: '☕',
        typeName: '休息',
        title: '短休息',
        startTime: '09:25',
        endTime: '09:30',
        date: this.formatDate(new Date()),
        duration: '5分钟',
        plannedDuration: '5分钟',
        status: 'completed',
        statusText: '已完成'
      },
      {
        id: 'session_003',
        type: 'pomodoro',
        typeIcon: '🍅',
        typeName: '番茄钟',
        title: '英语复习',
        taskName: '英语单词背诵',
        subject: '英语',
        examName: '四级考试',
        startTime: '14:00',
        endTime: '14:20',
        date: this.getDateBefore(1),
        duration: '20分钟',
        plannedDuration: '25分钟',
        status: 'interrupted',
        statusText: '中断',
        efficiency: 65,
        focusTime: '15分钟',
        interruptions: 3,
        backgroundSound: '海浪声',
        suggestions: [
          '中断次数较多，建议关闭通知',
          '可以选择更安静的复习环境',
          '尝试使用专注模式'
        ]
      },
      {
        id: 'session_004',
        type: 'study',
        typeIcon: '📚',
        typeName: '自由复习',
        title: '政治复习',
        taskName: '马原理论复习',
        subject: '政治',
        startTime: '19:30',
        endTime: '20:45',
        date: this.getDateBefore(2),
        duration: '75分钟',
        plannedDuration: '60分钟',
        status: 'completed',
        statusText: '已完成',
        efficiency: 88,
        focusTime: '68分钟',
        interruptions: 0,
        suggestions: [
          '复习时间较长，效率很高',
          '建议适当休息，避免疲劳',
          '可以分解为多个番茄钟'
        ]
      }
    ]

    // 保存示例数据
    wx.setStorageSync('studySessions', sampleSessions)

    // 处理并设置数据
    const processedSessions = this.processSessions(sampleSessions)
    this.setData({ sessions: processedSessions })
    this.filterSessions()
    this.calculateOverview()
  },

  // 处理会话数据
  processSessions(sessions) {
    return sessions.map(session => {
      // 确保所有必要字段都存在
      return {
        ...session,
        efficiency: session.efficiency || 0,
        interruptions: session.interruptions || 0,
        suggestions: session.suggestions || []
      }
    }).sort((a, b) => {
      // 默认按时间倒序排列
      return new Date(b.date + ' ' + b.startTime) - new Date(a.date + ' ' + a.startTime)
    })
  },

  // 筛选会话
  filterSessions() {
    const { sessions, filterType } = this.data
    let filtered = [...sessions]

    const today = new Date()
    const todayStr = this.formatDate(today)

    switch (filterType) {
      case 'today':
        filtered = sessions.filter(session => session.date === todayStr)
        break
      case 'week':
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
        filtered = sessions.filter(session => {
          const sessionDate = new Date(session.date)
          return sessionDate >= weekAgo && sessionDate <= today
        })
        break
      case 'month':
        const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
        filtered = sessions.filter(session => {
          const sessionDate = new Date(session.date)
          return sessionDate >= monthAgo && sessionDate <= today
        })
        break
      default:
        // 'all' - 显示所有
        break
    }

    // 应用排序
    this.sortSessions(filtered)
  },

  // 排序会话
  sortSessions(sessions) {
    const { sortOptions, sortIndex } = this.data
    const sortKey = sortOptions[sortIndex].key

    sessions.sort((a, b) => {
      switch (sortKey) {
        case 'date':
          return new Date(b.date + ' ' + b.startTime) - new Date(a.date + ' ' + a.startTime)
        case 'duration':
          return this.parseDuration(b.duration) - this.parseDuration(a.duration)
        case 'efficiency':
          return (b.efficiency || 0) - (a.efficiency || 0)
        case 'type':
          return a.typeName.localeCompare(b.typeName)
        default:
          return 0
      }
    })

    this.setData({ filteredSessions: sessions })
  },

  // 解析时长字符串为分钟数
  parseDuration(durationStr) {
    const match = durationStr.match(/(\d+)分钟/)
    return match ? parseInt(match[1]) : 0
  },

  // 计算概览统计
  calculateOverview() {
    const { filteredSessions } = this.data

    if (filteredSessions.length === 0) {
      this.setData({
        overview: {
          totalSessions: 0,
          totalTime: '0h',
          avgEfficiency: 0,
          completionRate: 0
        }
      })
      return
    }

    const totalSessions = filteredSessions.length
    const totalMinutes = filteredSessions.reduce((sum, session) => {
      return sum + this.parseDuration(session.duration)
    }, 0)

    const totalTime = totalMinutes >= 60
      ? `${Math.floor(totalMinutes / 60)}h${totalMinutes % 60 > 0 ? totalMinutes % 60 + 'm' : ''}`
      : `${totalMinutes}m`

    const efficiencySessions = filteredSessions.filter(s => s.efficiency > 0)
    const avgEfficiency = efficiencySessions.length > 0
      ? Math.round(efficiencySessions.reduce((sum, s) => sum + s.efficiency, 0) / efficiencySessions.length)
      : 0

    const completedSessions = filteredSessions.filter(s => s.status === 'completed').length
    const completionRate = Math.round((completedSessions / totalSessions) * 100)

    this.setData({
      overview: {
        totalSessions,
        totalTime,
        avgEfficiency,
        completionRate
      }
    })
  },

  // 切换筛选器
  switchFilter(e) {
    const type = e.currentTarget.dataset.type
    this.setData({ filterType: type })
    this.filterSessions()
    this.calculateOverview()
  },

  // 排序变化
  onSortChange(e) {
    const sortIndex = e.detail.value
    this.setData({ sortIndex })
    this.filterSessions()
  },

  // 查看会话详情
  viewSessionDetail(e) {
    const session = e.currentTarget.dataset.session
    this.setData({
      selectedSession: session,
      showSessionModal: true
    })
  },

  // 关闭会话详情弹窗
  closeSessionModal() {
    this.setData({ showSessionModal: false })
  },

  stopPropagation() {
    // 阻止事件冒泡
  },

  // 开始复习
  startStudy() {
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  // 工具方法
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  getDateBefore(days) {
    const date = new Date()
    date.setDate(date.getDate() - days)
    return this.formatDate(date)
  }
})