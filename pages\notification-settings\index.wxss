/* pages/notification-settings/index.wxss */

.container {
  padding: 20rpx;
}

.header {
  padding: 30rpx 0;
  text-align: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: #666;
}

.subscription-status {
  margin: 30rpx 0;
  padding: 30rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  text-align: center;
}

.status-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.status-text {
  font-size: 32rpx;
  margin-bottom: 10rpx;
}

.status-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.subscribe-button {
  margin-top: 20rpx;
  width: 80%;
}

.settings-group {
  margin-bottom: 30rpx;
}

.notice-box {
  margin: 30rpx 20rpx;
  padding: 20rpx;
  background-color: #f0f9ff;
  border-radius: 12rpx;
  border-left: 8rpx solid #1989fa;
}

.notice-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #1989fa;
}

.notice-content {
  font-size: 28rpx;
  color: #333;
}

.notice-content text {
  display: block;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

.footer {
  margin-top: 40rpx;
  padding: 20rpx;
  text-align: center;
  font-size: 24rpx;
  color: #999;
}

.test-button {
  margin-top: 30rpx;
}

/* 弹窗样式 */
.modal-content {
  padding: 30rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
}

.modal-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.modal-buttons {
  display: flex;
  justify-content: space-between;
}

.time-picker-container {
  margin-top: 20rpx;
}

.time-picker-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.time-picker {
  width: 100%;
  height: 400rpx;
}

.time-picker-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}
