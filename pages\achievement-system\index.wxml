<!--pages/achievement-system/index.wxml-->
<view class="container">
  <!-- 成就概览 -->
  <van-card 
    title="我的成就" 
    desc="记录你的复习里程碑"
    custom-class="overview-container"
    >
    <view slot="desc">
      <van-grid 
        column-num="4" 
        border="{{false}}"
        gutter="8"
        custom-class="overview-stats"
        >
        <van-grid-item>
          <view class="overview-stat">
            <text class="stat-value">{{overviewStats.totalUnlocked}}</text>
            <text class="stat-label">已解锁</text>
          </view>
        </van-grid-item>
        <van-grid-item>
          <view class="overview-stat">
            <text class="stat-value">{{overviewStats.totalAchievements}}</text>
            <text class="stat-label">总成就</text>
          </view>
        </van-grid-item>
        <van-grid-item>
          <view class="overview-stat">
            <text class="stat-value">{{overviewStats.completionRate}}%</text>
            <text class="stat-label">完成率</text>
          </view>
        </van-grid-item>
        <van-grid-item>
          <view class="overview-stat">
            <text class="stat-value">{{overviewStats.totalPoints}}</text>
            <text class="stat-label">成就点数</text>
          </view>
        </van-grid-item>
      </van-grid>

      <view class="progress-ring">
        <van-circle
          value="{{overviewStats.completionRate}}"
          size="160"
          layer-color="#F0F0F0"
          color="#52C41A"
          stroke-width="8"
          text="{{overviewStats.completionRate}}%"
          custom-class="completion-circle"
          />
      </view>
    </view>
  </van-card>

  <!-- 最近解锁 -->
  <van-card 
    title="最近解锁"
    custom-class="recent-container"
    wx:if="{{recentAchievements.length > 0}}"
    >
    <view slot="desc">
      <van-cell-group border="{{false}}">
        <van-cell
          wx:for="{{recentAchievements}}"
          wx:key="id"
          title="{{item.name}}"
          value="{{item.unlockedTime}}"
          border="{{false}}"
          custom-class="recent-achievement"
          >
          <view slot="icon" class="achievement-badge" style="background-color: {{item.color}}">
            <text class="badge-icon">{{item.icon}}</text>
          </view>
          <view slot="right-icon" wx:if="{{item.isNew}}">
            <van-tag type="danger" size="mini">NEW</van-tag>
          </view>
        </van-cell>
      </van-cell-group>
    </view>
  </van-card>

  <!-- 成就分类 -->
  <van-card 
    title="成就分类"
    custom-class="categories-container"
    >
    <view slot="tags">
      <van-tabs 
        active="{{selectedCategory}}" 
        bind:change="switchCategory"
        type="card"
        custom-class="category-tabs"
        >
        <van-tab 
          wx:for="{{achievementCategories}}" 
          wx:key="id"
          title="{{item.name}}" 
          name="{{item.id}}"
          >
        </van-tab>
      </van-tabs>
    </view>
    
    <view slot="desc">
      <van-cell-group custom-class="category-progress">
        <van-cell 
          title="{{currentCategoryInfo.name}}"
          value="{{currentCategoryInfo.unlockedCount}}/{{currentCategoryInfo.totalCount}}"
          border="{{false}}"
          >
          <view slot="extra" class="category-progress-container">
            <van-progress 
              percentage="{{currentCategoryInfo.progress}}" 
              color="#52C41A"
              track-color="#F0F0F0"
              stroke-width="8"
              show-pivot="{{false}}"
              custom-class="category-progress-bar"
              />
          </view>
        </van-cell>
      </van-cell-group>

      <van-cell-group border="{{false}}" custom-class="achievements-list">
        <van-cell
          wx:for="{{currentCategoryAchievements}}"
          wx:key="id"
          title="{{item.name}}"
          label="{{item.description}}"
          is-link
          border="{{false}}"
          custom-class="achievement-item {{item.unlocked ? 'unlocked' : 'locked'}}"
          bind:click="viewAchievementDetail"
          data-achievement="{{item}}"
          >
          <view slot="icon" class="achievement-icon-container">
            <view class="achievement-icon {{item.unlocked ? 'unlocked' : 'locked'}}"
                  style="background-color: {{item.unlocked ? item.color : '#F5F5F5'}}">
              <text class="icon-emoji">{{item.icon}}</text>
            </view>
            <view wx:if="{{item.level}}" class="achievement-level-badge">
              <text class="level-text">{{item.level}}</text>
            </view>
          </view>

          <view slot="title" class="achievement-header">
            <text class="achievement-title">{{item.name}}</text>
            <van-tag 
              wx:if="{{item.rarity}}"
              type="primary"
              size="mini"
              color="{{item.rarityColor}}"
              custom-class="achievement-rarity"
              >
              {{item.rarity}}
            </van-tag>
          </view>

          <view slot="extra" class="achievement-content">
            <view class="achievement-progress" wx:if="{{!item.unlocked && item.progress !== undefined}}">
              <van-progress 
                percentage="{{item.progress}}" 
                color="#1989FA"
                track-color="#F0F0F0"
                stroke-width="4"
                show-pivot="{{false}}"
                custom-class="progress-bar"
                />
              <text class="progress-text">{{item.currentValue}}/{{item.targetValue}}</text>
            </view>

            <view class="achievement-reward" wx:if="{{item.reward}}">
              <text class="reward-text">奖励：{{item.reward}}</text>
            </view>
          </view>
        </van-cell>
      </van-cell-group>
    </view>
  </van-card>

  <!-- 成就统计 -->
  <van-card 
    title="成就统计"
    custom-class="stats-container"
    >
    <view slot="desc">
      <van-grid 
        column-num="2" 
        border="{{false}}"
        gutter="16"
        custom-class="stats-grid"
        >
        <van-grid-item
          wx:for="{{achievementStats}}"
          wx:key="label"
          custom-class="stat-item"
          >
          <view class="stat-content" style="background-color: {{item.bgColor}}">
            <text class="stat-value" style="color: {{item.color}}">{{item.value}}</text>
            <text class="stat-label">{{item.label}}</text>
          </view>
        </van-grid-item>
      </van-grid>
    </view>
  </van-card>
</view>

<!-- 成就详情弹窗 -->
<van-popup 
  show="{{showAchievementDetail}}" 
  bind:close="hideAchievementDetail"
  closeable
  position="center"
  custom-style="width: 85%; max-width: 640rpx; border-radius: 16rpx;"
  >
  <view class="modal-content">
    <view class="modal-header">
      <view class="modal-achievement-icon-container">
        <view class="modal-achievement-icon" style="background-color: {{selectedAchievement.color}}">
          <text>{{selectedAchievement.icon}}</text>
        </view>
        <view wx:if="{{selectedAchievement.level}}" class="modal-achievement-level-badge">
          <text class="modal-level-text">{{selectedAchievement.level}}</text>
        </view>
      </view>
    </view>

    <view class="modal-body">
      <view class="modal-achievement-header">
        <text class="modal-achievement-name">{{selectedAchievement.name}}</text>
        <van-tag 
          wx:if="{{selectedAchievement.rarity}}"
          type="primary"
          size="mini"
          color="{{selectedAchievement.rarityColor}}"
          >
          {{selectedAchievement.rarity}}
        </van-tag>
      </view>
      
      <text class="modal-achievement-description">{{selectedAchievement.description}}</text>

      <van-divider content-position="left">详细信息</van-divider>
      <van-cell-group border="{{false}}" custom-class="modal-achievement-details">
        <van-cell 
          wx:if="{{selectedAchievement.category}}"
          title="分类"
          value="{{selectedAchievement.categoryName}}"
          border="{{false}}"
          />
        <van-cell 
          wx:if="{{selectedAchievement.rarity}}"
          title="稀有度"
          border="{{false}}"
          >
          <view slot="value" style="color: {{selectedAchievement.rarityColor}}">{{selectedAchievement.rarity}}</view>
        </van-cell>
        <van-cell 
          wx:if="{{selectedAchievement.points}}"
          title="成就点数"
          value="{{selectedAchievement.points}}"
          border="{{false}}"
          />
        <van-cell 
          wx:if="{{selectedAchievement.unlockedTime}}"
          title="解锁时间"
          value="{{selectedAchievement.unlockedTime}}"
          border="{{false}}"
          />
      </van-cell-group>

      <view class="modal-progress" wx:if="{{!selectedAchievement.unlocked}}">
        <van-divider content-position="left">完成进度</van-divider>
        <van-progress 
          percentage="{{selectedAchievement.progress}}" 
          color="#1989FA"
          track-color="#F0F0F0"
          stroke-width="8"
          show-pivot="{{false}}"
          custom-class="progress-bar"
          />
        <text class="progress-text">{{selectedAchievement.currentValue}}/{{selectedAchievement.targetValue}}</text>
      </view>

      <view class="modal-tips" wx:if="{{selectedAchievement.tips}}">
        <van-divider content-position="left">解锁提示</van-divider>
        <text class="tips-content">{{selectedAchievement.tips}}</text>
      </view>
    </view>
  </view>
</van-popup>