<!--pages/data-export/index.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header-container">
    <text class="page-title">数据导出</text>
    <text class="page-subtitle">导出你的复习数据，随时备份和分享</text>
  </view>

  <!-- 导出选项 -->
  <view class="export-options-container">
    <text class="section-title">导出内容</text>

    <view class="data-types">
      <view class="data-type-item" wx:for="{{exportDataTypes}}" wx:key="id">
        <view class="data-type-info">
          <text class="data-type-name">{{item.name}}</text>
          <text class="data-type-description">{{item.description}}</text>
          <text class="data-type-count">{{item.count}}条记录</text>
        </view>
        <switch checked="{{item.selected}}" bindchange="toggleDataType" data-type="{{item.id}}"/>
      </view>
    </view>
  </view>

  <!-- 导出格式 -->
  <view class="export-formats-container">
    <text class="section-title">导出格式</text>

    <view class="format-options">
      <button class="format-option {{selectedFormat === item.id ? 'active' : ''}}"
              wx:for="{{exportFormats}}"
              wx:key="id"
              bindtap="selectExportFormat"
              data-format="{{item.id}}">
        <text class="format-icon">{{item.icon}}</text>
        <text class="format-name">{{item.name}}</text>
      </button>
    </view>

    <view class="format-description" wx:if="{{selectedFormatInfo}}">
      <text class="format-desc-text">{{selectedFormatInfo.description}}</text>
    </view>
  </view>

  <!-- 时间范围 -->
  <view class="date-range-container">
    <text class="section-title">时间范围</text>

    <view class="date-range-options">
      <button class="date-range-option {{selectedDateRange === item.value ? 'active' : ''}}"
              wx:for="{{dateRangeOptions}}"
              wx:key="value"
              bindtap="selectDateRange"
              data-range="{{item.value}}">
        {{item.label}}
      </button>
    </view>

    <view class="custom-date-range" wx:if="{{selectedDateRange === 'custom'}}">
      <picker mode="date" value="{{customStartDate}}" bindchange="selectStartDate">
        <view class="date-picker">
          <text class="date-label">开始日期</text>
          <text class="date-value">{{customStartDate || '请选择'}}</text>
        </view>
      </picker>

      <text class="date-separator">至</text>

      <picker mode="date" value="{{customEndDate}}" bindchange="selectEndDate">
        <view class="date-picker">
          <text class="date-label">结束日期</text>
          <text class="date-value">{{customEndDate || '请选择'}}</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 导出预览 -->
  <view class="export-preview-container">
    <text class="section-title">导出预览</text>

    <view class="preview-stats">
      <view class="preview-stat">
        <text class="stat-value">{{exportPreview.totalRecords}}</text>
        <text class="stat-label">总记录数</text>
      </view>
      <view class="preview-stat">
        <text class="stat-value">{{exportPreview.fileSize}}</text>
        <text class="stat-label">预计大小</text>
      </view>
      <view class="preview-stat">
        <text class="stat-value">{{exportPreview.dateRange}}</text>
        <text class="stat-label">时间跨度</text>
      </view>
    </view>

    <view class="preview-content">
      <text class="preview-title">包含内容：</text>
      <view class="preview-items">
        <text class="preview-item" wx:for="{{exportPreview.items}}" wx:key="*this">
          • {{item}}
        </text>
      </view>
    </view>
  </view>

  <!-- 导出历史 -->
  <view class="export-history-container" wx:if="{{exportHistory.length > 0}}">
    <view class="history-header">
      <text class="section-title">导出历史</text>
      <button class="clear-history-btn" bindtap="clearExportHistory">清空</button>
    </view>

    <view class="history-list">
      <view class="history-item" wx:for="{{exportHistory}}" wx:key="id">
        <view class="history-info">
          <text class="history-name">{{item.name}}</text>
          <text class="history-details">{{item.format}} • {{item.size}} • {{item.date}}</text>
        </view>
        <view class="history-actions">
          <button class="history-action-btn" bindtap="downloadExport" data-item="{{item}}">
            <text class="action-icon">📥</text>
          </button>
          <button class="history-action-btn" bindtap="shareExport" data-item="{{item}}">
            <text class="action-icon">📤</text>
          </button>
          <button class="history-action-btn" bindtap="deleteExport" data-item="{{item}}">
            <text class="action-icon">🗑️</text>
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 导出按钮 -->
  <view class="export-actions">
    <button class="preview-btn" bindtap="previewExport">预览数据</button>
    <button class="export-btn" bindtap="startExport" disabled="{{!canExport}}">
      {{isExporting ? '导出中...' : '开始导出'}}
    </button>
  </view>
</view>

<!-- 导出进度弹窗 -->
<view class="export-modal" wx:if="{{showExportModal}}" bindtap="hideExportModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{isExporting ? '正在导出' : '导出完成'}}</text>
      <text class="modal-close" wx:if="{{!isExporting}}" bindtap="hideExportModal">×</text>
    </view>

    <view class="modal-body">
      <view class="export-progress" wx:if="{{isExporting}}">
        <view class="progress-circle">
          <text class="progress-text">{{exportProgress}}%</text>
        </view>
        <text class="progress-status">{{exportStatus}}</text>
      </view>

      <view class="export-result" wx:if="{{!isExporting && exportResult}}">
        <text class="result-icon">✅</text>
        <text class="result-title">导出成功！</text>
        <text class="result-details">文件名：{{exportResult.fileName}}</text>
        <text class="result-details">文件大小：{{exportResult.fileSize}}</text>
        <text class="result-details">记录数量：{{exportResult.recordCount}}</text>

        <view class="result-actions">
          <button class="result-action-btn primary" bindtap="downloadResult">下载文件</button>
          <button class="result-action-btn secondary" bindtap="shareResult">分享文件</button>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 数据预览弹窗 -->
<view class="preview-modal" wx:if="{{showPreviewModal}}" bindtap="hidePreviewModal">
  <view class="modal-content large" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">数据预览</text>
      <text class="modal-close" bindtap="hidePreviewModal">×</text>
    </view>

    <view class="modal-body">
      <view class="preview-tabs">
        <button class="preview-tab {{previewTab === item.id ? 'active' : ''}}"
                wx:for="{{previewTabs}}"
                wx:key="id"
                bindtap="switchPreviewTab"
                data-tab="{{item.id}}">
          {{item.name}}
        </button>
      </view>

      <view class="preview-data">
        <scroll-view scroll-y class="data-scroll">
          <view class="data-table" wx:if="{{previewData.length > 0}}">
            <view class="table-header">
              <text class="table-cell" wx:for="{{previewColumns}}" wx:key="*this">{{item}}</text>
            </view>
            <view class="table-row" wx:for="{{previewData}}" wx:key="id">
              <text class="table-cell" wx:for="{{previewColumns}}" wx:for-item="column" wx:key="*this">
                {{item[column] || '-'}}
              </text>
            </view>
          </view>

          <view class="no-data" wx:if="{{previewData.length === 0}}">
            <text class="no-data-text">暂无数据</text>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</view>