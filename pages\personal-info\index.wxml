<!--pages/personal-info/index.wxml-->
<view class="container">
  <!-- 主要内容区域 -->
  <view class="content">
    <!-- 头像编辑区域 -->
    <view class="avatar-section">
      <view class="section-title">头像</view>
      <view class="avatar-container">
        <view class="avatar-display" bind:tap="chooseAvatar">
          <van-image
            src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}"
            width="120rpx"
            height="120rpx"
            round
            fit="cover"
          />
          <view class="upload-mask">
            <van-icon name="camera-o" size="24" color="#fff" />
          </view>
        </view>
        <view class="avatar-tip">点击更换头像</view>
      </view>
    </view>

    <!-- 基本信息表单 -->
    <view class="form-section">
      <van-cell-group>
        <van-field
          name="nickname"
          label="昵称"
          placeholder="请输入昵称"
          value="{{userInfo.nickName}}"
          bind:change="updateNickname"
          maxlength="{{20}}"
          clearable
          left-icon="user-o"
          required
        />

        <van-field
          name="signature"
          label="个性签名"
          type="textarea"
          placeholder="写点什么激励自己..."
          value="{{userInfo.signature}}"
          bind:change="updateSignature"
          maxlength="{{100}}"
          autosize
          show-word-limit
          left-icon="chat-o"
          use-button-slot
        >
          <van-button 
            slot="button" 
            size="mini" 
            type="primary" 
            bind:click="showSignatureSelector"
            custom-class="signature-preset-btn"
            icon="star-o"
          >
            预设
          </van-button>
        </van-field>
      </van-cell-group>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-section">
      <van-button
        type="primary"
        size="large"
        block
        bind:click="saveUserInfo"
        disabled="{{!hasChanges}}"
        custom-class="save-button"
      >
        保存修改
      </van-button>
      
      <van-button
        type="default"
        size="large"
        block
        bind:click="resetInfo"
        disabled="{{!hasChanges}}"
        custom-class="reset-button"
      >
        重置修改
      </van-button>
    </view>
  </view>

  <!-- 底部提示 -->
  <view class="footer-tip" wx:if="{{hasChanges}}">
    <van-icon name="info-o" size="16" color="#ff9500" />
    <text>您有未保存的修改</text>
  </view>

  <!-- 预设签名选择弹窗 -->
  <van-popup
    show="{{showSignatureModal}}"
    position="bottom"
    custom-style="height: 60%"
    bind:close="hideSignatureSelector"
    closeable
    round
  >
    <view class="signature-modal">
      <view class="modal-header">
        <text class="modal-title">选择个性签名</text>
      </view>
      
      <view class="modal-body">
        <van-tabs active="{{activeSignatureTab}}" bind:change="onSignatureTabChange" color="#1890ff">
          <van-tab wx:for="{{signatureCategories}}" wx:key="name" title="{{item.name}}">
            <scroll-view 
              class="signature-scroll" 
              scroll-y="{{true}}" 
              enhanced="{{true}}"
              show-scrollbar="{{false}}"
            >
              <view class="signature-list">
                <view 
                  class="signature-item" 
                  wx:for="{{item.signatures}}" 
                  wx:key="*this"
                  wx:for-item="signature"
                  bind:tap="selectPresetSignature"
                  data-signature="{{signature}}"
                >
                  <text class="signature-text">{{signature}}</text>
                  <van-icon name="arrow" size="16" color="#999" />
                </view>
              </view>
            </scroll-view>
          </van-tab>
        </van-tabs>
      </view>
    </view>
  </van-popup>
</view> 