// 数据库初始化云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { action } = event
  
  console.log('initDatabase云函数调用:', { action })

  try {
    switch (action) {
      case 'createCollections':
        return await createCollections()
      case 'initAchievements':
        return await initAchievements()
      case 'checkCollections':
        return await checkCollections()
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('initDatabase云函数执行失败:', error)
    return { success: false, error: error.message }
  }
}

// 创建数据库集合
async function createCollections() {
  const collections = [
    'users',
    'tasks',
    'exams',
    'study_sessions',
    'pomodoro_sessions',
    'daily_stats',
    'achievements',
    'user_achievements',
    'app_settings',
    'study_groups',
    'group_invitations',
    'group_plan_shares',
    'group_activities'
  ]

  const results = []
  
  for (const collectionName of collections) {
    try {
      // 检查集合是否已存在
      const existing = await db.collection(collectionName).limit(1).get()
      results.push({
        collection: collectionName,
        status: 'exists',
        message: '集合已存在'
      })
    } catch (error) {
      if (error.errCode === -1) {
        // 集合不存在，需要创建
        try {
          await db.createCollection(collectionName)
          results.push({
            collection: collectionName,
            status: 'created',
            message: '集合创建成功'
          })
        } catch (createError) {
          results.push({
            collection: collectionName,
            status: 'error',
            message: `创建失败: ${createError.message}`
          })
        }
      } else {
        results.push({
          collection: collectionName,
          status: 'error',
          message: `检查失败: ${error.message}`
        })
      }
    }
  }

  return { success: true, data: results }
}

// 初始化成就数据
async function initAchievements() {
  const achievements = [
    // 备考成就
    {
      name: '初学者',
      description: '完成第一个复习任务',
      icon: '🌱',
      color: '#52C41A',
      category: 'study',
      condition: { type: 'task_count', target: 1, timeframe: 'total' },
      points: 10,
      isActive: true
    },
    {
      name: '勤奋学者',
      description: '累计完成50个复习任务',
      icon: '📚',
      color: '#1890FF',
      category: 'study',
      condition: { type: 'task_count', target: 50, timeframe: 'total' },
      points: 100,
      isActive: true
    },
    {
      name: '学霸',
      description: '累计完成200个复习任务',
      icon: '🎓',
      color: '#722ED1',
      category: 'study',
      condition: { type: 'task_count', target: 200, timeframe: 'total' },
      points: 500,
      isActive: true
    },
    // 专注成就
    {
      name: '专注新手',
      description: '完成第一个番茄钟',
      icon: '🍅',
      color: '#FA8C16',
      category: 'focus',
      condition: { type: 'pomodoro_count', target: 1, timeframe: 'total' },
      points: 10,
      isActive: true
    },
    {
      name: '专注达人',
      description: '累计完成50个番茄钟',
      icon: '🎯',
      color: '#FF4D4F',
      category: 'focus',
      condition: { type: 'pomodoro_count', target: 50, timeframe: 'total' },
      points: 100,
      isActive: true
    },
    {
      name: '专注大师',
      description: '累计完成200个番茄钟',
      icon: '🏆',
      color: '#722ED1',
      category: 'focus',
      condition: { type: 'pomodoro_count', target: 200, timeframe: 'total' },
      points: 500,
      isActive: true
    },
    // 习惯成就
    {
      name: '坚持不懈',
      description: '连续复习7天',
      icon: '🔥',
      color: '#FF4D4F',
      category: 'habit',
      condition: { type: 'streak_days', target: 7, timeframe: 'streak' },
      points: 50,
      isActive: true
    },
    {
      name: '持之以恒',
      description: '连续复习30天',
      icon: '💪',
      color: '#52C41A',
      category: 'habit',
      condition: { type: 'streak_days', target: 30, timeframe: 'streak' },
      points: 200,
      isActive: true
    },
    // 时长成就
    {
      name: '时间管理者',
      description: '累计复习100小时',
      icon: '⏰',
      color: '#1890FF',
      category: 'study',
      condition: { type: 'study_time', target: 100, timeframe: 'total' },
      points: 300,
      isActive: true
    }
  ]

  const results = []
  
  for (const achievement of achievements) {
    try {
      // 检查成就是否已存在
      const existing = await db.collection('achievements')
        .where({ name: achievement.name })
        .get()

      if (existing.data.length === 0) {
        await db.collection('achievements').add({
          data: {
            ...achievement,
            createTime: new Date().toISOString()
          }
        })
        results.push({
          achievement: achievement.name,
          status: 'created'
        })
      } else {
        results.push({
          achievement: achievement.name,
          status: 'exists'
        })
      }
    } catch (error) {
      results.push({
        achievement: achievement.name,
        status: 'error',
        message: error.message
      })
    }
  }

  return { success: true, data: results }
}

// 检查集合状态
async function checkCollections() {
  const collections = [
    'users', 'tasks', 'exams', 'study_sessions', 'pomodoro_sessions',
    'daily_stats', 'achievements', 'user_achievements', 'app_settings',
    'study_groups', 'group_invitations', 'group_plan_shares', 'group_activities'
  ]

  const results = []
  
  for (const collectionName of collections) {
    try {
      const result = await db.collection(collectionName).count()
      results.push({
        collection: collectionName,
        status: 'exists',
        count: result.total
      })
    } catch (error) {
      results.push({
        collection: collectionName,
        status: 'not_exists',
        error: error.message
      })
    }
  }

  return { success: true, data: results }
}
