/* pages/personal-info/index.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 20rpx;
}

/* 头像编辑区域 */
.avatar-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30rpx;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-display {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 20rpx;
  cursor: pointer;
}

.avatar-display:active {
  opacity: 0.8;
}

.upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-display:hover .upload-mask,
.avatar-display:active .upload-mask {
  opacity: 1;
}

.avatar-tip {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

/* 表单区域 */
.form-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 操作按钮区域 */
.action-section {
  padding: 20rpx 0;
}

.save-button {
  --van-button-primary-background-color: #1890ff;
  --van-button-primary-border-color: #1890ff;
  --van-button-large-height: 88rpx;
  --van-button-large-font-size: 32rpx;
  --van-button-border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.save-button[disabled] {
  --van-button-disabled-background-color: #f5f5f5;
  --van-button-disabled-color: #c8c9cc;
}

.reset-button {
  --van-button-default-background-color: #f8f9fa;
  --van-button-default-border-color: #e8e8e8;
  --van-button-default-color: #666;
  --van-button-large-height: 88rpx;
  --van-button-large-font-size: 32rpx;
  --van-button-border-radius: 12rpx;
}

.reset-button[disabled] {
  --van-button-disabled-background-color: #f5f5f5;
  --van-button-disabled-color: #c8c9cc;
}



/* 底部提示 */
.footer-tip {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  background-color: #fff7e6;
  border: 1rpx solid #ffd591;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  z-index: 100;
}

.footer-tip text {
  font-size: 24rpx;
  color: #ff9500;
  margin-left: 8rpx;
}

/* 表单字段样式 */
.van-field {
  --van-field-label-color: #333;
  --van-field-input-text-color: #333;
  --van-field-placeholder-text-color: #999;
}

/* 签名选择按钮 - 参考首页任务操作按钮样式 */
.signature-preset-btn {
  --van-button-primary-background-color: #1890ff;
  --van-button-primary-border-color: #1890ff;
  --van-button-mini-height: 56rpx;
  --van-button-mini-padding: 0;
  --van-button-mini-font-size: 22rpx;
  --van-button-border-radius: 12rpx;
  width: 80rpx !important;
  min-width: 80rpx !important;
  max-width: 80rpx !important;
  font-weight: 500 !important;
}

/* 更强的选择器确保按钮宽度生效 */
.signature-preset-btn .van-button,
.signature-preset-btn::after {
  width: 80rpx !important;
  min-width: 80rpx !important;
  max-width: 80rpx !important;
}

/* 预设签名弹窗 */
.signature-modal {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.signature-modal .modal-header {
  padding: 32rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.signature-modal .modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.signature-modal .modal-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 滚动视图样式 */
.signature-scroll {
  flex: 1;
  height: 100%;
}

.signature-list {
  padding: 0;
}

.signature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.signature-item:last-child {
  border-bottom: none;
}

.signature-item:active {
  background-color: #f8f9fa;
}

.signature-text {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
  margin-right: 20rpx;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .content {
    padding: 16rpx;
  }
  
  .avatar-section {
    padding: 32rpx;
  }
  
  .avatar-display {
    width: 100rpx;
    height: 100rpx;
  }
  
  .section-title {
    font-size: 26rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }
  
  .avatar-section,
  .form-section {
    background-color: #2a2a2a;
  }
  
  .section-title {
    color: #ffffff;
  }
  
  .avatar-tip {
    color: #cccccc;
  }
  
  .footer-tip {
    background-color: #2a2a2a;
    border-color: #ff9500;
  }
} 