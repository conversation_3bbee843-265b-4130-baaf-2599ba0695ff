// pages/add-exam/index.js
const SmartApi = require('../../utils/smartApi')
const NavigationUtils = require('../../utils/navigationUtils')
const ExamModel = require('../../models/ExamModel')
const NotificationApi = require('../../utils/notificationApi')

Page({
  data: {
    // 使用ExamModel的默认数据结构
    examForm: {
      title: '',
      subject: '',
      examDate: '',
      examTime: '',
      location: '',
      description: '',
      type: 'final',
      importance: 'medium',
      status: 'upcoming',
      reminderSettings: ['1day', '3days']
    },

    // 重要程度选项
    importanceOptions: ['一般', '重要', '非常重要'],
    selectedImportanceIndex: 1, // 默认选择"重要"

    // 考试类型选项
    typeOptions: [
      { label: '📚 期末考试', value: 'final' },
      { label: '📖 期中考试', value: 'midterm' },
      { label: '📝 小测验', value: 'quiz' },
      { label: '🏆 资格考试', value: 'certificate' },
      { label: '🎓 入学考试', value: 'entrance' },
      { label: '📋 其他', value: 'other' }
    ],

    // van-picker需要的列数据格式
    typeColumns: [],

    // 提醒设置选项
    reminderOptions: [
      { value: '1day', label: '考试前1天', checked: true },
      { value: '3days', label: '考试前3天', checked: true },
      { value: '1week', label: '考试前1周', checked: false },
      { value: '2weeks', label: '考试前2周', checked: false }
    ],

    todayDate: '',
    canSave: false,
    titleErrorMessage: '', // 动态错误消息，为空时不显示
    newSubject: '', // 新科目输入
    canAddSubject: false, // 是否可以添加科目
    selectedReminders: ['1day', '3days'],
    typeLabel: '📚 期末考试', // 默认类型标签

    // 弹窗控制
    showDatePopup: false,
    showTimePopup: false,
    showTypePopup: false,
    currentDate: new Date().getTime(),
    currentTime: '09:00',
    minDate: new Date().getTime()
  },

  async onLoad(options) {
    // 检查登录状态
    const app = getApp()
    const loginStatus = app.checkLoginStatus()

    if (!loginStatus.isLoggedIn) {
      wx.navigateTo({
        url: '/pages/login/index'
      })
      return
    }

    this.initPage()
  },

  // 初始化页面
  initPage() {
    try {
      // 使用ExamModel获取默认数据
      const defaultData = SmartApi.getDefaultExamData() || {
        title: '',
        subject: '',
        examDate: '',
        examTime: '',
        location: '',
        description: '',
        type: 'final',
        importance: 'medium',
        status: 'upcoming',
        reminderSettings: ['1day', '3days']
      }

      const today = new Date()
      const todayDate = this.formatDate(today)

      // 设置van-picker需要的列数据格式
      const typeColumns = this.data.typeOptions.map(option => option.label)

      this.setData({
        examForm: defaultData,
        todayDate: todayDate,
        typeColumns: typeColumns
      })

      this.validateForm()
    } catch (error) {
      console.error('初始化页面失败:', error)
      // 设置基本的默认值
      const typeColumns = this.data.typeOptions.map(option => option.label)

      this.setData({
        examForm: {
          title: '',
          subject: [], // 科目数组
          examDate: '',
          examTime: '',
          location: '',
          description: '',
          type: 'final',
          importance: 'medium',
          status: 'upcoming',
          reminderSettings: ['1day', '3days']
        },
        todayDate: new Date().toISOString().split('T')[0],
        typeColumns: typeColumns
      })
    }
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // Vant Weapp Field 输入事件处理
  onTitleChange(e) {
    const value = e.detail.value || e.detail || ''
    console.log('考试名称输入变化:', value)
    this.setData({
      'examForm.title': value
    })
    this.validateForm()
  },

  // 新科目输入变化
  onNewSubjectChange(e) {
    const value = e.detail.value || e.detail || ''
    console.log('科目输入变化:', value)
    const canAddSubject = value.trim().length > 0
    this.setData({
      newSubject: value,
      canAddSubject: canAddSubject
    })
  },

  // 添加科目
  addSubject() {
    // 检查是否可以添加
    if (!this.data.canAddSubject) return

    const newSubject = this.data.newSubject.trim()
    if (!newSubject) return

    const currentSubjects = this.data.examForm.subject || []

    // 检查是否已存在
    if (currentSubjects.includes(newSubject)) {
      wx.showToast({
        title: '科目已存在',
        icon: 'none'
      })
      return
    }

    // 检查科目数量限制
    if (currentSubjects.length >= 10) {
      wx.showToast({
        title: '最多只能添加10个科目',
        icon: 'none'
      })
      return
    }

    // 添加新科目
    const updatedSubjects = [...currentSubjects, newSubject]
    this.setData({
      'examForm.subject': updatedSubjects,
      newSubject: '',
      canAddSubject: false
    })
    this.validateForm()
  },

  // 移除科目
  removeSubject(e) {
    const index = e.currentTarget.dataset.index
    const currentSubjects = this.data.examForm.subject || []
    const updatedSubjects = currentSubjects.filter((_, i) => i !== index)

    this.setData({
      'examForm.subject': updatedSubjects
    })
    this.validateForm()
  },

  onLocationChange(e) {
    const value = e.detail.value || e.detail || ''
    console.log('考试地点输入变化:', value)
    this.setData({
      'examForm.location': value
    })
    this.validateForm()
  },

  onDescriptionChange(e) {
    const value = e.detail.value || e.detail || ''
    console.log('考试描述输入变化:', value)
    this.setData({
      'examForm.description': value
    })
    this.validateForm()
  },



  // Vant Tabs 重要程度选择
  onImportanceChange(e) {
    const index = e.detail.index || e.detail.name || 0
    const importanceMap = ['low', 'medium', 'high']

    // 确保 index 有效
    if (index >= 0 && index < importanceMap.length) {
      this.setData({
        'examForm.importance': importanceMap[index],
        selectedImportanceIndex: index
      })
      this.validateForm()
    }
  },



  // Vant Switch 提醒设置
  onReminderChange(e) {
    const value = e.currentTarget.dataset.value
    const checked = e.detail || false

    const reminderOptions = this.data.reminderOptions.map(item => {
      if (item.value === value) {
        return { ...item, checked }
      }
      return item
    })

    const selectedReminders = reminderOptions
      .filter(item => item.checked)
      .map(item => item.value)

    this.setData({
      reminderOptions,
      selectedReminders,
      'examForm.reminderSettings': selectedReminders
    })
  },

  // 表单验证
  validateForm() {
    const { title, examDate, examTime } = this.data.examForm || {}
    const hasTitle = Boolean(title && title.trim())
    const canSave = Boolean(hasTitle && examDate && examTime)

    this.setData({
      canSave,
      titleErrorMessage: '' // 清空错误消息，正常输入时不显示错误
    })
  },

  // 保存考试
  async saveExam() {
    // 检查具体的验证错误
    const { title, examDate, examTime } = this.data.examForm || {}
    const hasTitle = Boolean(title && title.trim())

    if (!hasTitle) {
      this.setData({ titleErrorMessage: '请输入考试名称' })
      wx.showToast({
        title: '请输入考试名称',
        icon: 'none'
      })
      return
    }

    if (!this.data.canSave) {
      wx.showToast({
        title: '请填写必填项',
        icon: 'none'
      })
      return
    }

    // 使用当前表单数据
    const examData = { ...this.data.examForm }

    console.log('添加考试 - 表单数据:', this.data.examForm)
    console.log('添加考试 - 准备保存的数据:', examData)

    // 详细检查数据格式
    console.log('数据格式检查:')
    console.log('- title:', examData.title, typeof examData.title)
    console.log('- examDate:', examData.examDate, typeof examData.examDate)
    console.log('- examTime:', examData.examTime, typeof examData.examTime)
    console.log('- type:', examData.type, typeof examData.type)
    console.log('- importance:', examData.importance, typeof examData.importance)

    // 先进行客户端验证
    const validation = SmartApi.validateExamData(examData)
    console.log('验证结果:', validation)

    if (!validation.isValid) {
      console.error('验证失败，错误:', validation.errors)
      wx.showToast({
        title: validation.errors[0] || '数据验证失败',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '保存中...',
      mask: true
    })

    try {
      // 使用SmartApi保存到云数据库（会自动使用ExamModel验证）
      const result = await SmartApi.addExam(examData)

      wx.hideLoading()

      if (result.success) {
        // 考试创建成功后处理通知订阅
        try {
          await NotificationApi.onExamCreated(examData)
        } catch (notificationError) {
          console.error('通知订阅处理失败:', notificationError)
          // 不影响主流程，继续执行
        }

        // 显示成功提示
        this.setData({ showSuccessModal: true })

        // 2秒后自动跳转
        setTimeout(() => {
          this.goToExamCenter()
        }, 2000)
      } else {
        wx.showToast({
          title: result.error || '保存失败',
          icon: 'none'
        })
      }

    } catch (error) {
      wx.hideLoading()
      console.error('保存考试失败:', error)
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      })
    }
  },

  // 取消添加
  cancelAdd() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消添加考试吗？已填写的内容将丢失。',
      success: (res) => {
        if (res.confirm) {
          NavigationUtils.safeNavigateBack()
        }
      }
    })
  },

  // 跳转到考试中心
  goToExamCenter() {
    wx.switchTab({
      url: '/pages/exam-center/index'
    })
  },

  // 显示日期选择器
  showDatePicker() {
    console.log('showDatePicker 被调用')
    this.setData({ showDatePopup: true })
    console.log('showDatePopup 设置为 true')
  },

  // 关闭日期选择器
  closeDatePicker() {
    this.setData({ showDatePopup: false })
  },

  // 确认日期选择
  onDateConfirm(event) {
    const dateValue = event.detail
    console.log('onDateConfirm 原始日期值:', dateValue, typeof dateValue)

    if (dateValue) {
      const date = new Date(dateValue)
      const dateStr = date.toISOString().split('T')[0]
      console.log('格式化后的日期:', dateStr)

      this.setData({
        'examForm.examDate': dateStr,
        showDatePopup: false
      })
      this.validateForm()
    }
  },

  // 显示时间选择器
  showTimePicker() {
    console.log('showTimePicker 被调用')
    this.setData({ showTimePopup: true })
    console.log('showTimePopup 设置为 true')
  },

  // 关闭时间选择器
  closeTimePicker() {
    this.setData({ showTimePopup: false })
  },

  // 确认时间选择
  onTimeConfirm(event) {
    const timeValue = event.detail || ''
    console.log('onTimeConfirm 原始时间值:', timeValue, typeof timeValue)

    // 确保时间格式为 HH:MM
    let formattedTime = timeValue
    if (timeValue) {
      // 如果是完整的日期时间，提取时间部分
      if (timeValue.includes('T')) {
        formattedTime = timeValue.split('T')[1].substring(0, 5)
      } else if (timeValue.includes(' ')) {
        formattedTime = timeValue.split(' ')[1].substring(0, 5)
      } else if (timeValue.length > 5) {
        // 如果是长时间格式，截取前5位
        formattedTime = timeValue.substring(0, 5)
      }
    }

    console.log('格式化后的时间:', formattedTime)

    this.setData({
      'examForm.examTime': formattedTime,
      showTimePopup: false
    })
    this.validateForm()
  },

  // 显示类型选择器
  showTypePicker() {
    console.log('showTypePicker 被调用')
    console.log('typeColumns:', this.data.typeColumns)
    console.log('typeOptions:', this.data.typeOptions)
    this.setData({ showTypePopup: true })
    console.log('showTypePopup 设置为 true')
  },

  // 关闭类型选择器
  closeTypePicker() {
    this.setData({ showTypePopup: false })
  },

  // 确认类型选择
  onTypeConfirm(event) {
    console.log('onTypeConfirm 事件:', event.detail)

    // van-picker 的 confirm 事件返回的是 { value, index }
    const { value, index } = event.detail

    if (index !== undefined && index >= 0 && this.data.typeOptions[index]) {
      const selectedType = this.data.typeOptions[index]
      console.log('选择的类型:', selectedType)

      this.setData({
        'examForm.type': selectedType.value,
        typeLabel: selectedType.label,
        showTypePopup: false
      })
      this.validateForm()
    } else {
      console.log('无效的选择索引:', index)
      this.setData({ showTypePopup: false })
    }
  }

})