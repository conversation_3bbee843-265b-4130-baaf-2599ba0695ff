// pages/splash/index.js
Page({
  data: {
    slogan: '让每一次复习都更高效',
    appName: '备考助手',
    showButton: false
  },

  onLoad() {
    // 检查是否是首次启动
    const hasLaunched = wx.getStorageSync('hasLaunched')
    
    if (hasLaunched) {
      // 非首次启动，快速跳转（显示0-1秒开屏）
      setTimeout(() => {
        this.enterApp()
      }, 1000) // 1秒开屏时间
    } else {
      // 首次启动，显示完整开屏
      setTimeout(() => {
        this.setData({
          showButton: true
        })
      }, 800)
    }
  },

  // 进入应用
  enterApp() {
    // 标记已完成开屏
    wx.setStorageSync('hasLaunched', true)
    
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  }
}) 