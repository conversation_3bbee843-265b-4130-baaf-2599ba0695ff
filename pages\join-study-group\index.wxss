/* pages/join-study-group/index.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-text {
  font-size: 32rpx;
  margin-top: 20rpx;
}

/* 邀请信息 */
.invite-info {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-top: 60rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.invite-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.invite-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}

.invite-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 小组卡片 */
.group-card {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.group-info {
  margin-bottom: 30rpx;
}

.group-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.exam-name {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.member-count {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.inviter-info {
  display: flex;
  align-items: center;
}

.inviter-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.inviter-details {
  flex: 1;
}

.inviter-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.invite-time {
  font-size: 24rpx;
  color: #666;
  display: block;
}

/* 操作按钮 */
.join-actions {
  margin-bottom: 40rpx;
}

.btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:disabled {
  background: #ccc;
  color: #999;
}

.btn-secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

/* 提示信息 */
.invite-tips {
  border-top: 2rpx solid #e9ecef;
  padding-top: 30rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

/* 错误状态 */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.error-content {
  text-align: center;
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}

.error-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 手动输入 */
.manual-input {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-top: 60rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.input-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.input-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.input-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.input-section {
  margin-bottom: 20rpx;
}

.invite-code-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  text-align: center;
  letter-spacing: 4rpx;
  margin-bottom: 30rpx;
  box-sizing: border-box;
}

.invite-code-input:focus {
  border-color: #667eea;
}

.verify-btn {
  margin-bottom: 0;
}
