<!--pages/task-detail/index.wxml-->
<view class="task-detail-container">
  <!-- 表单内容区域 -->
  <view class="detail-content">
    <!-- 详情区域 -->
    <view class="detail-sections">
      <!-- 加载状态 -->
      <view wx:if="{{loading}}" class="loading-container">
        <!-- 骨架屏 -->
        <view class="skeleton-content">
          <view class="skeleton-card">
            <view class="skeleton-header">
              <view class="skeleton-title"></view>
              <view class="skeleton-tags">
                <view class="skeleton-tag"></view>
                <view class="skeleton-tag"></view>
              </view>
            </view>
            <view class="skeleton-content-area">
              <view class="skeleton-line"></view>
              <view class="skeleton-line short"></view>
            </view>
          </view>

          <view class="skeleton-card">
            <view class="skeleton-grid">
              <view class="skeleton-grid-item" wx:for="{{[1,2,3,4]}}" wx:key="*this">
                <view class="skeleton-icon"></view>
                <view class="skeleton-text"></view>
              </view>
            </view>
          </view>

          <view class="skeleton-card">
            <view class="skeleton-progress">
              <view class="skeleton-progress-bar"></view>
              <view class="skeleton-progress-text"></view>
            </view>
          </view>
        </view>

        <!-- 加载提示 -->
        <view class="loading-tip">
          <van-loading type="spinner" size="20px" color="#1890ff" />
          <text class="loading-text">正在加载任务详情...</text>
        </view>
      </view>

      <!-- 错误状态 -->
      <view wx:elif="{{loadingError}}" class="error-container">
        <van-empty
          image="error"
          description="{{errorMessage || '加载失败'}}"
          custom-class="error-empty">
          <view class="error-actions">
            <van-button
              type="primary"
              size="small"
              bind:click="retryLoad"
              custom-class="retry-btn">
              重新加载
            </van-button>
            <van-button
              type="default"
              size="small"
              bind:click="goBack"
              custom-class="back-btn">
              返回
            </van-button>
          </view>
        </van-empty>
      </view>

      <!-- 空状态 -->
      <view wx:elif="{{isEmpty}}" class="empty-container">
        <van-empty
          image="search"
          description="任务内容为空"
          custom-class="empty-state">
          <view class="empty-actions">
            <van-button
              type="primary"
              size="small"
              bind:click="editTask"
              custom-class="edit-btn">
              编辑任务
            </van-button>
            <van-button
              type="default"
              size="small"
              bind:click="goBack"
              custom-class="back-btn">
              返回
            </van-button>
          </view>
        </van-empty>
      </view>

      <!-- 任务详情内容 -->
      <scroll-view
        wx:else
        scroll-y="{{true}}"
        refresher-enabled="{{true}}"
        refresher-triggered="{{refreshing}}"
        bind:refresherrefresh="onRefresh"
        custom-class="task-detail-content scroll-container">
        <!-- 任务头部信息 -->
  <van-card
    title="{{task.title}}"
    desc="{{task.description}}"
    custom-class="task-header-card">
    
    <!-- 标签组 -->
    <view slot="tags" class="task-tags">
      <van-tag wx:if="{{task.subject}}" type="primary" size="medium" custom-class="task-tag">{{task.subject}}</van-tag>
      <van-tag wx:if="{{task.examName}}" type="success" size="medium" custom-class="task-tag">{{task.examName}}</van-tag>
      <van-tag 
        type="{{task.priority === 'high' ? 'danger' : task.priority === 'medium' ? 'warning' : 'default'}}" 
        size="medium" 
        custom-class="task-tag">{{task.priorityText}}</van-tag>
      <van-tag 
        type="{{task.status === 'completed' ? 'success' : 'primary'}}" 
        size="medium" 
        custom-class="task-tag">{{task.statusText}}</van-tag>
    </view>

    <!-- 操作按钮 -->
    <view slot="footer" class="task-actions">
      <van-button type="primary" size="small" bind:click="editTask" custom-class="action-btn">编辑</van-button>
      <van-button type="default" size="small" bind:click="showMoreActions" custom-class="action-btn">更多</van-button>
    </view>
  </van-card>

  <!-- 任务概览卡片 -->
  <van-card
    title="任务概览"
    custom-class="overview-card section-card">
    <view slot="desc">
      <van-grid
        column-num="2"
        border="{{false}}"
        gutter="16"
        custom-class="overview-grid">
        <van-grid-item
          wx:for="{{overviewStats}}"
          wx:key="label"
          custom-class="overview-stat-card"
          bind:click="onStatClick"
          data-stat="{{item}}">
          <view class="overview-stat-content">
            <view class="overview-stat-icon" style="background-color: {{item.bgColor}}">
              <text>{{item.icon}}</text>
            </view>
            <text class="overview-stat-value">{{item.value}}</text>
            <text class="overview-stat-label">{{item.label}}</text>
            <view class="overview-stat-trend" wx:if="{{item.trend}}">
              <van-tag
                type="{{item.trend.type}}"
                size="mini"
                custom-class="overview-trend-tag">
                {{item.trend.icon}} {{item.trend.text}}
              </van-tag>
            </view>
          </view>
        </van-grid-item>
      </van-grid>
    </view>
  </van-card>

  <!-- 任务进度 -->
  <van-panel title="任务进度" custom-class="progress-panel">
    <view class="progress-content">
      <!-- 主进度条 -->
      <view class="main-progress">
        <view class="progress-header">
          <text class="progress-label">完成进度</text>
          <text class="progress-value">{{task.progress}}%</text>
        </view>
        <van-progress 
          percentage="{{task.progress}}" 
          stroke-width="8" 
          color="#1890fa"
          custom-class="task-progress" />
      </view>

      <!-- 统计信息 -->
      <van-grid column-num="3" border="{{false}}" custom-class="progress-stats">
        <van-grid-item custom-class="stat-item">
          <view class="stat-content">
            <text class="stat-value">{{task.completedSubtasks || 0}}</text>
            <text class="stat-label">已完成</text>
          </view>
        </van-grid-item>
        <van-grid-item custom-class="stat-item">
          <view class="stat-content">
            <text class="stat-value">{{task.totalSubtasks || 0}}</text>
            <text class="stat-label">总计</text>
          </view>
        </van-grid-item>
        <van-grid-item custom-class="stat-item">
          <view class="stat-content">
            <text class="stat-value">{{(task.totalSubtasks || 0) - (task.completedSubtasks || 0)}}</text>
            <text class="stat-label">剩余</text>
          </view>
        </van-grid-item>
      </van-grid>
    </view>
  </van-panel>

  <!-- 时间信息 -->
  <van-cell-group title="时间信息" custom-class="time-section">
    <van-cell 
      title="截止时间" 
      value="{{task.dueDate}} {{task.dueTime}}" 
      icon="clock-o" 
      border="{{false}}" />
    <van-cell 
      title="预计时长" 
      value="{{task.estimatedDuration}}" 
      icon="stopwatch-o" 
      border="{{false}}" />
    <van-cell 
      wx:if="{{task.actualDuration}}"
      title="实际时长" 
      value="{{task.actualDuration}}" 
      icon="passed" 
      border="{{false}}" />
    <van-cell 
      title="创建时间" 
      value="{{task.createdAt}}" 
      icon="calendar-o" 
      border="{{false}}" />
  </van-cell-group>

  <!-- 学习检查点 -->
  <view class="checkpoints-section">
    <van-collapse
      value="{{showSubtasks ? ['checkpoints'] : []}}"
      bind:change="onSubtasksChange"
      custom-class="checkpoints-collapse">
      <van-collapse-item
        name="checkpoints"
        custom-class="checkpoints-collapse-item">

        <!-- 自定义标题区域 -->
        <view slot="title" class="checkpoints-header">
          <view class="header-info">
            <view class="title-row">
              <text class="section-title">学习检查点</text>
              <view class="progress-badge">
                <text class="progress-text">{{task.completedSubtasks}}/{{task.totalSubtasks}}</text>
              </view>
            </view>

            <!-- 进度可视化 -->
            <view class="progress-visual">
              <van-circle
                value="{{checkpointProgress}}"
                size="60"
                stroke-width="4"
                color="{{checkpointProgress === 100 ? '#52c41a' : '#1890ff'}}"
                layer-color="#f0f0f0"
                text="{{checkpointProgress}}%"
                custom-class="checkpoint-circle" />
              <van-progress
                percentage="{{checkpointProgress}}"
                stroke-width="6"
                color="{{checkpointProgress === 100 ? '#52c41a' : '#1890ff'}}"
                track-color="#f0f0f0"
                show-pivot="{{false}}"
                custom-class="checkpoint-progress" />
              <text class="progress-label">{{checkpointProgress}}% 完成</text>
            </view>

            <!-- 批量操作区域 -->
            <view class="batch-operations" wx:if="{{task.subtasks && task.subtasks.length > 0}}">
              <van-button
                type="default"
                size="mini"
                bind:click="toggleSelectAll"
                custom-class="batch-btn">
                {{isAllSelected ? '取消全选' : '全选'}}
              </van-button>
              <van-button
                type="primary"
                size="mini"
                bind:click="batchComplete"
                disabled="{{selectedCheckpoints.length === 0}}"
                custom-class="batch-btn">
                批量完成
              </van-button>
              <van-button
                type="default"
                size="mini"
                bind:click="batchUncomplete"
                disabled="{{selectedCheckpoints.length === 0}}"
                custom-class="batch-btn">
                批量重置
              </van-button>
            </view>
          </view>

          <!-- 状态图标 -->
          <view class="status-icon">
            <van-icon
              name="{{showSubtasks ? 'arrow-up' : 'arrow-down'}}"
              size="16"
              color="#666" />
          </view>
        </view>

        <!-- 检查点列表 -->
        <view class="checkpoints-content">
          <view class="checkpoints-list">
            <view
              class="checkpoint-item {{item.completed ? 'completed' : 'pending'}} {{selectedCheckpoints.indexOf(item.id) !== -1 ? 'selected' : ''}}"
              wx:for="{{task.subtasks}}"
              wx:key="id"
              bind:tap="onCheckpointTap"
              bind:longpress="onCheckpointLongPress"
              data-id="{{item.id}}">

              <!-- 选择指示器 -->
              <view class="checkpoint-selector" bind:tap="toggleCheckpointSelection" data-id="{{item.id}}">
                <van-checkbox
                  value="{{selectedCheckpoints.indexOf(item.id) !== -1}}"
                  custom-class="selector-checkbox" />
              </view>

              <!-- 状态指示器 -->
              <view class="checkpoint-indicator">
                <van-icon
                  name="{{item.completed ? 'success' : 'circle'}}"
                  size="20"
                  color="{{item.completed ? '#52c41a' : '#d9d9d9'}}"
                  custom-class="status-icon {{item.completed ? 'completed-icon' : 'pending-icon'}}" />
              </view>

              <!-- 内容区域 -->
              <view class="checkpoint-content">
                <view class="checkpoint-title-row">
                  <text class="checkpoint-title {{item.completed ? 'completed-text' : ''}}">
                    {{item.title}}
                  </text>
                  <view class="checkpoint-status" wx:if="{{item.completed}}">
                    <van-tag type="success" size="small">已完成</van-tag>
                  </view>
                </view>

                <!-- 完成时间 -->
                <view class="checkpoint-meta" wx:if="{{item.completed && item.completedTime}}">
                  <van-icon name="clock-o" size="12" color="#999" />
                  <text class="completed-time">完成于 {{item.completedTime}}</text>
                </view>
              </view>

              <!-- 操作区域 -->
              <view class="checkpoint-action">
                <van-checkbox
                  value="{{item.completed}}"
                  bind:change="toggleSubtask"
                  data-id="{{item.id}}"
                  custom-class="checkpoint-checkbox" />
              </view>
            </view>
          </view>

          <!-- 空状态显示 -->
          <view class="empty-checkpoints" wx:if="{{!task.subtasks || task.subtasks.length === 0}}">
            <van-icon name="todo-list-o" size="48" color="#ddd" />
            <text class="empty-text">暂无学习检查点</text>
            <text class="empty-tip">检查点可以帮助您跟踪学习进度</text>
          </view>

          <!-- 完成状态总结 -->
          <view class="checkpoints-summary" wx:if="{{task.completedSubtasks > 0}}">
            <view class="summary-item">
              <van-icon name="passed" size="16" color="#52c41a" />
              <text class="summary-text">已完成 {{task.completedSubtasks}} 个检查点</text>
            </view>
            <view class="summary-item" wx:if="{{task.totalSubtasks - task.completedSubtasks > 0}}">
              <van-icon name="todo-list-o" size="16" color="#1890ff" />
              <text class="summary-text">还有 {{task.totalSubtasks - task.completedSubtasks}} 个待完成</text>
            </view>
          </view>
        </view>
      </van-collapse-item>
    </van-collapse>
  </view>

  <!-- 学习记录分析 -->
  <van-card
    wx:if="{{studyRecords.length > 0}}"
    title="学习记录分析"
    custom-class="records-section section-card">

    <view slot="desc">
      <!-- 学习统计概览 -->
      <view class="study-stats-overview">
        <van-grid column-num="3" border="{{false}}" gutter="12" custom-class="stats-grid">
          <van-grid-item wx:for="{{studyStats}}" wx:key="label" custom-class="stat-item">
            <view class="stat-content">
              <text class="stat-icon">{{item.icon}}</text>
              <text class="stat-value">{{item.value}}</text>
              <text class="stat-label">{{item.label}}</text>
            </view>
          </van-grid-item>
        </van-grid>
      </view>

      <!-- 学习效率趋势图 -->
      <view class="efficiency-chart" wx:if="{{efficiencyChartData.length > 0}}">
        <van-divider content-position="left">效率趋势</van-divider>
        <view class="chart-content">
          <view class="chart-bars">
            <view class="chart-bar" wx:for="{{efficiencyChartData}}" wx:key="date">
              <view class="bar-fill" style="height: {{item.efficiency}}%; background-color: {{item.efficiency >= 80 ? '#52C41A' : item.efficiency >= 60 ? '#FA8C16' : '#FF4D4F'}}"></view>
              <text class="bar-label">{{item.day}}</text>
              <text class="bar-value">{{item.efficiency}}%</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 时间分布分析 -->
      <view class="time-distribution" wx:if="{{timeDistribution.length > 0}}">
        <van-divider content-position="left">时间分布</van-divider>
        <view class="distribution-chart">
          <view class="time-slot" wx:for="{{timeDistribution}}" wx:key="period">
            <view class="slot-header">
              <text class="slot-period">{{item.period}}</text>
              <text class="slot-duration">{{item.duration}}</text>
            </view>
            <view class="slot-bar">
              <view class="slot-fill" style="width: {{item.percentage}}%; background-color: {{item.color}}"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 学习洞察 -->
      <view class="study-insights" wx:if="{{studyInsights.length > 0}}">
        <van-divider content-position="left">学习洞察</van-divider>
        <van-cell-group border="{{false}}">
          <van-cell
            wx:for="{{studyInsights}}"
            wx:key="id"
            title="{{item.text}}"
            icon="{{item.icon}}"
            custom-class="insight-cell" />
        </van-cell-group>
      </view>

      <!-- 详细记录列表 -->
      <view class="detailed-records">
        <van-divider content-position="left">
          <view class="records-header">
            <text>详细记录</text>
            <van-button
              type="default"
              size="mini"
              bind:click="toggleRecordsView"
              custom-class="toggle-btn">
              {{showDetailedRecords ? '收起' : '展开'}}
            </van-button>
          </view>
        </van-divider>

        <van-collapse
          value="{{showDetailedRecords ? ['detailed'] : []}}"
          custom-class="detailed-collapse">
          <van-collapse-item name="detailed" custom-class="detailed-item">
            <van-cell-group border="{{false}}">
              <van-cell
                wx:for="{{studyRecords}}"
                wx:key="id"
                title="{{item.title}}"
                label="{{item.time}}"
                value="{{item.duration}}"
                icon="description"
                border="{{false}}"
                custom-class="record-cell">

                <view slot="right-icon" wx:if="{{item.efficiency > 0}}">
                  <van-tag type="{{item.efficiency >= 80 ? 'success' : item.efficiency >= 60 ? 'warning' : 'default'}}" size="small">
                    效率{{item.efficiency}}%
                  </van-tag>
                </view>
              </van-cell>
            </van-cell-group>
          </van-collapse-item>
        </van-collapse>
      </view>

      <!-- 操作按钮 -->
      <view class="records-actions">
        <van-button
          type="default"
          size="small"
          bind:click="exportStudyReport"
          custom-class="action-btn">
          导出报告
        </van-button>
        <van-button
          type="primary"
          size="small"
          bind:click="addStudyRecord"
          custom-class="action-btn">
          添加记录
        </van-button>
      </view>
    </view>
  </van-card>

  <!-- 空状态 -->
  <van-card
    wx:else
    title="学习记录"
    custom-class="records-section section-card">
    <view slot="desc">
      <van-empty description="暂无学习记录" />
      <view class="empty-actions">
        <van-button
          type="primary"
          size="small"
          bind:click="addStudyRecord"
          custom-class="add-record-btn">
          添加第一条记录
        </van-button>
      </view>
    </view>
  </van-card>

  <!-- 智能快速操作面板 -->
  <van-card
    title="快速操作"
    custom-class="actions-panel section-card">

    <view slot="desc">
      <!-- 主要操作区域 -->
      <view class="primary-actions">
        <van-grid column-num="3" border="{{false}}" gutter="12" custom-class="primary-grid">
          <van-grid-item
            wx:for="{{smartActions}}"
            wx:key="id"
            bind:click="onSmartAction"
            data-action="{{item}}"
            custom-class="smart-action-item {{item.type}}">
            <view class="action-content">
              <van-icon
                name="{{item.icon}}"
                size="28"
                color="{{item.color}}"
                custom-class="action-icon" />
              <text class="action-text">{{item.text}}</text>
              <view class="action-badge" wx:if="{{item.badge}}">
                <van-tag type="{{item.badge.type}}" size="mini">{{item.badge.text}}</van-tag>
              </view>
            </view>
          </van-grid-item>
        </van-grid>
      </view>

      <!-- 次要操作区域 -->
      <view class="secondary-actions" wx:if="{{secondaryActions.length > 0}}">
        <van-divider content-position="left">更多操作</van-divider>
        <van-grid column-num="4" border="{{false}}" gutter="8" custom-class="secondary-grid">
          <van-grid-item
            wx:for="{{secondaryActions}}"
            wx:key="id"
            bind:click="onSecondaryAction"
            data-action="{{item}}"
            custom-class="secondary-action-item">
            <view class="secondary-content">
              <van-icon
                name="{{item.icon}}"
                size="20"
                color="{{item.color}}"
                custom-class="secondary-icon" />
              <text class="secondary-text">{{item.text}}</text>
            </view>
          </van-grid-item>
        </van-grid>
      </view>

      <!-- 快捷提示 -->
      <view class="action-tips" wx:if="{{actionTips}}">
        <van-notice-bar
          text="{{actionTips}}"
          background="#f0f9ff"
          color="#1890fa"
          left-icon="info-o"
          custom-class="tips-notice" />
      </view>
    </view>
  </van-card>

        <!-- 完成任务提示 -->
        <van-notice-bar
          wx:if="{{task.completed}}"
          text="任务已完成，继续保持良好的复习习惯！"
          background="#f0f9ff"
          color="#1890fa"
          left-icon="success"
          custom-class="completed-notice" />

        <!-- 开发测试工具 (仅开发环境显示) -->
        <view class="dev-tools" wx:if="{{__DEV__}}">
          <van-divider content-position="center">开发测试工具</van-divider>
          <van-cell-group>
            <van-cell
              title="运行功能测试"
              is-link
              bind:click="runFunctionalTests"
              icon="play-circle-o" />
            <van-cell
              title="性能监控报告"
              is-link
              bind:click="showPerformanceReport"
              icon="chart-trending-o" />
            <van-cell
              title="模拟网络错误"
              is-link
              bind:click="simulateNetworkError"
              icon="warning-o" />
            <van-cell
              title="清空测试数据"
              is-link
              bind:click="clearTestData"
              icon="delete-o" />
          </van-cell-group>
        </view>

      </scroll-view>
    </view>
  </view>
</view>

<!-- 更多操作菜单 -->
<van-action-sheet
  show="{{showActionSheet}}"
  title="更多操作"
  actions="{{actionSheetActions}}"
  bind:close="hideActionSheet"
  bind:select="onActionSelect"
  custom-class="more-actions-sheet" />