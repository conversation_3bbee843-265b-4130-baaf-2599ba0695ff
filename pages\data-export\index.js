// pages/data-export/index.js
Page({
  data: {
    exportDataTypes: [
      {
        id: 'tasks',
        name: '复习任务',
        description: '包含任务标题、描述、完成状态、时间等',
        count: 25,
        selected: true
      },
      {
        id: 'exams',
        name: '考试信息',
        description: '包含考试名称、时间、科目、准备进度等',
        count: 8,
        selected: true
      },
      {
        id: 'pomodoro',
        name: '专注记录',
        description: '包含番茄钟时长、效率评分、复习内容等',
        count: 156,
        selected: true
      },
      {
        id: 'achievements',
        name: '成就记录',
        description: '包含解锁的成就、获得时间、成就描述等',
        count: 12,
        selected: false
      },
      {
        id: 'statistics',
        name: '复习统计',
        description: '包含每日复习时长、效率分析、趋势数据等',
        count: 90,
        selected: false
      }
    ],

    selectedFormat: 'excel',
    exportFormats: [
      {
        id: 'excel',
        name: 'Excel',
        icon: '📊',
        description: 'Excel格式，支持多个工作表，便于数据分析和编辑'
      },
      {
        id: 'csv',
        name: 'CSV',
        icon: '📄',
        description: 'CSV格式，通用性强，可在各种软件中打开'
      },
      {
        id: 'json',
        name: 'JSON',
        icon: '🔧',
        description: 'JSON格式，适合程序员和技术用户，保留完整数据结构'
      },
      {
        id: 'pdf',
        name: 'PDF报告',
        icon: '📋',
        description: 'PDF格式的复习报告，包含图表和分析，适合打印和分享'
      }
    ],

    selectedDateRange: 'all',
    dateRangeOptions: [
      { value: 'all', label: '全部时间' },
      { value: 'last_week', label: '最近一周' },
      { value: 'last_month', label: '最近一月' },
      { value: 'last_3months', label: '最近三月' },
      { value: 'custom', label: '自定义' }
    ],

    customStartDate: '',
    customEndDate: '',

    exportPreview: {
      totalRecords: 0,
      fileSize: '0KB',
      dateRange: '全部时间',
      items: []
    },

    exportHistory: [],

    canExport: true,
    isExporting: false,
    showExportModal: false,
    exportProgress: 0,
    exportStatus: '准备中...',
    exportResult: null,

    showPreviewModal: false,
    previewTab: 'tasks',
    previewTabs: [
      { id: 'tasks', name: '任务' },
      { id: 'exams', name: '考试' },
      { id: 'pomodoro', name: '专注' }
    ],
    previewData: [],
    previewColumns: []
  },

  onLoad() {
    this.initExportData()
    this.loadExportHistory()
    this.updateExportPreview()
  },

  // 初始化导出数据
  initExportData() {
    // 计算各类数据的数量
    this.calculateDataCounts()

    // 更新选中格式信息
    this.updateSelectedFormatInfo()
  },

  // 计算数据数量
  calculateDataCounts() {
    try {
      // 获取各类数据
      const tasks = wx.getStorageSync('tasks') || []
      const exams = wx.getStorageSync('exams') || []
      const pomodoroRecords = wx.getStorageSync('pomodoroRecords') || []
      const achievements = wx.getStorageSync('userAchievements') || {}
      const statistics = wx.getStorageSync('dailyStats') || []

      // 更新数据类型计数
      const dataTypes = this.data.exportDataTypes.map(type => {
        let count = 0
        switch (type.id) {
          case 'tasks':
            count = tasks.length
            break
          case 'exams':
            count = exams.length
            break
          case 'pomodoro':
            count = pomodoroRecords.length
            break
          case 'achievements':
            count = Object.keys(achievements).length
            break
          case 'statistics':
            count = statistics.length
            break
        }
        return { ...type, count }
      })

      this.setData({ exportDataTypes: dataTypes })

    } catch (error) {
      console.error('计算数据数量失败:', error)
    }
  },

  // 切换数据类型选择
  toggleDataType(e) {
    const typeId = e.currentTarget.dataset.type
    const checked = e.detail.value

    const dataTypes = this.data.exportDataTypes.map(type => {
      if (type.id === typeId) {
        return { ...type, selected: checked }
      }
      return type
    })

    this.setData({ exportDataTypes: dataTypes })
    this.updateExportPreview()
  },

  // 选择导出格式
  selectExportFormat(e) {
    const formatId = e.currentTarget.dataset.format
    this.setData({ selectedFormat: formatId })
    this.updateSelectedFormatInfo()
    this.updateExportPreview()
  },

  // 更新选中格式信息
  updateSelectedFormatInfo() {
    const format = this.data.exportFormats.find(f => f.id === this.data.selectedFormat)
    this.setData({ selectedFormatInfo: format })
  },

  // 选择时间范围
  selectDateRange(e) {
    const range = e.currentTarget.dataset.range
    this.setData({ selectedDateRange: range })
    this.updateExportPreview()
  },

  // 选择开始日期
  selectStartDate(e) {
    this.setData({ customStartDate: e.detail.value })
    this.updateExportPreview()
  },

  // 选择结束日期
  selectEndDate(e) {
    this.setData({ customEndDate: e.detail.value })
    this.updateExportPreview()
  },

  // 更新导出预览
  updateExportPreview() {
    const selectedTypes = this.data.exportDataTypes.filter(type => type.selected)
    const totalRecords = selectedTypes.reduce((sum, type) => sum + type.count, 0)

    // 计算预计文件大小
    let fileSize = '0KB'
    if (totalRecords > 0) {
      const sizeKB = Math.ceil(totalRecords * 0.5) // 简化计算，每条记录约0.5KB
      if (sizeKB > 1024) {
        fileSize = `${(sizeKB / 1024).toFixed(1)}MB`
      } else {
        fileSize = `${sizeKB}KB`
      }
    }

    // 计算时间跨度
    let dateRange = '全部时间'
    const rangeOption = this.data.dateRangeOptions.find(opt => opt.value === this.data.selectedDateRange)
    if (rangeOption) {
      dateRange = rangeOption.label
    }
    if (this.data.selectedDateRange === 'custom' && this.data.customStartDate && this.data.customEndDate) {
      dateRange = `${this.data.customStartDate} 至 ${this.data.customEndDate}`
    }

    // 生成包含内容列表
    const items = selectedTypes.map(type => `${type.name}(${type.count}条)`)

    this.setData({
      exportPreview: {
        totalRecords,
        fileSize,
        dateRange,
        items
      },
      canExport: totalRecords > 0
    })
  },

  // 预览数据
  previewExport() {
    this.setData({ showPreviewModal: true })
    this.loadPreviewData('tasks')
  },

  // 加载预览数据
  loadPreviewData(tab) {
    let data = []
    let columns = []

    try {
      switch (tab) {
        case 'tasks':
          const tasks = wx.getStorageSync('tasks') || []
          data = tasks.slice(0, 10) // 只显示前10条
          columns = ['标题', '状态', '优先级', '截止时间', '创建时间']
          data = data.map(task => ({
            '标题': task.title,
            '状态': task.completed ? '已完成' : '进行中',
            '优先级': task.priority === 'high' ? '高' : task.priority === 'medium' ? '中' : '低',
            '截止时间': task.dueDate || '-',
            '创建时间': task.createdAt || '-'
          }))
          break

        case 'exams':
          const exams = wx.getStorageSync('exams') || []
          data = exams.slice(0, 10)
          columns = ['考试名称', '考试时间', '状态', '准备进度', '创建时间']
          data = data.map(exam => ({
            '考试名称': exam.name,
            '考试时间': exam.date,
            '状态': exam.status === 'upcoming' ? '即将到来' : exam.status === 'preparing' ? '备考中' : '已完成',
            '准备进度': `${exam.progress || 0}%`,
            '创建时间': exam.createdAt || '-'
          }))
          break

        case 'pomodoro':
          const records = wx.getStorageSync('pomodoroRecords') || []
          data = records.slice(0, 10)
          columns = ['日期', '专注时长', '效率评分', '关联任务', '复习内容']
          data = data.map(record => ({
            '日期': record.date,
            '专注时长': `${record.duration}分钟`,
            '效率评分': `${record.efficiency || 0}分`,
            '关联任务': record.taskTitle || '-',
            '复习内容': record.content || '-'
          }))
          break
      }

      this.setData({
        previewData: data,
        previewColumns: columns
      })

    } catch (error) {
      console.error('加载预览数据失败:', error)
      this.setData({
        previewData: [],
        previewColumns: []
      })
    }
  },

  // 切换预览标签
  switchPreviewTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({ previewTab: tab })
    this.loadPreviewData(tab)
  },

  // 开始导出
  startExport() {
    if (!this.data.canExport) {
      wx.showToast({
        title: '请选择要导出的数据',
        icon: 'none'
      })
      return
    }

    this.setData({
      showExportModal: true,
      isExporting: true,
      exportProgress: 0,
      exportStatus: '准备导出数据...'
    })

    this.performExport()
  },

  // 执行导出
  performExport() {
    const steps = [
      { progress: 20, status: '收集数据中...' },
      { progress: 40, status: '处理数据格式...' },
      { progress: 60, status: '生成文件...' },
      { progress: 80, status: '压缩文件...' },
      { progress: 100, status: '导出完成！' }
    ]

    let currentStep = 0

    const updateProgress = () => {
      if (currentStep < steps.length) {
        const step = steps[currentStep]
        this.setData({
          exportProgress: step.progress,
          exportStatus: step.status
        })

        currentStep++
        setTimeout(updateProgress, 800)
      } else {
        this.completeExport()
      }
    }

    updateProgress()
  },

  // 完成导出
  completeExport() {
    const selectedTypes = this.data.exportDataTypes.filter(type => type.selected)
    const totalRecords = selectedTypes.reduce((sum, type) => sum + type.count, 0)
    const format = this.data.exportFormats.find(f => f.id === this.data.selectedFormat)

    const result = {
      fileName: `复习数据导出_${new Date().toISOString().split('T')[0]}.${this.data.selectedFormat}`,
      fileSize: this.data.exportPreview.fileSize,
      recordCount: totalRecords,
      format: format.name,
      exportTime: new Date().toLocaleString()
    }

    // 保存到导出历史
    this.saveToExportHistory(result)

    this.setData({
      isExporting: false,
      exportResult: result
    })
  },

  // 保存到导出历史
  saveToExportHistory(result) {
    try {
      let history = wx.getStorageSync('exportHistory') || []

      const historyItem = {
        id: Date.now().toString(),
        name: result.fileName,
        format: result.format,
        size: result.fileSize,
        date: result.exportTime,
        recordCount: result.recordCount
      }

      history.unshift(historyItem)

      // 限制历史记录数量
      if (history.length > 10) {
        history = history.slice(0, 10)
      }

      wx.setStorageSync('exportHistory', history)
      this.setData({ exportHistory: history })

    } catch (error) {
      console.error('保存导出历史失败:', error)
    }
  },

  // 加载导出历史
  loadExportHistory() {
    try {
      const history = wx.getStorageSync('exportHistory') || []
      this.setData({ exportHistory: history })
    } catch (error) {
      console.error('加载导出历史失败:', error)
    }
  },

  // 下载导出文件
  downloadResult() {
    wx.showToast({
      title: '文件已保存到相册',
      icon: 'success'
    })
    this.hideExportModal()
  },

  // 分享导出结果
  shareResult() {
    wx.showShareMenu({
      withShareTicket: true,
      success: () => {
        wx.showToast({
          title: '分享成功',
          icon: 'success'
        })
      }
    })
  },

  // 下载历史文件
  downloadExport(e) {
    const item = e.currentTarget.dataset.item
    wx.showToast({
      title: `下载 ${item.name}`,
      icon: 'none'
    })
  },

  // 分享历史文件
  shareExport(e) {
    const item = e.currentTarget.dataset.item
    wx.showToast({
      title: `分享 ${item.name}`,
      icon: 'none'
    })
  },

  // 删除历史文件
  deleteExport(e) {
    const item = e.currentTarget.dataset.item

    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${item.name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          const history = this.data.exportHistory.filter(h => h.id !== item.id)

          try {
            wx.setStorageSync('exportHistory', history)
            this.setData({ exportHistory: history })

            wx.showToast({
              title: '已删除',
              icon: 'success'
            })
          } catch (error) {
            console.error('删除导出历史失败:', error)
          }
        }
      }
    })
  },

  // 清空导出历史
  clearExportHistory() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有导出历史吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('exportHistory')
            this.setData({ exportHistory: [] })

            wx.showToast({
              title: '已清空',
              icon: 'success'
            })
          } catch (error) {
            console.error('清空导出历史失败:', error)
          }
        }
      }
    })
  },

  // 隐藏导出弹窗
  hideExportModal() {
    this.setData({
      showExportModal: false,
      exportResult: null
    })
  },

  // 隐藏预览弹窗
  hidePreviewModal() {
    this.setData({ showPreviewModal: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '我的复习数据导出',
      path: '/pages/data-export/index',
      imageUrl: '/images/share-data-export.png'
    }
  }
})