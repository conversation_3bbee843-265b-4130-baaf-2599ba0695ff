/**app.wxss**/

/* 全局样式 */

/* 重置样式 */
page {
  background-color: #F5F5F5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
}

/* 容器样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 卡片样式 */
.card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

/* 按钮样式 */
.btn {
  border-radius: 8rpx;
  font-size: 28rpx;
  padding: 16rpx 32rpx;
  border: none;
  text-align: center;
  display: inline-block;
  box-sizing: border-box;
}

.btn-primary {
  background-color: #1890FF;
  color: #FFFFFF;
}

.btn-primary:active {
  background-color: #096DD9;
}

.btn-success {
  background-color: #52C41A;
  color: #FFFFFF;
}

.btn-success:active {
  background-color: #389E0D;
}

.btn-warning {
  background-color: #FA8C16;
  color: #FFFFFF;
}

.btn-warning:active {
  background-color: #D46B08;
}

.btn-danger {
  background-color: #FF4D4F;
  color: #FFFFFF;
}

.btn-danger:active {
  background-color: #CF1322;
}

.btn-default {
  background-color: #F5F5F5;
  color: #666666;
  border: 1rpx solid #D9D9D9;
}

.btn-default:active {
  background-color: #E6E6E6;
}

.btn-outline {
  background-color: transparent;
  border: 1rpx solid #1890FF;
  color: #1890FF;
}

.btn-outline:active {
  background-color: #E6F7FF;
}

/* 文本样式 */
.text-primary {
  color: #1890FF;
}

.text-success {
  color: #52C41A;
}

.text-warning {
  color: #FA8C16;
}

.text-danger {
  color: #FF4D4F;
}

.text-muted {
  color: #999999;
}

.text-secondary {
  color: #666666;
}

/* 字体大小 */
.text-xs {
  font-size: 20rpx;
}

.text-sm {
  font-size: 24rpx;
}

.text-base {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-2xl {
  font-size: 48rpx;
}

/* 字体粗细 */
.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.flex-1 {
  flex: 1;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* 间距样式 */
.gap-1 {
  gap: 8rpx;
}

.gap-2 {
  gap: 16rpx;
}

.gap-3 {
  gap: 24rpx;
}

.gap-4 {
  gap: 32rpx;
}

/* 内边距 */
.p-1 {
  padding: 8rpx;
}

.p-2 {
  padding: 16rpx;
}

.p-3 {
  padding: 24rpx;
}

.p-4 {
  padding: 32rpx;
}

.px-1 {
  padding-left: 8rpx;
  padding-right: 8rpx;
}

.px-2 {
  padding-left: 16rpx;
  padding-right: 16rpx;
}

.px-3 {
  padding-left: 24rpx;
  padding-right: 24rpx;
}

.px-4 {
  padding-left: 32rpx;
  padding-right: 32rpx;
}

.py-1 {
  padding-top: 8rpx;
  padding-bottom: 8rpx;
}

.py-2 {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
}

.py-3 {
  padding-top: 24rpx;
  padding-bottom: 24rpx;
}

.py-4 {
  padding-top: 32rpx;
  padding-bottom: 32rpx;
}

/* 外边距 */
.m-1 {
  margin: 8rpx;
}

.m-2 {
  margin: 16rpx;
}

.m-3 {
  margin: 24rpx;
}

.m-4 {
  margin: 32rpx;
}

.mb-1 {
  margin-bottom: 8rpx;
}

.mb-2 {
  margin-bottom: 16rpx;
}

.mb-3 {
  margin-bottom: 24rpx;
}

.mb-4 {
  margin-bottom: 32rpx;
}

.mt-1 {
  margin-top: 8rpx;
}

.mt-2 {
  margin-top: 16rpx;
}

.mt-3 {
  margin-top: 24rpx;
}

.mt-4 {
  margin-top: 32rpx;
}

/* 文本对齐 */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* 圆角 */
.rounded {
  border-radius: 8rpx;
}

.rounded-lg {
  border-radius: 16rpx;
}

.rounded-full {
  border-radius: 50%;
}

/* 阴影 */
.shadow {
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.shadow-lg {
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.shadow-xl {
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
}

/* 背景色 */
.bg-white {
  background-color: #FFFFFF;
}

.bg-gray {
  background-color: #F5F5F5;
}

.bg-primary {
  background-color: #1890FF;
}

.bg-success {
  background-color: #52C41A;
}

.bg-warning {
  background-color: #FA8C16;
}

.bg-danger {
  background-color: #FF4D4F;
}

/* 宽度 */
.w-full {
  width: 100%;
}

/* 高度 */
.h-full {
  height: 100%;
}

/* 隐藏 */
.hidden {
  display: none;
}

/* 动画 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}
