# 备考助手微信小程序

一个专注于考试复习和复习管理的微信小程序。

## 项目概述

备考助手是一个全面的考试准备工具，让每次复习都更高效。帮助用户制定复习计划、管理复习任务、跟踪复习进度，并提供番茄钟等高效复习工具。

## 主要功能

- 📚 **考试管理**: 创建和管理多个考试，设置考试时间和科目
- ✅ **任务系统**: 创建、编辑和追踪复习任务，支持优先级和提醒设置
- 🍅 **番茄钟**: 专注计时器，帮助用户保持高效复习状态
- 📊 **数据分析**: 详细的复习数据统计和效率分析
- 🏆 **成就系统**: 激励用户坚持复习的成就和徽章系统
- 👥 **复习小组**: 与其他用户组成复习小组，互相监督和激励
- 🔔 **智能提醒**: 基于复习计划的智能提醒系统

## 技术栈

- **前端框架**: 微信小程序原生开发
- **UI组件库**: Vant Weapp
- **后端服务**: 微信云开发
- **数据库**: 云数据库
- **状态管理**: 原生小程序数据绑定

## 最近更新 - Vant UI 重构 🎉

### 重构概述

2025年1月，我们完成了对项目的全面UI重构，将原有的自定义组件替换为业界标准的Vant Weapp组件库，提升了用户体验和开发效率。

### 重构页面

以下页面已完成Vant重构：

1. **exam-detail** - 考试详情页面
2. **add-task** - 添加任务页面  
3. **edit-task** - 编辑任务页面
4. **edit-exam** - 编辑考试页面
5. **data-center** - 数据中心页面
6. **achievement-system** - 成就系统页面

### 技术改进

- ✅ **统一设计风格**: 使用Vant官方设计规范，界面更加统一和专业
- ✅ **组件标准化**: 使用经过充分测试的Vant组件，减少自定义维护成本
- ✅ **用户体验提升**: 更流畅的交互效果和更清晰的视觉层次
- ✅ **开发效率**: 使用成熟组件库，加快后续功能开发

### 兼容性说明

- **微信小程序基础库**: 最低支持2.6.5版本
- **Vant Weapp版本**: @vant/weapp ^1.11.7
- **向后兼容**: 所有原有功能完全保持不变

## 项目结构

```
├── pages/                 # 页面文件
│   ├── exam-detail/      # 考试详情 ✨ 已重构
│   ├── add-task/         # 添加任务 ✨ 已重构  
│   ├── edit-task/        # 编辑任务 ✨ 已重构
│   ├── edit-exam/        # 编辑考试 ✨ 已重构
│   ├── data-center/      # 数据中心 ✨ 已重构
│   ├── achievement-system/ # 成就系统 ✨ 已重构
│   └── ...               # 其他页面
├── components/           # 自定义组件
├── utils/               # 工具函数
├── models/              # 数据模型
├── docs/                # 项目文档
└── cloudfunctions/      # 云函数
```

## 开发指南

### 环境要求

- 微信开发者工具
- Node.js 16+
- npm 或 yarn

### 安装依赖

```bash
npm install
# 或
yarn install
```

### Vant Weapp 配置

项目已配置Vant Weapp组件库，在页面JSON文件中引入需要的组件：

```json
{
  "usingComponents": {
    "van-button": "@vant/weapp/button/index",
    "van-cell": "@vant/weapp/cell/index"
  }
}
```

### 开发调试

1. 使用微信开发者工具打开项目
2. 确保已登录微信开发者账号
3. 配置云开发环境ID
4. 点击编译运行

## 部署发布

1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 登录微信公众平台提交审核
4. 审核通过后发布

## 贡献指南

欢迎贡献代码和提出建议！请遵循以下指南：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/new-feature`)
3. 提交更改 (`git commit -am 'Add new feature'`)
4. 推送到分支 (`git push origin feature/new-feature`)
5. 创建Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系我们

如有问题或建议，请通过以下方式联系：

- 项目Issues: [GitHub Issues](../../issues)
- 邮箱: <EMAIL>

---

**注意**: 本项目仍在持续开发中，功能和文档会不断更新。感谢您的关注和支持！ 