# 首页改版设计方案 v3.0

## 📋 改版背景

### 问题分析
- 原首页信息密度过高，用户认知负担重
- 功能与考试页面、任务页面存在重复
- 缺乏明确的价值定位和使用场景

### 设计目标
- 聚焦"今日行动"，突出当天最重要的任务
- 营造考试紧迫感，激发用户复习动力
- 提供跨考试的任务聚合视图
- 简化操作路径，提高使用效率

## 🎨 整体设计方案

### 页面布局结构
```
┌─────────────────────────────────┐
│ 状态栏                          │
├─────────────────────────────────┤
│                                │
│ 📚 考试板块（横向滑动）          │
│ ← [考试1] [考试2] [考试3] →     │
│                                │
├─────────────────────────────────┤
│                                │
│ 📝 今日任务聚合列表              │
│                                │
│                                │
│                                │
│                                │
│                                │
└─────────────────────────────────┘
│ TabBar                         │
└─────────────────────────────────┘
```

## 🎯 核心区域设计

### 1. 考试板块区域（横向滑动）
**尺寸**: 高度 240rpx，外边距 24rpx
**布局**:
```
┌─────────────────────────────────┐
│ ← [考试1] [考试2] [考试3] →     │ ← 横向滑动指示
│   ┌─────────────────────────┐   │
│   │ 高等数学期末考试         │   │ ← 考试名称 (32rpx)
│   │ 📅 还有 𝟏𝟓 天 (2025-12-12) │ ← 倒计时大数字 (48rpx, 加粗)
│   │ ████████░░ 80%          │   │ ← 个人备考进度条
│   │ (已完成8/总共10个任务)    │   │ ← 任务完成情况 (24rpx)
│   │ ─────────────────────   │   │ ← 分隔线
│   │ 👥 最快搭子: ██████████ 95% │ ← 搭子最高进度
│   └─────────────────────────┘   │
└─────────────────────────────────┘
```

**无搭子状态**:
```
┌─────────────────────────────────┐
│   ┌─────────────────────────┐   │
│   │ 高等数学期末考试         │   │ ← 点击区域1：考试详情
│   │ 📅 还有 𝟏𝟓 天 (2025-12-12) │ ← 倒计时大数字
│   │ ████████░░ 80%          │   │ ← 个人进度
│   │ (已完成8/总共10个任务)    │   │
│   │ ─────────────────────   │   │ ← 分隔线
│   │ 👥 找个搭子一起复习吧！   │   │ ← 引导文案
│   │    [创建搭子小组]        │   │ ← 点击区域2：创建功能
│   └─────────────────────────┘   │
└─────────────────────────────────┘
```

**样式规范**:
- 背景：白色卡片 (#FFFFFF)
- 圆角：16rpx
- 阴影：0 4rpx 12rpx rgba(0, 0, 0, 0.1)
- 倒计时数字：48rpx，加粗，主题蓝色 (#1890FF)
- 进度条：WuxUI Progress组件，主题蓝色
- 分隔线：1rpx solid #f0f0f0

**交互设计**:
- **横向滑动**: 使用swiper组件展示多个考试
- **点击区域1**: 考试信息区域 → 跳转考试详情页面
- **点击区域2**: 搭子功能区域 →
  - 有搭子：跳转搭子小组页面
  - 无搭子：跳转创建搭子小组页面
- **滑动指示**: 底部圆点显示当前位置

**数据结构**:
```javascript
exam: {
  id: "exam_001",
  name: "高等数学期末考试",
  date: "2025-12-12",
  daysLeft: 15,
  progress: {
    completed: 8,
    total: 10,
    percentage: 80
  },
  partner: {
    hasPartner: true,
    bestProgress: 95,
    bestPartnerName: "学霸小王"
  }
}
```

### 2. 今日任务区域标题
**尺寸**: 高度 80rpx
```
┌─────────────────────────────────┐
│  📝 今日任务 (3/8)               │
│                                │
└─────────────────────────────────┘
```

**样式规范**:
- 字体大小：32rpx，加粗
- 颜色：#333333
- 边距：24rpx 左右，16rpx 上下
- 显示完成进度：(已完成数/总任务数)

### 3. 任务列表项
**尺寸**: 每项高度 120rpx
```
┌─────────────────────────────────┐
│ [数学] ⭕ 高等数学复习            │
│ 🔴 重要 | ⏰ 今天 18:00 截止     │
│                        [开始]   │
├─────────────────────────────────┤
│ [英语] ⭕ 单词背诵              │
│ 🟡 一般 | ⏰ 明天 09:00 截止     │
│                        [开始]   │
├─────────────────────────────────┤
│ [政治] ✅ 知识点整理             │
│ 🟢 较低 | ✅ 已于 14:30 完成     │
│                      [已完成]   │
└─────────────────────────────────┘
```

**排序逻辑**:
1. **完成状态**: 未完成任务排在前面，已完成任务排在后面
2. **截止时间**: 未完成任务按截止时间升序排列（最紧急的在前）
3. **重要程度**: 相同截止时间的任务按重要程度排序（高→中→低）

**样式规范**:
- **第一行**：
  - 科目标签：圆角矩形 (12rpx)，不同科目不同颜色，20rpx白色文字
  - 任务状态图标：28rpx
  - 任务标题：28rpx，#333333，加粗
- **第二行**：
  - 重要程度标识：
    - 🔴 重要（高优先级）
    - 🟡 一般（中优先级）
    - 🟢 较低（低优先级）
  - 时间信息：24rpx
    - 今天截止：#FF4D4F（红色）
    - 明天截止：#FA8C16（橙色）
    - 后天及以后：#666666（灰色）
    - 已完成：#52C41A（绿色）
- **操作按钮**：
  - 开始：#1890FF，白色文字
  - 已完成：#F0F0F0，#999999文字
  - 圆角：16rpx，内边距：8rpx 16rpx

**交互**:
- 点击任务标题：查看任务详情
- 点击开始按钮：直接进入专注模式
- 点击状态图标：切换完成状态

### 4. 添加任务入口
**尺寸**: 高度 80rpx
```
┌─────────────────────────────────┐
│  + 添加今日任务                  │
└─────────────────────────────────┘
```

**样式规范**:
- 边框：2rpx 虚线 #1890FF
- 背景：透明
- 文字：#1890FF，28rpx
- 圆角：12rpx
- 居中对齐

**交互**:
- 点击进入添加任务页面
- 自动设置为今日任务

### 5. 有考试但无今日任务引导状态
**显示条件**: `examList.length > 0 && todayTasks.length === 0`
**尺寸**: 高度 400rpx，外边距 40rpx
**布局**:
```
┌─────────────────────────────────┐
│                                 │
│            📝                   │  ← 大图标 (80rpx)
│      今天还没有复习任务          │  ← 标题 (32rpx, 加粗)
│  为您的考试创建今日复习任务，    │  ← 描述 (28rpx)
│      开始高效复习               │
│                                 │
│      [创建今日任务]              │  ← 主要按钮 (positive)
│      [智能生成任务]              │  ← 次要按钮 (default)
│                                 │
└─────────────────────────────────┘
```

**样式规范**:
- 背景：透明
- 图标：📝 emoji，80rpx
- 标题：32rpx，加粗，#333333
- 描述：28rpx，#666666，行高1.5，最大宽度500rpx
- 按钮间距：16rpx
- 按钮容器最大宽度：400rpx
- 整体居中对齐

**交互设计**:
- **创建今日任务**: 跳转到添加任务页面 (`onAddTask`)
- **智能生成任务**: 显示考试选择弹窗 → AI生成任务 (`onQuickCreateTasks`)

**功能流程**:
```
智能生成任务流程:
1. 用户点击"智能生成任务"
2. 显示考试选择ActionSheet
3. 用户选择考试
4. 调用AI生成任务API (SmartApi.generateTasksForExam)
5. 显示生成进度 (wx.showLoading)
6. 任务生成完成，显示成功提示
7. 重新加载今日数据 (loadTodayData)
8. 引导状态消失，显示生成的任务列表
```

**技术实现**:
```javascript
// 显示条件
wx:if="{{todayTasks.length === 0 && examList.length > 0}}"

// 智能生成任务方法
onQuickCreateTasks() {
  const examNames = this.data.examList.map(exam => exam.name)
  wx.showActionSheet({
    itemList: examNames,
    success: (res) => {
      const selectedExam = this.data.examList[res.tapIndex]
      this.generateTasksForExam(selectedExam)
    }
  })
}
```

## 🎈 悬浮快捷入口设计

### 位置和样式
- **位置**: 右下角，距离底部 140rpx，距离右边 40rpx
- **主按钮**: 直径 112rpx，蓝色圆形
- **图标**: 加号，36rpx，白色
- **阴影**: 0 8rpx 24rpx rgba(24, 144, 255, 0.4)

### 展开菜单
```
                    [📝 添加任务]      ●
                    [📅 添加考试]      ●
                    [🍅 开始专注]      ●
                                      ●  ← 主按钮
```

**菜单项样式**:
- 按钮：直径 96rpx，蓝色主题
- 标签：黑色半透明背景，白色文字
- 动画：滑动展开，淡入效果

**功能映射**:
- 📝 添加任务 → 添加任务页面
- 📅 添加考试 → 添加考试页面
- 🍅 开始专注 → 专注页面（TabBar切换）

## 🎨 色彩方案

### 主色调
- **品牌蓝**: #1890FF
- **警示红**: #FF6B6B
- **成功绿**: #52C41A

### 辅助色
- **深灰**: #333333 (主要文字)
- **中灰**: #666666 (次要文字)
- **浅灰**: #999999 (禁用状态)
- **背景灰**: #F8F9FA

### 科目标签色彩
- **数学**: #FF6B6B (红色)
- **英语**: #4ECDC4 (青色)
- **政治**: #45B7D1 (蓝色)
- **专业课**: #96CEB4 (绿色)
- **其他**: #FECA57 (黄色)

## 📱 响应式设计

### 适配说明
- 支持不同屏幕尺寸
- 最小宽度：320px
- 最大宽度：480px
- 使用rpx单位确保等比缩放

### 内容优先级
1. 考试倒计时（必显示）
2. 今日任务列表（最多显示8个，按优先级排序）
3. 添加任务入口（必显示）
4. 悬浮按钮（必显示）

### 任务排序算法
```javascript
// 排序权重计算
function getTaskPriority(task) {
  let weight = 0;

  // 完成状态权重（未完成优先）
  if (!task.completed) weight += 1000;

  // 截止时间权重（越紧急权重越高）
  const hoursUntilDue = getHoursUntilDue(task.dueTime);
  weight += (100 - Math.min(hoursUntilDue, 99));

  // 重要程度权重
  const priorityWeight = {
    'high': 10,
    'medium': 5,
    'low': 1
  };
  weight += priorityWeight[task.priority] || 1;

  return weight;
}
```

## 🔄 与其他页面的差异化

### 首页 vs 考试页面
- **首页**: 今日聚焦 + 紧急提醒
- **考试页面**: 考试管理 + 长期规划

### 首页 vs 任务页面  
- **首页**: 跨考试任务聚合 + 快速启动
- **任务页面**: 单考试任务管理 + 详细操作

### 首页 vs 专注页面
- **首页**: 任务选择 + 快速开始
- **专注页面**: 专注执行 + 时间管理

## 📊 成功指标

### 用户体验指标
- 首页停留时间 < 30秒（快速决策）
- 任务开始转化率 > 60%
- 用户满意度 > 4.5/5

### 功能使用指标
- 考试板块点击率 > 25%
- 搭子功能使用率 > 15%
- 今日任务完成率 > 70%
- 悬浮按钮使用率 > 30%
- 引导状态转化率 > 50%（有考试无任务时的任务创建率）
- 智能生成任务使用率 > 20%（引导状态下的AI生成使用率）

---

**设计版本**: v3.0  
**设计日期**: 2025-07-03  
**设计师**: AI交互设计专家  
**状态**: 待开发实现
