/**
 * 考试数据模型类
 * 提供考试数据的验证、格式化、转换等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-04
 */

class ExamModel {
  /**
   * 构造函数
   * @param {Object} data - 考试数据
   */
  constructor(data = {}) {
    this.rawData = data
    this.data = this.validate(data)
  }

  /**
   * 数据验证
   * @param {Object} data - 待验证的数据
   * @returns {Object} 验证后的数据
   * @throws {Error} 验证失败时抛出错误
   */
  validate(data) {
    const errors = []
    
    // 必填字段验证
    if (!data.title || typeof data.title !== 'string' || data.title.trim().length === 0) {
      errors.push('考试名称不能为空')
    }
    
    if (data.title && data.title.trim().length > 50) {
      errors.push('考试名称不能超过50个字符')
    }
    
    if (!data.examDate || !this.isValidDate(data.examDate)) {
      errors.push('考试日期格式不正确，请使用YYYY-MM-DD格式')
    }
    
    if (!data.examTime || !this.isValidTime(data.examTime)) {
      errors.push('考试时间格式不正确，请使用HH:MM格式')
    }
    
    // 可选字段验证 - 科目支持数组格式
    if (data.subject) {
      if (Array.isArray(data.subject)) {
        // 科目数组验证
        if (data.subject.length > 10) {
          errors.push('考试科目不能超过10个')
        }
        data.subject.forEach((subj, index) => {
          if (typeof subj !== 'string' || subj.trim().length === 0) {
            errors.push(`第${index + 1}个科目不能为空`)
          } else if (subj.trim().length > 30) {
            errors.push(`第${index + 1}个科目不能超过30个字符`)
          }
        })
      } else if (typeof data.subject === 'string') {
        // 兼容旧的字符串格式
        if (data.subject.length > 30) {
          errors.push('考试科目不能超过30个字符')
        }
      } else {
        errors.push('考试科目格式不正确，应为字符串或字符串数组')
      }
    }
    
    if (data.location && data.location.length > 100) {
      errors.push('考试地点不能超过100个字符')
    }
    
    if (data.description && data.description.length > 500) {
      errors.push('考试描述不能超过500个字符')
    }
    
    // 枚举值验证 - 放宽类型验证，允许未知类型
    if (data.type && !this.isValidExamType(data.type)) {
      console.warn('考试类型不在预定义枚举中，将使用默认类型:', data.type)
      // 不再抛出错误，而是在后续处理中使用默认值
    }
    
    if (data.importance && !this.isValidImportance(data.importance)) {
      errors.push('重要程度不正确')
    }
    
    if (data.status && !this.isValidStatus(data.status)) {
      console.warn('考试状态验证失败，使用默认状态:', {
        考试数据: data,
        状态值: data.status,
        状态类型: typeof data.status
      })
      // 临时修复：如果状态无效，设置为默认状态而不是抛出错误
      data.status = 'preparing'
    }
    
    // 提醒设置验证
    if (data.reminderSettings && !this.isValidReminderSettings(data.reminderSettings)) {
      errors.push('提醒设置格式不正确')
    }
    
    // 业务逻辑验证
    if (data.examDate && this.isDateInPast(data.examDate) && !data._id) {
      errors.push('考试日期不能早于当前日期')
    }
    
    if (errors.length > 0) {
      throw new Error(`数据验证失败: ${errors.join(', ')}`)
    }
    
    return this.format(data)
  }

  /**
   * 数据格式化
   * @param {Object} data - 待格式化的数据
   * @returns {Object} 格式化后的数据
   */
  format(data) {
    const formatted = {
      title: this.trimString(data.title),
      subject: this.normalizeSubjects(data.subject),
      examDate: this.normalizeDate(data.examDate),
      examTime: this.normalizeTime(data.examTime),
      location: this.trimString(data.location) || '',
      description: this.trimString(data.description) || '',
      type: this.isValidExamType(data.type) ? data.type : ExamModel.TYPE.FINAL,
      importance: data.importance || ExamModel.IMPORTANCE.MEDIUM,
      status: data.status || ExamModel.STATUS.PREPARING,
      reminderSettings: this.normalizeReminderSettings(data.reminderSettings)
    }

    // 保留系统字段
    if (data._id) formatted._id = data._id
    if (data.userId) formatted.userId = data.userId
    if (data.createTime) formatted.createTime = data.createTime
    if (data.updateTime) formatted.updateTime = data.updateTime

    return formatted
  }

  /**
   * 转换为API格式
   * @returns {Object} API格式的数据
   */
  toApiFormat() {
    const apiData = { ...this.data }
    
    // 移除系统生成的字段（创建时）
    if (!apiData._id) {
      delete apiData._id
      delete apiData.createTime
      delete apiData.updateTime
    }
    
    return apiData
  }

  /**
   * 转换为显示格式
   * @returns {Object} 用于界面显示的数据
   */
  toDisplayFormat() {
    return {
      ...this.data,
      typeText: this.getTypeText(this.data.type),
      importanceText: this.getImportanceText(this.data.importance),
      statusText: this.getStatusText(this.data.status),
      dateTimeText: `${this.data.examDate} ${this.data.examTime}`,
      reminderText: this.getReminderText(this.data.reminderSettings)
    }
  }

  /**
   * 获取考试类型文本
   * @param {string} type - 考试类型
   * @returns {string} 类型文本
   */
  getTypeText(type) {
    const typeMap = {
      [ExamModel.TYPE.FINAL]: '期末考试',
      [ExamModel.TYPE.MIDTERM]: '期中考试',
      [ExamModel.TYPE.QUIZ]: '小测验',
      [ExamModel.TYPE.CERTIFICATE]: '资格考试',
      [ExamModel.TYPE.ENTRANCE]: '入学考试',
      [ExamModel.TYPE.OTHER]: '其他'
    }
    return typeMap[type] || '未知'
  }

  /**
   * 获取重要程度文本
   * @param {string} importance - 重要程度
   * @returns {string} 重要程度文本
   */
  getImportanceText(importance) {
    const importanceMap = {
      [ExamModel.IMPORTANCE.HIGH]: '非常重要',
      [ExamModel.IMPORTANCE.MEDIUM]: '重要',
      [ExamModel.IMPORTANCE.LOW]: '一般'
    }
    return importanceMap[importance] || '未知'
  }

  /**
   * 获取状态文本
   * @param {string} status - 考试状态
   * @returns {string} 状态文本
   */
  getStatusText(status) {
    const statusMap = {
      [ExamModel.STATUS.UPCOMING]: '即将到来',
      [ExamModel.STATUS.PREPARING]: '备考中',
      [ExamModel.STATUS.COMPLETED]: '已完成',
      [ExamModel.STATUS.PAST]: '已过期',
      [ExamModel.STATUS.ONGOING]: '进行中',
      [ExamModel.STATUS.FINISHED]: '已结束'
    }
    return statusMap[status] || '未知'
  }

  /**
   * 获取提醒设置文本
   * @param {Array} reminderSettings - 提醒设置
   * @returns {string} 提醒文本
   */
  getReminderText(reminderSettings) {
    if (!Array.isArray(reminderSettings) || reminderSettings.length === 0) {
      return '无提醒'
    }
    
    const reminderMap = {
      [ExamModel.REMINDER.ONE_DAY]: '考前1天',
      [ExamModel.REMINDER.THREE_DAYS]: '考前3天',
      [ExamModel.REMINDER.ONE_WEEK]: '考前1周',
      [ExamModel.REMINDER.TWO_WEEKS]: '考前2周'
    }
    
    return reminderSettings.map(setting => reminderMap[setting] || setting).join('、')
  }

  // ==================== 验证方法 ====================

  /**
   * 验证日期格式
   * @param {string} dateStr - 日期字符串
   * @returns {boolean} 是否有效
   */
  isValidDate(dateStr) {
    if (typeof dateStr !== 'string') return false
    const regex = /^\d{4}-\d{2}-\d{2}$/
    if (!regex.test(dateStr)) return false
    const date = new Date(dateStr)
    return date instanceof Date && !isNaN(date) && date.toISOString().split('T')[0] === dateStr
  }

  /**
   * 验证时间格式
   * @param {string} timeStr - 时间字符串
   * @returns {boolean} 是否有效
   */
  isValidTime(timeStr) {
    if (typeof timeStr !== 'string') return false
    const regex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    return regex.test(timeStr)
  }

  /**
   * 验证考试类型
   * @param {string} type - 考试类型
   * @returns {boolean} 是否有效
   */
  isValidExamType(type) {
    return Object.values(ExamModel.TYPE).includes(type)
  }

  /**
   * 验证重要程度
   * @param {string} importance - 重要程度
   * @returns {boolean} 是否有效
   */
  isValidImportance(importance) {
    return Object.values(ExamModel.IMPORTANCE).includes(importance)
  }

  /**
   * 验证考试状态
   * @param {string} status - 考试状态
   * @returns {boolean} 是否有效
   */
  isValidStatus(status) {
    const validStatuses = Object.values(ExamModel.STATUS)
    const isValid = validStatuses.includes(status)

    if (!isValid) {
      console.error('状态验证失败:', {
        输入状态: status,
        有效状态列表: validStatuses,
        STATUS枚举: ExamModel.STATUS
      })
    }

    return isValid
  }

  /**
   * 验证提醒设置
   * @param {Array} reminderSettings - 提醒设置
   * @returns {boolean} 是否有效
   */
  isValidReminderSettings(reminderSettings) {
    if (!Array.isArray(reminderSettings)) return false
    return reminderSettings.every(setting => Object.values(ExamModel.REMINDER).includes(setting))
  }

  /**
   * 检查日期是否在过去
   * @param {string} dateStr - 日期字符串
   * @returns {boolean} 是否在过去
   */
  isDateInPast(dateStr) {
    const examDate = new Date(dateStr)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return examDate < today
  }

  // ==================== 格式化方法 ====================

  /**
   * 去除字符串首尾空格
   * @param {string} str - 字符串
   * @returns {string} 处理后的字符串
   */
  trimString(str) {
    return typeof str === 'string' ? str.trim() : str
  }

  /**
   * 标准化日期格式
   * @param {string} dateStr - 日期字符串
   * @returns {string} 标准化后的日期
   */
  normalizeDate(dateStr) {
    const date = new Date(dateStr)
    return date.toISOString().split('T')[0]
  }

  /**
   * 标准化时间格式
   * @param {string} timeStr - 时间字符串
   * @returns {string} 标准化后的时间
   */
  normalizeTime(timeStr) {
    const [hours, minutes] = timeStr.split(':')
    return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`
  }

  /**
   * 标准化提醒设置
   * @param {Array} reminderSettings - 提醒设置
   * @returns {Array} 标准化后的提醒设置
   */
  normalizeReminderSettings(reminderSettings) {
    if (!Array.isArray(reminderSettings)) {
      return [ExamModel.REMINDER.ONE_DAY, ExamModel.REMINDER.THREE_DAYS]
    }
    
    // 去重并过滤无效值
    const validSettings = [...new Set(reminderSettings)].filter(setting => 
      Object.values(ExamModel.REMINDER).includes(setting)
    )
    
    return validSettings.length > 0 ? validSettings : [ExamModel.REMINDER.ONE_DAY, ExamModel.REMINDER.THREE_DAYS]
  }

  /**
   * 标准化科目数据
   * @param {string|Array} subjects - 科目数据
   * @returns {Array} 标准化后的科目数组
   */
  normalizeSubjects(subjects) {
    if (!subjects) {
      return []
    }

    if (Array.isArray(subjects)) {
      // 过滤空值并去重
      return [...new Set(subjects
        .map(subj => typeof subj === 'string' ? subj.trim() : '')
        .filter(subj => subj.length > 0)
      )]
    }

    if (typeof subjects === 'string') {
      const trimmed = subjects.trim()
      return trimmed ? [trimmed] : []
    }

    return []
  }

  // ==================== 静态方法 ====================

  /**
   * 从API数据创建考试模型
   * @param {Object} apiData - API返回的数据
   * @returns {ExamModel} 考试模型实例
   */
  static fromApiData(apiData) {
    return new ExamModel(apiData)
  }

  /**
   * 从旧格式数据创建考试模型（兼容性处理）
   * @param {Object} oldData - 旧格式数据
   * @returns {ExamModel} 考试模型实例
   */
  static fromLegacyData(oldData) {
    const mappedData = ExamModel.mapLegacyFields(oldData)
    return new ExamModel(mappedData)
  }

  /**
   * 批量格式化考试列表
   * @param {Array} examList - 考试数据列表
   * @returns {Array} 格式化后的考试列表
   */
  static formatList(examList) {
    if (!Array.isArray(examList)) return []

    return examList.map(exam => {
      try {
        return new ExamModel(exam).data
      } catch (error) {
        console.error('格式化考试数据失败:', error.message, exam)

        // 如果失败，尝试使用默认值补全数据
        try {
          const fixedExam = {
            ...this.getDefaultData(),
            ...exam,
            // 确保必填字段有值
            title: exam.title || exam.name || '未命名考试',
            examDate: exam.examDate || exam.date || '',
            examTime: exam.examTime || exam.time || '',
            type: Object.values(ExamModel.TYPE).includes(exam.type) ? exam.type : ExamModel.TYPE.FINAL,
            importance: exam.importance || 'medium',
            status: exam.status || 'preparing'
          }

          return new ExamModel(fixedExam).data
        } catch (secondError) {
          console.error('使用默认值修复数据也失败:', secondError, exam)
          return null
        }
      }
    }).filter(exam => exam !== null)
  }

  /**
   * 批量转换为显示格式
   * @param {Array} examList - 考试数据列表
   * @returns {Array} 显示格式的考试列表
   */
  static toDisplayList(examList) {
    if (!Array.isArray(examList)) return []
    return examList.map(exam => {
      try {
        return new ExamModel(exam).toDisplayFormat()
      } catch (error) {
        console.error('转换考试显示格式失败:', error, exam)
        return null
      }
    }).filter(exam => exam !== null)
  }

  /**
   * 映射旧字段到新字段（兼容性处理）
   * @param {Object} oldData - 旧格式数据
   * @returns {Object} 映射后的数据
   */
  static mapLegacyFields(oldData) {
    const mappedData = { ...oldData }

    // 字段名映射
    const fieldMapping = {
      'name': 'title',
      'date': 'examDate',
      'time': 'examTime',
      'notes': 'description'
    }

    Object.keys(fieldMapping).forEach(oldField => {
      if (oldData[oldField] !== undefined) {
        mappedData[fieldMapping[oldField]] = oldData[oldField]
        delete mappedData[oldField]
      }
    })

    // 科目字段特殊处理（数组转单个）
    if (oldData.subjects) {
      if (Array.isArray(oldData.subjects)) {
        mappedData.subject = oldData.subjects.length > 0 ? oldData.subjects[0] : ''
      } else {
        mappedData.subject = oldData.subjects
      }
      delete mappedData.subjects
    }

    // 提醒设置特殊处理
    if (oldData.reminderEnabled !== undefined || oldData.reminderFrequency !== undefined) {
      if (oldData.reminderEnabled) {
        const frequency = oldData.reminderFrequency || 'daily'
        mappedData.reminderSettings = frequency === 'daily' ? ['1day'] : ['3days']
      } else {
        mappedData.reminderSettings = []
      }
      delete mappedData.reminderEnabled
      delete mappedData.reminderFrequency
    }

    return mappedData
  }

  /**
   * 验证考试数据（静态方法）
   * @param {Object} data - 待验证的数据
   * @returns {Object} 验证结果 { isValid: boolean, errors: Array }
   */
  static validateData(data) {
    try {
      new ExamModel(data)
      return { isValid: true, errors: [] }
    } catch (error) {
      const errors = error.message.replace('数据验证失败: ', '').split(', ')
      return { isValid: false, errors }
    }
  }

  /**
   * 获取默认考试数据
   * @returns {Object} 默认考试数据
   */
  static getDefaultData() {
    const now = new Date()
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)

    return {
      title: '',
      subject: [], // 科目数组
      examDate: tomorrow.toISOString().split('T')[0],
      examTime: '09:00',
      location: '',
      description: '',
      type: ExamModel.TYPE.FINAL,
      importance: ExamModel.IMPORTANCE.MEDIUM,
      status: ExamModel.STATUS.PREPARING,
      reminderSettings: [ExamModel.REMINDER.ONE_DAY, ExamModel.REMINDER.THREE_DAYS]
    }
  }
}

// ==================== 常量定义 ====================

/**
 * 考试类型枚举
 */
ExamModel.TYPE = {
  FINAL: 'final',           // 期末考试
  MIDTERM: 'midterm',       // 期中考试
  QUIZ: 'quiz',             // 小测验
  CERTIFICATE: 'certificate', // 资格考试
  ENTRANCE: 'entrance',     // 入学考试
  OTHER: 'other'            // 其他
}

/**
 * 重要程度枚举
 */
ExamModel.IMPORTANCE = {
  HIGH: 'high',       // 非常重要
  MEDIUM: 'medium',   // 重要
  LOW: 'low'          // 一般
}

/**
 * 考试状态枚举
 */
ExamModel.STATUS = {
  UPCOMING: 'upcoming',   // 即将到来
  PREPARING: 'preparing', // 备考中
  COMPLETED: 'completed', // 已完成
  PAST: 'past',          // 已过期
  ONGOING: 'ongoing',     // 进行中（保留兼容）
  FINISHED: 'finished'    // 已结束（保留兼容）
}

/**
 * 提醒设置枚举
 */
ExamModel.REMINDER = {
  ONE_DAY: '1day',       // 考试前1天
  THREE_DAYS: '3days',   // 考试前3天
  ONE_WEEK: '1week',     // 考试前1周
  TWO_WEEKS: '2weeks'    // 考试前2周
}

/**
 * 错误类型枚举
 */
ExamModel.ERROR_TYPES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',     // 数据验证错误
  NOT_FOUND: 'NOT_FOUND',                   // 考试不存在
  PERMISSION_DENIED: 'PERMISSION_DENIED',   // 权限不足
  DUPLICATE_ERROR: 'DUPLICATE_ERROR',       // 重复数据
  SYSTEM_ERROR: 'SYSTEM_ERROR'              // 系统错误
}

/**
 * 错误消息模板
 */
ExamModel.ERROR_MESSAGES = {
  TITLE_REQUIRED: '考试名称不能为空',
  TITLE_TOO_LONG: '考试名称不能超过50个字符',
  SUBJECT_TOO_LONG: '考试科目不能超过30个字符',
  LOCATION_TOO_LONG: '考试地点不能超过100个字符',
  DESCRIPTION_TOO_LONG: '考试描述不能超过500个字符',
  INVALID_DATE: '考试日期格式不正确，请使用YYYY-MM-DD格式',
  INVALID_TIME: '考试时间格式不正确，请使用HH:MM格式',
  INVALID_TYPE: '考试类型不正确',
  INVALID_IMPORTANCE: '重要程度不正确',
  INVALID_STATUS: '考试状态不正确',
  INVALID_REMINDER: '提醒设置格式不正确',
  DATE_IN_PAST: '考试日期不能早于当前日期',
  EXAM_NOT_FOUND: '考试不存在或已被删除',
  PERMISSION_DENIED: '您没有权限操作此考试'
}

// 导出模块（支持多种模块系统）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ExamModel
} else if (typeof window !== 'undefined') {
  window.ExamModel = ExamModel
}
