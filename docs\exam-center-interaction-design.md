# 考试中心页面交互设计规范

## 📋 文档概述

**文档版本**: v1.0  
**创建日期**: 2025-01-10  
**设计师**: AI交互设计专家  
**文档类型**: 详细交互设计规范

## 🎯 交互设计原则

### 核心原则
1. **紧迫感优先**: 所有交互都要体现时间紧迫性
2. **社交激励**: 突出小组对比和竞争元素
3. **一步到位**: 减少操作步骤，提供快捷入口
4. **视觉反馈**: 每个操作都有明确的视觉反馈

## 📱 页面整体交互流程

### 页面加载交互
```
1. 骨架屏显示 (0.5s)
2. 考试卡片依次出现 (每个延迟0.1s)
3. 紧急考试优先显示，带闪烁效果
4. 统计数据动画填充
5. 小组信息最后加载显示
```

### 页面刷新交互
```
下拉刷新:
- 触发距离: 80rpx
- 刷新图标: 旋转的⚡闪电图标
- 完成提示: "数据已更新" + 绿色✓
- 新增内容: 高亮显示3秒后恢复正常
```

## 🔥 考试卡片交互设计

### 卡片状态视觉设计

#### 紧急考试卡片 (3天内)
```
视觉特征:
- 左侧红色指示条 (6rpx宽)
- 🔥 火焰图标在标题前
- 红色倒计时文字 (28rpx, 加粗)
- 开始复习按钮红色闪烁
- 整体卡片轻微阴影加深

动画效果:
- 火焰图标: 1.5s缓慢脉冲动画
- 复习按钮: 2s闪烁循环 (opacity 0.7 ↔ 1.0)
- 倒计时: 每秒轻微缩放 (scale 1.0 ↔ 1.05)
```

#### 正常考试卡片
```
视觉特征:
- 左侧蓝色指示条 (4rpx宽)
- 标准字体颜色和大小
- 蓝色主题按钮
- 标准阴影效果

交互状态:
- hover: 轻微上移 (-2rpx)
- active: 轻微缩放 (scale 0.98)
```

#### 已完成考试卡片
```
视觉特征:
- 左侧绿色指示条 (4rpx宽)
- 整体透明度 0.8
- 绿色✓完成标识
- 降低视觉权重

折叠交互:
- 默认显示1个已完成考试
- 超过1个时显示"查看更多已完成 (2)"
- 点击展开/收起动画 (0.3s ease-out)
```

### 快速状态切换交互

#### 切换按钮设计
```
位置: 卡片右上角
尺寸: 64rpx × 32rpx
圆角: 16rpx

状态样式:
备考中 → 已完成:
- 按钮: "✓完成" 绿色背景
- 点击: 缩放动画 + 成功提示音
- 反馈: "已标记为完成" Toast

已完成 → 备考中:
- 按钮: "恢复" 灰色边框
- 点击: 确认对话框 "确定恢复备考状态？"
- 反馈: "已恢复备考" Toast
```

#### 切换动画序列
```
1. 按钮点击缩放 (0.1s)
2. 卡片状态颜色渐变 (0.3s)
3. 位置重新排序 (0.4s ease-in-out)
4. 统计数据更新 (0.2s)
5. 成功提示显示 (2s后自动消失)
```

### 科目进度交互

#### 进度显示逻辑
```
科目进度计算:
- 单科目: "数学 85%" (大字显示)
- 多科目: "数学 85% | 英语 72%" (滚动显示)
- 超过3科目: "数学 85% | 英语 72% 等5科"

交互行为:
- 点击科目进度: 展开详细进度弹窗
- 长按科目: 快速跳转到该科目任务列表
```

#### 进度条交互
```
进度条样式:
- 高度: 8rpx (比标准更粗，突出显示)
- 圆角: 4rpx
- 动画: 加载时从0%动画到实际进度 (1s ease-out)

颜色分级:
- 80%+: #52C41A (绿色) + "优秀"
- 60-79%: #1890FF (蓝色) + "良好"
- 40-59%: #FA8C16 (橙色) + "需努力"
- <40%: #FF4D4F (红色) + "紧急"

交互反馈:
- 点击进度条: 显示详细任务完成情况
- 进度更新时: 闪烁效果 + 数字跳动动画
```

## 👥 备考搭子社交交互

### 小组信息展示交互

#### 小组成员显示
```
布局设计:
┌─────────────────────────────────────┐
│ 👥 备考搭子: 小明、小红 (3人小组)     │
│ 📊 小组平均进度: 82% | 我的排名: 2/3  │
└─────────────────────────────────────┘

交互行为:
- 点击成员名字: 查看该成员详细进度
- 点击小组信息: 进入小组详情页
- 长按小组区域: 快速分享小组进度
```

#### 排名对比交互
```
排名显示逻辑:
- 第1名: 🥇 + 金色文字
- 第2名: 🥈 + 银色文字  
- 第3名: 🥉 + 铜色文字
- 其他: 数字 + 普通文字

动画效果:
- 排名上升: 绿色箭头↗ + 庆祝动画
- 排名下降: 红色箭头↘ + 警告动画
- 排名不变: 蓝色横线→
```

#### 激励提示交互
```
进度状态提示:
领先状态: "🎉 进度领先，保持优势！" (绿色背景)
落后状态: "⚠️ 进度落后，加油追赶！" (红色背景)
追平状态: "💪 即将追平，再接再厉！" (橙色背景)

显示逻辑:
- 自动显示3秒后淡出
- 点击可手动关闭
- 每日最多显示3次，避免过度打扰
```

### 小组按钮交互

#### 按钮状态设计
```
有小组状态:
- 按钮: [👥小组] 蓝色背景
- 徽章: 显示未读消息数量
- 点击: 进入小组详情页

无小组状态:
- 按钮: [➕加入] 绿色背景
- 点击: 显示创建/加入小组选项

有邀请状态:
- 按钮: [🔔邀请] 橙色背景 + 闪烁
- 点击: 显示邀请列表
```

## 🚀 操作按钮交互设计

### 按钮布局和优先级
```
按钮配置: [🚀开始复习] [📋任务] [👥小组] [⚙更多]

视觉权重:
1. 开始复习: 最大，主色调，可能闪烁
2. 任务: 标准大小，次要色调
3. 小组: 标准大小，社交色调
4. 更多: 最小，灰色调
```

### 开始复习按钮特殊交互

#### 正常状态
```
样式: 蓝色背景，白色文字
文案: "🚀开始复习"
点击: 跳转到复习页面
```

#### 紧急状态 (3天内)
```
样式: 红色背景，白色文字，闪烁效果
文案: "🔥紧急复习"
动画: 2s闪烁循环 (background-color #FF4D4F ↔ #FF7875)
点击: 震动反馈 + 跳转复习页面
```

#### 高效状态 (进度领先)
```
样式: 绿色背景，白色文字
文案: "⚡高效复习"
特效: 成功光晕效果
点击: 正向音效 + 跳转复习页面
```

### 左滑操作交互

#### 滑动手势识别
```
触发条件:
- 水平滑动距离 > 30rpx
- 垂直滑动距离 < 20rpx
- 滑动速度 > 0.5rpx/ms

滑动反馈:
- 实时跟随手指移动
- 阻尼效果 (resistance = 0.3)
- 超过120rpx后按钮完全显示
```

#### 操作按钮显示
```
按钮布局:
┌─────────────────┬────────┬────────┐
│   考试卡片内容    │  编辑   │  删除   │
│                 │ 📝     │ 🗑️     │
│                 │ #1890FF│ #FF4444│
└─────────────────┴────────┴────────┘

交互行为:
- 编辑按钮: 点击跳转编辑页面
- 删除按钮: 点击显示确认对话框
- 点击其他区域: 自动回弹 (0.3s ease-out)
```

## 📊 统计卡片交互设计

### 简化统计布局
```
┌─────────┬─────────┬─────────┐
│   2     │  78%    │  3天    │
│ 进行中   │ 平均进度 │ 最近考试 │
└─────────┴─────────┴─────────┘
```

### 统计卡片交互
```
点击交互:
- 进行中: 筛选显示备考中的考试
- 平均进度: 显示详细进度分析弹窗
- 最近考试: 跳转到最近的考试详情

视觉反馈:
- hover: 轻微放大 (scale 1.02)
- active: 轻微缩小 (scale 0.98)
- 点击: 波纹扩散效果
```

### 数据更新动画
```
数值变化动画:
- 数字递增/递减: 0.5s 数字滚动动画
- 颜色变化: 0.3s 渐变过渡
- 新数据高亮: 绿色闪烁 2s 后恢复

实时更新:
- 考试状态变化时立即更新
- 任务完成时实时反映进度变化
- 小组数据每5分钟自动刷新
```

## 🎨 微交互设计细节

### 加载状态设计
```
骨架屏:
- 卡片轮廓: 灰色矩形 + 圆角
- 文字区域: 灰色条纹 + 闪烁动画
- 按钮区域: 灰色圆角矩形
- 整体动画: 1.5s 循环闪烁

加载完成:
- 内容从上到下依次显示
- 每个元素 fadeIn 0.3s
- 重要内容 (紧急考试) 优先显示
```

### 错误状态交互
```
网络错误:
- 图标: 📶 信号图标 + 红色叉号
- 文案: "网络连接失败，请检查网络设置"
- 操作: [重试] 按钮 + [设置] 链接

数据错误:
- 图标: ⚠️ 警告图标
- 文案: "数据加载异常，请稍后重试"
- 操作: [刷新] 按钮

空状态:
- 图标: 📚 书本图标 (大号，灰色)
- 文案: "还没有考试安排，添加第一个考试开始备考"
- 操作: [添加考试] 主按钮
```

### 成功反馈设计
```
操作成功:
- Toast提示: 绿色背景 + ✓ 图标
- 震动反馈: 轻微震动 (50ms)
- 音效: 成功提示音 (可选)

状态变化:
- 进度更新: 进度条闪烁 + 数字跳动
- 排名变化: 箭头动画 + 颜色变化
- 小组更新: 徽章闪烁 + 数量变化
```

## 📱 响应式交互适配

### 小屏幕适配 (≤375px)
```
交互调整:
- 按钮最小点击区域: 88rpx × 88rpx
- 滑动触发距离: 减少到 20rpx
- 长按触发时间: 增加到 600ms
- 文字大小: 整体增加 4rpx
```

### 大屏幕适配 (≥414px)
```
交互增强:
- 支持悬停效果
- 增加快捷键支持
- 显示更多信息
- 支持批量操作
```

## 🔧 技术实现要点

### 性能优化
```
动画性能:
- 使用 transform 替代 position
- 开启硬件加速: will-change
- 避免重排重绘: 使用 opacity 和 transform
- 动画帧率: 保持 60fps

内存管理:
- 及时清理事件监听器
- 图片懒加载
- 虚拟列表 (如果考试数量增多)
```

### 无障碍设计
```
可访问性:
- 所有按钮添加 aria-label
- 支持键盘导航
- 颜色对比度 ≥ 4.5:1
- 支持屏幕阅读器

语义化:
- 使用语义化 HTML 标签
- 正确的标题层级
- 表单元素关联标签
```

---

**交互设计完成度**: 100% ✅  
**开发就绪状态**: 可以开始开发 🚀  
**后续支持**: 开发过程中提供交互细节澄清和调整建议
