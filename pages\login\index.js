// pages/login/index.js
const LoginApi = require('../../utils/loginApi')

Page({
  data: {
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    hasUserInfo: false,
    loading: false,
    registerLoading: false,
    activeTab: 'wechat', // username, wechat
    needUserAuth: false,
    
    // 登录表单
    loginForm: {
      username: '',
      password: ''
    },
    
    // 注册表单
    registerForm: {
      username: '',
      password: '',
      confirmPassword: ''
    },
    
    // 弹窗状态
    showRegisterModal: false
  },

  onLoad() {
    const app = getApp()
    
    // 检查是否已经登录
    const loginStatus = app.checkLoginStatus()
    
    if (loginStatus.isLoggedIn) {
      this.setData({
        hasUserInfo: true
      })
      
      // 已登录，跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/home/<USER>'
        })
      }, 1000)
    }
  },

  // 返回按钮点击事件
  onClickLeft() {
    // 获取页面栈
    const pages = getCurrentPages()
    
    if (pages.length > 1) {
      // 有上一页，返回上一页
      wx.navigateBack()
    } else {
      // 没有上一页，跳转到首页
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }
  },

  // 切换登录方式
  onTabChange(e) {
    this.setData({
      activeTab: e.detail.name
    })
  },

  // 用户名密码登录相关方法
  onUsernameChange(e) {
    this.setData({
      'loginForm.username': e.detail || e.detail.value
    })
  },

  onPasswordChange(e) {
    this.setData({
      'loginForm.password': e.detail || e.detail.value
    })
  },

  // 用户名密码登录
  async usernameLogin() {
    const { loginForm } = this.data
    
    // 表单验证
    if (!loginForm.username || !loginForm.username.trim()) {
      wx.showToast({
        title: '请输入用户名或邮箱',
        icon: 'error'
      })
      return
    }
    
    if (!loginForm.password || !loginForm.password.trim()) {
      wx.showToast({
        title: '请输入密码',
        icon: 'error'
      })
      return
    }

    this.setData({ loading: true })

    try {
      const result = await LoginApi.loginWithPassword({
        username: loginForm.username.trim(),
        password: loginForm.password
      })
      
      if (result.success) {
        this.setData({
          hasUserInfo: true,
          loading: false
        })

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          })
        }, 1500)
      } else {
        this.setData({ loading: false })
        
        // 根据错误类型提供不同的处理建议
        const errorMsg = result.error || '登录失败'
        
        if (errorMsg.includes('用户名或密码错误') || errorMsg.includes('用户不存在') || errorMsg.includes('未找到用户') || errorMsg.includes('请先注册账号')) {
          // 用户不存在，提示注册
          wx.showModal({
            title: '登录失败',
            content: '账号不存在或密码错误，是否立即注册新账号？',
            confirmText: '立即注册',
            cancelText: '重新输入',
            success: (res) => {
              if (res.confirm) {
                this.showRegister()
              }
            }
          })
        } else {
          wx.showToast({
            title: errorMsg,
            icon: 'error',
            duration: 3000
          })
        }
      }
    } catch (error) {
      console.error('用户名密码登录异常:', error)
      this.setData({ loading: false })
      
      // 检查是否是云函数调用失败
      if (error.errMsg && error.errMsg.includes('cloud function')) {
        wx.showModal({
          title: '网络连接异常',
          content: '无法连接到服务器，请检查网络连接或稍后重试。如果持续出现此问题，可以尝试注册新账号。',
          confirmText: '注册账号',
          cancelText: '重试',
          success: (res) => {
            if (res.confirm) {
              this.showRegister()
            }
          }
        })
      } else {
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'error'
        })
      }
    }
  },

  // 微信授权登录相关方法
  // 获取用户信息的封装方法
  getUserProfileAsync() {
    return new Promise((resolve, reject) => {
      // 尝试使用 wx.getUserProfile
      if (wx.getUserProfile) {
        wx.getUserProfile({
          desc: '用于完成用户注册',
          success: resolve,
          fail: reject
        })
      } else {
        // 降级到旧的方式，创建一个临时的授权方案
        reject(new Error('getUserProfile not available'))
      }
    })
  },



  // 处理用户授权
  async onGetUserInfo(e) {
    console.log('用户授权事件:', e)
    
    if (e.detail.userInfo) {
      this.setData({ 
        loading: true,
        needUserAuth: false
      })
      
      try {
        console.log('获取到微信用户信息，进行注册:', e.detail.userInfo)
        await this.loginWithUserInfo(e.detail.userInfo)
      } catch (error) {
        console.error('注册失败:', error)
        this.setData({ 
          loading: false
        })
        wx.showToast({
          title: '注册失败，请重试',
          icon: 'error'
        })
      }
    } else {
      console.log('用户拒绝授权')
      wx.showToast({
        title: '需要授权才能完成注册',
        icon: 'none'
      })
      this.setData({ needUserAuth: false })
    }
  },

  // 微信登录
  async wechatLogin() {
    console.log('开始微信登录...')
    this.setData({ loading: true })

    try {
      // 第一步：调用微信登录，获取openid并查询用户是否存在
      console.log('调用微信登录获取openid...')
      const app = getApp()
      const checkResult = await app.checkWechatUser()
      
      console.log('用户检查结果:', checkResult)
      
      if (checkResult.success) {
        if (checkResult.userExists) {
          // 用户已存在，直接登录
          console.log('用户已存在，直接登录')
          this.setData({
            hasUserInfo: true,
            loading: false
          })

          wx.showToast({
            title: '欢迎回来',
            icon: 'success'
          })

          // 跳转到首页
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/home/<USER>'
            })
          }, 1500)
        } else {
          // 用户不存在，需要获取用户信息进行注册
          console.log('用户不存在，需要获取用户信息进行注册')
          this.setData({ loading: false })
          
          wx.showModal({
            title: '新用户注册',
            content: '检测到您是新用户，需要获取您的微信头像和昵称来完成注册',
            confirmText: '立即注册',
            cancelText: '取消',
            success: async (res) => {
              if (res.confirm) {
                // 用户确认注册，立即获取用户信息
                this.setData({ loading: true })
                
                try {
                  // 在用户手势中调用getUserProfile
                  const userProfileRes = await this.getUserProfileAsync()
                  
                  if (userProfileRes && userProfileRes.userInfo) {
                    console.log('获取到微信用户信息，进行注册:', userProfileRes.userInfo)
                    await this.loginWithUserInfo(userProfileRes.userInfo)
                  } else {
                    this.setData({ loading: false })
                    wx.showToast({
                      title: '获取用户信息失败',
                      icon: 'error'
                    })
                  }
                } catch (error) {
                  console.error('获取用户信息失败:', error)
                  this.setData({ loading: false })
                  
                  if (error.message && error.message.includes('auth deny')) {
                    wx.showToast({
                      title: '需要授权才能注册',
                      icon: 'none'
                    })
                  } else {
                    // 如果getUserProfile失败，降级到button授权方式
                    this.setData({ needUserAuth: true })
                    wx.showToast({
                      title: '请点击下方按钮完成授权',
                      icon: 'none'
                    })
                  }
                }
              }
            }
          })
        }
      } else {
        this.setData({ loading: false })
        wx.showToast({
          title: checkResult.error || '登录失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('微信登录整体失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'error'
      })
    }
  },

  // 使用用户信息登录
  async loginWithUserInfo(userInfo) {
    console.log('开始处理微信登录，用户信息:', userInfo)
    
    try {
      // 验证用户信息
      if (!userInfo) {
        throw new Error('用户信息为空')
      }

      const app = getApp()
      if (!app) {
        throw new Error('无法获取应用实例')
      }

      if (typeof app.loginWithUserInfo !== 'function') {
        throw new Error('应用登录方法不存在')
      }

      console.log('调用应用登录方法...')
      const result = await app.loginWithUserInfo(userInfo)
      console.log('登录结果:', result)
      
      if (!result) {
        throw new Error('登录结果为空')
      }

      if (result.success) {
        this.setData({
          hasUserInfo: true,
          loading: false
        })

        wx.showToast({
          title: '欢迎加入备考助手',
          icon: 'success'
        })

        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          })
        }, 1500)
      } else {
        this.setData({ loading: false })
        wx.showToast({
          title: result.error || '登录失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('微信登录异常详情:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '登录异常: ' + (error.message || '未知错误'),
        icon: 'error',
        duration: 3000
      })
    }
  },

  // 注册相关方法
  showRegister() {
    this.setData({
      showRegisterModal: true,
      registerForm: {
        username: '',
        password: '',
        confirmPassword: ''
      }
    })
  },

  hideRegister() {
    this.setData({
      showRegisterModal: false
    })
  },

  onRegUsernameChange(e) {
    this.setData({
      'registerForm.username': e.detail || e.detail.value
    })
  },

  onRegPasswordChange(e) {
    this.setData({
      'registerForm.password': e.detail || e.detail.value
    })
  },

  onRegConfirmPasswordChange(e) {
    this.setData({
      'registerForm.confirmPassword': e.detail || e.detail.value
    })
  },

  // 提交注册
  async submitRegister() {
    const { registerForm } = this.data
    
    // 表单验证
    if (!registerForm.username || registerForm.username.trim().length < 3) {
      wx.showToast({
        title: '用户名至少3个字符',
        icon: 'error'
      })
      return
    }
    
    if (!registerForm.password || registerForm.password.length < 6) {
      wx.showToast({
        title: '密码至少6个字符',
        icon: 'error'
      })
      return
    }
    
    if (registerForm.password !== registerForm.confirmPassword) {
      wx.showToast({
        title: '两次输入的密码不一致',
        icon: 'error'
      })
      return
    }

    this.setData({ registerLoading: true })

    try {
      const result = await LoginApi.register({
        username: registerForm.username.trim(),
        password: registerForm.password
      })
      
      if (result.success) {
        wx.showToast({
          title: '注册成功',
          icon: 'success'
        })

        // 关闭注册弹窗
        this.setData({
          showRegisterModal: false,
          registerLoading: false
        })

        // 自动填充登录表单并切换到账号密码登录标签
        this.setData({
          'loginForm.username': registerForm.username,
          'loginForm.password': registerForm.password,
          activeTab: 'username'
        })

        // 提示用户可以直接登录
        setTimeout(() => {
          wx.showModal({
            title: '注册成功',
            content: '账号已创建成功，将自动为您登录',
            showCancel: false,
            confirmText: '确定',
            success: () => {
              // 自动登录
              setTimeout(() => {
                this.usernameLogin()
              }, 500)
            }
          })
        }, 1000)
      } else {
        this.setData({ registerLoading: false })
        wx.showToast({
          title: result.error || '注册失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('注册异常:', error)
      this.setData({ registerLoading: false })
      wx.showToast({
        title: '注册失败，请重试',
        icon: 'error'
      })
    }
  }

})