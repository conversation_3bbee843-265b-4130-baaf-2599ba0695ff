/* pages/test/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

/* 测试控制区 */
.test-controls {
  margin-bottom: 40rpx;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-btn.primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.test-btn.secondary {
  background: white;
  color: #333;
  border: 2rpx solid #e0e0e0;
}

.test-btn.disabled {
  opacity: 0.6;
  background: #ccc;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-top: 20rpx;
}

.quick-actions .test-btn {
  font-size: 24rpx;
  height: 60rpx;
}

/* 测试进度 */
.test-progress {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #333;
}

.progress-percent {
  font-size: 24rpx;
  color: #666;
  font-weight: bold;
}

.progress-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 测试结果摘要 */
.test-summary {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.summary-time {
  font-size: 20rpx;
  color: #999;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
}

.stat-item.success {
  background: #e8f5e8;
  color: #2e7d32;
}

.stat-item.failed {
  background: #ffebee;
  color: #c62828;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 20rpx;
  opacity: 0.8;
}

.summary-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 60rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  background: #f0f0f0;
  color: #333;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.danger {
  background: #ffebee;
  color: #c62828;
}

/* 详细测试结果 */
.test-details {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.details-header {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.details-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.test-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  border-left: 6rpx solid #ddd;
}

.test-item.passed {
  border-left-color: #4caf50;
  background: #e8f5e8;
}

.test-item.failed {
  border-left-color: #f44336;
  background: #ffebee;
}

.test-info {
  flex: 1;
}

.test-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.test-message {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.test-status {
  margin-left: 20rpx;
}

.status-icon {
  font-size: 32rpx;
}

/* 测试说明 */
.test-info-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.info-header {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
}

.info-label {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.info-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 测试覆盖范围 */
.test-coverage {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.coverage-header {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.coverage-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.coverage-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.coverage-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
}

.coverage-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  margin-top: 4rpx;
}

.coverage-info {
  flex: 1;
}

.coverage-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.coverage-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
