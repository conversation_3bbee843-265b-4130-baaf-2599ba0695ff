// 云函数入口文件
const cloud = require('wx-server-sdk')
const crypto = require('crypto')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// MD5加密函数
function md5(str) {
  return crypto.createHash('md5').update(str).digest('hex')
}

// 数据迁移：将明文密码转换为MD5加密密码
async function migratePasswords() {
  try {
    console.log('开始密码迁移...')
    
    // 查找所有密码登录用户
    const usersQuery = await db.collection('users')
      .where({
        loginType: 'password'
      })
      .get()

    const users = usersQuery.data
    console.log('找到', users.length, '个密码用户需要迁移')

    let migratedCount = 0
    
    for (const user of users) {
      // 检查密码是否已经是MD5格式（MD5固定32字符长度）
      if (user.password && user.password.length !== 32) {
        // 不是MD5格式，进行加密
        const encryptedPassword = md5(user.password)
        
        await db.collection('users')
          .doc(user._id)
          .update({
            data: {
              password: encryptedPassword,
              updateTime: new Date()
            }
          })
        
        console.log('用户', user.username, '密码已迁移')
        migratedCount++
      }
    }

    console.log('密码迁移完成，共迁移', migratedCount, '个用户')
    return { success: true, migratedCount }
  } catch (error) {
    console.error('密码迁移失败:', error)
    return { success: false, error: error.message }
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { userInfo, action = 'login' } = event

  try {
    switch (action) {
      case 'login':
        return await handleLogin(wxContext, userInfo)
      case 'checkWechatUser':
        return await checkWechatUser(wxContext)
      case 'loginWithPassword':
        return await handlePasswordLogin(event.credentials)
      case 'register':
        return await handleRegister(event.userInfo)
      case 'updateProfile':
        return await updateUserProfile(wxContext, userInfo, event.userId)
      case 'getUserInfo':
        return await getUserInfo(wxContext)
      case 'migratePasswords':  // 新增迁移密码功能
        return await migratePasswords()
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('登录云函数错误:', error)
    return { success: false, error: error.message }
  }
}

// 检查微信用户是否存在
async function checkWechatUser(wxContext) {
  const openid = wxContext.OPENID
  
  try {
    console.log('检查微信用户是否存在，openid:', openid)
    console.log('wxContext详情:', JSON.stringify(wxContext, null, 2))
    
    // 查询用户是否已存在
    const userQuery = await db.collection('users')
      .where({
        openid: openid
      })
      .get()

    console.log('数据库查询结果:', userQuery.data.length, '条记录')
    console.log('查询到的用户数据:', JSON.stringify(userQuery.data, null, 2))

    if (userQuery.data.length === 0) {
      // 用户不存在
      console.log('微信用户不存在，需要注册')
      return {
        success: true,
        userExists: false
      }
    } else {
      // 用户存在，更新登录时间
      const user = userQuery.data[0]
      const now = new Date()
      
      console.log('找到微信用户:', user.nickName, 'openid:', user.openid, 'loginType:', user.loginType)
      
      const updateData = {
        lastLoginTime: now,
        loginCount: (user.loginCount || 0) + 1,
        updateTime: now
      }

      await db.collection('users')
        .doc(user._id)
        .update({
          data: updateData
        })

      // 更新本地用户对象
      Object.assign(user, updateData)
      
      console.log('微信用户已存在，直接登录:', user.nickName)
      
      return {
        success: true,
        userExists: true,
        data: {
          openid: openid,
          user: user
        }
      }
    }
  } catch (error) {
    console.error('检查微信用户失败:', error)
    return { success: false, error: error.message }
  }
}

// 处理登录
async function handleLogin(wxContext, userInfo) {
  const openid = wxContext.OPENID
  const appid = wxContext.APPID
  const unionid = wxContext.UNIONID

  try {
    // 查询用户是否已存在（使用openid字段而不是_openid）
    const userQuery = await db.collection('users')
      .where({
        openid: openid
      })
      .get()

    let user
    const now = new Date()

    if (userQuery.data.length === 0) {
      // 新用户，创建用户记录
      const newUser = {
        _openid: openid,  // 微信云开发自动字段
        openid: openid,   // 自定义字段，用于查询
        appid: appid,
        unionid: unionid,
        nickName: userInfo?.nickName || '微信用户',
        avatarUrl: userInfo?.avatarUrl || '',
        signature: userInfo?.signature || '努力备考，金榜题名！',
        gender: userInfo?.gender || 0,
        country: userInfo?.country || '',
        province: userInfo?.province || '',
        city: userInfo?.city || '',
        language: userInfo?.language || 'zh_CN',
        loginType: 'wechat', // 添加登录类型标识
        isFirstLogin: true,
        loginCount: 1,
        lastLoginTime: now,
        createTime: now,
        updateTime: now
      }

      const result = await db.collection('users').add({
        data: newUser
      })

      user = { _id: result._id, ...newUser }
      console.log('新用户注册:', openid)
    } else {
      // 老用户，直接登录，只更新登录相关信息，完全不修改个人信息
      user = userQuery.data[0]

      const updateData = {
        lastLoginTime: now,
        loginCount: (user.loginCount || 0) + 1,
        updateTime: now,
        isFirstLogin: false
      }

      await db.collection('users')
        .doc(user._id)
        .update({
          data: updateData
        })

      // 更新本地用户对象（只更新元数据）
      Object.assign(user, updateData)
      console.log('老用户登录:', openid, '昵称:', user.nickName)
    }

    return {
      success: true,
      data: {
        openid: openid,
        appid: appid,
        unionid: unionid,
        user: user,
        isFirstLogin: user.isFirstLogin
      }
    }
  } catch (error) {
    console.error('登录处理失败:', error)
    return { success: false, error: error.message }
  }
}

// 更新用户资料
async function updateUserProfile(wxContext, userInfo, userId) {
  try {
    // 调试信息：打印接收到的参数
    console.log('云函数 updateUserProfile：接收到的 userId:', userId)
    console.log('云函数 updateUserProfile：接收到的 userInfo:', JSON.stringify(userInfo, null, 2))
    console.log('云函数 updateUserProfile：用户签名值:', userInfo.signature)

    if (!userId) {
      return { success: false, error: '用户ID不能为空' }
    }

    const updateData = {
      updateTime: new Date()
    }

    if (userInfo.nickName) updateData.nickName = userInfo.nickName
    if (userInfo.avatarUrl) updateData.avatarUrl = userInfo.avatarUrl
    if (userInfo.signature !== undefined) updateData.signature = userInfo.signature
    if (userInfo.gender !== undefined) updateData.gender = userInfo.gender
    if (userInfo.country) updateData.country = userInfo.country
    if (userInfo.province) updateData.province = userInfo.province
    if (userInfo.city) updateData.city = userInfo.city
    if (userInfo.language) updateData.language = userInfo.language

    // 调试信息：打印要更新的数据
    console.log('云函数 updateUserProfile：准备更新的数据:', JSON.stringify(updateData, null, 2))

    // 使用用户_id作为主键直接更新
    const result = await db.collection('users')
      .doc(userId)
      .update({
        data: updateData
      })

    console.log('云函数 updateUserProfile：数据库更新结果:', result)

    return { success: true, data: result }
  } catch (error) {
    console.error('更新用户资料失败:', error)
    return { success: false, error: error.message }
  }
}

// 获取用户信息
async function getUserInfo(wxContext) {
  const openid = wxContext.OPENID

  try {
    const result = await db.collection('users')
      .where({
        openid: openid
      })
      .get()

    if (result.data.length === 0) {
      return { success: false, error: '用户不存在' }
    }

    return { success: true, data: result.data[0] }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return { success: false, error: error.message }
  }
}

// 处理密码登录
async function handlePasswordLogin(credentials) {
  try {
    const { username, password } = credentials

    // 查找用户（仅支持用户名登录，在users集合中查找loginType为password的用户）
    const userQuery = await db.collection('users')
      .where({
        username: username,
        loginType: 'password'
      })
      .get()

    if (userQuery.data.length === 0) {
      return { success: false, error: '用户不存在，请先注册账号' }
    }

    const user = userQuery.data[0]

    // 验证密码（将输入的明文密码加密后与存储的密码比较）
    if (user.password !== md5(password)) {
      return { success: false, error: '密码错误，请重新输入' }
    }

    // 更新登录信息
    const now = new Date()
    const updateData = {
      lastLoginTime: now,
      loginCount: (user.loginCount || 0) + 1,
      updateTime: now,
      isFirstLogin: false
    }

    await db.collection('users')
      .doc(user._id)
      .update({
        data: updateData
      })

    // 更新本地用户对象
    Object.assign(user, updateData)

    return {
      success: true,
      data: {
        openid: user._id,
        user: user,
        isFirstLogin: false
      }
    }
  } catch (error) {
    console.error('密码登录失败:', error)
    return { success: false, error: error.message }
  }
}

// 处理用户注册
async function handleRegister(userInfo) {
  try {
    const { username, password } = userInfo
    
    console.log('云函数 handleRegister: 接收到的用户信息:', JSON.stringify(userInfo, null, 2))
    console.log('云函数 handleRegister: 原始密码:', password)

    // 检查用户名是否已存在（在users集合中查找）
    const usernameQuery = await db.collection('users')
      .where({ 
        username: username,
        loginType: 'password'
      })
      .get()

    if (usernameQuery.data.length > 0) {
      return { success: false, error: '用户名已存在' }
    }

    // 对密码进行MD5加密
    const encryptedPassword = md5(password)
    console.log('云函数 handleRegister: MD5加密后的密码:', encryptedPassword)

    // 创建新用户
    const now = new Date()
    const newUser = {
      username: username,
      password: encryptedPassword, // 对明文密码进行MD5加密后存储
      nickName: username,
      avatarUrl: '',
      signature: '努力备考，金榜题名！',
      gender: 0,
      country: '',
      province: '',
      city: '',
      language: 'zh_CN',
      loginType: 'password', // 添加登录类型标识
      isFirstLogin: true,
      loginCount: 0,
      lastLoginTime: null,
      createTime: now,
      updateTime: now
    }

    console.log('云函数 handleRegister: 准备保存的用户数据:', JSON.stringify(newUser, null, 2))

    const result = await db.collection('users').add({
      data: newUser
    })

    console.log('云函数 handleRegister: 数据库保存结果:', result)

    const user = { _id: result._id, ...newUser }

    return {
      success: true,
      data: {
        openid: result._id,
        user: user
      }
    }
  } catch (error) {
    console.error('用户注册失败:', error)
    return { success: false, error: error.message }
  }
}
