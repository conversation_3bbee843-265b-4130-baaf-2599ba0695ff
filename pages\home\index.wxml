<!--pages/home/<USER>
<view class="page">
  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" size="24px" text-size="14px">加载中...</van-loading>
  <view wx:else>
    <!-- 主要内容 -->
    <view class="container">
      <!-- 考试板块（横向滑动） -->
      <view class="exam-section" wx:if="{{examList.length > 0}}">
        <swiper
          class="exam-swiper"
          style="height: {{swiperHeight || 400}}rpx;"
          indicator-dots="{{examList.length > 1}}"
          indicator-color="rgba(0, 0, 0, .3)"
          indicator-active-color="#1890FF"
          autoplay="{{false}}"
          circular="{{false}}"
          duration="300"
          bindchange="onExamSwiperChange"
        >
          <swiper-item wx:for="{{examList}}" wx:key="id" wx:for-item="exam">
            <view class="exam-card">
              <!-- 考试信息区域 - 点击跳转考试详情 -->
              <view class="exam-info" bindtap="onExamInfoTap" data-exam-id="{{exam.id}}">
                <view class="exam-title">{{exam.name}}</view>
                <view class="exam-countdown">
                  <text class="countdown-text">📅 </text>
                  <text class="countdown-number" wx:if="{{exam.isToday}}">今天考试</text>
                  <block wx:else>
                    <text class="countdown-text">还有 </text>
                    <text class="countdown-number">{{exam.daysLeft}}</text>
                    <text class="countdown-text"> 天</text>
                  </block>
                  <text class="countdown-text"> ({{exam.dateText}})</text>
                </view>
                <view class="exam-progress">
                  <van-progress
                    percentage="{{exam.progressPercent}}"
                    color="#1890FF"
                    show-pivot="{{true}}"
                    stroke-width="{{4}}"
                  />
                  <text class="progress-text">(已完成{{exam.completedTasks}}/总共{{exam.totalTasks}}个任务)</text>
                </view>

                <!-- 科目信息区域 -->
                <view class="subjects-section" wx:if="{{exam.hasSubjects}}">
                  <view class="subjects-header">
                    <text class="subjects-title">📚 科目进度</text>
                    <text class="subjects-count">({{exam.subjectStats.length}}个科目)</text>
                  </view>
                  <view class="subjects-list">
                    <view
                      wx:for="{{exam.subjectStats}}"
                      wx:key="name"
                      class="subject-item"
                      bindtap="onSubjectTap"
                      data-exam-id="{{exam.id}}"
                      data-subject="{{item.name}}"
                    >
                      <view class="subject-info">
                        <text class="subject-icon">{{item.icon}}</text>
                        <text class="subject-name">{{item.name}}</text>
                        <text class="subject-progress">{{item.progress}}%</text>
                      </view>
                      <view class="subject-progress-bar">
                        <view
                          class="subject-progress-fill"
                          style="width: {{item.progress}}%; background-color: {{item.progressColor}}"
                        ></view>
                      </view>
                      <text class="subject-tasks">{{item.completedTasks}}/{{item.totalTasks}}</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 分隔线 -->
              <view class="divider"></view>

              <!-- 搭子小组区域 -->
              <view class="partners-section">
                <view wx:if="{{exam.hasPartner}}" class="partners-content">
                  <view class="partners-header">
                    <text class="partners-title">👥 搭子小组</text>
                    <text class="partners-count">({{exam.topMembers.length}}个成员)</text>
                  </view>
                  <view class="partners-list">
                    <view
                      wx:for="{{exam.topMembers}}"
                      wx:key="id"
                      class="partner-item"
                      bindtap="onPartnerMemberTap"
                      data-exam-id="{{exam.id}}"
                      data-member-id="{{item.id}}"
                    >
                      <view class="partner-info">
                        <image class="partner-avatar" src="{{item.avatar}}" mode="aspectFill" />
                        <text class="partner-name">{{item.name || '搭子'}}</text>
                        <text class="partner-progress">{{item.progressPercent}}%</text>
                      </view>
                      <view class="partner-progress-bar">
                        <view
                          class="partner-progress-fill"
                          style="width: {{item.progressPercent}}%; background-color: {{item.progressPercent >= 80 ? '#52c41a' : item.progressPercent >= 60 ? '#fa8c16' : item.progressPercent >= 40 ? '#1890ff' : '#ff4d4f'}}"
                        ></view>
                      </view>
                      <text class="partner-tasks">{{item.completedTasks}}/{{item.totalTasks}}</text>
                    </view>
                  </view>
                </view>
                <view wx:else class="partners-empty">
                  <view class="partners-header">
                    <text class="partners-title">👥 搭子小组</text>
                    <text class="partners-count">(0个成员)</text>
                  </view>
                  <view class="partners-create" bindtap="onCreatePartner" data-exam-id="{{exam.id}}">
                    <text class="create-guide">找个搭子一起备考吧！</text>
                    <text class="create-action">点击创建搭子小组 →</text>
                  </view>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 无考试状态 -->
      <view class="empty-state" wx:else>
        <text class="empty-icon">📚</text>
        <text class="empty-title">还没有考试安排</text>
        <text class="empty-desc">添加您的第一个考试，开始制定复习计划</text>
        <van-button type="primary" size="normal" bindtap="onAddExam">创建考试</van-button>
      </view>

      <!-- 间距 -->
      <van-divider wx:if="{{examList.length > 0}}" />

      <!-- 今日任务列表 -->
      <van-cell-group title="📝 今日任务 ({{completedCount}}/{{totalCount}})" wx:if="{{todayTasks.length > 0}}">
        <!-- 任务列表项 -->
        <van-cell
          wx:for="{{todayTasks}}"
          wx:for-item="task"
          wx:key="id"
          clickable
          bindtap="onTaskTap"
          data-task-id="{{task.id}}"
          custom-class="custom-task-cell"
        >
          <!-- 左侧状态图标 -->
          <view slot="icon" class="task-status" catchtap="stopPropagation">
            <van-checkbox
              value="{{task.completed}}"
              bind:change="toggleTask"
              data-id="{{task.id}}"
              custom-class="task-checkbox"
              catch:change="toggleTask" />
          </view>

          <!-- 中间任务信息 -->
          <view slot="title" class="task-main">
            <!-- 第一行：任务名称 + 科目标签 -->
            <view class="task-title-row">
              <text class="task-title {{task.completed ? 'completed' : ''}}">{{task.title.length > 30 ? task.title.substring(0, 30) + '...' : task.title}}</text>
              <van-tag size="small" color="#1989fa" class="subject-tag">{{task.examSubject}}</van-tag>
            </view>

            <!-- 考试名称行（新增） -->
            <text wx:if="{{task.examName}}" class="task-exam-name">{{task.examName.length > 10 ? task.examName.substring(0, 10) + '...' : task.examName}}</text>

            <!-- 第二行：截止时间 + 重要程度标签 -->
            <view class="task-meta-row">
              <text class="due-time">⏰ {{task.dueTimeText}}</text>
              <van-tag
                size="small"
                color="{{task.priority === 'high' ? '#ee0a24' : (task.priority === 'medium' ? '#ff976a' : '#07c160')}}"
                class="priority-tag"
              >
                {{task.priorityText}}
              </van-tag>
            </view>

            <!-- 检查点进度预览行（新增） -->
            <view class="checkpoint-preview-line" wx:if="{{task.subtasks && task.subtasks.length > 0}}"
                  catchtap="openCheckpointModal"
                  data-id="{{task.id}}">
              <view class="checkpoint-progress-wrapper">
                <view class="checkpoint-progress-bar">
                  <view class="progress-fill" style="width: {{(task.completedSubtasks / task.subtasks.length) * 100}}%"></view>
                </view>
                <text class="checkpoint-stats">{{task.completedSubtasks}}/{{task.subtasks.length}} 检查点</text>
              </view>
              <van-icon name="arrow" size="12" class="expand-icon" />
            </view>

            <!-- 添加检查点引导（新增） -->
            <view class="add-checkpoint-guide" wx:if="{{!task.subtasks || task.subtasks.length === 0}}"
                  catchtap="openCheckpointModal"
                  data-id="{{task.id}}">
              <van-icon name="plus" size="14" color="#1890ff" />
              <text class="guide-text">添加检查点来跟踪学习进度</text>
              <van-icon name="arrow" size="12" color="#999999" class="guide-arrow" />
            </view>
          </view>

          <!-- 右侧操作按钮 -->
          <view slot="right-icon" catchtap="stopPropagation">
            <van-button
              wx:if="{{!task.completed}}"
              size="mini"
              type="primary"
              bind:click="onStartStudy"
              data-task-id="{{task.id}}"
              class="task-action-btn"
              custom-style="width: 100rpx !important; min-width: 80rpx !important; max-width: 100rpx !important;"
            >
              开始专注
            </van-button>
          </view>
        </van-cell>

        <!-- 添加任务入口 -->
        <van-cell title="添加今日任务" clickable bindtap="onAddTask" icon="plus" />
      </van-cell-group>

      <!-- 有考试但无今日任务的引导状态 -->
      <view class="empty-tasks" wx:if="{{todayTasks.length === 0 && examList.length > 0}}">
        <view class="empty-tasks-content">
          <text class="empty-icon">📝</text>
          <text class="empty-title">今天还没有复习任务</text>
          <text class="empty-desc">为您的考试创建今日复习任务，开始高效复习</text>
          <view class="empty-actions">
            <van-button type="primary" size="normal" bindtap="onAddTask">创建今日任务</van-button>
            <van-button type="default" size="normal" bindtap="onQuickCreateTasks">智能生成任务</van-button>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view style="padding-bottom: env(safe-area-inset-bottom);"></view>

      <!-- 额外的底部间距，确保内容不被导航栏遮挡 -->
      <view class="bottom-spacer"></view>
    </view>
  </view>

  <!-- 悬浮操作按钮 - 新的可拖动组件 -->
  <floating-action-button
    expand-mode="fan"
    draggable="{{true}}"
    buttons="{{fabMenuItems}}"
    main-button-style="{{fabMainButtonStyle}}"
    initial-position="{{fabInitialPosition}}"
    bind:buttonclick="onFabButtonClick"
    bind:positionchange="onFabPositionChange"
    bind:expandchange="onFabExpandChange"
  />
</view>

<!-- 检查点弹窗 -->
<van-popup
  show="{{showCheckpointModal}}"
  position="center"
  custom-style="width: 80%; max-height: 70%; border-radius: 16rpx;"
  bind:close="closeCheckpointModal"
  custom-class="checkpoint-modal"
  z-index="10001">

  <!-- 弹窗头部 -->
  <view class="checkpoint-modal-header">
    <text class="modal-title">{{checkpointModalData.taskTitle}}</text>
    <van-icon name="cross" size="20" bind:click="closeCheckpointModal" />
  </view>

  <!-- 弹窗内容区域 -->
  <view class="checkpoint-modal-content">
    <!-- 统计信息卡片 -->
    <view class="checkpoint-stats-card" wx:if="{{checkpointModalData.subtasks.length > 0}}">
      <view class="stats-content">
        <view class="stats-main">
          <text class="stats-number">{{checkpointModalData.subtasks.length}}</text>
          <text class="stats-label">个检查点</text>
        </view>
        <view class="stats-detail">
          <view class="progress-info">
            <text class="completed-count">已完成 {{checkpointModalData.completedCount}}</text>
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{checkpointModalData.subtasks.length > 0 ? (checkpointModalData.completedCount / checkpointModalData.subtasks.length * 100) : 0}}%"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 检查点列表卡片 -->
    <view class="checkpoint-list-card" wx:if="{{checkpointModalData.subtasks.length > 0}}">
      <view
        class="checkpoint-item"
        wx:for="{{checkpointModalData.subtasks}}"
        wx:key="index"
        data-index="{{index}}">

        <!-- 复选框 -->
        <van-checkbox
          value="{{item.completed}}"
          bind:change="toggleCheckpointInModal"
          data-index="{{index}}"
          custom-class="checkpoint-checkbox" />

        <!-- 内容区域 -->
        <view class="checkpoint-content">
          <view class="checkpoint-text-wrapper {{item.completed ? 'completed-wrapper' : ''}}">
            <van-field
              value="{{item.title}}"
              placeholder="检查点内容"
              bind:change="updateCheckpointInModal"
              data-index="{{index}}"
              maxlength="30"
              border="{{false}}"
              custom-class="checkpoint-field" />
          </view>
        </view>

        <!-- 操作区域 -->
        <view class="checkpoint-actions">
          <van-icon
            name="delete"
            size="16"
            bind:click="removeCheckpointInModal"
            data-index="{{index}}"
            custom-class="delete-action" />
        </view>
      </view>
    </view>

    <!-- 添加检查点卡片 -->
    <view class="add-checkpoint-card">
      <!-- 默认状态：点击展开 -->
      <view class="add-checkpoint-trigger" wx:if="{{!showAddCheckpointInput}}" bind:tap="toggleAddCheckpointInput">
        <van-icon name="plus" size="16" color="#1890ff" />
        <text class="trigger-text">添加检查项</text>
      </view>

      <!-- 展开状态：输入和操作 -->
      <view class="add-checkpoint-expanded" wx:if="{{showAddCheckpointInput}}">
        <view class="add-input-section">
          <van-cell-group>
            <van-field
              value="{{newCheckpointTitle}}"
              placeholder="请输入检查项内容"
              bind:change="updateNewCheckpointTitle"
              maxlength="30"
              focus="{{showAddCheckpointInput}}"
              border="{{false}}"
              custom-class="add-checkpoint-field" />
          </van-cell-group>
        </view>

        <!-- 按钮操作区域 -->
        <view class="checkpoint-actions">
          <view class="checkpoint-action-button save-action">
            <van-button
              type="primary"
              size="small"
              bind:click="addCheckpointInModal"
              disabled="{{!newCheckpointTitle}}"
              custom-class="save-checkpoint-btn">
              保存
            </van-button>
          </view>
          <view class="checkpoint-action-button cancel-action">
            <van-button
              type="default"
              size="small"
              bind:click="cancelAddCheckpoint"
              custom-class="cancel-checkpoint-btn">
              取消
            </van-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态提示 -->
    <view class="empty-checkpoints" wx:if="{{checkpointModalData.subtasks.length === 0}}">
      <van-icon name="todo-list-o" size="48" color="#cccccc" />
      <text class="empty-text">暂无检查点</text>
      <text class="empty-desc">添加检查点来跟踪学习进度</text>
    </view>

    <!-- 弹窗底部操作区域 -->
    <view class="checkpoint-modal-footer" wx:if="{{checkpointModalData.subtasks.length > 0}}">
      <view class="checkpoint-actions">
        <view class="checkpoint-action-button reset-action">
          <van-button
            type="default"
            size="small"
            bind:click="resetAllCheckpoints"
            custom-class="reset-checkpoint-btn">
            全部重置
          </van-button>
        </view>
        <view class="checkpoint-action-button complete-action">
          <van-button
            type="primary"
            size="small"
            bind:click="completeAllCheckpoints"
            custom-class="complete-checkpoint-btn">
            全部完成
          </van-button>
        </view>
      </view>
    </view>
  </view>
</van-popup>