# 微信小程序通知功能完整需求文档

## 项目概述

### 功能目标
为"备考助手"微信小程序实现完整的通知推送功能，包括任务提醒、考试提醒和搭子小组动态汇总，提升用户学习效率和参与度。

### 核心需求
1. **任务提醒**：在任务截止前的指定时间发送提醒
2. **考试提醒**：在考试前的多个时间点发送提醒
3. **小组动态汇总**：每日汇总搭子小组的活动动态

## 技术方案

### 通知机制选择
- **采用方案**：微信订阅消息
- **优势**：官方推荐、到达率高、用户体验好
- **限制**：一次订阅只能发送一次消息

### 架构设计
```
前端页面 → NotificationApi → CloudApi → 云函数 → 微信订阅消息
    ↓           ↓           ↓        ↓           ↓
订阅管理    API封装    统一调用   业务逻辑    消息发送
```

## 功能详细设计

### 1. 任务提醒功能

#### 触发机制
- **触发时机**：任务截止前指定时间（15分钟-1周前）
- **发送方式**：定时批量发送（每天9:00）
- **发送频率**：每个任务只发送一次提醒

#### 用户交互流程
1. 用户创建任务时选择提醒时间
2. 系统智能引导用户订阅任务提醒模板
3. 在指定时间自动发送提醒消息
4. 用户点击消息跳转到任务详情页

#### 数据结构
```javascript
// 任务表中的提醒字段
{
  reminderEnabled: true,
  reminderTime: '1hour',  // 提醒时间选项
  reminderSent: false     // 是否已发送提醒
}
```

#### 消息模板
```
任务提醒
任务名称：{{thing1.DATA}}
截止时间：{{date2.DATA}}
提醒内容：{{thing3.DATA}}
应用名称：{{thing4.DATA}}
```

### 2. 考试提醒功能

#### 触发机制
- **触发时机**：考试前指定天数（1天-2周前）
- **发送方式**：定时批量发送（每天9:00）
- **发送频率**：每个时间点发送一次，支持多时间点

#### 多时间点提醒解决方案
由于微信订阅消息"一次订阅，一次推送"的限制，采用**智能多次订阅策略**：

1. **透明告知**：创建考试时明确告知用户订阅限制
2. **智能追踪**：系统追踪哪些提醒还未发送
3. **主动提醒**：在合适时机引导用户重新订阅
4. **用户友好**：提供清晰的重新订阅流程

#### 数据结构
```javascript
// 考试表中的提醒设置
{
  reminderSettings: ['1day', '3days', '1week']  // 多个提醒时间点
}

// exam_reminders 集合（每个时间点一条记录）
{
  userId: "user123",
  examId: "exam456",
  examTitle: "期末考试",
  examDate: "2024-12-20",
  reminderDate: "2024-12-19",  // 具体提醒日期
  reminderDays: 1,             // 提前天数
  reminderType: "1day",        // 提醒类型
  sent: false                  // 发送状态
}

// resubscription_needs 集合（重新订阅需求追踪）
{
  userId: "user123",
  examId: "exam456",
  pendingRemindersCount: 2,        // 剩余提醒数量
  nextReminderDate: "2024-12-17",  // 下次提醒日期
  needResubscription: true,        // 是否需要重新订阅
  processed: false                 // 是否已处理
}
```

#### 消息模板
```
考试提醒
考试名称：{{thing1.DATA}}
考试时间：{{date2.DATA}}
剩余天数：{{number3.DATA}}天
提醒内容：{{thing4.DATA}}
应用名称：{{thing5.DATA}}
```

### 3. 小组动态汇总功能

#### 触发机制
- **触发时机**：每天晚上20:00
- **发送方式**：定期汇总模式（方案A）
- **汇总周期**：每日汇总前一天的活动

#### 汇总算法
```javascript
// 活动类型统计
{
  complete_task: 5,    // 完成任务5个
  join_group: 2,       // 新成员加入2人
  share_plan: 3,       // 分享计划3个
  other: 1             // 其他活动1个
}

// 生成汇总文本
"昨日共有11个动态：完成任务5个，新成员加入2人，分享计划3个，其他活动1个"
```

#### 数据结构
```javascript
// group_activities 集合
{
  groupId: "group123",
  userId: "user456",
  activityType: "complete_task",  // 活动类型
  activityData: { taskTitle: "数学复习" },
  createTime: "2024-12-10T15:30:00Z",
  notified: false,  // 是否已通知（新增字段）
  type: "complete_task"  // 便于查询的类型字段
}
```

#### 消息模板
```
小组动态汇总
小组名称：{{thing1.DATA}}
活动汇总：{{thing2.DATA}}
活动数量：{{number3.DATA}}个
日期：{{time4.DATA}}
提醒文案：{{thing5.DATA}}
```

## 技术实现

### 云函数架构

#### notificationManager 云函数
**主要功能**：
- 处理所有类型的通知发送
- 管理用户订阅状态
- 执行定时批量发送任务
- 处理重新订阅逻辑

**核心方法**：
- `sendTaskReminder()` - 发送任务提醒
- `sendExamReminder()` - 发送考试提醒
- `sendBatchReminders()` - 批量发送提醒（定时任务）
- `sendDailyGroupDigest()` - 发送小组动态汇总
- `checkAndRecordResubscriptionNeed()` - 检查重新订阅需求
- `smartResubscriptionRequest()` - 智能重新订阅请求

#### taskManager 云函数
**修改内容**：
- 在任务完成时触发小组动态通知
- 调用 notificationManager 发送小组活动通知

#### studyGroupManager 云函数
**修改内容**：
- 在 createActivity 函数中添加 notified 和 type 字段
- 支持小组动态的状态追踪

### 前端API设计

#### NotificationApi 类
**主要方法**：
- `requestSubscription()` - 请求订阅权限
- `checkSubscriptionStatus()` - 检查订阅状态
- `smartRequestSubscription()` - 智能订阅请求
- `onTaskCreated()` - 任务创建后处理
- `onExamCreated()` - 考试创建后处理
- `requestExamSubscriptionWithWarning()` - 考试订阅特殊处理
- `checkResubscriptionNeed()` - 检查重新订阅需求

#### CloudApi 类
**新增方法**：
- `sendDailyGroupDigest()` - 调用小组动态汇总
- `checkResubscriptionNeed()` - 检查重新订阅需求

### 定时任务配置

#### 触发器设置
1. **dailyReminders**
   - 时间：每天上午9:00
   - Cron表达式：`0 0 9 * * * *`
   - 功能：执行任务和考试提醒

2. **dailyGroupDigest**
   - 时间：每天晚上20:00
   - Cron表达式：`0 0 20 * * * *`
   - 功能：执行小组动态汇总

## 数据库设计

### 新增集合

#### user_subscriptions（用户订阅状态）
```javascript
{
  _id: "subscription_001",
  openid: "user_openid_123",
  userId: "user123",
  templateIds: ["TASK_TEMPLATE_ID", "EXAM_TEMPLATE_ID"],
  status: "active",  // active/inactive
  subscriptionTime: "2024-12-10T10:00:00Z",
  updateTime: "2024-12-10T10:00:00Z"
}
```

#### notification_logs（通知发送日志）
```javascript
{
  _id: "log_001",
  openid: "user_openid_123",
  userId: "user123",
  type: "task_reminder",  // task_reminder/exam_reminder/group_digest
  templateId: "TASK_TEMPLATE_ID",
  targetId: "task_or_exam_id",
  sendTime: "2024-12-10T09:00:00Z",
  success: true,
  errcode: 0,
  errmsg: "ok"
}
```

#### resubscription_needs（重新订阅需求）
```javascript
{
  _id: "resub_001",
  userId: "user123",
  examId: "exam456",
  pendingRemindersCount: 2,
  nextReminderDate: "2024-12-17",
  needResubscription: true,
  createTime: "2024-12-13T09:00:00Z",
  processed: false
}
```

### 修改现有集合

#### tasks 集合
**新增字段**：
- `reminderSent: false` - 是否已发送提醒

#### group_activities 集合
**新增字段**：
- `notified: false` - 是否已通知
- `type: "complete_task"` - 活动类型（便于查询）

## 部署指南

### 1. 微信公众平台配置

#### 申请订阅消息模板
需要在微信公众平台申请以下3个模板：

1. **任务提醒模板**
   - 关键词：任务名称(thing) + 截止时间(date) + 提醒内容(thing) + 应用名称(thing)

2. **考试提醒模板**
   - 关键词：考试名称(thing) + 考试时间(date) + 剩余天数(number) + 提醒内容(thing) + 应用名称(thing)

3. **小组动态汇总模板**
   - 关键词：小组名称(thing) + 活动汇总(thing) + 活动数量(number) + 日期(time) + 提醒文案(thing)

#### 配置模板ID
获得模板ID后，替换以下文件中的占位符：
- `utils/notificationApi.js` - TEMPLATE_IDS 配置
- `cloudfunctions/notificationManager/index.js` - 模板ID占位符
- `cloudfunctions/taskManager/index.js` - 小组动态模板ID

### 2. 云函数部署

#### 部署步骤
1. 右键 `cloudfunctions/notificationManager` → 上传并部署
2. 右键 `cloudfunctions/taskManager` → 上传并部署
3. 右键 `cloudfunctions/studyGroupManager` → 上传并部署

#### 配置定时触发器
在云开发控制台为 notificationManager 云函数配置两个定时触发器：
- dailyReminders：每天9:00执行
- dailyGroupDigest：每天20:00执行

### 3. 数据库初始化

#### 创建新集合
在云开发控制台创建以下集合：
- `user_subscriptions`
- `notification_logs`
- `resubscription_needs`

#### 设置索引
```javascript
// user_subscriptions 索引
db.user_subscriptions.createIndex({ "openid": 1 })
db.user_subscriptions.createIndex({ "status": 1 })

// notification_logs 索引
db.notification_logs.createIndex({ "userId": 1, "sendTime": -1 })
db.notification_logs.createIndex({ "type": 1, "sendTime": -1 })

// resubscription_needs 索引
db.resubscription_needs.createIndex({ "userId": 1, "processed": 1 })
db.resubscription_needs.createIndex({ "nextReminderDate": 1 })
```

### 4. 前端集成

#### 应用启动时检查
在 `app.js` 的 `onShow` 方法中添加：
```javascript
onShow() {
  // 检查是否需要重新订阅
  setTimeout(async () => {
    await NotificationApi.checkResubscriptionNeed()
  }, 3000)  // 延迟3秒，避免影响启动速度
}
```

#### 页面集成确认
确保以下页面已正确集成：
- `pages/add-task/index.js` - 任务创建时订阅
- `pages/add-exam/index.js` - 考试创建时订阅
- `pages/notification-settings/index.js` - 通知设置管理

## 测试验证

### 功能测试清单

#### 任务提醒测试
- [ ] 创建任务时订阅引导正常
- [ ] 任务提醒按时发送
- [ ] 消息内容格式正确
- [ ] 点击消息跳转正常

#### 考试提醒测试
- [ ] 创建考试时多次订阅说明清晰
- [ ] 多个提醒时间点都能发送
- [ ] 重新订阅提醒机制正常
- [ ] 用户体验流畅

#### 小组动态汇总测试
- [ ] 小组活动正确记录
- [ ] 每日汇总内容准确
- [ ] 汇总通知按时发送
- [ ] 活动状态正确标记

#### 系统稳定性测试
- [ ] 定时触发器稳定运行
- [ ] 批量发送性能良好
- [ ] 异常情况处理正确
- [ ] 日志记录完整

### 性能指标

#### 目标指标
- 通知到达率：≥95%
- 发送延迟：≤5分钟
- 系统可用性：≥99.9%
- 用户订阅率：≥60%

## 运维监控

### 关键监控指标
1. **通知发送成功率**
2. **定时任务执行状态**
3. **用户订阅状态分布**
4. **重新订阅转化率**
5. **云函数执行性能**

### 日志分析
- 定期分析 notification_logs 表
- 监控发送失败的原因
- 优化通知内容和时机

### 用户反馈
- 收集用户对通知功能的反馈
- 持续优化通知内容和频率
- 改进重新订阅的用户体验

## 总结

本通知功能设计充分考虑了微信订阅消息的限制，通过智能多次订阅策略解决了多时间点提醒的技术难题，为用户提供了完整、可靠的学习提醒服务。

### 核心优势
- ✅ **功能完整**：支持任务、考试、小组三种通知类型
- ✅ **技术可靠**：完善的状态追踪和错误处理
- ✅ **用户友好**：透明的订阅机制和智能的重新订阅
- ✅ **架构清晰**：模块化设计，易于维护和扩展

### 创新点
- **智能多次订阅策略**：解决微信订阅消息限制
- **透明告知机制**：让用户理解技术限制
- **主动服务理念**：系统主动提醒重新订阅
- **汇总通知模式**：减少打扰，提升体验

## 附录

### A. 代码实现示例

#### A1. 考试多时间点提醒核心代码
```javascript
// 创建考试提醒记录（支持多时间点）
async function createExamReminder(userId, examId, examData) {
  const examDate = new Date(examData.examDate)
  const reminderSettings = examData.reminderSettings || []

  const reminderDaysMap = {
    '1day': 1, '3days': 3, '1week': 7, '2weeks': 14
  }

  // 为每个提醒设置创建独立记录
  for (const reminderSetting of reminderSettings) {
    const days = reminderDaysMap[reminderSetting] || 3
    const reminderDate = new Date(examDate)
    reminderDate.setDate(reminderDate.getDate() - days)

    const reminder = {
      userId, examId, examTitle: examData.title,
      examDate, reminderDate, reminderDays: days,
      reminderType: reminderSetting, sent: false,
      createTime: new Date()
    }

    await db.collection('exam_reminders').add({ data: reminder })
  }
}
```

#### A2. 智能重新订阅检查
```javascript
// 检查并记录重新订阅需求
async function checkAndRecordResubscriptionNeed(userId, examId) {
  const pendingReminders = await db.collection('exam_reminders')
    .where({ userId, examId, sent: false }).get()

  if (pendingReminders.data.length > 0) {
    await db.collection('resubscription_needs').add({
      data: {
        userId, examId,
        pendingRemindersCount: pendingReminders.data.length,
        nextReminderDate: pendingReminders.data[0].reminderDate,
        needResubscription: true, processed: false,
        createTime: new Date()
      }
    })
  }
}
```

#### A3. 小组动态汇总算法
```javascript
// 分析小组活动数据
function analyzeGroupActivities(activities) {
  const typeCount = {}
  activities.forEach(activity => {
    const type = activity.type || 'other'
    typeCount[type] = (typeCount[type] || 0) + 1
  })

  return { totalCount: activities.length, typeCount }
}

// 生成汇总内容
function generateDigestContent(stats) {
  let typeSummary = ''
  if (stats.typeCount.complete_task) {
    typeSummary += `完成任务${stats.typeCount.complete_task}个`
  }
  if (stats.typeCount.join_group) {
    typeSummary += (typeSummary ? '，' : '') +
      `新成员加入${stats.typeCount.join_group}人`
  }

  return `昨日共有${stats.totalCount}个动态：${typeSummary}`
}
```

### B. 配置文件模板

#### B1. 模板ID配置
```javascript
// utils/notificationApi.js
static TEMPLATE_IDS = {
  TASK_REMINDER: 'YOUR_TASK_REMINDER_TEMPLATE_ID',
  EXAM_REMINDER: 'YOUR_EXAM_REMINDER_TEMPLATE_ID',
  GROUP_DIGEST: 'YOUR_GROUP_DIGEST_TEMPLATE_ID'
}
```

#### B2. 定时触发器配置
```json
{
  "triggers": [
    {
      "name": "dailyReminders",
      "type": "timer",
      "config": "0 0 9 * * * *",
      "description": "每天9点执行任务和考试提醒"
    },
    {
      "name": "dailyGroupDigest",
      "type": "timer",
      "config": "0 0 20 * * * *",
      "description": "每天20点执行小组动态汇总"
    }
  ]
}
```

### C. 故障排除指南

#### C1. 常见问题及解决方案

**问题1：通知未发送**
- 检查用户订阅状态
- 验证模板ID配置
- 查看notification_logs错误记录
- 确认微信订阅消息配额

**问题2：定时任务未执行**
- 检查触发器配置和状态
- 验证Cron表达式格式
- 查看云函数执行日志
- 确认云开发环境状态

**问题3：重新订阅不生效**
- 检查resubscription_needs记录
- 验证前端检查逻辑
- 确认用户操作流程
- 查看API调用日志

#### C2. 性能优化建议

**数据库优化**
- 为常用查询字段创建索引
- 定期清理过期的通知日志
- 使用批量操作减少数据库调用

**云函数优化**
- 合理设置内存和超时时间
- 使用连接池优化数据库连接
- 实现请求缓存减少重复计算

**用户体验优化**
- 优化订阅请求时机
- 简化重新订阅流程
- 提供清晰的状态反馈

### D. 扩展功能规划

#### D1. 短期扩展（1-3个月）
- 个性化通知时间设置
- 通知效果统计分析
- 用户通知偏好学习

#### D2. 中期扩展（3-6个月）
- 多渠道通知整合（邮件、短信）
- 智能通知频率调节
- 群组通知功能增强

#### D3. 长期规划（6个月以上）
- AI驱动的个性化提醒
- 跨平台通知同步
- 高级数据分析和洞察

### E. 项目交付清单

#### E1. 代码交付
- [ ] 云函数代码（notificationManager、taskManager、studyGroupManager）
- [ ] 前端API代码（notificationApi.js、cloudApi.js）
- [ ] 页面集成代码（add-task、add-exam、notification-settings）
- [ ] 数据库初始化脚本

#### E2. 配置交付
- [ ] 微信订阅消息模板申请指南
- [ ] 云函数定时触发器配置指南
- [ ] 数据库集合和索引配置
- [ ] 环境变量和配置文件

#### E3. 文档交付
- [ ] 完整需求文档（本文档）
- [ ] 技术实现文档
- [ ] 部署指南文档
- [ ] 测试验证文档
- [ ] 运维监控文档

#### E4. 测试交付
- [ ] 功能测试用例
- [ ] 性能测试报告
- [ ] 兼容性测试结果
- [ ] 用户体验测试反馈

---

**文档版本**：v1.0
**创建日期**：2024-12-10
**最后更新**：2024-12-10
**文档状态**：已完成
