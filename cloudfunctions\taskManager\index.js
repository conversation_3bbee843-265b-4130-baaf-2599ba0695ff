// 任务管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 根据openid获取用户ID - 修复版本
async function getUserId(openid) {
  try {
    const userResult = await db.collection('users')
      .where({ openid: openid })
      .get()

    if (userResult.data.length > 0) {
      const user = userResult.data[0]
      console.log('🔍 用户ID查询结果:', {
        openid: openid,
        userId: user._id,
        userOpenid: user.openid
      })

      // 检查任务数据中使用的是哪种ID格式
      const taskSample = await db.collection('tasks')
        .where({ userId: user._id })
        .limit(1)
        .get()

      const taskSampleByOpenid = await db.collection('tasks')
        .where({ userId: openid })
        .limit(1)
        .get()

      console.log('📝 任务ID格式检查:', {
        tasksByUserId: taskSample.data.length,
        tasksByOpenid: taskSampleByOpenid.data.length
      })

      // 如果任务数据中使用的是openid，则返回openid；否则返回_id
      if (taskSampleByOpenid.data.length > 0 && taskSample.data.length === 0) {
        console.log('⚠️ 检测到任务数据使用openid作为userId，返回openid')
        return openid
      } else {
        console.log('✅ 任务数据使用_id作为userId，返回_id')
        return user._id
      }
    }
    return null
  } catch (error) {
    console.error('获取用户ID失败:', error)
    return null
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action, data } = event

  try {
    // 获取用户ID
    const userId = await getUserId(wxContext.OPENID)
    if (!userId) {
      return { success: false, error: '用户未登录或不存在' }
    }

    switch (action) {
      case 'getTasks':
        return await getTasks(userId, data)
      case 'getTaskById':
        return await getTaskById(userId, data)
      case 'addTask':
        return await addTask(userId, data)
      case 'updateTask':
        return await updateTask(userId, data)
      case 'deleteTask':
        return await deleteTask(userId, data)
      case 'completeTask':
        return await completeTask(userId, data)
      case 'getTaskStats':
        return await getTaskStats(userId, data)
      case 'getBatchTasks':
        return await getBatchTasks(userId, data)
      case 'fixTaskUserIds':
        return await fixTaskUserIds(userId, data)
      case 'updateTaskSubtasks':
        return await updateTaskSubtasks(userId, data)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('任务管理云函数错误:', error)
    return { success: false, error: error.message }
  }
}

// 获取任务列表
async function getTasks(userId, params) {
  const { filter, limit = 20, skip = 0 } = params || {}

  console.log('🔍 getTasks 调用详情:', {
    userId: userId,
    filter: filter,
    limit: limit,
    skip: skip
  })

  let query = db.collection('tasks').where({
    userId: userId
  })
  
  if (filter) {
    if (filter.examId) {
      query = query.where({ examId: filter.examId })
    }
    // 只有在明确指定completed状态且不是includeCompleted时才过滤
    if (filter.completed !== undefined && !filter.includeCompleted) {
      query = query.where({ completed: filter.completed })
    }
    if (filter.priority) {
      query = query.where({ priority: filter.priority })
    }
    if (filter.subject) {
      query = query.where({ subject: filter.subject })
    }
    if (filter.dateRange) {
      const { start, end } = filter.dateRange
      query = query.where({
        dueDate: _.gte(new Date(start)).and(_.lte(new Date(end)))
      })
    }
    // 添加今日任务过滤
    if (filter.todayOnly) {
      const today = new Date()
      const todayString = today.toISOString().split('T')[0] // 获取 YYYY-MM-DD 格式的今天日期
      
      // 今日任务定义：截止日期 <= 今天的所有未完成任务，或截止日期 = 今天的所有任务
      query = query.where(
        _.or([
          // 未完成且截止日期 <= 今天的任务（包括过期任务）
          {
            completed: false,
            dueDate: _.lte(todayString)
          },
          // 截止日期是今天的所有任务（包括已完成的）
          {
            dueDate: _.eq(todayString)
          }
        ])
      )
      
      console.log('今日任务查询条件:', {
        todayString: todayString,
        queryType: '截止日期<=今天的未完成任务 或 截止日期=今天的所有任务'
      })
    }
  }
  
  const result = await query
    .orderBy('completed', 'asc')  // 未完成的任务排在前面
    .orderBy('dueDate', 'asc')    // 按截止日期升序
    .orderBy('priority', 'desc')  // 高优先级排在前面
    .skip(skip)
    .limit(limit)
    .get()
  
  console.log('任务查询结果:', {
    userId: userId,
    filter: filter,
    resultCount: result.data.length,
    sampleTasks: result.data.slice(0, 3).map(task => ({
      _id: task._id,
      title: task.title,
      userId: task.userId,
      dueDate: task.dueDate,
      completed: task.completed,
      priority: task.priority
    }))
  })
  
  // 额外检查：验证返回的任务是否真的属于当前用户
  const invalidTasks = result.data.filter(task => task.userId !== userId)
  if (invalidTasks.length > 0) {
    console.error('⚠️ 发现不属于当前用户的任务:', {
      currentUserId: userId,
      invalidTasks: invalidTasks.map(task => ({
        _id: task._id,
        title: task.title,
        userId: task.userId
      }))
    })
  }
  
  return { success: true, data: result.data, total: result.data.length }
}

// 根据ID获取单个任务
async function getTaskById(userId, params) {
  const { taskId } = params || {}

  if (!taskId) {
    return { success: false, error: '任务ID不能为空' }
  }

  try {
    const result = await db.collection('tasks').doc(taskId).get()

    if (!result.data) {
      return { success: false, error: '任务不存在' }
    }

    // 检查任务是否属于当前用户
    if (result.data.userId !== userId) {
      return { success: false, error: '无权访问该任务' }
    }

    return { success: true, data: result.data }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    return { success: false, error: '获取任务详情失败' }
  }
}

// 添加任务
async function addTask(userId, taskData) {
  const task = {
    ...taskData,
    userId: userId,
    completed: false,
    createTime: new Date(),
    updateTime: new Date()
  }

  const result = await db.collection('tasks').add({ data: task })
  return { success: true, data: { _id: result._id, ...task } }
}

// 更新任务
async function updateTask(userId, { taskId, updates }) {
  const updateData = {
    ...updates,
    updateTime: new Date()
  }

  const result = await db.collection('tasks')
    .where({ _id: taskId, userId: userId })
    .update({ data: updateData })

  return { success: true, data: result }
}

// 删除任务
async function deleteTask(userId, { taskId }) {
  const result = await db.collection('tasks')
    .where({ _id: taskId, userId: userId })
    .remove()

  return { success: true, data: result }
}

// 完成任务
async function completeTask(userId, { taskId, completed = true }) {
  try {
    // 先获取任务信息
    const taskResult = await db.collection('tasks').doc(taskId).get()
    if (!taskResult.data || taskResult.data.userId !== userId) {
      return { success: false, error: '任务不存在或无权限' }
    }

    const task = taskResult.data

    const updateData = {
      completed,
      updateTime: new Date()
    }

    if (completed) {
      updateData.completedTime = new Date()
    }

    const result = await db.collection('tasks')
      .where({ _id: taskId, userId: userId })
      .update({ data: updateData })

    // 如果任务完成，检查是否需要发送小组动态通知
    if (completed && task.examId) {
      try {
        await notifyGroupMembers(userId, task)
      } catch (notifyError) {
        console.error('发送小组通知失败，但不影响任务完成:', notifyError)
      }
    }

    return { success: true, data: result }
  } catch (error) {
    console.error('完成任务失败:', error)
    return { success: false, error: error.message }
  }
}

// 通知小组成员任务完成
async function notifyGroupMembers(userId, task) {
  try {
    // 查找用户所在的小组
    const groupResult = await db.collection('study_groups')
      .where({
        examId: task.examId,
        'members.userId': userId,
        status: 'active'
      })
      .get()

    if (groupResult.data.length === 0) {
      console.log('用户不在任何小组中，跳过通知')
      return
    }

    const group = groupResult.data[0]

    // 调用通知管理云函数发送小组动态通知
    await wx.cloud.callFunction({
      name: 'notificationManager',
      data: {
        action: 'sendGroupActivityNotification',
        data: {
          groupId: group._id,
          actorUserId: userId,
          activityType: 'complete_task',
          activityData: {
            taskTitle: task.title,
            taskId: task._id
          },
          templateId: 'GROUP_DIGEST_TEMPLATE_ID_PLACEHOLDER' // 需要在微信公众平台申请后替换
        }
      }
    })

    console.log('小组动态通知已发送')
  } catch (error) {
    console.error('通知小组成员失败:', error)
    throw error
  }
}

// 获取任务统计
async function getTaskStats(userId, params) {
  const { dateRange } = params || {}

  let query = db.collection('tasks').where({
    userId: userId
  })
  
  if (dateRange) {
    const { start, end } = dateRange
    query = query.where({
      createTime: _.gte(new Date(start)).and(_.lte(new Date(end)))
    })
  }
  
  const allTasks = await query.get()
  const tasks = allTasks.data
  const today = new Date()
  
  // 计算今日任务
  const todayTasks = tasks.filter(task => {
    if (!task.dueDate) return false
    const dueDate = new Date(task.dueDate)
    return dueDate.toDateString() === today.toDateString()
  })
  
  const stats = {
    total: tasks.length,
    completed: tasks.filter(t => t.completed).length,
    pending: tasks.filter(t => !t.completed).length,
    today: todayTasks.length,
    todayCompleted: todayTasks.filter(t => t.completed).length,
    overdue: tasks.filter(t => !t.completed && new Date(t.dueDate) < new Date()).length,
    byPriority: {
      high: tasks.filter(t => t.priority === 'high').length,
      medium: tasks.filter(t => t.priority === 'medium').length,
      low: tasks.filter(t => t.priority === 'low').length
    },
    bySubject: {}
  }
  
  // 按科目统计
  tasks.forEach(task => {
    if (task.subject) {
      stats.bySubject[task.subject] = (stats.bySubject[task.subject] || 0) + 1
    }
  })
  
  console.log('任务统计结果:', stats)
  return { success: true, data: stats }
}

// 批量更新任务子任务状态
async function updateTaskSubtasks(userId, params) {
  const { taskId, updates } = params || {}

  if (!taskId || !updates || !Array.isArray(updates)) {
    return { success: false, error: '参数错误' }
  }

  try {
    // 先获取任务信息
    const taskResult = await db.collection('tasks').doc(taskId).get()
    if (!taskResult.data || taskResult.data.userId !== userId) {
      return { success: false, error: '任务不存在或无权限' }
    }

    const task = taskResult.data
    let subtasks = task.subtasks || []

    // 更新子任务状态
    updates.forEach(update => {
      const index = subtasks.findIndex(sub => sub.id === update.id)
      if (index !== -1) {
        subtasks[index] = {
          ...subtasks[index],
          ...update,
          completedTime: update.completed ? (update.completedTime || new Date()) : null
        }
      }
    })

    // 计算完成统计
    const completedSubtasks = subtasks.filter(sub => sub.completed).length
    const totalSubtasks = subtasks.length

    // 更新任务
    const updateData = {
      subtasks,
      completedSubtasks,
      totalSubtasks,
      updateTime: new Date()
    }

    const result = await db.collection('tasks')
      .where({ _id: taskId, userId: userId })
      .update({ data: updateData })

    return { success: true, data: { updated: result.stats.updated, completedSubtasks, totalSubtasks } }
  } catch (error) {
    console.error('批量更新子任务失败:', error)
    return { success: false, error: '批量更新子任务失败' }
  }
}

// 批量获取多个考试的任务
async function getBatchTasks(userId, data) {
  try {
    const { examIds, includeCompleted = true, todayOnly = false } = data

    if (!Array.isArray(examIds) || examIds.length === 0) {
      return { success: false, error: '考试ID列表不能为空' }
    }

    console.log('批量获取任务，考试数量:', examIds.length, '今日任务:', todayOnly)

    // 构建基础查询条件
    let query = db.collection('tasks').where({
      userId: userId,
      examId: _.in(examIds)
    })

    // 如果不包含已完成任务，添加过滤条件
    if (!includeCompleted) {
      query = query.where({ completed: false })
    }

    // 如果只要今日任务，添加日期过滤
    if (todayOnly) {
      const today = new Date()
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0)
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59)
      query = query.where({
        dueDate: _.gte(startOfDay).and(_.lte(endOfDay))
      })
    }

    // 批量查询所有考试的任务
    const result = await query
      .orderBy('createTime', 'desc')
      .get()

    console.log('批量查询到任务数量:', result.data.length)

    // 按考试ID分组任务
    const tasksByExam = {}
    examIds.forEach(examId => {
      tasksByExam[examId] = []
    })

    result.data.forEach(task => {
      if (tasksByExam[task.examId]) {
        tasksByExam[task.examId].push(task)
      }
    })

    return { success: true, data: tasksByExam }
  } catch (error) {
    console.error('批量获取任务失败:', error)
    return { success: false, error: error.message }
  }
}

// 修复任务数据中的用户ID问题
async function fixTaskUserIds(currentUserId, data) {
  try {
    console.log('🔧 开始修复任务用户ID关联问题')
    
    // 1. 获取当前用户的OpenID
    const currentUserResult = await db.collection('users').doc(currentUserId).get()
    if (!currentUserResult.data) {
      return { success: false, error: '当前用户不存在' }
    }
    
    const currentUserOpenId = currentUserResult.data.openid
    console.log('当前用户信息:', { userId: currentUserId, openid: currentUserOpenId })
    
    // 2. 查找所有可能属于当前用户但userId字段不正确的任务
    // 这些任务可能使用了openid作为userId
    const tasksWithWrongUserId = await db.collection('tasks')
      .where({
        userId: currentUserOpenId  // 查找userId字段是openid的任务
      })
      .get()
    
    console.log('找到需要修复的任务数量:', tasksWithWrongUserId.data.length)
    
    if (tasksWithWrongUserId.data.length === 0) {
      return { success: true, message: '没有需要修复的任务' }
    }
    
    // 3. 批量修复这些任务的userId字段
    let fixedCount = 0
    for (const task of tasksWithWrongUserId.data) {
      try {
        await db.collection('tasks').doc(task._id).update({
          data: {
            userId: currentUserId,  // 修正为真正的用户ID
            updateTime: new Date()
          }
        })
        fixedCount++
        console.log(`修复任务: ${task.title} (${task._id})`)
      } catch (error) {
        console.error(`修复任务 ${task._id} 失败:`, error)
      }
    }
    
    console.log(`✅ 修复完成: ${fixedCount}/${tasksWithWrongUserId.data.length}`)
    
    return { 
      success: true, 
      message: `成功修复 ${fixedCount} 个任务的用户ID`,
      fixedCount: fixedCount,
      totalFound: tasksWithWrongUserId.data.length
    }
    
  } catch (error) {
    console.error('修复任务用户ID失败:', error)
    return { success: false, error: error.message }
  }
}
