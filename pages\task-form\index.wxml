<!--pages/task-form/index.wxml-->
<view class="task-form-container">
  <!-- 表单内容区域 -->
  <view class="form-content">
    <!-- 加载状态 -->
    <view class="loading-content" wx:if="{{loading}}">
      <van-loading type="spinner" size="24px" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 表单内容 -->
    <view class="form-sections" wx:else>
      <!-- 基本信息 -->
      <van-cell-group title="基本信息" custom-class="form-section">
        <van-field
          label="复习内容"
          placeholder="请输入复习内容"
          value="{{taskForm.title}}"
          bind:input="updateTitle"
          bind:change="updateTitle"
          maxlength="50"
          show-word-limit
          required
          error-message="{{titleError}}"
          custom-class="form-field" />

        <van-field
          label="复习说明"
          type="textarea"
          placeholder="详细描述复习内容和要求..."
          value="{{taskForm.description}}"
          bind:input="updateDescription"
          bind:change="updateDescription"
          maxlength="200"
          show-word-limit
          autosize
          custom-class="form-field" />
      </van-cell-group>

      <!-- 复习分类 -->
      <van-cell-group title="复习分类" custom-class="form-section">
        <van-cell
          title="关联考试"
          value="{{taskForm.examName || '选择考试'}}"
          is-link
          bind:click="selectExam"
          custom-class="form-cell" />

        <van-cell
          title="学科"
          value="{{taskForm.subject || (taskForm.examId ? '选择学科' : '请先选择考试')}}"
          is-link
          bind:click="selectSubject"
          custom-class="form-cell" />
      </van-cell-group>

      <!-- 复习设置 -->
      <van-cell-group title="复习设置" custom-class="form-section">
        <van-cell
          title="优先级"
          value="{{selectedPriorityLabel || '选择优先级'}}"
          is-link
          bind:click="selectPriority"
          custom-class="form-cell" />

        <van-cell
          title="截止日期"
          value="{{taskForm.dueDate || '选择日期'}}"
          is-link
          bind:click="showDatePicker"
          custom-class="form-cell" />

        <van-cell
          title="截止时间"
          value="{{taskForm.dueTime || '选择时间'}}"
          is-link
          bind:click="showTimePicker"
          custom-class="form-cell" />

        <van-cell
          title="预计时长"
          value="{{taskForm.estimatedDuration || '选择预计时长'}}"
          is-link
          bind:click="selectDuration"
          custom-class="form-cell" />

        <van-cell
          title="开启提醒"
          custom-class="form-cell">
          <van-switch
            slot="right-icon"
            checked="{{taskForm.reminderEnabled}}"
            bind:change="toggleReminder"
            size="24px"
            custom-class="reminder-switch" />
        </van-cell>

        <van-cell
          wx:if="{{taskForm.reminderEnabled}}"
          title="提醒时间"
          value="{{reminderTimeText}}"
          is-link
          bind:click="selectReminderTime"
          custom-class="form-cell" />
      </van-cell-group>

      <!-- 学习检查点 -->
      <van-cell-group title="学习检查点（可选）" custom-class="form-section">
        <van-cell
          title="管理检查点"
          value="{{taskForm.subtasks.length > 0 ? taskForm.subtasks.length + '个检查点' : '帮助您跟踪学习进度'}}"
          is-link
          bind:click="toggleCheckpointSection"
          custom-class="form-cell" />

        <!-- 检查点编辑区域 -->
        <view class="checkpoint-section" wx:if="{{showCheckpointSection}}">
          <!-- 统计信息卡片 -->
          <view class="checkpoint-stats-card" wx:if="{{taskForm.subtasks.length > 0}}">
            <view class="stats-content">
              <view class="stats-main">
                <text class="stats-number">{{taskForm.subtasks.length}}</text>
                <text class="stats-label">个检查点</text>
              </view>
              <view class="stats-detail">
                <view class="progress-info">
                  <text class="completed-count">已完成 {{completedCheckpointsCount}}</text>
                  <view class="progress-bar">
                    <view class="progress-fill" style="width: {{taskForm.subtasks.length > 0 ? (completedCheckpointsCount / taskForm.subtasks.length * 100) : 0}}%"></view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 检查点列表卡片 -->
          <view class="checkpoint-list-card" wx:if="{{taskForm.subtasks.length > 0}}">
            <view
              class="checkpoint-item {{dragIndex === index ? 'dragging' : ''}}"
              wx:for="{{taskForm.subtasks}}"
              wx:key="index"
              data-index="{{index}}">

              <!-- 拖拽手柄 -->
              <view
                class="drag-handle"
                bind:longpress="startDrag"
                bind:touchstart="onTouchStart"
                bind:touchmove="onTouchMove"
                bind:touchend="onTouchEnd"
                data-index="{{index}}">
                <van-icon name="bars" size="14" color="{{dragIndex === index ? '#1890ff' : '#ccc'}}" />
              </view>

              <!-- 复选框 -->
              <van-checkbox
                value="{{item.completed}}"
                bind:change="toggleCheckpointStatus"
                data-index="{{index}}"
                custom-class="checkpoint-checkbox" />

              <!-- 内容区域 -->
              <view class="checkpoint-content">
                <view class="checkpoint-text-wrapper {{item.completed ? 'completed-wrapper' : ''}}">
                  <van-field
                    value="{{item.title}}"
                    placeholder="检查点内容"
                    bind:change="updateCheckpoint"
                    data-index="{{index}}"
                    maxlength="30"
                    border="{{false}}"
                    custom-class="checkpoint-field" />
                </view>
              </view>

              <!-- 简化操作区域 -->
              <view class="checkpoint-actions">
                <van-icon
                  name="delete"
                  size="16"
                  bind:click="removeCheckpoint"
                  data-index="{{index}}"
                  custom-class="delete-action" />
              </view>
            </view>
          </view>

          <!-- 添加检查点卡片 -->
          <view class="add-checkpoint-card">
            <!-- 默认状态：点击展开 -->
            <view class="add-checkpoint-trigger" wx:if="{{!showAddCheckpointInput}}" bind:tap="showAddCheckpointInput">
              <van-icon name="plus" size="16" color="#1890ff" />
              <text class="trigger-text">添加检查项</text>
            </view>

            <!-- 展开状态：输入和操作 -->
            <view class="add-checkpoint-expanded" wx:if="{{showAddCheckpointInput}}">
              <view class="add-input-section">
                <van-field
                  value="{{newCheckpointTitle}}"
                  placeholder="请输入检查项内容"
                  bind:change="updateNewCheckpoint"
                  maxlength="30"
                  focus="{{showAddCheckpointInput}}"
                  custom-class="add-checkpoint-field" />
              </view>

              <!-- 按钮操作区域 - 参考GoodsAction设计 -->
              <view class="checkpoint-actions">
                <view class="checkpoint-action-button save-action">
                  <van-button
                    type="primary"
                    size="small"
                    bind:click="saveNewCheckpoint"
                    disabled="{{!newCheckpointTitle}}"
                    custom-class="save-checkpoint-btn">
                    保存
                  </van-button>
                </view>
                <view class="checkpoint-action-button cancel-action">
                  <van-button
                    type="default"
                    size="small"
                    bind:click="cancelAddCheckpoint"
                    custom-class="cancel-checkpoint-btn">
                    取消
                  </van-button>
                </view>
              </view>
            </view>
          </view>


        </view>
      </van-cell-group>

      <!-- 操作按钮 -->
      <view class="form-actions">
        <view class="form-action-button cancel-action">
          <van-button
            type="default"
            size="large"
            bind:click="cancelTask"
            custom-class="cancel-btn">
            取消
          </van-button>
        </view>
        <view class="form-action-button submit-action">
          <van-button
            type="primary"
            size="large"
            bind:click="submitTask"
            loading="{{isSubmitting}}"
            custom-class="submit-btn">
            {{isEditMode ? '保存修改' : '创建复习计划'}}
          </van-button>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 考试选择弹窗 -->
<van-action-sheet
  show="{{showExamModal}}"
  title="选择考试"
  bind:close="hideExamModal"
  custom-class="exam-action-sheet">
  <view class="exam-options">
    <van-cell
      wx:for="{{examOptions}}"
      wx:key="id"
      title="{{item.name}}"
      label="{{item.date}}"
      is-link
      bind:click="selectExamOption"
      data-exam="{{item}}"
      custom-class="exam-option-cell" />

    <van-cell
      title="不关联考试"
      is-link
      bind:click="clearExamSelection"
      custom-class="exam-option-cell clear-option" />
  </view>
</van-action-sheet>

<!-- 学科选择弹窗 -->
<van-action-sheet
  show="{{showSubjectModal}}"
  title="选择学科"
  bind:close="hideSubjectModal"
  custom-class="subject-action-sheet">
  <view class="subject-options">
    <van-cell
      wx:for="{{subjectOptions}}"
      wx:key="*this"
      title="{{item}}"
      is-link
      bind:click="selectSubjectOption"
      data-subject="{{item}}"
      custom-class="subject-option-cell" />
  </view>
</van-action-sheet>

<!-- 优先级选择弹窗 -->
<van-action-sheet
  show="{{showPriorityModal}}"
  title="选择优先级"
  bind:close="hidePriorityModal"
  custom-class="priority-action-sheet">
  <view class="priority-options">
    <van-cell
      wx:for="{{priorityOptions}}"
      wx:key="value"
      bind:click="selectPriorityOption"
      data-priority="{{item}}"
      custom-class="priority-option-cell">
      <view slot="title" class="priority-content">
        <text class="priority-icon" style="color: {{item.color}}">{{item.icon}}</text>
        <text class="priority-text">{{item.label}}</text>
      </view>
      <van-icon
        wx:if="{{taskForm.priority === item.value}}"
        name="success"
        color="#1989fa"
        slot="right-icon" />
    </van-cell>
  </view>
</van-action-sheet>

<!-- 日期选择器 -->
<van-popup
  show="{{showDatePicker}}"
  position="bottom"
  bind:close="hideDatePicker"
  custom-class="date-picker-popup">
  <van-datetime-picker
    type="date"
    value="{{currentDate}}"
    min-date="{{minDate}}"
    bind:confirm="confirmDate"
    bind:cancel="hideDatePicker"
    title="选择截止日期" />
</van-popup>

<!-- 时间选择器 -->
<van-popup
  show="{{showTimePicker}}"
  position="bottom"
  bind:close="hideTimePicker"
  custom-class="time-picker-popup">
  <van-datetime-picker
    type="time"
    value="{{currentTime}}"
    bind:confirm="confirmTime"
    bind:cancel="hideTimePicker"
    title="选择截止时间" />
</van-popup>

<!-- 预计时长选择弹窗 -->
<van-action-sheet
  show="{{showDurationPicker}}"
  title="选择预计时长"
  bind:close="hideDurationPicker"
  custom-class="duration-action-sheet">
  <view class="duration-options">
    <van-cell
      wx:for="{{durationOptions}}"
      wx:key="*this"
      title="{{item}}"
      is-link
      bind:click="selectDurationOption"
      data-duration="{{item}}"
      custom-class="duration-option-cell" />
  </view>
</van-action-sheet>

<!-- 提醒时间选择弹窗 -->
<van-action-sheet
  show="{{showReminderPicker}}"
  title="选择提醒时间"
  bind:close="hideReminderPicker"
  custom-class="reminder-action-sheet">
  <view class="reminder-options">
    <van-cell
      wx:for="{{reminderOptions}}"
      wx:key="value"
      title="{{item.label}}"
      is-link
      bind:click="selectReminderOption"
      data-reminder="{{item}}"
      custom-class="reminder-option-cell" />
  </view>
</van-action-sheet>

<!-- Vant组件 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
