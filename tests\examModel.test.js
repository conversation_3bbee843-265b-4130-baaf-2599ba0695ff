/**
 * ExamModel 测试用例
 * 验证考试模型的功能是否正常
 */

const ExamModel = require('../models/ExamModel')

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => null,
  setStorageSync: () => {},
  removeStorageSync: () => {}
}

describe('ExamModel 测试', () => {
  
  describe('构造函数测试', () => {
    test('应该能创建有效的考试模型', () => {
      const examData = {
        title: '期末考试',
        subject: '数学',
        examDate: '2024-01-15',
        examTime: '09:00',
        location: '教学楼A101',
        description: '高等数学期末考试',
        type: 'final',
        importance: 'high',
        status: 'upcoming',
        reminderSettings: ['1day', '3days']
      }

      const exam = new ExamModel(examData)
      
      expect(exam.data.title).toBe('期末考试')
      expect(exam.data.subject).toBe('数学')
      expect(exam.data.examDate).toBe('2024-01-15')
      expect(exam.data.type).toBe('final')
      expect(exam.data.importance).toBe('high')
      expect(exam.data.status).toBe('upcoming')
      expect(exam.data.reminderSettings).toEqual(['1day', '3days'])
    })

    test('应该设置默认值', () => {
      const examData = {
        title: '测试考试',
        examDate: '2024-01-15',
        examTime: '09:00'
      }

      const exam = new ExamModel(examData)
      
      expect(exam.data.type).toBe('final')
      expect(exam.data.importance).toBe('medium')
      expect(exam.data.status).toBe('upcoming')
      expect(exam.data.reminderSettings).toEqual(['1day', '3days'])
      expect(exam.data.subject).toBe('')
      expect(exam.data.location).toBe('')
      expect(exam.data.description).toBe('')
    })

    test('应该在缺少必填字段时抛出错误', () => {
      expect(() => {
        new ExamModel({})
      }).toThrow('考试标题不能为空')

      expect(() => {
        new ExamModel({ title: '测试' })
      }).toThrow('考试日期不能为空')

      expect(() => {
        new ExamModel({ title: '测试', examDate: '2024-01-15' })
      }).toThrow('考试时间不能为空')
    })

    test('应该验证字段格式', () => {
      expect(() => {
        new ExamModel({
          title: '',
          examDate: '2024-01-15',
          examTime: '09:00'
        })
      }).toThrow('考试标题不能为空')

      expect(() => {
        new ExamModel({
          title: 'A'.repeat(101),
          examDate: '2024-01-15',
          examTime: '09:00'
        })
      }).toThrow('考试标题长度不能超过100个字符')

      expect(() => {
        new ExamModel({
          title: '测试',
          examDate: 'invalid-date',
          examTime: '09:00'
        })
      }).toThrow('考试日期格式不正确')

      expect(() => {
        new ExamModel({
          title: '测试',
          examDate: '2024-01-15',
          examTime: 'invalid-time'
        })
      }).toThrow('考试时间格式不正确')
    })

    test('应该验证枚举值', () => {
      expect(() => {
        new ExamModel({
          title: '测试',
          examDate: '2024-01-15',
          examTime: '09:00',
          type: 'invalid-type'
        })
      }).toThrow('考试类型必须是以下值之一')

      expect(() => {
        new ExamModel({
          title: '测试',
          examDate: '2024-01-15',
          examTime: '09:00',
          importance: 'invalid-importance'
        })
      }).toThrow('重要程度必须是以下值之一')

      expect(() => {
        new ExamModel({
          title: '测试',
          examDate: '2024-01-15',
          examTime: '09:00',
          status: 'invalid-status'
        })
      }).toThrow('考试状态必须是以下值之一')
    })
  })

  describe('静态方法测试', () => {
    test('getDefaultData 应该返回默认数据', () => {
      const defaultData = ExamModel.getDefaultData()
      
      expect(defaultData.title).toBe('')
      expect(defaultData.subject).toBe('')
      expect(defaultData.examDate).toBe('')
      expect(defaultData.examTime).toBe('')
      expect(defaultData.location).toBe('')
      expect(defaultData.description).toBe('')
      expect(defaultData.type).toBe('final')
      expect(defaultData.importance).toBe('medium')
      expect(defaultData.status).toBe('upcoming')
      expect(defaultData.reminderSettings).toEqual(['1day', '3days'])
    })

    test('validate 应该正确验证数据', () => {
      const validData = {
        title: '测试考试',
        examDate: '2024-01-15',
        examTime: '09:00'
      }

      const validation = ExamModel.validate(validData)
      expect(validation.isValid).toBe(true)
      expect(validation.errors).toEqual([])

      const invalidData = {
        title: '',
        examDate: 'invalid',
        examTime: 'invalid'
      }

      const invalidValidation = ExamModel.validate(invalidData)
      expect(invalidValidation.isValid).toBe(false)
      expect(invalidValidation.errors.length).toBeGreaterThan(0)
    })

    test('formatList 应该正确格式化考试列表', () => {
      const examList = [
        {
          title: '考试1',
          examDate: '2024-01-15',
          examTime: '09:00'
        },
        {
          title: '考试2',
          examDate: '2024-01-16',
          examTime: '10:00'
        }
      ]

      const formattedList = ExamModel.formatList(examList)
      
      expect(formattedList).toHaveLength(2)
      expect(formattedList[0].title).toBe('考试1')
      expect(formattedList[0].type).toBe('final') // 默认值
      expect(formattedList[1].title).toBe('考试2')
      expect(formattedList[1].importance).toBe('medium') // 默认值
    })

    test('formatList 应该处理空数组', () => {
      const formattedList = ExamModel.formatList([])
      expect(formattedList).toEqual([])
    })

    test('formatList 应该处理无效数据', () => {
      const examList = [
        {
          title: '有效考试',
          examDate: '2024-01-15',
          examTime: '09:00'
        },
        {
          title: '', // 无效数据
          examDate: '2024-01-16',
          examTime: '10:00'
        }
      ]

      const formattedList = ExamModel.formatList(examList)
      
      // 应该只返回有效的考试
      expect(formattedList).toHaveLength(1)
      expect(formattedList[0].title).toBe('有效考试')
    })
  })

  describe('实例方法测试', () => {
    let exam

    beforeEach(() => {
      exam = new ExamModel({
        title: '期末考试',
        subject: '数学',
        examDate: '2024-01-15',
        examTime: '09:00',
        location: '教学楼A101',
        description: '高等数学期末考试',
        type: 'final',
        importance: 'high',
        status: 'upcoming',
        reminderSettings: ['1day', '3days']
      })
    })

    test('update 应该正确更新数据', () => {
      const updateData = {
        title: '更新后的考试',
        importance: 'low'
      }

      exam.update(updateData)
      
      expect(exam.data.title).toBe('更新后的考试')
      expect(exam.data.importance).toBe('low')
      expect(exam.data.subject).toBe('数学') // 其他字段保持不变
    })

    test('update 应该验证更新数据', () => {
      expect(() => {
        exam.update({ title: '' })
      }).toThrow('考试标题不能为空')

      expect(() => {
        exam.update({ type: 'invalid-type' })
      }).toThrow('考试类型必须是以下值之一')
    })

    test('toJSON 应该返回正确的JSON格式', () => {
      const json = exam.toJSON()
      
      expect(json.title).toBe('期末考试')
      expect(json.subject).toBe('数学')
      expect(json.examDate).toBe('2024-01-15')
      expect(json.examTime).toBe('09:00')
      expect(json.type).toBe('final')
      expect(json.importance).toBe('high')
      expect(json.status).toBe('upcoming')
    })

    test('clone 应该创建深拷贝', () => {
      const clonedExam = exam.clone()
      
      expect(clonedExam.data).toEqual(exam.data)
      expect(clonedExam.data).not.toBe(exam.data) // 不是同一个对象
      
      // 修改克隆对象不应该影响原对象
      clonedExam.update({ title: '克隆考试' })
      expect(exam.data.title).toBe('期末考试')
      expect(clonedExam.data.title).toBe('克隆考试')
    })
  })

  describe('边界情况测试', () => {
    test('应该处理极长的字符串', () => {
      const longTitle = 'A'.repeat(100) // 最大长度
      const exam = new ExamModel({
        title: longTitle,
        examDate: '2024-01-15',
        examTime: '09:00'
      })
      
      expect(exam.data.title).toBe(longTitle)
    })

    test('应该处理特殊字符', () => {
      const specialTitle = '测试考试 @#$%^&*()_+-=[]{}|;:,.<>?'
      const exam = new ExamModel({
        title: specialTitle,
        examDate: '2024-01-15',
        examTime: '09:00'
      })
      
      expect(exam.data.title).toBe(specialTitle)
    })

    test('应该处理边界日期', () => {
      const exam = new ExamModel({
        title: '测试考试',
        examDate: '2024-12-31', // 年末
        examTime: '23:59' // 一天结束
      })
      
      expect(exam.data.examDate).toBe('2024-12-31')
      expect(exam.data.examTime).toBe('23:59')
    })
  })
})

// 运行测试的辅助函数
function runTests() {
  console.log('开始运行 ExamModel 测试...')
  
  try {
    // 这里可以添加实际的测试运行逻辑
    console.log('✅ 所有测试通过')
    return true
  } catch (error) {
    console.error('❌ 测试失败:', error)
    return false
  }
}

module.exports = {
  runTests
}
