<!--pages/help/index.wxml-->
<view class="container">
  <!-- 内容区域 -->
  <view class="content">

    <!-- 常见问题 -->
    <van-cell-group title="❓ 常见问题" custom-class="section-group">
      <van-cell
        wx:for="{{faqList}}"
        wx:key="id"
        title="{{item.question}}"
        is-link
        bind:click="toggleFaq"
        data-id="{{item.id}}"
        custom-class="faq-cell"
      >
        <van-icon slot="right-icon" name="{{item.expanded ? 'arrow-up' : 'arrow-down'}}" />
      </van-cell>
      <view wx:for="{{faqList}}" wx:key="id" class="faq-answer {{item.expanded ? 'show' : ''}}" wx:if="{{item.expanded}}">
        <text class="answer-text">{{item.answer}}</text>
      </view>
    </van-cell-group>

    <!-- 使用指南 -->
    <van-cell-group title="📖 使用指南" custom-class="section-group">
      <van-cell
        wx:for="{{guideList}}"
        wx:key="id"
        title="{{item.title}}"
        label="{{item.description}}"
        is-link
        bind:click="openGuide"
        data-guide="{{item}}"
        custom-class="guide-cell"
      >
        <view slot="icon" class="guide-icon">{{item.icon}}</view>
      </van-cell>
    </van-cell-group>

    <!-- 联系我们 -->
    <van-cell-group title="📞 联系我们" custom-class="section-group">
      <van-cell
        title="邮箱支持"
        label="<EMAIL>"
        is-link
        bind:click="copyContact"
        data-text="<EMAIL>"
        custom-class="contact-cell"
      >
        <view slot="icon" class="contact-icon">📧</view>
        <text slot="right-icon" class="contact-action">复制</text>
      </van-cell>

      <van-cell
        title="用户群"
        label="加入用户交流群"
        is-link
        bind:click="joinGroup"
        custom-class="contact-cell"
      >
        <view slot="icon" class="contact-icon">👥</view>
        <text slot="right-icon" class="contact-action">加入</text>
      </van-cell>

      <van-cell
        title="开源项目"
        label="GitHub 项目地址"
        is-link
        bind:click="openGithub"
        custom-class="contact-cell"
      >
        <view slot="icon" class="contact-icon">💻</view>
        <text slot="right-icon" class="contact-action">访问</text>
      </van-cell>
    </van-cell-group>



  </view>
</view>

<!-- 使用指南弹窗 -->
<view class="guide-modal" wx:if="{{showGuideModal}}" bindtap="closeGuideModal">
  <view class="guide-modal-content" catchtap="stopPropagation">
    <view class="guide-modal-header">
      <text class="guide-modal-title">{{currentGuide.title}}</text>
      <text class="guide-modal-close" bindtap="closeGuideModal">×</text>
    </view>
    <view class="guide-modal-body">
      <text class="guide-modal-text">{{currentGuide.content}}</text>
    </view>
  </view>
</view>
