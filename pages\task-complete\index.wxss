/* pages/task-complete/index.wxss */

/* 完成庆祝 */
.celebration-container {
  background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%);
  color: #FFFFFF;
  padding: 60rpx 32rpx 40rpx;
  text-align: center;
  margin-bottom: 24rpx;
  position: relative;
  overflow: hidden;
}

.celebration-animation {
  position: relative;
  margin-bottom: 32rpx;
}

.celebration-icon {
  font-size: 120rpx;
  animation: bounce 1.5s ease-in-out;
  display: block;
}

.confetti {
  position: absolute;
  top: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 32rpx;
  animation: confetti 2s ease-out;
}

.celebration-title {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  display: block;
}

.task-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
  opacity: 0.9;
}

.completion-message {
  font-size: 26rpx;
  opacity: 0.8;
  margin-bottom: 32rpx;
  display: block;
}

.task-summary {
  background-color: rgba(255,255,255,0.15);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.summary-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.summary-stat {
  text-align: center;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 22rpx;
  opacity: 0.8;
}

.completion-time {
  font-size: 22rpx;
  opacity: 0.7;
  text-align: center;
}

.achievement-notification {
  background-color: #FFF7E6;
  border: 1rpx solid #FFD591;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-top: 24rpx;
}

.achievement-title {
  font-size: 24rpx;
  color: #FA8C16;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.achievement-list {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
  justify-content: center;
}

.achievement-badge {
  display: flex;
  align-items: center;
  gap: 4rpx;
  background-color: #FFFFFF;
  border-radius: 6rpx;
  padding: 4rpx 8rpx;
}

.achievement-icon {
  font-size: 16rpx;
}

.achievement-name {
  font-size: 20rpx;
  color: #FA8C16;
}

/* 复习反思 */
.reflection-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.reflection-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 24rpx;
  display: block;
}

.difficulty-rating {
  margin-bottom: 24rpx;
}

.difficulty-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: block;
}

.difficulty-stars {
  display: flex;
  justify-content: center;
  gap: 12rpx;
}

.difficulty-star {
  font-size: 36rpx;
  background-color: transparent;
  border: none;
  padding: 4rpx;
  color: #E9ECEF;
}

.satisfaction-rating {
  margin-bottom: 24rpx;
}

.satisfaction-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: block;
}

.satisfaction-options {
  display: flex;
  justify-content: center;
  gap: 16rpx;
}

.satisfaction-option {
  font-size: 48rpx;
  background-color: transparent;
  border: none;
  padding: 8rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.satisfaction-option.active {
  background-color: #F0F0F0;
  transform: scale(1.2);
}

.reflection-notes {
  position: relative;
}

.notes-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: block;
}

.notes-input {
  width: 100%;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
  min-height: 120rpx;
}

.notes-input:focus {
  border-color: #52C41A;
  background-color: #FFFFFF;
}

.notes-counter {
  position: absolute;
  bottom: 8rpx;
  right: 12rpx;
  font-size: 20rpx;
  color: #999999;
}

/* 复习建议 */
.suggestions-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.suggestions-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 24rpx;
  display: block;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  border: 1rpx solid;
  border-radius: 12rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
}

.suggestion-item:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.suggestion-icon {
  font-size: 32rpx;
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
}

.suggestion-description {
  font-size: 22rpx;
  color: #666666;
  display: block;
}

.suggestion-arrow {
  font-size: 24rpx;
  color: #999999;
  flex-shrink: 0;
}

/* 今日进度 */
.progress-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.progress-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 24rpx;
  display: block;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.progress-item {
  text-align: center;
}

.progress-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  display: block;
}

.progress-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
  display: block;
}

.progress-label {
  font-size: 20rpx;
  color: #666666;
  display: block;
}

.progress-bar {
  background-color: #F0F0F0;
  border-radius: 8rpx;
  height: 12rpx;
  overflow: hidden;
}

.progress-fill {
  background-color: #52C41A;
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

/* 快捷操作 */
.quick-actions-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background-color: #F8F9FA;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  transition: all 0.3s ease;
}

.action-btn:active {
  background-color: #E9ECEF;
  transform: scale(0.98);
}

.continue-btn {
  background-color: #F6FFED;
  color: #52C41A;
}

.break-btn {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.progress-btn {
  background-color: #E6F7FF;
  color: #1890FF;
}

.share-btn {
  background-color: #F9F0FF;
  color: #722ED1;
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 底部操作 */
.bottom-actions {
  padding: 32rpx;
  background-color: #FFFFFF;
  position: sticky;
  bottom: 0;
  border-top: 1rpx solid #F0F0F0;
}

.finish-btn {
  width: 100%;
  background-color: #52C41A;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.finish-btn:active {
  background-color: #389E0D;
}

/* 动画效果 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-30rpx);
  }
  70% {
    transform: translateY(-15rpx);
  }
  90% {
    transform: translateY(-4rpx);
  }
}

@keyframes confetti {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0) rotate(0deg);
  }
  50% {
    opacity: 0.8;
    transform: translateX(-50%) translateY(-20rpx) rotate(180deg);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-40rpx) rotate(360deg);
  }
}
