<!-- pages/share-plan/index.wxml -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">分享复习计划</text>
    <text class="page-subtitle">将您的复习计划分享给搭子小组</text>
  </view>

  <!-- 小组信息 -->
  <view class="section" wx:if="{{groupInfo}}">
    <view class="section-title">目标小组</view>
    <view class="group-card">
      <view class="group-info">
        <text class="group-name">{{groupInfo.groupName}}</text>
        <text class="exam-name">{{groupInfo.examName}}</text>
      </view>
      <view class="member-count">
        <text>{{groupInfo.currentMembers}}/{{groupInfo.maxMembers}}人</text>
      </view>
    </view>
  </view>

  <!-- 考试信息 -->
  <view class="section" wx:if="{{examInfo}}">
    <view class="section-title">考试信息</view>
    <view class="exam-card">
      <text class="exam-title">{{examInfo.title}}</text>
      <text class="exam-subject" wx:if="{{examInfo.subject}}">{{examInfo.subject}}</text>
      <text class="exam-date" wx:if="{{examInfo.examDate}}">考试时间：{{examInfo.examDate}}</text>
    </view>
  </view>

  <!-- 复习计划预览 -->
  <view class="section">
    <view class="section-title">复习计划预览</view>
    
    <view class="plan-summary">
      <view class="summary-item">
        <text class="summary-label">任务总数</text>
        <text class="summary-value">{{relatedTasks.length}}个</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">预估时长</text>
        <text class="summary-value">{{calculateEstimatedDays(relatedTasks)}}天</text>
      </view>
    </view>

    <view class="task-list" wx:if="{{relatedTasks.length > 0}}">
      <view class="task-item" wx:for="{{relatedTasks}}" wx:key="_id" wx:for-index="index">
        <view class="task-header">
          <text class="task-title">{{item.title}}</text>
          <view class="task-priority priority-{{item.priority}}">
            <text>{{item.priority === 'high' ? '高' : item.priority === 'medium' ? '中' : '低'}}</text>
          </view>
        </view>
        <text class="task-subject" wx:if="{{item.subject}}">{{item.subject}}</text>
        <text class="task-desc" wx:if="{{item.description}}">{{item.description}}</text>
        <view class="task-meta">
          <text class="task-duration" wx:if="{{item.estimatedDuration}}">预计{{item.estimatedDuration}}小时</text>
          <text class="task-status status-{{item.completed ? 'completed' : 'pending'}}">
            {{item.completed ? '已完成' : '待完成'}}
          </text>
        </view>
      </view>
    </view>

    <view class="empty-tasks" wx:if="{{relatedTasks.length === 0}}">
      <text class="empty-icon">📝</text>
      <text class="empty-text">该考试暂无复习任务</text>
      <text class="empty-tip">请先添加复习任务再分享计划</text>
    </view>
  </view>

  <!-- 分享说明 -->
  <view class="section">
    <view class="share-tips">
      <view class="tip-item">
        <text class="tip-icon">📋</text>
        <text class="tip-text">搭子可以复制您的计划并修改</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">🔒</text>
        <text class="tip-text">您的原计划不会被修改</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">👥</text>
        <text class="tip-text">小组成员都能看到分享的计划</text>
      </view>
    </view>
  </view>

  <!-- 分享按钮 -->
  <view class="bottom-actions">
    <button
      class="btn btn-primary btn-large"
      bindtap="onSharePlan"
      disabled="{{relatedTasks.length === 0 || loading}}"
      loading="{{loading}}"
    >
      {{loading ? '分享中...' : '分享给搭子'}}
    </button>
  </view>
</view>
