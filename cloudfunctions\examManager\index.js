// 考试管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 根据openid获取用户ID - 修复版本
async function getUserId(openid) {
  try {
    const userResult = await db.collection('users')
      .where({ openid: openid })
      .get()

    if (userResult.data.length > 0) {
      const user = userResult.data[0]
      console.log('🔍 考试管理-用户ID查询结果:', {
        openid: openid,
        userId: user._id,
        userOpenid: user.openid
      })

      // 检查考试数据中使用的是哪种ID格式
      const examSample = await db.collection('exams')
        .where({ userId: user._id })
        .limit(1)
        .get()

      const examSampleByOpenid = await db.collection('exams')
        .where({ userId: openid })
        .limit(1)
        .get()

      console.log('📚 考试ID格式检查:', {
        examsByUserId: examSample.data.length,
        examsByOpenid: examSampleByOpenid.data.length
      })

      // 如果考试数据中使用的是openid，则返回openid；否则返回_id
      if (examSampleByOpenid.data.length > 0 && examSample.data.length === 0) {
        console.log('⚠️ 检测到考试数据使用openid作为userId，返回openid')
        return openid
      } else {
        console.log('✅ 考试数据使用_id作为userId，返回_id')
        return user._id
      }
    }
    return null
  } catch (error) {
    console.error('获取用户ID失败:', error)
    return null
  }
}

/**
 * 获取带分析数据的考试列表
 */
async function getExamsWithAnalytics(userId, params) {
  const { includeProgress = true, includeTasks = false } = params || {};

  console.log('🔍 getExamsWithAnalytics - 查询参数:', { userId, includeProgress, includeTasks });

  try {
    // 获取用户的所有考试
    const examResult = await db.collection('exams')
      .where({
        userId: userId,
        isDeleted: _.neq(true)
      })
      .orderBy('examDate', 'asc')
      .get();

    console.log('📚 getExamsWithAnalytics - 查询结果:', {
      count: examResult.data.length,
      exams: examResult.data.map(e => ({ id: e._id, title: e.title, userId: e.userId }))
    });

    let exams = examResult.data;
    
    // 如果需要包含进度信息
    if (includeProgress) {
      for (let exam of exams) {
        try {
          // 获取考试相关的任务数据
          let tasks = [];

          try {
            console.log(`🔍 查询考试 "${exam.title}" 的任务数据，查询条件:`, {
              userId: userId,
              examId: exam._id,
              isDeleted: { $ne: true }
            });

            const tasksResult = await db.collection('tasks')
              .where({
                userId: userId,
                examId: exam._id,
                isDeleted: _.neq(true)
              })
              .get();
            tasks = tasksResult.data;

            console.log(`📝 考试 "${exam.title}" 任务查询结果:`, {
              找到任务数: tasks.length,
              任务列表: tasks.map(t => ({ id: t._id, title: t.title, status: t.status }))
            });
          } catch (taskError) {
            console.log(`❌ 考试 "${exam.title}" tasks集合查询失败:`, taskError.message);
            tasks = [];
          }

          // 统计已完成的任务
          const completedTasks = tasks.filter(task => task.status === 'completed');

          // 计算准备进度：完成的任务数 / 总任务数 * 100
          const progressPercent = tasks.length > 0 ?
            Math.round((completedTasks.length / tasks.length) * 100) : 0;

          // 添加进度数据
          exam.preparationProgress = progressPercent;
          exam.completedTasksCount = completedTasks.length;
          exam.totalTasksCount = tasks.length;

          if (includeTasks) {
            exam.tasks = tasks;
          }

          console.log(`📊 考试 "${exam.title}" 进度统计:`, {
            总任务数: tasks.length,
            已完成任务数: completedTasks.length,
            进度百分比: progressPercent + '%'
          });

        } catch (examError) {
          console.error(`❌ 处理考试 "${exam.title}" 进度时出错:`, examError);
          // 设置默认值
          exam.preparationProgress = 0;
          exam.completedTasksCount = 0;
          exam.totalTasksCount = 0;
        }
      }
    }

    console.log('✅ getExamsWithAnalytics - 最终返回数据:', {
      count: exams.length,
      exams: exams.map(e => ({
        id: e._id,
        title: e.title,
        preparationProgress: e.preparationProgress,
        totalTasksCount: e.totalTasksCount,
        completedTasksCount: e.completedTasksCount
      }))
    });

    return {
      success: true,
      data: exams
    };
    
  } catch (error) {
    console.error('Get exams with analytics error:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action, data } = event

  try {
    // 获取用户ID
    const userId = await getUserId(wxContext.OPENID)
    if (!userId) {
      return { success: false, error: '用户未登录或不存在' }
    }

    switch (action) {
      case 'getExams':
        return await getExams(userId, data)
      case 'getExamById':
        return await getExamById(userId, data)
      case 'addExam':
        return await addExam(userId, data)
      case 'updateExam':
        return await updateExam(userId, data)
      case 'deleteExam':
        return await deleteExam(userId, data)
      case 'getUpcomingExams':
        return await getUpcomingExams(userId, data)
      case 'getExamStats':
        return await getExamStats(userId, data)
      case 'setExamReminder':
        return await setExamReminder(userId, data)
      case 'getExamsWithAnalytics':
        return await getExamsWithAnalytics(userId, data);
      case 'batchSetReminder':
        return await batchSetReminder(userId, data);
      case 'batchDeleteExams':
        return await batchDeleteExams(userId, data);
      case 'generateExportFile':
        return await generateExportFile(userId, data);
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('考试管理云函数错误:', error)
    return { success: false, error: error.message }
  }
}

// 获取考试列表
async function getExams(userId, params) {
  const { filter, limit = 20, skip = 0 } = params || {}

  let query = db.collection('exams').where({
    userId: userId
  })
  
  if (filter) {
    if (filter.subject) {
      query = query.where({ subject: filter.subject })
    }
    if (filter.dateRange) {
      const { start, end } = filter.dateRange
      query = query.where({
        examDate: _.gte(new Date(start)).and(_.lte(new Date(end)))
      })
    }
    if (filter.status === 'upcoming') {
      query = query.where({ examDate: _.gte(new Date()) })
    } else if (filter.status === 'past') {
      query = query.where({ examDate: _.lt(new Date()) })
    }
  }
  
  const result = await query
    .orderBy('examDate', 'asc')
    .skip(skip)
    .limit(limit)
    .get()

  // 对结果进行智能排序：置顶的考试在前，然后按日期排序
  const sortedExams = result.data.sort((a, b) => {
    // 首先按置顶状态排序
    const aIsPinned = a.isPinned || false
    const bIsPinned = b.isPinned || false
    
    if (aIsPinned && !bIsPinned) return -1  // a置顶，b不置顶，a在前
    if (!aIsPinned && bIsPinned) return 1   // b置顶，a不置顶，b在前
    
    // 如果置顶状态相同，按日期排序
    const aDate = new Date(a.examDate)
    const bDate = new Date(b.examDate)
    return aDate - bDate
  })

  console.log('考试排序完成，置顶考试:', sortedExams.filter(e => e.isPinned).map(e => e.title))
  console.log('普通考试:', sortedExams.filter(e => !e.isPinned).map(e => e.title))

  // 返回排序后的数据
  return { success: true, data: sortedExams, total: sortedExams.length }
}

// 根据ID获取单个考试
async function getExamById(userId, params) {
  const { examId } = params || {}

  if (!examId) {
    return { success: false, error: '考试ID不能为空' }
  }

  try {
    const result = await db.collection('exams').doc(examId).get()

    if (!result.data) {
      return { success: false, error: '考试不存在' }
    }

    // 检查考试是否属于当前用户
    if (result.data.userId !== userId) {
      return { success: false, error: '无权访问该考试' }
    }

    // 直接返回原始数据
    return { success: true, data: result.data }
  } catch (error) {
    console.error('获取考试详情失败:', error)
    return { success: false, error: '获取考试详情失败' }
  }
}

// 添加考试
async function addExam(userId, examData) {
  try {
    // 直接使用原始数据，添加必要的系统字段
    const exam = {
      ...examData,
      userId: userId,
      createTime: new Date(),
      updateTime: new Date()
    }

    const result = await db.collection('exams').add({ data: exam })

    // 如果设置了提醒，创建提醒记录
    if (exam.reminderSettings && exam.reminderSettings.length > 0) {
      try {
        await createExamReminder(userId, result._id, exam)
        console.log('考试提醒创建成功')
      } catch (reminderError) {
        console.error('创建考试提醒失败，但考试已保存:', reminderError)
        // 不影响考试保存，只记录错误
      }
    }

    return { success: true, data: { _id: result._id, ...exam } }
  } catch (error) {
    console.error('添加考试失败:', error)
    return { success: false, error: error.message }
  }
}

// 更新考试
async function updateExam(userId, { examId, updates }) {
  try {
    // 如果提供了完整的考试数据，使用ExamModel验证
    if (updates && Object.keys(updates).length > 1) {
      // 先获取现有数据
      const existingResult = await db.collection('exams').doc(examId).get()
      if (existingResult.data && existingResult.data.userId === userId) {
        // 直接使用更新数据，移除不应该更新的系统字段
        delete updates._id
        delete updates.userId
        delete updates.createTime
      }
    }

    const updateData = {
      ...updates,
      updateTime: new Date()
    }

    const result = await db.collection('exams')
      .where({ _id: examId, userId: userId })
      .update({ data: updateData })

    if (result.stats.updated === 0) {
      return { success: false, error: '考试不存在或无权限更新' }
    }

    // 获取更新后的数据
    const updatedResult = await db.collection('exams').doc(examId).get()

    return { success: true, data: updatedResult.data }
  } catch (error) {
    console.error('更新考试失败:', error)
    return { success: false, error: error.message }
  }
}

// 删除考试
async function deleteExam(userId, { examId }) {
  try {
    // 删除考试记录
    const result = await db.collection('exams')
      .where({ _id: examId, userId: userId })
      .remove()
    
    // 删除相关提醒
    await db.collection('exam_reminders')
      .where({ examId: examId, userId: userId })
      .remove()
    
    return { success: true, data: result }
  } catch (error) {
    console.error('删除考试失败:', error)
    return { success: false, error: error.message }
  }
}

// 获取即将到来的考试
async function getUpcomingExams(userId, params) {
  const { days = 7, limit = 10 } = params || {}
  
  const now = new Date()
  const futureDate = new Date()
  futureDate.setDate(futureDate.getDate() + days)
  
  const result = await db.collection('exams')
    .where({
      userId: userId,
      examDate: _.gte(now).and(_.lte(futureDate))
    })
    .orderBy('examDate', 'asc')
    .limit(limit)
    .get()
  
  // 对即将到来的考试也进行置顶排序
  const sortedExams = result.data.sort((a, b) => {
    const aIsPinned = a.isPinned || false
    const bIsPinned = b.isPinned || false
    
    if (aIsPinned && !bIsPinned) return -1
    if (!aIsPinned && bIsPinned) return 1
    
    const aDate = new Date(a.examDate)
    const bDate = new Date(b.examDate)
    return aDate - bDate
  })
  
  return { success: true, data: sortedExams }
}

// 获取考试统计
async function getExamStats(userId, params) {
  const { dateRange } = params || {}

  console.log('📊 getExamStats - 查询参数:', { userId, dateRange });

  let query = db.collection('exams').where({
    userId: userId
  })

  if (dateRange) {
    const { start, end } = dateRange
    query = query.where({
      examDate: _.gte(new Date(start)).and(_.lte(new Date(end)))
    })
  }

  const allExams = await query.get()
  const exams = allExams.data

  console.log('📊 getExamStats - 查询结果:', {
    count: exams.length,
    exams: exams.map(e => ({ id: e._id, title: e.title, userId: e.userId }))
  });
  const now = new Date()
  
  // 计算即将到来的考试（7天内）
  const weekFromNow = new Date()
  weekFromNow.setDate(weekFromNow.getDate() + 7)
  
  const upcomingExams = exams.filter(e => {
    const examDate = new Date(e.examDate)
    return examDate >= now && examDate <= weekFromNow
  })
  
  const pastExams = exams.filter(e => new Date(e.examDate) < now)
  const futureExams = exams.filter(e => new Date(e.examDate) >= now)
  
  // 备考中的考试：将来的考试且没有标记为已完成
  const preparingExams = futureExams.filter(e => 
    !e.status || e.status !== 'completed'
  )
  
  // 已完成的考试：过去的考试或者状态为completed的考试
  const completedExams = exams.filter(e => 
    new Date(e.examDate) < now || e.status === 'completed'
  )
  
  const stats = {
    total: exams.length,
    upcoming: upcomingExams.length, // 即将到来（7天内）
    preparing: preparingExams.length, // 备考中
    completed: completedExams.length, // 已完成
    past: pastExams.length, // 已过期
    thisWeek: upcomingExams.length, // 本周考试
    bySubject: {}
  }
  
  // 按科目统计
  exams.forEach(exam => {
    if (exam.subject) {
      stats.bySubject[exam.subject] = (stats.bySubject[exam.subject] || 0) + 1
    }
  })
  
  console.log('考试统计结果:', stats)
  return { success: true, data: stats }
}

// 设置考试提醒
async function setExamReminder(openid, { examId, reminderDays }) {
  const exam = await db.collection('exams').doc(examId).get()
  
  if (!exam.data || exam.data._openid !== openid) {
    return { success: false, error: '考试不存在或无权限' }
  }
  
  await createExamReminder(openid, examId, exam.data, reminderDays)
  
  return { success: true }
}

// 创建考试提醒
async function createExamReminder(userId, examId, examData, reminderDays = null) {
  const examDate = new Date(examData.examDate)

  // 支持多个提醒时间
  const reminderSettings = examData.reminderSettings || []

  // 将提醒设置转换为天数
  const reminderDaysMap = {
    '1day': 1,
    '3days': 3,
    '1week': 7,
    '2weeks': 14
  }

  // 为每个提醒设置创建提醒记录
  for (const reminderSetting of reminderSettings) {
    const days = reminderDaysMap[reminderSetting] || 3
    const reminderDate = new Date(examDate)
    reminderDate.setDate(reminderDate.getDate() - days)

    const reminder = {
      userId: userId,
      examId: examId,
      examTitle: examData.title,
      examSubject: examData.subject,
      examDate: examDate,
      reminderDate: reminderDate,
      reminderDays: days,
      reminderType: reminderSetting,
      sent: false,
      createTime: new Date()
    }

    await db.collection('exam_reminders').add({ data: reminder })
  }
}

/**
 * 批量设置提醒
 */
async function batchSetReminder(userId, params) {
  const { examIds, reminderDays } = params;
  
  try {
    if (!examIds || !Array.isArray(examIds) || examIds.length === 0) {
      throw new Error('考试ID列表不能为空');
    }
    
    if (!reminderDays || reminderDays < 1 || reminderDays > 365) {
      throw new Error('提醒天数必须在1-365之间');
    }
    
    const results = [];
    
    for (let examId of examIds) {
      // 获取考试信息
      const examResult = await db.collection('exams')
        .doc(examId)
        .get();
      
      if (examResult.data) {
        const exam = examResult.data;
        
        // 计算提醒时间
        const examDate = new Date(exam.examDate);
        const reminderDate = new Date(examDate.getTime() - reminderDays * 24 * 60 * 60 * 1000);
        
        // 更新考试的提醒设置
        await db.collection('exams')
          .doc(examId)
          .update({
            data: {
              reminderSettings: {
                enabled: true,
                reminderDays: reminderDays,
                reminderDate: reminderDate,
                updatedAt: new Date()
              },
              updatedAt: new Date()
            }
          });
        
        // 创建提醒任务（可以调用通知管理服务）
        try {
          await cloud.callFunction({
            name: 'notificationManager',
            data: {
              action: 'scheduleExamReminder',
              userId: userId,
              examId: examId,
              examTitle: exam.title,
              reminderDate: reminderDate,
              examDate: examDate
            }
          });
        } catch (notifyError) {
          console.warn('创建提醒任务失败:', notifyError);
          // 不影响主流程，继续处理
        }
        
        results.push({
          examId: examId,
          examTitle: exam.title,
          reminderDate: reminderDate,
          success: true
        });
      }
    }
    
    return {
      success: true,
      data: {
        processedCount: results.length,
        reminderDays: reminderDays,
        results: results
      }
    };
    
  } catch (error) {
    console.error('批量设置提醒失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 批量删除考试
 */
async function batchDeleteExams(userId, params) {
  const { examIds } = params;
  
  try {
    if (!examIds || !Array.isArray(examIds) || examIds.length === 0) {
      throw new Error('考试ID列表不能为空');
    }
    
    const results = [];
    
    for (let examId of examIds) {
      try {
        // 软删除考试
        await db.collection('exams')
          .doc(examId)
          .update({
            data: {
              isDeleted: true,
              deletedAt: new Date(),
              updatedAt: new Date()
            }
          });
        
        // 软删除相关任务
        await db.collection('tasks')
          .where({
            userId: userId,
            examId: examId
          })
          .update({
            data: {
              isDeleted: true,
              deletedAt: new Date(),
              updatedAt: new Date()
            }
          });
        
        // 删除相关提醒
        await db.collection('notifications')
          .where({
            userId: userId,
            examId: examId
          })
          .remove();
        
        results.push({
          examId: examId,
          success: true
        });
        
      } catch (itemError) {
        console.error(`删除考试${examId}失败:`, itemError);
        results.push({
          examId: examId,
          success: false,
          error: itemError.message
        });
      }
    }
    
    const successCount = results.filter(r => r.success).length;
    
    return {
      success: true,
      data: {
        totalCount: examIds.length,
        successCount: successCount,
        failedCount: examIds.length - successCount,
        results: results
      }
    };
    
  } catch (error) {
    console.error('批量删除考试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 生成导出文件
 */
async function generateExportFile(userId, params) {
  const { exportData, format } = params;
  
  try {
    let fileContent;
    let fileName;
    let contentType;
    
    switch (format) {
      case 'csv':
        fileContent = exportData.content;
        fileName = exportData.filename;
        contentType = 'text/csv';
        break;
        
      case 'text':
        fileContent = exportData.content;
        fileName = exportData.filename;
        contentType = 'text/plain';
        break;
        
      case 'excel':
        // 生成简单的HTML表格格式（可以被Excel打开）
        fileContent = generateHTMLTable(exportData);
        fileName = exportData.filename.replace('.xlsx', '.html');
        contentType = 'text/html';
        break;
        
      default:
        throw new Error('不支持的导出格式');
    }
    
    // 将文件内容上传到云存储
    const fileBuffer = Buffer.from(fileContent, 'utf8');
    const cloudPath = `exports/${userId}/${Date.now()}_${fileName}`;
    
    const uploadResult = await cloud.uploadFile({
      cloudPath: cloudPath,
      fileContent: fileBuffer
    });
    
    if (uploadResult.fileID) {
      // 获取下载链接
      const tempUrlResult = await cloud.getTempFileURL({
        fileList: [uploadResult.fileID]
      });
      
      if (tempUrlResult.fileList && tempUrlResult.fileList[0]) {
        return {
          success: true,
          data: {
            fileId: uploadResult.fileID,
            fileUrl: tempUrlResult.fileList[0].tempFileURL,
            fileName: fileName,
            contentType: contentType
          }
        };
      }
    }
    
    throw new Error('文件上传失败');
    
  } catch (error) {
    console.error('生成导出文件失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 生成HTML表格（Excel格式）
 */
function generateHTMLTable(exportData) {
  const { headers, rows } = exportData;
  
  let html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>考试数据导出</title>
    <style>
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .center { text-align: center; }
    </style>
</head>
<body>
    <h2>考试数据报告</h2>
    <p>导出时间：${new Date().toLocaleString('zh-CN')}</p>
    <table>
        <thead>
            <tr>
`;
  
  // 添加表头
  headers.forEach(header => {
    html += `                <th>${header}</th>\n`;
  });
  
  html += `            </tr>
        </thead>
        <tbody>
`;
  
  // 添加数据行
  rows.forEach(row => {
    html += `            <tr>\n`;
    row.forEach(cell => {
      html += `                <td>${cell}</td>\n`;
    });
    html += `            </tr>\n`;
  });
  
  html += `        </tbody>
    </table>
</body>
</html>`;
  
  return html;
}
