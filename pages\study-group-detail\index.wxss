/* pages/study-group-detail/index.wxss */
.container {
  background: #f7f8fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 小组信息卡片 */
.group-card {
  margin: 16rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.group-tags {
  display: flex;
  gap: 12rpx;
}

.group-actions {
  display: flex;
  gap: 16rpx;
  padding: 16rpx 0;
}

.group-actions .action-btn {
  flex: 1;
}

/* Tab导航 */
.group-tabs {
  margin: 16rpx 16rpx 0 16rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 成员列表 */
.member-list {
  padding: 16rpx;
}

.member-cell {
  margin-bottom: 16rpx !important;
  border-radius: 16rpx !important;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.member-avatar-slot {
  margin-right: 16rpx;
}

.member-avatar {
  position: relative;
  width: 80rpx;
  height: 80rpx;
}

.online-status {
  position: absolute;
  bottom: 2rpx;
  right: 2rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  border: 2rpx solid white;
}

.online-status.online {
  background: #52c41a;
}

.online-status.offline {
  background: #bfbfbf;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.member-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.member-status {
  flex-shrink: 0;
}

.member-stats {
  margin-top: 16rpx;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

.stat-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.progress-item {
  margin-bottom: 16rpx;
}

.member-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.like-info {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.like-count {
  font-size: 24rpx;
  color: #666;
}

/* 共享计划列表 */
.share-list {
  padding: 16rpx;
}

.share-cell {
  margin-bottom: 16rpx !important;
  border-radius: 16rpx !important;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.sharer-avatar-slot {
  margin-right: 16rpx;
}

.plan-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12rpx;
}

.sharer-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.share-time {
  font-size: 22rpx;
  color: #999;
  flex-shrink: 0;
}

.plan-content {
  margin-top: 12rpx;
}

.plan-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.plan-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
  line-height: 1.5;
}

.plan-stats {
  display: flex;
  gap: 12rpx;
  margin-top: 12rpx;
}

.share-actions {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.share-actions .action-btn {
  min-width: 80rpx;
}

/* 小组动态列表 */
.activity-list {
  padding: 16rpx;
}

.activity-cell {
  margin-bottom: 16rpx !important;
  border-radius: 16rpx !important;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.activity-avatar-slot {
  margin-right: 16rpx;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.user-name {
  font-weight: 500;
  color: #1890ff;
}

.activity-time {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 空状态按钮 */
.empty-action-btn {
  margin-top: 16rpx;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx;
  background: white;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.1);
}

/* 全局样式覆盖 */
.van-card {
  background: white;
}

.van-tabs__nav {
  background: white;
}

.van-tab {
  font-weight: 500;
}

.van-cell {
  background: white;
}

.van-cell-group {
  background: transparent;
}

.van-empty {
  background: white;
  border-radius: 16rpx;
  margin: 16rpx;
  padding: 40rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.van-progress {
  background: #f5f5f5;
}

.van-tag {
  border: none;
}

.van-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 32rpx 48rpx;
  border-radius: 16rpx;
  z-index: 9999;
}

/* 响应式布局 */
@media (max-width: 375px) {
  .group-actions {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .share-actions {
    flex-direction: column;
    gap: 6rpx;
  }
  
  .member-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }
}

/* 动画效果 */
.member-cell,
.share-cell,
.activity-cell {
  transition: all 0.3s ease;
}

.member-cell:hover,
.share-cell:hover,
.activity-cell:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15);
}

/* 加载动画 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.container {
  animation: fadeIn 0.5s ease-out;
}

/* 进度条动画 */
.van-progress__portion {
  transition: width 0.8s ease;
}

/* 标签动画 */
.van-tag {
  transition: all 0.3s ease;
}

/* 按钮动画 */
.van-button {
  transition: all 0.3s ease;
}

.van-button:active {
  transform: scale(0.98);
}
