// 通知管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action, data } = event

  console.log('notificationManager云函数调用:', {
    action,
    userId: wxContext.OPENID,
    triggerSource: context.source || 'manual' // 区分定时触发和手动调用
  })

  try {
    // 处理定时触发器调用
    if (context.source === 'timer') {
      console.log('定时触发器调用，触发器名称:', context.name)

      // 根据触发器名称执行对应的定时任务
      switch (context.name) {
        case 'dailyReminders':
          // 每天9点执行的任务/考试提醒
          return await sendBatchReminders()
        case 'dailyGroupDigest':
          // 每天20点执行的小组动态汇总
          return await sendDailyGroupDigest()
        default:
          console.log('未知的定时触发器:', context.name)
          return { success: false, error: '未知的定时触发器' }
      }
    }

    // 处理手动调用
    switch (action) {
      case 'requestSubscription':
        return await requestSubscription(wxContext.OPENID, data)
      case 'sendTaskReminder':
        return await sendTaskReminder(data)
      case 'sendExamReminder':
        return await sendExamReminder(data)
      case 'sendBatchReminders':
        return await sendBatchReminders()
      case 'sendGroupActivityNotification':
        return await sendGroupActivityNotification(data)
      case 'sendDailyGroupDigest':
        return await sendDailyGroupDigest()
      case 'getUserSubscriptions':
        return await getUserSubscriptions(wxContext.OPENID)
      case 'updateSubscriptionStatus':
        return await updateSubscriptionStatus(wxContext.OPENID, data)
      case 'checkResubscriptionNeed':
        return await smartResubscriptionRequest(wxContext.OPENID)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('通知管理云函数错误:', error)
    return { success: false, error: error.message }
  }
}

// 请求订阅消息权限
async function requestSubscription(openid, data) {
  try {
    const { templateIds, subscriptionData } = data
    
    // 保存用户订阅状态
    const subscription = {
      openid: openid,
      templateIds: templateIds,
      subscriptionTime: new Date(),
      status: 'active',
      preferences: subscriptionData || {},
      updateTime: new Date()
    }

    // 查找是否已有订阅记录
    const existingResult = await db.collection('user_subscriptions')
      .where({ openid: openid })
      .get()

    if (existingResult.data.length > 0) {
      // 更新现有记录
      await db.collection('user_subscriptions')
        .doc(existingResult.data[0]._id)
        .update({
          data: {
            templateIds: templateIds,
            subscriptionTime: new Date(),
            status: 'active',
            preferences: subscriptionData || {},
            updateTime: new Date()
          }
        })
    } else {
      // 创建新记录
      await db.collection('user_subscriptions').add({
        data: subscription
      })
    }

    console.log('用户订阅状态已保存:', openid)
    return { success: true, message: '订阅状态已保存' }
  } catch (error) {
    console.error('保存订阅状态失败:', error)
    return { success: false, error: error.message }
  }
}

// 发送任务提醒
async function sendTaskReminder(data) {
  try {
    const { userId, taskId, taskTitle, dueDate, templateId } = data
    
    // 获取用户openid
    const userResult = await db.collection('users').doc(userId).get()
    if (!userResult.data) {
      return { success: false, error: '用户不存在' }
    }
    
    const openid = userResult.data.openid
    
    // 检查用户是否订阅了该模板
    const subscriptionResult = await db.collection('user_subscriptions')
      .where({
        openid: openid,
        status: 'active'
      })
      .get()
    
    if (subscriptionResult.data.length === 0) {
      console.log('用户未订阅通知:', openid)
      return { success: false, error: '用户未订阅通知' }
    }
    
    const subscription = subscriptionResult.data[0]
    if (!subscription.templateIds.includes(templateId)) {
      console.log('用户未订阅该模板:', templateId)
      return { success: false, error: '用户未订阅该模板' }
    }

    // 发送订阅消息
    const sendResult = await cloud.openapi.subscribeMessage.send({
      touser: openid,
      template_id: templateId,
      page: `pages/task-detail/index?id=${taskId}`,
      data: {
        thing1: { value: taskTitle.substring(0, 20) }, // 任务名称
        date2: { value: dueDate }, // 截止时间
        thing3: { value: '请及时完成复习任务' }, // 提醒内容
        thing4: { value: '备考助手' } // 应用名称
      }
    })

    // 记录发送日志
    await db.collection('notification_logs').add({
      data: {
        openid: openid,
        userId: userId,
        type: 'task_reminder',
        templateId: templateId,
        targetId: taskId,
        sendTime: new Date(),
        success: sendResult.errcode === 0,
        errcode: sendResult.errcode,
        errmsg: sendResult.errmsg
      }
    })

    console.log('任务提醒发送结果:', sendResult)
    return { 
      success: sendResult.errcode === 0, 
      errcode: sendResult.errcode,
      errmsg: sendResult.errmsg 
    }
  } catch (error) {
    console.error('发送任务提醒失败:', error)
    return { success: false, error: error.message }
  }
}

// 发送考试提醒
async function sendExamReminder(data) {
  try {
    const { userId, examId, examTitle, examDate, reminderDays, templateId } = data
    
    // 获取用户openid
    const userResult = await db.collection('users').doc(userId).get()
    if (!userResult.data) {
      return { success: false, error: '用户不存在' }
    }
    
    const openid = userResult.data.openid
    
    // 检查订阅状态
    const subscriptionResult = await db.collection('user_subscriptions')
      .where({
        openid: openid,
        status: 'active'
      })
      .get()
    
    if (subscriptionResult.data.length === 0 || 
        !subscriptionResult.data[0].templateIds.includes(templateId)) {
      console.log('用户未订阅考试提醒:', openid)
      return { success: false, error: '用户未订阅考试提醒' }
    }

    // 发送订阅消息
    const sendResult = await cloud.openapi.subscribeMessage.send({
      touser: openid,
      template_id: templateId,
      page: `pages/exam-detail/index?id=${examId}`,
      data: {
        thing1: { value: examTitle.substring(0, 20) }, // 考试名称
        date2: { value: examDate }, // 考试时间
        number3: { value: reminderDays }, // 剩余天数
        thing4: { value: '请做好考试准备' }, // 提醒内容
        thing5: { value: '备考助手' } // 应用名称
      }
    })

    // 记录发送日志
    await db.collection('notification_logs').add({
      data: {
        openid: openid,
        userId: userId,
        type: 'exam_reminder',
        templateId: templateId,
        targetId: examId,
        sendTime: new Date(),
        success: sendResult.errcode === 0,
        errcode: sendResult.errcode,
        errmsg: sendResult.errmsg
      }
    })

    console.log('考试提醒发送结果:', sendResult)
    return { 
      success: sendResult.errcode === 0, 
      errcode: sendResult.errcode,
      errmsg: sendResult.errmsg 
    }
  } catch (error) {
    console.error('发送考试提醒失败:', error)
    return { success: false, error: error.message }
  }
}

// 批量发送提醒（定时任务调用）
async function sendBatchReminders() {
  try {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    let successCount = 0
    let failCount = 0

    // 1. 发送任务提醒（今日截止的未完成任务）
    const tasksResult = await db.collection('tasks')
      .where({
        completed: false,
        dueDate: db.command.gte(today).and(db.command.lt(tomorrow)),
        reminderSent: db.command.neq(true)
      })
      .get()

    for (const task of tasksResult.data) {
      const result = await sendTaskReminder({
        userId: task.userId,
        taskId: task._id,
        taskTitle: task.title,
        dueDate: task.dueDate,
        templateId: 'TASK_REMINDER_TEMPLATE_ID_PLACEHOLDER' // 需要在微信公众平台申请后替换
      })

      if (result.success) {
        successCount++
        // 标记提醒已发送
        await db.collection('tasks').doc(task._id).update({
          data: { reminderSent: true }
        })
      } else {
        failCount++
      }
    }

    // 2. 发送考试提醒
    const remindersResult = await db.collection('exam_reminders')
      .where({
        reminderDate: db.command.gte(today).and(db.command.lt(tomorrow)),
        sent: false
      })
      .get()

    for (const reminder of remindersResult.data) {
      const result = await sendExamReminder({
        userId: reminder.userId,
        examId: reminder.examId,
        examTitle: reminder.examTitle,
        examDate: reminder.examDate,
        reminderDays: reminder.reminderDays,
        templateId: 'EXAM_REMINDER_TEMPLATE_ID_PLACEHOLDER' // 需要在微信公众平台申请后替换
      })

      if (result.success) {
        successCount++
        // 标记提醒已发送
        await db.collection('exam_reminders').doc(reminder._id).update({
          data: { sent: true, sentTime: new Date() }
        })

        // 检查该考试是否还有未发送的提醒，如果有则记录需要重新订阅
        await checkAndRecordResubscriptionNeed(reminder.userId, reminder.examId)
      } else {
        failCount++
      }
    }

    console.log(`批量提醒发送完成: 成功${successCount}条, 失败${failCount}条`)
    return { 
      success: true, 
      data: { successCount, failCount },
      message: `批量提醒发送完成: 成功${successCount}条, 失败${failCount}条`
    }
  } catch (error) {
    console.error('批量发送提醒失败:', error)
    return { success: false, error: error.message }
  }
}

// 发送搭子小组动态通知
async function sendGroupActivityNotification(data) {
  try {
    const { groupId, actorUserId, activityType, activityData, templateId } = data

    // 获取小组信息
    const groupResult = await db.collection('study_groups').doc(groupId).get()
    if (!groupResult.data) {
      return { success: false, error: '小组不存在' }
    }

    const group = groupResult.data

    // 获取操作者信息
    const actorResult = await db.collection('users').doc(actorUserId).get()
    if (!actorResult.data) {
      return { success: false, error: '用户不存在' }
    }

    const actor = actorResult.data

    // 获取需要通知的成员（除了操作者本人）
    const membersToNotify = group.members.filter(member => member.userId !== actorUserId)

    let successCount = 0
    let failCount = 0

    // 为每个成员发送通知
    for (const member of membersToNotify) {
      try {
        // 获取成员的openid
        const memberResult = await db.collection('users').doc(member.userId).get()
        if (!memberResult.data) {
          failCount++
          continue
        }

        const memberOpenid = memberResult.data.openid

        // 检查成员是否订阅了小组动态通知
        const subscriptionResult = await db.collection('user_subscriptions')
          .where({
            openid: memberOpenid,
            status: 'active'
          })
          .get()

        if (subscriptionResult.data.length === 0 ||
            !subscriptionResult.data[0].templateIds.includes(templateId)) {
          console.log('成员未订阅小组动态通知:', memberOpenid)
          failCount++
          continue
        }

        // 根据活动类型生成通知内容
        let notificationContent = ''
        let pageUrl = `pages/study-group-detail/index?id=${groupId}`

        switch (activityType) {
          case 'complete_task':
            notificationContent = `${actor.nickName}完成了任务：${activityData.taskTitle}`
            break
          case 'join_group':
            notificationContent = `${actor.nickName}加入了小组`
            break
          case 'share_plan':
            notificationContent = `${actor.nickName}分享了复习计划`
            break
          default:
            notificationContent = `${actor.nickName}在小组中有新动态`
        }

        // 发送订阅消息
        const sendResult = await cloud.openapi.subscribeMessage.send({
          touser: memberOpenid,
          template_id: templateId,
          page: pageUrl,
          data: {
            thing1: { value: group.groupName.substring(0, 20) }, // 小组名称
            thing2: { value: actor.nickName.substring(0, 20) }, // 操作者昵称
            thing3: { value: notificationContent.substring(0, 20) }, // 动态内容
            time4: { value: new Date().toLocaleString() }, // 时间
            thing5: { value: '快来看看吧！' } // 提醒文案
          }
        })

        // 记录发送日志
        await db.collection('notification_logs').add({
          data: {
            openid: memberOpenid,
            userId: member.userId,
            type: 'group_activity',
            templateId: templateId,
            targetId: groupId,
            activityType: activityType,
            sendTime: new Date(),
            success: sendResult.errcode === 0,
            errcode: sendResult.errcode,
            errmsg: sendResult.errmsg
          }
        })

        if (sendResult.errcode === 0) {
          successCount++
        } else {
          failCount++
        }

      } catch (memberError) {
        console.error('发送成员通知失败:', memberError)
        failCount++
      }
    }

    console.log(`小组动态通知发送完成: 成功${successCount}条, 失败${failCount}条`)
    return {
      success: true,
      data: { successCount, failCount },
      message: `通知发送完成: 成功${successCount}条, 失败${failCount}条`
    }
  } catch (error) {
    console.error('发送小组动态通知失败:', error)
    return { success: false, error: error.message }
  }
}

// 获取用户订阅状态
async function getUserSubscriptions(openid) {
  try {
    const result = await db.collection('user_subscriptions')
      .where({ openid: openid })
      .get()

    return { 
      success: true, 
      data: result.data.length > 0 ? result.data[0] : null 
    }
  } catch (error) {
    console.error('获取用户订阅状态失败:', error)
    return { success: false, error: error.message }
  }
}

// 更新订阅状态
async function updateSubscriptionStatus(openid, data) {
  try {
    const { status, preferences } = data
    
    const result = await db.collection('user_subscriptions')
      .where({ openid: openid })
      .get()

    if (result.data.length > 0) {
      await db.collection('user_subscriptions')
        .doc(result.data[0]._id)
        .update({
          data: {
            status: status,
            preferences: preferences || result.data[0].preferences,
            updateTime: new Date()
          }
        })
    }

    return { success: true, message: '订阅状态已更新' }
  } catch (error) {
    console.error('更新订阅状态失败:', error)
    return { success: false, error: error.message }
  }
}

// 标记活动为已通知
async function markActivitiesAsNotified(activityIds) {
  try {
    // 批量更新活动状态
    const updatePromises = activityIds.map(activityId => {
      return db.collection('group_activities').doc(activityId).update({
        data: {
          notified: true,
          notifiedTime: new Date()
        }
      })
    })

    await Promise.all(updatePromises)
    console.log(`已标记${activityIds.length}条活动为已通知状态`)
    return true
  } catch (error) {
    console.error('标记活动为已通知状态失败:', error)
    return false
  }
}

// 发送小组动态汇总通知
async function sendGroupDigestNotification(groupId, activities) {
  try {
    // 获取小组信息
    const groupResult = await db.collection('study_groups').doc(groupId).get()
    if (!groupResult.data) {
      return {
        success: false,
        error: '小组不存在',
        data: { successCount: 0, failCount: 0 }
      }
    }

    const group = groupResult.data

    // 统计活动数据
    const activityStats = analyzeGroupActivities(activities)

    // 生成汇总内容
    const digestContent = generateDigestContent(activityStats)

    // 获取小组成员
    const members = group.members || []

    let successCount = 0
    let failCount = 0

    // 为每个成员发送通知
    for (const member of members) {
      try {
        // 获取成员的openid
        const memberResult = await db.collection('users').doc(member.userId).get()
        if (!memberResult.data || !memberResult.data.openid) {
          failCount++
          continue
        }

        const memberOpenid = memberResult.data.openid

        // 检查成员是否订阅了小组动态通知
        const subscriptionResult = await db.collection('user_subscriptions')
          .where({
            openid: memberOpenid,
            status: 'active'
          })
          .get()

        if (subscriptionResult.data.length === 0 ||
            !subscriptionResult.data[0].templateIds.includes('GROUP_DIGEST_TEMPLATE_ID_PLACEHOLDER')) {
          console.log('成员未订阅小组动态通知:', memberOpenid)
          failCount++
          continue
        }

        // 发送订阅消息
        const sendResult = await cloud.openapi.subscribeMessage.send({
          touser: memberOpenid,
          template_id: 'GROUP_DIGEST_TEMPLATE_ID_PLACEHOLDER',
          page: `pages/study-group-detail/index?id=${groupId}`,
          data: {
            thing1: { value: group.groupName.substring(0, 20) }, // 小组名称
            thing2: { value: digestContent.summary.substring(0, 20) }, // 活动汇总
            number3: { value: activities.length }, // 活动数量
            time4: { value: new Date().toLocaleString().substring(0, 20) }, // 日期
            thing5: { value: '点击查看详情' } // 提醒文案
          }
        })

        // 记录发送日志
        await db.collection('notification_logs').add({
          data: {
            openid: memberOpenid,
            userId: member.userId,
            type: 'group_digest',
            templateId: 'GROUP_DIGEST_TEMPLATE_ID_PLACEHOLDER',
            targetId: groupId,
            activityCount: activities.length,
            sendTime: new Date(),
            success: sendResult.errcode === 0,
            errcode: sendResult.errcode,
            errmsg: sendResult.errmsg
          }
        })

        if (sendResult.errcode === 0) {
          successCount++
        } else {
          failCount++
        }
      } catch (memberError) {
        console.error('发送成员通知失败:', memberError)
        failCount++
      }
    }

    console.log(`小组${groupId}动态汇总通知发送完成: 成功${successCount}条, 失败${failCount}条`)
    return {
      success: true,
      data: { successCount, failCount }
    }
  } catch (error) {
    console.error('发送小组动态汇总通知失败:', error)
    return {
      success: false,
      error: error.message,
      data: { successCount: 0, failCount: 1 }
    }
  }
}

// 分析小组活动数据
function analyzeGroupActivities(activities) {
  // 按类型分组
  const typeCount = {}

  activities.forEach(activity => {
    const type = activity.type || 'other'
    typeCount[type] = (typeCount[type] || 0) + 1
  })

  // 找出最活跃的用户
  const userActivities = {}
  activities.forEach(activity => {
    const userId = activity.userId
    userActivities[userId] = (userActivities[userId] || 0) + 1
  })

  // 排序找出最活跃的用户
  const sortedUsers = Object.entries(userActivities)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 3)
    .map(([userId, count]) => ({ userId, count }))

  return {
    totalCount: activities.length,
    typeCount: typeCount,
    mostActiveUsers: sortedUsers
  }
}

// 生成汇总内容
function generateDigestContent(stats) {
  // 生成类型统计文本
  let typeSummary = ''

  if (stats.typeCount.complete_task) {
    typeSummary += `完成任务${stats.typeCount.complete_task}个`
  }

  if (stats.typeCount.join_group) {
    typeSummary += (typeSummary ? '，' : '') + `新成员加入${stats.typeCount.join_group}人`
  }

  if (stats.typeCount.share_plan) {
    typeSummary += (typeSummary ? '，' : '') + `分享计划${stats.typeCount.share_plan}个`
  }

  if (stats.typeCount.other) {
    typeSummary += (typeSummary ? '，' : '') + `其他活动${stats.typeCount.other}个`
  }

  // 生成总结
  const summary = `昨日共有${stats.totalCount}个动态：${typeSummary}`

  return {
    summary: summary,
    details: `小组昨日活跃度：${calculateActivityScore(stats)}分`
  }
}

// 计算活跃度分数
function calculateActivityScore(stats) {
  // 简单算法：每个活动2分，最高100分
  const score = Math.min(stats.totalCount * 2, 100)
  return score
}

// 检查并记录重新订阅需求
async function checkAndRecordResubscriptionNeed(userId, examId) {
  try {
    // 查询该考试是否还有未发送的提醒
    const pendingReminders = await db.collection('exam_reminders')
      .where({
        userId: userId,
        examId: examId,
        sent: false
      })
      .get()

    if (pendingReminders.data.length > 0) {
      // 还有未发送的提醒，记录需要重新订阅
      const resubscriptionRecord = {
        userId: userId,
        examId: examId,
        pendingRemindersCount: pendingReminders.data.length,
        nextReminderDate: pendingReminders.data[0].reminderDate,
        needResubscription: true,
        createTime: new Date(),
        processed: false
      }

      await db.collection('resubscription_needs').add({
        data: resubscriptionRecord
      })

      console.log(`用户${userId}的考试${examId}需要重新订阅，还有${pendingReminders.data.length}个提醒待发送`)
    }
  } catch (error) {
    console.error('检查重新订阅需求失败:', error)
  }
}

// 智能重新请求订阅
async function smartResubscriptionRequest(userId) {
  try {
    // 查询用户的重新订阅需求
    const resubscriptionNeeds = await db.collection('resubscription_needs')
      .where({
        userId: userId,
        needResubscription: true,
        processed: false
      })
      .orderBy('nextReminderDate', 'asc')
      .limit(1)
      .get()

    if (resubscriptionNeeds.data.length > 0) {
      const need = resubscriptionNeeds.data[0]

      // 获取考试信息
      const examResult = await db.collection('exams').doc(need.examId).get()
      if (!examResult.data) {
        return { success: false, error: '考试不存在' }
      }

      const exam = examResult.data

      // 发送重新订阅请求到前端
      // 这里需要通过某种方式通知前端用户需要重新订阅
      // 可以通过小程序的实时消息推送或者在用户下次打开应用时提醒

      // 标记为已处理
      await db.collection('resubscription_needs').doc(need._id).update({
        data: {
          processed: true,
          processTime: new Date()
        }
      })

      return {
        success: true,
        data: {
          examTitle: exam.title,
          pendingRemindersCount: need.pendingRemindersCount,
          nextReminderDate: need.nextReminderDate
        }
      }
    }

    return { success: true, message: '无需重新订阅' }
  } catch (error) {
    console.error('智能重新订阅请求失败:', error)
    return { success: false, error: error.message }
  }
}

// 发送每日小组动态汇总通知
async function sendDailyGroupDigest() {
  try {
    console.log('开始执行每日小组动态汇总通知')

    // 计算昨日时间范围
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    yesterday.setHours(0, 0, 0, 0)

    const today = new Date()
    today.setHours(0, 0, 0, 0)

    console.log('查询时间范围:', {
      from: yesterday.toISOString(),
      to: today.toISOString()
    })

    // 查询昨日所有未通知的小组活动
    const activitiesResult = await db.collection('group_activities')
      .where({
        createTime: db.command.gte(yesterday).and(db.command.lt(today)),
        notified: db.command.neq(true)
      })
      .get()

    console.log(`找到${activitiesResult.data.length}条未通知的活动记录`)

    if (activitiesResult.data.length === 0) {
      return {
        success: true,
        message: '昨日无小组活动，跳过汇总通知',
        data: { totalActivities: 0, groupsNotified: 0 }
      }
    }

    // 按小组ID分组活动数据
    const groupedActivities = {}
    activitiesResult.data.forEach(activity => {
      const groupId = activity.groupId
      if (!groupedActivities[groupId]) {
        groupedActivities[groupId] = []
      }
      groupedActivities[groupId].push(activity)
    })

    console.log(`活动涉及${Object.keys(groupedActivities).length}个小组`)

    let totalNotificationsSent = 0
    let totalNotificationsFailed = 0
    let groupsProcessed = 0

    // 为每个小组发送汇总通知
    for (const [groupId, activities] of Object.entries(groupedActivities)) {
      try {
        const result = await sendGroupDigestNotification(groupId, activities)

        if (result.success) {
          totalNotificationsSent += result.data.successCount
          totalNotificationsFailed += result.data.failCount

          // 标记活动为已通知
          await markActivitiesAsNotified(activities.map(a => a._id))
        }

        groupsProcessed++
      } catch (groupError) {
        console.error(`处理小组${groupId}失败:`, groupError)
        totalNotificationsFailed++
      }
    }

    const summary = {
      totalActivities: activitiesResult.data.length,
      groupsProcessed: groupsProcessed,
      totalNotificationsSent: totalNotificationsSent,
      totalNotificationsFailed: totalNotificationsFailed
    }

    console.log('每日小组动态汇总完成:', summary)

    return {
      success: true,
      message: `汇总通知发送完成: 处理${groupsProcessed}个小组, 成功${totalNotificationsSent}条, 失败${totalNotificationsFailed}条`,
      data: summary
    }
  } catch (error) {
    console.error('发送每日小组动态汇总失败:', error)
    return { success: false, error: error.message }
  }
}
