# 备考搭子功能 - 数据库设计

## 新增数据库集合

### 1. study_groups (搭子小组表)
```javascript
{
  _id: "小组ID",
  examId: "关联的考试ID",
  examName: "考试名称",
  groupName: "小组名称", // 可选，默认为考试名称
  members: [
    {
      userId: "用户ID",
      userInfo: {
        nickName: "用户昵称",
        avatarUrl: "头像URL"
      },
      joinTime: "加入时间",
      role: "member", // 都是member，不区分角色
      isActive: true // 是否活跃状态
    }
  ],
  maxMembers: 3, // 最大成员数
  currentMembers: 2, // 当前成员数
  creatorId: "创建者ID",
  createTime: "创建时间",
  updateTime: "更新时间",
  status: "active", // active/disbanded
  inviteCode: "邀请码", // 用于分享邀请
  settings: {
    allowShare: true, // 是否允许分享计划
    allowInvite: true // 是否允许邀请新成员
  }
}
```

### 2. group_plan_shares (小组计划分享表)
```javascript
{
  _id: "分享记录ID",
  groupId: "小组ID",
  sharerId: "分享者ID",
  sharerInfo: {
    nickName: "分享者昵称",
    avatarUrl: "分享者头像"
  },
  examId: "考试ID",
  planData: {
    title: "计划标题",
    description: "计划描述",
    tasks: [], // 任务列表
    totalTasks: 10,
    estimatedDays: 30
  },
  shareTime: "分享时间",
  copyCount: 2, // 被复制次数
  likes: [
    {
      userId: "点赞用户ID",
      likeTime: "点赞时间"
    }
  ],
  status: "active" // active/deleted
}
```

### 3. group_activities (小组动态表)
```javascript
{
  _id: "动态ID",
  groupId: "小组ID",
  userId: "用户ID",
  userInfo: {
    nickName: "用户昵称",
    avatarUrl: "用户头像"
  },
  activityType: "task_complete", // task_complete/plan_share/join_group/like_plan
  activityData: {
    taskTitle: "任务标题", // 如果是任务完成
    planTitle: "计划标题", // 如果是计划分享
    targetUserId: "目标用户ID" // 如果是点赞等
  },
  createTime: "创建时间",
  isRead: false // 是否已读
}
```

### 4. group_invitations (小组邀请表)
```javascript
{
  _id: "邀请ID",
  groupId: "小组ID",
  inviterId: "邀请者ID",
  inviterInfo: {
    nickName: "邀请者昵称",
    avatarUrl: "邀请者头像"
  },
  inviteCode: "邀请码",
  examName: "考试名称",
  createTime: "创建时间",
  expireTime: "过期时间",
  status: "pending", // pending/accepted/expired/cancelled
  usedBy: "使用者ID", // 如果已被使用
  usedTime: "使用时间"
}
```

## 数据关系设计

### 关联关系
- study_groups.examId → exams._id
- study_groups.members.userId → users._id
- group_plan_shares.groupId → study_groups._id
- group_plan_shares.examId → exams._id
- group_activities.groupId → study_groups._id
- group_invitations.groupId → study_groups._id

### 索引设计
```javascript
// study_groups集合索引
db.study_groups.createIndex({ "examId": 1 })
db.study_groups.createIndex({ "members.userId": 1 })
db.study_groups.createIndex({ "inviteCode": 1 })
db.study_groups.createIndex({ "status": 1, "createTime": -1 })

// group_plan_shares集合索引
db.group_plan_shares.createIndex({ "groupId": 1, "shareTime": -1 })
db.group_plan_shares.createIndex({ "sharerId": 1 })

// group_activities集合索引
db.group_activities.createIndex({ "groupId": 1, "createTime": -1 })
db.group_activities.createIndex({ "userId": 1, "createTime": -1 })

// group_invitations集合索引
db.group_invitations.createIndex({ "inviteCode": 1 })
db.group_invitations.createIndex({ "status": 1, "expireTime": 1 })
```

## 权限设计

### 数据访问权限
- 用户只能查看自己加入的小组信息
- 用户只能操作自己创建的分享记录
- 小组成员可以查看小组内的所有动态
- 邀请码有时效性（7天过期）

### 操作权限
- 任何用户都可以创建小组
- 小组成员都可以分享计划
- 小组成员都可以邀请新成员（在未满3人时）
- 用户可以随时退出小组
- 创建者可以解散小组
