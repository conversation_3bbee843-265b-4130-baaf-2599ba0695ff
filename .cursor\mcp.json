{"mcpServers": {"mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "env": {"MCP_DESKTOP_MODE": "true", "MCP_WEB_HOST": "127.0.0.1", "MCP_WEB_PORT": "8767", "MCP_DEBUG": "false"}}, "Context 7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "Sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "./mcp-shrimp-task-manager/data", "TEMPLATES_USE": "zh", "ENABLE_GUI": "false"}}}}