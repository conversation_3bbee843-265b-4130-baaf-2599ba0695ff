/* pages/task-detail/index.wxss */

/* ==================== CSS变量系统 ==================== */
page {
  /* Z-index层级 */
  --van-popup-z-index: 10001;
  --van-action-sheet-z-index: 10001;
  --van-overlay-z-index: 10000;
  --van-dialog-z-index: 10001;
  --van-toast-z-index: 10001;

  /* 颜色系统 - 浅色模式 */
  --primary-color: #1890ff;
  --primary-light: #40a9ff;
  --primary-dark: #096dd9;

  --success-color: #52c41a;
  --success-light: #73d13d;
  --success-dark: #389e0d;

  --warning-color: #fa8c16;
  --warning-light: #ffa940;
  --warning-dark: #d46b08;

  --error-color: #ff4d4f;
  --error-light: #ff7875;
  --error-dark: #cf1322;

  --text-color: #262626;
  --text-secondary: #8c8c8c;
  --text-disabled: #bfbfbf;

  --background-color: #f7f8fa;
  --background-secondary: #ffffff;
  --background-disabled: #f5f5f5;

  --border-color: #d9d9d9;
  --border-light: #f0f0f0;
  --border-dark: #bfbfbf;

  /* 间距系统 */
  --spacing-xs: 8rpx;
  --spacing-sm: 12rpx;
  --spacing-md: 16rpx;
  --spacing-lg: 24rpx;
  --spacing-xl: 32rpx;
  --spacing-xxl: 40rpx;

  /* 字体大小 */
  --font-size-xs: 20rpx;
  --font-size-sm: 22rpx;
  --font-size-md: 24rpx;
  --font-size-lg: 26rpx;
  --font-size-xl: 28rpx;
  --font-size-xxl: 32rpx;
  --font-size-title: 36rpx;

  /* 圆角 */
  --border-radius-sm: 4rpx;
  --border-radius-md: 8rpx;
  --border-radius-lg: 12rpx;
  --border-radius-xl: 16rpx;

  /* 阴影 */
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  --shadow-md: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);

  /* 动画时长 */
  --animation-duration-fast: 0.2s;
  --animation-duration-base: 0.3s;
  --animation-duration-slow: 0.5s;
}

/* 暗黑模式变量覆盖 */
@media (prefers-color-scheme: dark) {
  page {
    /* 颜色系统 - 暗黑模式 */
    --primary-color: #40a9ff;
    --primary-light: #69c0ff;
    --primary-dark: #1890ff;

    --success-color: #73d13d;
    --success-light: #95de64;
    --success-dark: #52c41a;

    --warning-color: #ffa940;
    --warning-light: #ffbb96;
    --warning-dark: #fa8c16;

    --error-color: #ff7875;
    --error-light: #ffa39e;
    --error-dark: #ff4d4f;

    --text-color: #ffffff;
    --text-secondary: #cccccc;
    --text-disabled: #999999;

    --background-color: #1a1a1a;
    --background-secondary: #2d2d2d;
    --background-disabled: #404040;

    --border-color: #404040;
    --border-light: #595959;
    --border-dark: #262626;

    /* 阴影 */
    --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    --shadow-md: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 4rpx 16rpx rgba(0, 0, 0, 0.4);
  }
}

/* ==================== 基础容器样式 ==================== */
.task-detail-container {
  background-color: var(--background-color);
  min-height: 100vh;
  padding-bottom: calc(var(--spacing-xl) + env(safe-area-inset-bottom));
}

/* 详情内容区域 */
.detail-content {
  padding: var(--spacing-xl) var(--spacing-md) var(--spacing-md) var(--spacing-md);
}

/* 详情区域样式 */
.detail-sections {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* ==================== 状态处理样式 ==================== */

/* 加载状态 */
.loading-container {
  padding: 40rpx 32rpx;
  min-height: 80vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.skeleton-content {
  margin-bottom: 60rpx;
}

.skeleton-card {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.skeleton-title {
  width: 60%;
  height: 40rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
}

.skeleton-tags {
  display: flex;
  gap: 12rpx;
}

.skeleton-tag {
  width: 80rpx;
  height: 32rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 16rpx;
}

.skeleton-content-area {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.skeleton-line {
  height: 28rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
}

.skeleton-line.short {
  width: 70%;
}

.skeleton-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}

.skeleton-grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.skeleton-icon {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 50%;
}

.skeleton-text {
  width: 60rpx;
  height: 24rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
}

.skeleton-progress {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.skeleton-progress-bar {
  height: 12rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6rpx;
}

.skeleton-progress-text {
  width: 40%;
  height: 24rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.loading-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 错误状态 */
.error-container {
  padding: 80rpx 32rpx;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-empty {
  width: 100%;
}

.error-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
  justify-content: center;
}

.retry-btn,
.back-btn,
.edit-btn {
  --van-button-small-height: 64rpx !important;
  --van-button-small-font-size: 26rpx !important;
  min-width: 120rpx;
}

/* 空状态 */
.empty-container {
  padding: 80rpx 32rpx;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state {
  width: 100%;
}

.empty-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
  justify-content: center;
}

/* 下拉刷新 */
.scroll-container {
  height: 100vh;
}

/* 任务详情内容容器 */
.task-detail-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* ==================== 状态样式 ==================== */
/* 加载状态样式 */
.loading-container {
  background: #ffffff;
  padding: 60rpx 32rpx;
  border-radius: 12rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

/* 错误状态样式 */
.error-container {
  background: #ffffff;
  padding: 60rpx 32rpx;
  border-radius: 12rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

/* ==================== 统一设计系统 ==================== */
/* 统一卡片样式 */
.detail-card,
.section-card {
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  margin-bottom: 16rpx;
}

.detail-card {
  padding: 24rpx;
}

/* 统一颜色系统 */
:root {
  --primary-color: #1890fa;
  --success-color: #52c41a;
  --warning-color: #fa8c16;
  --danger-color: #ff4d4f;
  --text-color: #333333;
  --text-secondary: #666666;
  --text-disabled: #999999;
  --border-color: #f0f0f0;
  --background-color: #f7f8fa;
}

/* 统一间距系统 */
.spacing-xs { margin: 8rpx; }
.spacing-sm { margin: 16rpx; }
.spacing-md { margin: 24rpx; }
.spacing-lg { margin: 32rpx; }

.padding-xs { padding: 8rpx; }
.padding-sm { padding: 16rpx; }
.padding-md { padding: 24rpx; }
.padding-lg { padding: 32rpx; }

/* ==================== 任务头部样式 ==================== */
.task-header-card {
  /* 继承section-card样式 */
}

/* ==================== 任务概览卡片样式 ==================== */
.overview-card {
  /* 继承section-card样式 */
}

.overview-grid {
  padding: 0;
}

.overview-stat-card {
  padding: 0;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.overview-stat-card:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.overview-stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 16rpx;
  gap: 8rpx;
  height: 100%;
  justify-content: center;
}

.overview-stat-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-bottom: 4rpx;
}

.overview-stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
  line-height: 1.2;
}

.overview-stat-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  text-align: center;
}

.overview-stat-trend {
  margin-top: 4rpx;
}

.overview-trend-tag {
  font-size: 20rpx !important;
  padding: 2rpx 6rpx !important;
  border-radius: 4rpx !important;
}

.task-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 16rpx;
  margin-bottom: 16rpx;
}

.task-tag {
  margin-right: 0 !important;
}

.task-actions {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
}

.action-btn {
  flex: 1;
}

/* ==================== 进度面板样式 ==================== */
.progress-panel {
  /* 继承section-card样式 */
}

.progress-content {
  padding: 16rpx;
}

.main-progress {
  margin-bottom: 24rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.progress-label {
  font-size: 24rpx;
  color: #646566;
}

.progress-value {
  font-size: 24rpx;
  color: #1890fa;
  font-weight: 600;
}

.task-progress {
  border-radius: 8rpx;
}

/* 统计网格 */
.progress-stats {
  margin-top: 16rpx;
}

.stat-item {
  padding: 16rpx 0;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 700;
  color: #1890fa;
}

.stat-label {
  font-size: 20rpx;
  color: #646566;
}

/* ==================== 时间信息样式 ==================== */
.time-section {
  /* 继承section-card样式 */
}

/* ==================== 子任务部分样式 ==================== */
.subtasks-section {
  /* 继承section-card样式 */
}

.subtasks-collapse {
  background-color: white;
  border-radius: 12rpx;
}

.subtask-cell {
  padding: 12rpx 16rpx;
}

.subtask-checkbox {
  margin-right: 12rpx;
}

.subtask-title-wrapper {
  flex: 1;
}

.subtask-title {
  font-size: 26rpx;
  color: #323233;
}

.subtask-title.completed {
  text-decoration: line-through;
  color: #969799;
}

/* ==================== 学习记录分析样式 ==================== */
.records-section {
  /* 继承section-card样式 */
}

/* 学习统计概览 */
.study-stats-overview {
  margin-bottom: 24rpx;
}

.stats-grid {
  padding: 0;
}

.stat-item {
  padding: 0;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 16rpx;
  gap: 8rpx;
}

.stat-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-color);
}

.stat-label {
  font-size: 22rpx;
  color: var(--text-secondary);
}

/* 效率趋势图 */
.efficiency-chart {
  margin: 24rpx 0;
}

.chart-content {
  padding: 16rpx 0;
}

.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 200rpx;
  padding: 0 16rpx;
  gap: 8rpx;
}

.chart-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.bar-fill {
  width: 100%;
  min-height: 8rpx;
  border-radius: 4rpx 4rpx 0 0;
  transition: all 0.3s ease;
}

.bar-label {
  font-size: 20rpx;
  color: var(--text-secondary);
  margin-top: 8rpx;
}

.bar-value {
  font-size: 18rpx;
  color: var(--text-color);
  font-weight: 500;
  position: absolute;
  top: -24rpx;
  white-space: nowrap;
}

/* 时间分布分析 */
.time-distribution {
  margin: 24rpx 0;
}

.distribution-chart {
  padding: 16rpx 0;
}

.time-slot {
  margin-bottom: 16rpx;
}

.slot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.slot-period {
  font-size: 26rpx;
  color: var(--text-color);
  font-weight: 500;
}

.slot-duration {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.slot-bar {
  height: 16rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  overflow: hidden;
}

.slot-fill {
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

/* 学习洞察 */
.study-insights {
  margin: 24rpx 0;
}

.insight-cell {
  padding: 16rpx 0;
}

/* 详细记录 */
.detailed-records {
  margin: 24rpx 0;
}

.records-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
  color: var(--text-color);
}

.toggle-btn {
  --van-button-mini-height: 48rpx !important;
  --van-button-mini-font-size: 20rpx !important;
}

.detailed-collapse {
  margin-top: 16rpx;
}

.record-cell {
  padding: 16rpx 0;
}

/* 操作按钮 */
.records-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 24rpx;
  justify-content: center;
}

.action-btn {
  --van-button-small-height: 64rpx !important;
  --van-button-small-font-size: 26rpx !important;
  flex: 1;
  max-width: 200rpx;
}

/* 空状态 */
.empty-actions {
  text-align: center;
  margin-top: 24rpx;
}

.add-record-btn {
  --van-button-small-height: 72rpx !important;
  --van-button-small-font-size: 28rpx !important;
}

/* ==================== 智能操作面板样式 ==================== */
.actions-panel {
  /* 继承section-card样式 */
}

/* 主要操作区域 */
.primary-actions {
  margin-bottom: 24rpx;
}

.primary-grid {
  padding: 0;
}

.smart-action-item {
  padding: 0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.smart-action-item:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.action-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  gap: 8rpx;
  position: relative;
  height: 100%;
  justify-content: center;
}

.action-icon {
  margin-bottom: 4rpx;
}

.action-text {
  font-size: 24rpx;
  color: var(--text-color);
  font-weight: 500;
  text-align: center;
}

.action-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
}

/* 操作类型样式 */
.smart-action-item.primary {
  background: linear-gradient(135deg, #E6F7FF 0%, #BAE7FF 100%);
  border: 1rpx solid #91D5FF;
}

.smart-action-item.success {
  background: linear-gradient(135deg, #F6FFED 0%, #D9F7BE 100%);
  border: 1rpx solid #B7EB8F;
}

.smart-action-item.warning {
  background: linear-gradient(135deg, #FFF7E6 0%, #FFE7BA 100%);
  border: 1rpx solid #FFD591;
}

.smart-action-item.default {
  background: linear-gradient(135deg, #FAFAFA 0%, #F0F0F0 100%);
  border: 1rpx solid #D9D9D9;
}

/* 次要操作区域 */
.secondary-actions {
  margin-top: 24rpx;
}

.secondary-grid {
  padding: 0;
}

.secondary-action-item {
  padding: 0;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.secondary-action-item:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.secondary-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 8rpx;
  gap: 6rpx;
}

.secondary-icon {
  margin-bottom: 2rpx;
}

.secondary-text {
  font-size: 20rpx;
  color: var(--text-secondary);
  text-align: center;
}

/* 快捷提示 */
.action-tips {
  margin-top: 16rpx;
}

.tips-notice {
  border-radius: 8rpx !important;
  --van-notice-bar-font-size: 24rpx !important;
  --van-notice-bar-padding: 12rpx 16rpx !important;
}

.actions-grid {
  padding: 16rpx;
}

.action-grid-item {
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  border: 1rpx solid #ebedf0;
  background-color: white;
  transition: all 0.3s ease;
}

.action-grid-item:active {
  transform: scale(0.95);
  background-color: #f7f8fa;
}

.action-primary {
  border-color: #1890fa;
  color: #1890fa;
}

.action-success {
  border-color: #52c41a;
  color: #52c41a;
}

.action-default {
  border-color: #d9d9d9;
  color: #646566;
}

.action-info {
  border-color: #1890fa;
  color: #1890fa;
}

/* 完成提示 */
.completed-notice {
  margin-bottom: 16rpx;
  border-radius: 12rpx;
}

/* 更多操作菜单 */
.more-actions-sheet {
  border-radius: 16rpx 16rpx 0 0;
}

/* ==================== 自定义Vant组件样式 ==================== */
.van-card,
.van-panel,
.van-cell-group,
.van-collapse {
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
}

.van-button--primary {
  background-color: #1890fa;
  border-color: #1890fa;
}

.van-button--default {
  background-color: white;
  border-color: #d9d9d9;
  color: #646566;
}

.van-tag--primary {
  background-color: #e6f7ff;
  color: #1890fa;
}

.van-tag--success {
  background-color: #f6ffed;
  color: #52c41a;
}

.van-tag--warning {
  background-color: #fff7e6;
  color: #fa8c16;
}

.van-tag--danger {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.van-tag--default {
  background-color: #fafafa;
  color: #646566;
}

.van-progress {
  background-color: #f2f3f5;
}

.van-grid-item {
  border: none;
}

.van-checkbox__icon--checked {
  background-color: #1890fa;
  border-color: #1890fa;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .task-tags {
    gap: 6rpx;
  }
  
  .task-actions {
    flex-direction: column;
    gap: 8rpx;
  }
  
  .action-btn {
    width: 100%;
  }
}

/* 动画效果 */
.task-header-card,
.progress-panel,
.time-section,
.subtasks-section,
.records-section,
.actions-panel {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 响应式布局和暗黑模式 ==================== */
/* 响应式布局 */
@media screen and (max-width: 750rpx) {
  .detail-content {
    padding: 16rpx 12rpx;
  }

  .detail-card {
    padding: 20rpx;
  }
}

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  .task-detail-container {
    background-color: #1a1a1a;
  }

  .detail-card,
  .loading-container,
  .error-container,
  .van-card,
  .van-panel,
  .van-cell-group,
  .van-collapse {
    background-color: #2d2d2d;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.3);
  }

  .progress-label,
  .stat-label {
    color: #cccccc;
  }

  .subtask-title,
  .checkpoint-title {
    color: #ffffff;
  }

  .overview-stat-value {
    color: #ffffff;
  }

  .overview-stat-label {
    color: #cccccc;
  }

  .checkpoint-item.selected {
    background: linear-gradient(90deg, rgba(24, 144, 255, 0.15) 0%, rgba(24, 144, 255, 0.08) 100%);
    border-left-color: #40a9ff;
  }

  .stat-value,
  .slot-period,
  .bar-value {
    color: #ffffff;
  }

  .stat-label,
  .slot-duration,
  .bar-label {
    color: #cccccc;
  }

  .slot-bar {
    background: #404040;
  }

  .action-text,
  .secondary-text {
    color: #ffffff;
  }

  .smart-action-item.primary {
    background: linear-gradient(135deg, #1f1f1f 0%, #2d2d2d 100%);
    border-color: #40a9ff;
  }

  .smart-action-item.success {
    background: linear-gradient(135deg, #1f2b1f 0%, #2d3d2d 100%);
    border-color: #73d13d;
  }

  .smart-action-item.warning {
    background: linear-gradient(135deg, #2b251f 0%, #3d352d 100%);
    border-color: #ffa940;
  }

  .smart-action-item.default {
    background: linear-gradient(135deg, #1f1f1f 0%, #2d2d2d 100%);
    border-color: #434343;
  }

  .action-grid-item {
    background-color: #2d2d2d;
    border-color: #404040;
  }

  /* 状态处理暗黑模式 */
  .skeleton-card {
    background-color: #2d2d2d;
  }

  .skeleton-title,
  .skeleton-tag,
  .skeleton-line,
  .skeleton-icon,
  .skeleton-text,
  .skeleton-progress-bar,
  .skeleton-progress-text {
    background: linear-gradient(90deg, #404040 25%, #505050 50%, #404040 75%);
    background-size: 200% 100%;
    animation: skeleton-loading-dark 1.5s infinite;
  }

  .loading-text {
    color: #cccccc;
  }

  /* 更多暗黑模式样式 */
  .task-title,
  .task-description,
  .section-title,
  .checkpoint-title,
  .progress-label,
  .summary-text,
  .empty-text,
  .completed-time {
    color: #ffffff;
  }

  .task-description,
  .empty-tip,
  .insight-text {
    color: #cccccc;
  }

  .section-card,
  .task-header-card,
  .overview-card,
  .records-section,
  .actions-panel {
    background-color: #2d2d2d;
    border: 1rpx solid #404040;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);
  }

  .checkpoints-content,
  .checkpoints-summary {
    background-color: #2d2d2d;
  }

  .checkpoints-summary {
    background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
    border-top-color: #404040;
  }

  .checkpoint-item {
    border-bottom-color: #404040;
  }

  .checkpoint-title.completed-text {
    color: #73d13d;
  }

  .progress-badge {
    background: linear-gradient(135deg, #40a9ff, #69c0ff);
  }

  .chart-bars {
    background-color: transparent;
  }

  .distribution-chart {
    background-color: transparent;
  }

  /* 表单元素暗黑模式 */
  .van-cell {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
  }

  .van-cell::after {
    border-color: #404040 !important;
  }

  .van-tag {
    background-color: #404040 !important;
    color: #ffffff !important;
  }

  .van-tag--success {
    background-color: #1f2b1f !important;
    color: #73d13d !important;
  }

  .van-tag--warning {
    background-color: #2b251f !important;
    color: #ffa940 !important;
  }

  .van-tag--danger {
    background-color: #2b1f1f !important;
    color: #ff7875 !important;
  }

  .van-button--default {
    background-color: #404040 !important;
    border-color: #595959 !important;
    color: #ffffff !important;
  }

  .van-button--primary {
    background-color: #1890ff !important;
    border-color: #40a9ff !important;
  }

  .van-button--success {
    background-color: #52c41a !important;
    border-color: #73d13d !important;
  }

  .van-notice-bar {
    background-color: #1f2b1f !important;
    color: #73d13d !important;
  }

  .van-empty {
    color: #cccccc !important;
  }

  .van-loading {
    color: #40a9ff !important;
  }

  /* 进度条暗黑模式 */
  .van-progress {
    background-color: #404040 !important;
  }

  .van-circle {
    color: #ffffff !important;
  }

  /* 复选框暗黑模式 */
  .van-checkbox {
    color: #ffffff !important;
  }

  .van-checkbox__icon--checked {
    background-color: #52c41a !important;
    border-color: #52c41a !important;
  }

  /* 图标暗黑模式 */
  .van-icon {
    color: #cccccc !important;
  }

  .status-icon .van-icon {
    color: #999999 !important;
  }

  /* 分割线暗黑模式 */
  .van-divider {
    color: #cccccc !important;
    border-color: #404040 !important;
  }

  .van-divider::before,
  .van-divider::after {
    border-color: #404040 !important;
  }
}

@keyframes skeleton-loading-dark {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ==================== 检查点视觉优化样式 ==================== */
.checkpoints-section {
  /* 继承section-card样式 */
  overflow: hidden;
}

/* 批量操作区域 */
.batch-operations {
  display: flex;
  gap: 8rpx;
  margin-top: 16rpx;
  flex-wrap: wrap;
}

.batch-btn {
  --van-button-mini-height: 56rpx !important;
  --van-button-mini-font-size: 22rpx !important;
  --van-button-mini-padding: 0 16rpx !important;
}

/* 圆形进度条 */
.checkpoint-circle {
  margin-right: 16rpx;
  --van-circle-text-font-size: 20rpx !important;
  --van-circle-text-font-weight: 600 !important;
}

.checkpoints-collapse {
  --van-collapse-item-content-padding: 0;
}

.checkpoints-collapse-item {
  --van-cell-padding: 0;
}

.checkpoints-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1rpx solid #f0f0f0;
}

.header-info {
  flex: 1;
  min-width: 0;
}

.title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.progress-badge {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  min-width: 80rpx;
  text-align: center;
}

.progress-text {
  font-size: 22rpx;
  color: #ffffff;
  font-weight: 500;
}

.progress-visual {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.checkpoint-progress {
  flex: 1;
  --van-progress-pivot-background-color: transparent;
}

.progress-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  min-width: 80rpx;
  text-align: right;
}

.status-icon {
  margin-left: 16rpx;
  padding: 8rpx;
}

.checkpoints-content {
  background-color: #ffffff;
}

.checkpoints-list {
  padding: 0 24rpx;
}

.checkpoint-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  gap: 16rpx;
  transition: all 0.3s ease;
  position: relative;
}

.checkpoint-item.selected {
  background: linear-gradient(90deg, rgba(24, 144, 255, 0.08) 0%, rgba(24, 144, 255, 0.04) 100%);
  border-left: 4rpx solid #1890ff;
  padding-left: 16rpx;
}

/* 选择器样式 */
.checkpoint-selector {
  flex-shrink: 0;
  padding: 8rpx;
  margin-right: 8rpx;
}

.selector-checkbox {
  --van-checkbox-size: 32rpx;
  --van-checkbox-checked-icon-color: #1890ff;
  --van-checkbox-border-color: #d9d9d9;
}

.checkpoint-item:last-child {
  border-bottom: none;
}

.checkpoint-item.completed {
  background: linear-gradient(90deg, rgba(82, 196, 26, 0.05) 0%, rgba(82, 196, 26, 0.02) 100%);
}

.checkpoint-item.pending:active {
  background-color: #f8f9fa;
}

.checkpoint-indicator {
  flex-shrink: 0;
  position: relative;
}

.completed-icon {
  animation: checkpointComplete 0.6s ease-out;
}

@keyframes checkpointComplete {
  0% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.pending-icon {
  transition: color 0.3s ease;
}

.checkpoint-content {
  flex: 1;
  min-width: 0;
}

.checkpoint-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4rpx;
}

.checkpoint-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.checkpoint-title.completed-text {
  color: #52c41a;
  text-decoration: line-through;
  opacity: 0.8;
}

.checkpoint-status {
  margin-left: 12rpx;
  flex-shrink: 0;
}

.checkpoint-meta {
  display: flex;
  align-items: center;
  gap: 6rpx;
  margin-top: 8rpx;
}

.completed-time {
  font-size: 22rpx;
  color: #999;
}

.checkpoint-action {
  flex-shrink: 0;
}

.checkpoint-checkbox {
  --van-checkbox-size: 40rpx;
  --van-checkbox-checked-icon-color: #52c41a;
  --van-checkbox-border-color: #d9d9d9;
}

.checkpoints-summary {
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-top: 1rpx solid #f0f0f0;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-text {
  font-size: 24rpx;
  color: #666;
}

.empty-checkpoints {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 24rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin: 16rpx 0 8rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #999;
}

/* ==================== 响应式布局优化 ==================== */

/* 小屏设备优化 (iPhone SE, 小屏Android) */
@media screen and (max-width: 375px) {
  .task-detail-container {
    padding-bottom: calc(60rpx + env(safe-area-inset-bottom));
  }

  .detail-content {
    padding: 24rpx 12rpx 12rpx 12rpx;
  }

  .detail-sections {
    gap: 12rpx;
  }

  /* 卡片间距优化 */
  .section-card {
    margin-bottom: 12rpx;
    padding: 24rpx 20rpx;
  }

  /* 字体大小调整 */
  .task-title {
    font-size: 32rpx;
    line-height: 1.4;
  }

  .task-description {
    font-size: 26rpx;
    line-height: 1.5;
  }

  /* 概览统计优化 */
  .overview-grid {
    gap: 8rpx;
  }

  .overview-stat-content {
    padding: 16rpx 12rpx;
    gap: 6rpx;
  }

  .overview-stat-value {
    font-size: 28rpx;
  }

  .overview-stat-label {
    font-size: 20rpx;
  }

  /* 检查点优化 */
  .checkpoints-list {
    padding: 0 16rpx;
  }

  .checkpoint-item {
    padding: 16rpx 0;
    gap: 12rpx;
  }

  .checkpoint-title {
    font-size: 26rpx;
  }

  /* 操作面板优化 */
  .primary-grid {
    gap: 8rpx;
  }

  .action-content {
    padding: 20rpx 12rpx;
    gap: 6rpx;
  }

  .action-text {
    font-size: 22rpx;
  }

  .secondary-grid {
    gap: 6rpx;
  }

  .secondary-content {
    padding: 12rpx 6rpx;
    gap: 4rpx;
  }

  .secondary-text {
    font-size: 18rpx;
  }
}

/* 中等屏幕优化 (iPhone 6/7/8, 标准Android) */
@media screen and (min-width: 376px) and (max-width: 414px) {
  .detail-content {
    padding: 28rpx 14rpx 14rpx 14rpx;
  }

  .section-card {
    padding: 28rpx 24rpx;
  }

  .overview-stat-content {
    padding: 18rpx 14rpx;
  }

  .action-content {
    padding: 22rpx 14rpx;
  }
}

/* 大屏设备优化 (iPhone Plus, 大屏Android, 平板) */
@media screen and (min-width: 768px) {
  .task-detail-container {
    max-width: 1200rpx;
    margin: 0 auto;
    padding: 0 40rpx calc(60rpx + env(safe-area-inset-bottom)) 40rpx;
  }

  .detail-content {
    padding: 40rpx 32rpx 24rpx 32rpx;
  }

  .detail-sections {
    gap: 24rpx;
  }

  /* 两列布局 */
  .overview-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;
  }

  .primary-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 16rpx;
  }

  .secondary-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 12rpx;
  }

  /* 字体大小增大 */
  .task-title {
    font-size: 40rpx;
  }

  .task-description {
    font-size: 30rpx;
  }

  .overview-stat-value {
    font-size: 36rpx;
  }

  .overview-stat-label {
    font-size: 26rpx;
  }

  .checkpoint-title {
    font-size: 30rpx;
  }

  .action-text {
    font-size: 26rpx;
  }
}

/* 横屏模式优化 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .loading-container {
    padding: 20rpx 32rpx;
    min-height: 60vh;
  }

  .error-container,
  .empty-container {
    padding: 40rpx 32rpx;
    min-height: 40vh;
  }

  .skeleton-card {
    padding: 24rpx;
    margin-bottom: 16rpx;
  }

  .section-card {
    padding: 24rpx;
    margin-bottom: 16rpx;
  }
}

/* 安全区域处理 */
.task-detail-container {
  padding-left: calc(16rpx + env(safe-area-inset-left));
  padding-right: calc(16rpx + env(safe-area-inset-right));
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}

.scroll-container {
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

/* ==================== 性能优化 ==================== */

/* GPU加速优化 */
.section-card,
.skeleton-card,
.smart-action-item,
.secondary-action-item,
.checkpoint-item {
  transform: translateZ(0);
  will-change: transform;
}

/* 动画性能优化 */
.smart-action-item:active,
.secondary-action-item:active,
.checkpoint-item:active {
  transform: translateZ(0) scale(0.95);
}

/* 减少重绘重排 */
.overview-stat-content,
.action-content,
.secondary-content {
  contain: layout style;
}

.skeleton-title,
.skeleton-tag,
.skeleton-line,
.skeleton-icon,
.skeleton-text,
.skeleton-progress-bar,
.skeleton-progress-text {
  contain: strict;
}

/* 滚动性能优化 */
.scroll-container {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 字体渲染优化 */
.task-title,
.section-title,
.checkpoint-title,
.action-text {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 图片和图标优化 */
.van-icon {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* 减少布局抖动 */
.overview-grid,
.primary-grid,
.secondary-grid {
  contain: layout;
}

/* 内存优化 */
.skeleton-content {
  contain: layout style paint;
}

/* 避免不必要的重绘 */
.loading-container,
.error-container,
.empty-container {
  contain: layout style;
}

/* 优化渲染层 */
.task-detail-container {
  isolation: isolate;
}

/* 减少复合层创建 */
.checkpoint-item.selected {
  contain: layout style;
}

/* 优化动画帧率 */
@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

@keyframes skeleton-loading-dark {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* 预加载关键资源 */
.section-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  pointer-events: none;
  z-index: -1;
}

/* ==================== 开发测试工具样式 ==================== */
.dev-tools {
  margin-top: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--background-secondary);
  border-radius: var(--border-radius-lg);
  border: 2rpx dashed var(--warning-color);
}

.dev-tools .van-cell {
  background-color: transparent !important;
}

.dev-tools .van-cell__title {
  color: var(--warning-color) !important;
  font-weight: 500;
}

.dev-tools .van-icon {
  color: var(--warning-color) !important;
}

/* 暗黑模式下的开发工具 */
@media (prefers-color-scheme: dark) {
  .dev-tools {
    background: #2d2d2d;
    border-color: #ffa940;
  }

  .dev-tools .van-cell__title {
    color: #ffa940 !important;
  }

  .dev-tools .van-icon {
    color: #ffa940 !important;
  }
}

/* ==================== 进一步性能优化 ==================== */

/* 关键渲染路径优化 */
.task-detail-container {
  /* 移除不兼容的content-visibility */
  min-height: 100vh;
}

/* 非关键内容延迟渲染 */
.secondary-actions,
.detailed-records,
.dev-tools {
  /* 移除不兼容的content-visibility */
  min-height: 200rpx;
}

/* 图片和媒体优化 */
image {
  /* 移除不兼容的content-visibility */
  width: 100rpx;
  height: 100rpx;
}

/* 减少布局抖动 */
.skeleton-card,
.section-card {
  min-height: 120rpx;
}

.overview-grid {
  min-height: 160rpx;
}

.primary-grid {
  min-height: 180rpx;
}

/* 优化滚动性能 */
.scroll-container {
  overscroll-behavior: contain;
  scroll-snap-type: y proximity;
}

.section-card {
  scroll-snap-align: start;
}

/* 减少重排重绘 */
.checkpoint-item,
.smart-action-item,
.secondary-action-item {
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 字体加载优化 */
@font-face {
  font-family: 'system';
  src: local('PingFang SC'), local('Helvetica Neue'), local('Arial');
  font-display: swap;
}

/* 关键CSS内联优化 */
.task-title,
.section-title {
  font-family: 'system', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 动画性能优化 */
@media (prefers-reduced-motion: reduce) {
  .smart-action-item,
  .secondary-action-item,
  .checkpoint-item,
  .skeleton-title,
  .skeleton-tag,
  .skeleton-line,
  .skeleton-icon,
  .skeleton-text,
  .skeleton-progress-bar,
  .skeleton-progress-text {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 内存使用优化 */
.loading-container,
.error-container,
.empty-container {
  contain: layout style paint size;
}

/* 避免不必要的层创建 */
.overview-stat-content,
.action-content,
.secondary-content {
  transform: none;
  will-change: auto;
}

/* 优化复合层 */
.smart-action-item:hover,
.secondary-action-item:hover {
  transform: translateZ(0) scale(1.02);
}

/* 减少绘制区域 */
.checkpoint-item.selected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: var(--primary-color);
  border-radius: 2rpx;
}

/* 优化文本渲染 */
.task-title,
.section-title,
.checkpoint-title {
  text-rendering: optimizeSpeed;
  font-kerning: none;
}

/* 减少样式计算 */
.van-grid-item {
  contain: layout style;
}

.van-cell {
  contain: layout style;
}
