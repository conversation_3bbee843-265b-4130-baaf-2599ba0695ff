/* pages/pomodoro-complete/index.wxss */

/* 完成庆祝 */
.celebration-container {
  text-align: center;
  padding: 60rpx 32rpx 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #FFFFFF;
  margin-bottom: 24rpx;
}

.celebration-icon {
  margin-bottom: 24rpx;
}

.celebration-emoji {
  font-size: 120rpx;
  animation: bounce 1s ease-in-out;
}

.celebration-title {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  display: block;
}

.celebration-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 本次统计 */
.session-stats-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.stat-card {
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
}

.stat-icon {
  font-size: 32rpx;
  margin-bottom: 12rpx;
  display: block;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
  display: block;
}

/* 效率评价 */
.efficiency-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.efficiency-rating {
  text-align: center;
  margin-bottom: 24rpx;
}

.rating-stars {
  display: flex;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.star {
  font-size: 48rpx;
  color: #E9ECEF;
  transition: all 0.3s ease;
}

.star.active {
  color: #FFD700;
  transform: scale(1.1);
}

.rating-text {
  font-size: 26rpx;
  color: #666666;
}

.efficiency-feedback {
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
}

.feedback-title {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.feedback-text {
  font-size: 26rpx;
  color: #52C41A;
  font-weight: 600;
}

/* 复习笔记 */
.notes-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.notes-input-container {
  position: relative;
}

.notes-input {
  width: 100%;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
  min-height: 120rpx;
}

.notes-input:focus {
  border-color: #1890FF;
  background-color: #FFFFFF;
}

.notes-counter {
  position: absolute;
  bottom: 8rpx;
  right: 12rpx;
  font-size: 20rpx;
  color: #999999;
}

/* 今日成就 */
.achievements-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.achievements-grid {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}

.achievement-item {
  flex: 1;
  text-align: center;
}

.achievement-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin: 0 auto 12rpx;
  transition: all 0.3s ease;
}

.achievement-icon.unlocked {
  background-color: #F6FFED;
  border: 2rpx solid #52C41A;
}

.achievement-icon.locked {
  background-color: #F5F5F5;
  border: 2rpx solid #E9ECEF;
  opacity: 0.6;
}

.achievement-name {
  font-size: 22rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
}

.achievement-progress {
  font-size: 20rpx;
  color: #666666;
  display: block;
}

/* 休息建议 */
.break-suggestion-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.suggestion-card {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background-color: #E6F7FF;
  border-radius: 12rpx;
  padding: 20rpx;
}

.suggestion-icon {
  font-size: 32rpx;
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-size: 26rpx;
  color: #333333;
  font-weight: 600;
  margin-bottom: 4rpx;
  display: block;
}

.suggestion-description {
  font-size: 22rpx;
  color: #666666;
  display: block;
}

.suggestion-duration {
  flex-shrink: 0;
}

.duration-text {
  font-size: 24rpx;
  color: #1890FF;
  font-weight: 600;
}

/* 下一步行动 */
.next-actions-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background-color: #F8F9FA;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  transition: all 0.3s ease;
}

.action-btn:active {
  background-color: #E9ECEF;
  transform: scale(0.98);
}

.continue-btn {
  background-color: #F6FFED;
  color: #52C41A;
}

.break-btn {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.task-btn {
  background-color: #E6F7FF;
  color: #1890FF;
}

.stats-btn {
  background-color: #F9F0FF;
  color: #722ED1;
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 分享成果 */
.share-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.share-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.share-info {
  flex: 1;
}

.share-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.share-description {
  font-size: 24rpx;
  color: #666666;
  display: block;
}

.share-btn {
  background-color: #52C41A;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
}

.share-btn:active {
  background-color: #389E0D;
}

.share-icon {
  font-size: 24rpx;
}

.share-text {
  font-size: 26rpx;
}

/* 底部操作 */
.bottom-actions {
  padding: 32rpx;
  background-color: #FFFFFF;
  position: sticky;
  bottom: 0;
  border-top: 1rpx solid #F0F0F0;
}

.finish-btn {
  width: 100%;
  background-color: #1890FF;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.finish-btn:active {
  background-color: #096DD9;
}

/* 休息计时器弹窗 */
.break-timer-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.modal-body {
  padding: 32rpx;
}

.break-timer {
  text-align: center;
}

.timer-circle {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
}

.timer-progress {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.timer-inner {
  width: 160rpx;
  height: 160rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.timer-time {
  font-size: 48rpx;
  font-weight: 700;
  color: #52C41A;
  margin-bottom: 8rpx;
}

.timer-label {
  font-size: 20rpx;
  color: #666666;
}

.timer-controls {
  display: flex;
  justify-content: center;
  gap: 16rpx;
}

.timer-btn {
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
}

.timer-btn:active {
  background-color: #E6E6E6;
}

/* 动画效果 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
