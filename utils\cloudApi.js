// 云开发API工具类
class CloudApi {
  // 任务管理相关API
  static async getTasks(filter = {}, limit = 20, skip = 0) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'getTasks',
          data: { filter, limit, skip }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取任务列表失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getTaskById(taskId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'getTaskById',
          data: { taskId }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取任务详情失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async addTask(taskData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'addTask',
          data: taskData
        }
      })
      return result.result
    } catch (error) {
      console.error('添加任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async updateTask(taskId, updates) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'updateTask',
          data: { taskId, updates }
        }
      })
      return result.result
    } catch (error) {
      console.error('更新任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async deleteTask(taskId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'deleteTask',
          data: { taskId }
        }
      })
      return result.result
    } catch (error) {
      console.error('删除任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async completeTask(taskId, completed = true) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'completeTask',
          data: { taskId, completed }
        }
      })
      return result.result
    } catch (error) {
      console.error('完成任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async updateTaskSubtasks(taskId, updates) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'updateTaskSubtasks',
          data: { taskId, updates }
        }
      })
      return result.result
    } catch (error) {
      console.error('批量更新子任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getTaskStats(dateRange = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'getTaskStats',
          data: { dateRange }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取任务统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 批量获取多个考试的任务
  static async getBatchTasks(examIds, includeCompleted = true, todayOnly = false) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'getBatchTasks',
          data: { examIds, includeCompleted, todayOnly }
        }
      })
      return result.result
    } catch (error) {
      console.error('批量获取任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 修复任务用户ID问题
  static async fixTaskUserIds() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'fixTaskUserIds',
          data: {}
        }
      })
      return result.result
    } catch (error) {
      console.error('修复任务用户ID失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 考试管理相关API
  static async getExams(filter = {}, limit = 20, skip = 0) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'getExams',
          data: { filter, limit, skip }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取考试列表失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getExamsWithAnalytics(includeProgress = true, includeTasks = false) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'getExamsWithAnalytics',
          data: { includeProgress, includeTasks }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取考试分析数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getExamById(examId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'getExamById',
          data: { examId }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取考试详情失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async addExam(examData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'addExam',
          data: examData
        }
      })
      return result.result
    } catch (error) {
      console.error('添加考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async updateExam(examId, updates) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'updateExam',
          data: { examId, updates }
        }
      })
      return result.result
    } catch (error) {
      console.error('更新考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async deleteExam(examId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'deleteExam',
          data: { examId }
        }
      })
      return result.result
    } catch (error) {
      console.error('删除考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getUpcomingExams(days = 7, limit = 10) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'getUpcomingExams',
          data: { days, limit }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取即将到来的考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getExamStats(dateRange = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'getExamStats',
          data: { dateRange }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取考试统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 复习管理相关API
  static async startStudySession(sessionData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'startStudySession',
          data: sessionData
        }
      })
      return result.result
    } catch (error) {
      console.error('开始复习会话失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async endStudySession(sessionId, endTime = null, notes = '') {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'endStudySession',
          data: { sessionId, endTime, notes }
        }
      })
      return result.result
    } catch (error) {
      console.error('结束复习会话失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getStudyStats(dateRange = null, groupBy = 'day') {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'getStudyStats',
          data: { dateRange, groupBy }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取复习统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 统计数据管理API
  static async getUserStats(timeRange = 'week') {
    try {
      const result = await wx.cloud.callFunction({
        name: 'statsManager',
        data: {
          action: 'getUserStats',
          data: { timeRange }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取用户统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async updateDailyStats(statsData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'statsManager',
        data: {
          action: 'updateDailyStats',
          data: statsData
        }
      })
      return result.result
    } catch (error) {
      console.error('更新每日统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getDailyStats(date = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'statsManager',
        data: {
          action: 'getDailyStats',
          data: { date }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取每日统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 成就系统API
  static async getUserAchievements(category = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'achievementManager',
        data: {
          action: 'getUserAchievements',
          data: { category }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取用户成就失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async checkAchievements(triggerType, value) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'achievementManager',
        data: {
          action: 'checkAchievements',
          data: { triggerType, value }
        }
      })
      return result.result
    } catch (error) {
      console.error('检查成就失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getAchievementStats() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'achievementManager',
        data: {
          action: 'getAchievementStats',
          data: {}
        }
      })
      return result.result
    } catch (error) {
      console.error('获取成就统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async addPomodoroSession(pomodoroData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'addPomodoroSession',
          data: pomodoroData
        }
      })
      return result.result
    } catch (error) {
      console.error('添加番茄钟会话失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getPomodoroStats(dateRange = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'getPomodoroStats',
          data: { dateRange }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取番茄钟统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 用户登录
  static async login() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'login'
      })
      return result.result
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, error: error.message }
    }
  }
  // 备考搭子API
  static async createStudyGroup(examId, examName, groupName) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'createGroup',
          data: { examId, examName, groupName }
        }
      })
      return result.result
    } catch (error) {
      console.error('创建搭子小组失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async verifyInviteCode(inviteCode) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'verifyInviteCode',
          data: { inviteCode }
        }
      })
      return result.result
    } catch (error) {
      console.error('验证邀请码失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async joinStudyGroup(inviteCode) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'joinGroup',
          data: { inviteCode }
        }
      })
      return result.result
    } catch (error) {
      console.error('加入搭子小组失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async leaveStudyGroup(groupId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'leaveGroup',
          data: { groupId }
        }
      })
      return result.result
    } catch (error) {
      console.error('退出搭子小组失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getMyStudyGroups() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'getMyGroups',
          data: {}
        }
      })
      return result.result
    } catch (error) {
      console.error('获取我的搭子小组失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getStudyGroupDetail(groupId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'getGroupDetail',
          data: { groupId }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取搭子小组详情失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getStudyGroupByExam(examId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'getGroupByExam',
          data: { examId }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取考试搭子小组失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 批量获取多个考试的搭子组信息
  static async getBatchStudyGroupsByExam(examIds) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'getBatchGroupsByExam',
          data: { examIds }
        }
      })
      return result.result
    } catch (error) {
      console.error('批量获取搭子组失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async shareGroupPlan(groupId, planData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'sharePlan',
          data: { groupId, planData }
        }
      })
      return result.result
    } catch (error) {
      console.error('分享小组计划失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async copyGroupPlan(shareId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'copyPlan',
          data: { shareId }
        }
      })
      return result.result
    } catch (error) {
      console.error('复制小组计划失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async likeGroupPlan(shareId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'likePlan',
          data: { shareId }
        }
      })
      return result.result
    } catch (error) {
      console.error('点赞小组计划失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async fixGroupData() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'fixGroupData',
          data: {}
        }
      })
      return result.result
    } catch (error) {
      console.error('修复小组数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 反馈管理相关API
  static async submitFeedback(feedbackData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'feedbackManager',
        data: {
          action: 'submitFeedback',
          data: feedbackData
        }
      })
      return result.result
    } catch (error) {
      console.error('提交反馈失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getFeedbackHistory(page = 1, limit = 10) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'feedbackManager',
        data: {
          action: 'getFeedbackHistory',
          data: { page, limit }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取反馈历史失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async updateFeedbackStatus(feedbackId, status, reply = '') {
    try {
      const result = await wx.cloud.callFunction({
        name: 'feedbackManager',
        data: {
          action: 'updateFeedbackStatus',
          data: { feedbackId, status, reply }
        }
      })
      return result.result
    } catch (error) {
      console.error('更新反馈状态失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getAllFeedbacks(page = 1, limit = 20) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'feedbackManager',
        data: {
          action: 'getAllFeedbacks',
          data: { page, limit }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取所有反馈失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 通知管理相关API
  static async requestSubscription(templateIds, subscriptionData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'notificationManager',
        data: {
          action: 'requestSubscription',
          data: { templateIds, subscriptionData }
        }
      })
      return result.result
    } catch (error) {
      console.error('请求订阅失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async sendTaskReminder(taskData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'notificationManager',
        data: {
          action: 'sendTaskReminder',
          data: taskData
        }
      })
      return result.result
    } catch (error) {
      console.error('发送任务提醒失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async sendExamReminder(examData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'notificationManager',
        data: {
          action: 'sendExamReminder',
          data: examData
        }
      })
      return result.result
    } catch (error) {
      console.error('发送考试提醒失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getUserSubscriptions() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'notificationManager',
        data: {
          action: 'getUserSubscriptions'
        }
      })
      return result.result
    } catch (error) {
      console.error('获取用户订阅状态失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async sendDailyGroupDigest() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'notificationManager',
        data: {
          action: 'sendDailyGroupDigest'
        }
      })
      return result.result
    } catch (error) {
      console.error('发送每日小组动态汇总失败:', error)
      return { success: false, error: error.message }
    }
  }


}

module.exports = CloudApi
