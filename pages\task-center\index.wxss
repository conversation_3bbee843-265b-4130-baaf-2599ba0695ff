/* pages/task-center/index.wxss */

/* 基础容器 */
.container {
  padding: 16rpx 0;
  padding-bottom: 140rpx; /* 为底部导航留出空间 */
  background-color: #f7f8fa;
  min-height: 100vh;
}

/* 头部区域 */
.header-section {
  margin-bottom: 20rpx;
  padding: 0 16rpx;
}

/* 顶部统计区域 */
.stats-section {
  margin-bottom: 16rpx;
  padding: 0 16rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}

.stat-card {
  flex: 1;
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
  overflow: hidden;
}

.stat-content {
  text-align: center;
  padding: 24rpx 16rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1890FF;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #646566;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20rpx;
}

/* 筛选模式指示器 */
.filter-mode-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  margin-bottom: 12rpx;
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 20rpx;
  border: 1rpx solid rgba(24, 144, 255, 0.2);
}

.mode-text {
  font-size: 24rpx;
  color: #1890FF;
  font-weight: 500;
  margin-left: 8rpx;
}

/* 筛选状态提示 */
.filter-status-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 12rpx;
  margin-top: 8rpx;
  background-color: rgba(82, 196, 26, 0.1);
  border-radius: 16rpx;
}

.tip-text {
  font-size: 22rpx;
  color: #52C41A;
  font-weight: 500;
}

/* 自定义van-tabs样式 - 与考试中心保持一致 */
.van-tabs {
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
}

/* 特殊标签样式 */
.back-button-tab {
  position: relative;
}

.back-button-tab .van-tab__text {
  color: #666666 !important;
  font-weight: 500 !important;
}

.exam-filter-tab {
  position: relative;
}

.exam-filter-tab .van-tab__text {
  color: #1890FF !important;
  font-weight: 600 !important;
}

/* 考试控制区域样式 */
.exam-controls-section {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  background: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  padding: 16rpx;
  position: relative; /* 为绝对定位做准备 */
}

/* 下拉菜单样式优化 */
.exam-controls-section .van-dropdown-menu {
  flex: 1;
  background: transparent;
  box-shadow: none;
  margin-right: 20rpx; /* 与按钮保持距离 */
  /* 移除固定的max-width限制，让短标题正常显示 */
}

.exam-controls-section .van-dropdown-menu__bar {
  background: transparent;
  box-shadow: none;
  border: none;
  /* 让下拉菜单栏可以自适应内容，但不超过容器 */
  max-width: calc(100% - 160rpx) !important;
  overflow: hidden !important;
}

/* 下拉菜单项标题样式 - 智能截断 */
.exam-controls-section .van-dropdown-item__title {
  /* 不设置固定的max-width，让CSS自然处理溢出 */
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  display: block !important;
}

/* 添加任务快捷按钮样式 */
.add-task-quick-btn {
  font-size: 26rpx !important;
  padding: 0 24rpx !important;
  height: 64rpx !important;
  line-height: 64rpx !important;
  border-radius: 32rpx !important;
  min-width: 140rpx !important;
  min-height: 44rpx !important;
  box-shadow: 0 2rpx 4rpx rgba(24, 144, 255, 0.2) !important;
  position: absolute !important; /* 绝对定位 */
  right: 16rpx !important; /* 距离右边16rpx */
  top: 50% !important; /* 垂直居中 */
  transform: translateY(-50%) !important; /* 垂直居中 */
}

/* 标签图标样式 */
.tab-icon {
  margin-right: 4rpx;
  vertical-align: middle;
}



/* 任务区域 */
.tasks-section {
  margin-bottom: 24rpx;
  padding: 0 16rpx;
}

.task-group {
  margin-bottom: 24rpx;
}

.group-divider {
  margin: 16rpx 0;
  background-color: transparent;
}

.group-date {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 16rpx;
}

.group-count {
  font-size: 22rpx;
  color: #999999;
}

/* 任务列表 */
.task-cell-group {
  border-radius: 16rpx;
  overflow: visible; /* 允许检查点展开区域显示 */
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.task-cell {
  position: relative;
  padding: 16rpx;
}

.task-status-wrapper {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.task-status-indicator {
  width: 6rpx;
  height: 40rpx;
  border-radius: 3rpx;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.status-pending {
  background-color: #FA8C16;
}

.status-completed {
  background-color: #52C41A;
}

.status-overdue {
  background-color: #FF4D4F;
}

.task-checkbox {
  margin-left: 16rpx;
}

.task-title-wrapper {
  flex: 1;
  min-width: 0; /* 防止flex子项溢出 */
}

/* 标题和优先级行 */
.task-title-line {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 6rpx;
}

.task-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  line-height: 1.4;
  /* 移除flex: 1，让标题自然宽度，优先级紧跟其后 */
  /* 文本溢出处理 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 120rpx); /* 为优先级标签预留空间 */
}

.task-title.completed {
  text-decoration: line-through;
  color: #999999;
}

.task-priority-tag {
  font-size: 20rpx !important;
  flex-shrink: 0;
}

/* 考试名称行 */
.task-exam-name {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.3;
  margin-bottom: 8rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 科目和截止时间行 */
.task-meta-line {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.task-subject-tag {
  font-size: 20rpx !important;
  flex-shrink: 0;
}

.task-due {
  font-size: 22rpx;
  color: #666666;
  flex-shrink: 0;
  white-space: nowrap;
}

/* 检查点进度预览行 */
.checkpoint-preview-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8rpx;
  padding: 12rpx 8rpx;
  background-color: rgba(24, 144, 255, 0.02);
  border-radius: 8rpx;
  border: 1rpx solid rgba(24, 144, 255, 0.1);
  transition: all 0.2s ease;
}

.checkpoint-preview-line:active {
  background-color: rgba(24, 144, 255, 0.1);
  border-color: rgba(24, 144, 255, 0.3);
  transform: scale(0.98);
}

.checkpoint-progress-wrapper {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
  transition: all 0.3s ease;
}

.checkpoint-progress-bar {
  flex: 1;
  height: 4rpx;
  background-color: #f0f0f0;
  border-radius: 2rpx;
  overflow: hidden;
  max-width: 120rpx;
  position: relative;
}

.checkpoint-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0.3) 0%, transparent 50%, rgba(255,255,255,0.3) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.checkpoint-preview-line:active .checkpoint-progress-bar::after {
  transform: translateX(100%);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 2rpx;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.progress-fill::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%);
  transform: translateX(-100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.checkpoint-stats {
  font-size: 20rpx;
  color: #666666;
  white-space: nowrap;
  transition: color 0.3s ease;
}

.checkpoint-preview-line:active .checkpoint-stats {
  color: #1890ff;
}

.expand-icon {
  color: #999999;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.checkpoint-preview-line:active .expand-icon {
  color: #1890ff;
  transform: translateX(4rpx) scale(1.1);
}

/* 添加检查点引导样式 */
.add-checkpoint-guide {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 8rpx;
  margin-top: 8rpx;
  border-radius: 8rpx;
  border: 1rpx dashed rgba(24, 144, 255, 0.3);
  background-color: rgba(24, 144, 255, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-checkpoint-guide:active {
  background-color: rgba(24, 144, 255, 0.1);
  border-color: rgba(24, 144, 255, 0.5);
  transform: scale(0.98);
}

.guide-text {
  flex: 1;
  font-size: 24rpx;
  color: #1890ff;
  font-weight: 500;
}

.guide-arrow {
  opacity: 0.6;
  transition: all 0.3s ease;
}

.add-checkpoint-guide:active .guide-arrow {
  opacity: 1;
  transform: translateX(4rpx);
}

.checkpoint-quick-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.quick-complete-btn {
  font-size: 18rpx !important;
  padding: 0 8rpx !important;
  height: 40rpx !important;
  border-radius: 20rpx !important;
  min-width: 40rpx !important;
  font-weight: 600 !important;
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.3) !important;
  transition: all 0.2s ease !important;
}

.quick-complete-btn:active {
  transform: scale(0.9) !important;
}

.checkpoint-quick-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.quick-complete-btn {
  font-size: 18rpx !important;
  padding: 0 8rpx !important;
  height: 40rpx !important;
  border-radius: 20rpx !important;
  min-width: 40rpx !important;
  font-weight: 600 !important;
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.3) !important;
  transition: all 0.2s ease !important;
}

.quick-complete-btn:active {
  transform: scale(0.9) !important;
}

/* 检查点快速展开区域 */
.checkpoint-expand-area {
  background-color: #e6f7ff;
  border-radius: 0 0 16rpx 16rpx;
  padding: 16rpx;
  margin-top: 0;
  border: 2rpx solid #1890ff;
  border-top: none;
  min-height: 100rpx;
  position: relative;
  z-index: 10;
}

.checkpoint-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.checkpoint-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 8rpx 0;
  transition: all 0.2s ease;
}

.checkpoint-item:active {
  background-color: rgba(24, 144, 255, 0.05);
  border-radius: 8rpx;
  padding: 8rpx 12rpx;
  margin: 0 -12rpx;
}

.checkpoint-checkbox {
  flex-shrink: 0;
}

.checkpoint-title {
  font-size: 24rpx;
  color: #333333;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.checkpoint-title.completed {
  text-decoration: line-through;
  color: #999999;
}

.task-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

/* 任务详情 */
.task-details-collapse {
  margin-top: 16rpx;
  animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.task-details {
  padding: 16rpx;
  background-color: #f7f8fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-20rpx);
    max-height: 0;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    max-height: 1000rpx;
  }
}

.task-time-info {
  display: flex;
  gap: 24rpx;
  margin-bottom: 16rpx;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.time-text {
  font-size: 22rpx;
  color: #666666;
}

/* 进度条 */
.task-progress-wrapper {
  margin-bottom: 16rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.progress-label {
  font-size: 22rpx;
  color: #666666;
}

.progress-percentage {
  font-size: 22rpx;
  color: #1890FF;
  font-weight: 600;
}

.task-progress {
  border-radius: 6rpx;
}

/* 子任务 */
.subtasks-wrapper {
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.subtasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #eee;
  cursor: pointer;
  transition: all 0.2s ease;
}

.subtasks-header:active {
  background-color: rgba(24, 144, 255, 0.05);
}

.subtasks-title {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
  transition: color 0.2s ease;
  flex: 1;
}

.subtasks-header:active .subtasks-title {
  color: #1890ff;
}

.subtasks-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.batch-btn {
  font-size: 20rpx !important;
  padding: 0 12rpx !important;
  height: 48rpx !important;
  border-radius: 24rpx !important;
}

.subtasks-toggle {
  color: #666666;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.subtasks-header:active .subtasks-toggle {
  color: #1890ff;
  transform: scale(1.1);
}

.subtasks-list {
  margin-top: 8rpx;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
    max-height: 0;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    max-height: 500rpx;
  }
}

.subtask-cell {
  padding: 12rpx;
  font-size: 22rpx;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.subtask-cell::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(82, 196, 26, 0.1), transparent);
  transition: left 0.3s ease;
}

.subtask-cell:active {
  background-color: rgba(24, 144, 255, 0.05);
  transform: scale(0.98);
}

.subtask-cell:active::before {
  left: 100%;
}

.subtask-title {
  font-size: 22rpx;
  color: #333333;
  transition: all 0.3s ease;
}

.subtask-title.completed {
  text-decoration: line-through;
  color: #999999;
  animation: completionPulse 0.5s ease;
}

@keyframes completionPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); color: #52c41a; }
  100% { transform: scale(1); }
}

/* 批量操作模式 */
.subtask-cell.batch-mode {
  background-color: rgba(24, 144, 255, 0.02);
  border-left: 3rpx solid #1890ff;
}

.subtask-icon-wrapper {
  display: flex;
  align-items: center;
}

.batch-checkbox {
  border: 2rpx solid #1890ff !important;
}

.batch-actions {
  display: flex;
  gap: 12rpx;
  padding: 16rpx;
  background-color: rgba(24, 144, 255, 0.05);
  border-radius: 8rpx;
  margin-top: 8rpx;
  animation: slideInUp 0.3s ease;
}

.batch-action-btn {
  flex: 1;
  font-size: 20rpx !important;
  height: 56rpx !important;
  border-radius: 8rpx !important;
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 快捷操作 */
.task-quick-actions {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
}

.quick-action-btn {
  flex: 1;
  font-size: 22rpx;
}

/* 空状态样式 */
.empty-state {
  padding: 80rpx 32rpx;
  text-align: center;
  margin-bottom: 120rpx; /* 为底部导航留出空间 */
}

.empty-add-btn {
  margin-top: 32rpx;
  font-size: 28rpx !important;
  padding: 0 40rpx !important;
  height: 80rpx !important;
  border-radius: 40rpx !important;
  font-weight: 600 !important;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3) !important;
}

/* 添加任务按钮 */
.add-task-section {
  padding: 32rpx 0;
  margin-bottom: 120rpx; /* 为底部导航留出空间 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.add-task-btn {
  width: auto; /* 改为自适应宽度 */
  min-width: 300rpx; /* 设置最小宽度保证美观 */
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
  border-radius: 50rpx; /* 添加圆角让按钮更美观 */
}

/* 操作菜单 */
.task-action-sheet {
  border-radius: 16rpx 16rpx 0 0;
}

/* 筛选弹窗 */
.filter-popup {
  border-radius: 16rpx 16rpx 0 0;
}

.filter-menu-content {
  padding: 32rpx;
}

.filter-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #eee;
}

.filter-menu-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.filter-menu-body {
  margin-bottom: 32rpx;
}

.filter-section {
  margin-bottom: 32rpx;
}

.filter-section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.filter-option-cell {
  font-size: 26rpx;
}

.filter-menu-footer {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eee;
}

.filter-reset-btn,
.filter-apply-btn {
  flex: 1;
  font-size: 28rpx;
  font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .task-title-line {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }

  .task-title {
    white-space: normal; /* 小屏允许换行 */
    overflow: visible;
    text-overflow: unset;
  }

  .task-meta-line {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }

  .task-due {
    align-self: flex-start;
  }

  .task-quick-actions {
    flex-direction: column;
    gap: 8rpx;
  }
}

/* 动画效果 */
.task-cell {
  transition: all 0.3s ease;
}

.task-cell:active {
  transform: scale(0.98);
  background-color: #f5f5f5;
}

.task-status-indicator {
  animation: status-pulse 2s infinite;
}

@keyframes status-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* 自定义Vant主题色 */
.van-tab--active {
  color: #1890FF;
}

.van-tabs__line {
  background-color: #1890FF;
}

.van-button--primary {
  background-color: #1890FF;
  border-color: #1890FF;
}

.van-checkbox__icon--checked {
  background-color: #1890FF;
  border-color: #1890FF;
}

.van-tag--primary {
  background-color: #e6f7ff;
  color: #1890FF;
}

.van-tag--success {
  background-color: #f6ffed;
  color: #52C41A;
}

.van-tag--warning {
  background-color: #fff7e6;
  color: #FA8C16;
}

.van-tag--danger {
  background-color: #fff2f0;
  color: #FF4D4F;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .filter-mode-indicator {
    padding: 6rpx 12rpx;
    margin-bottom: 8rpx;
  }

  .mode-text {
    font-size: 22rpx;
  }

  .tip-text {
    font-size: 20rpx;
  }

  .van-tabs {
    border-radius: 12rpx;
  }
}

@media (max-width: 600rpx) {
  .filter-section {
    margin-bottom: 16rpx;
  }

  .filter-mode-indicator {
    padding: 4rpx 8rpx;
    margin-bottom: 6rpx;
  }

  .mode-text {
    font-size: 20rpx;
  }
}

/* 无障碍访问支持 */
.filter-mode-indicator,
.filter-status-tip,
.back-button-tab,
.exam-filter-tab {
  /* 确保足够的触摸目标大小 */
  min-height: 44rpx;
  /* 提供清晰的焦点指示 */
  transition: all 0.3s ease;
}

.filter-mode-indicator:focus,
.filter-status-tip:focus {
  outline: 2rpx solid #1890FF;
  outline-offset: 2rpx;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .filter-mode-indicator {
    border-width: 2rpx;
    background-color: rgba(24, 144, 255, 0.2);
  }

  .filter-status-tip {
    background-color: rgba(82, 196, 26, 0.2);
    border: 1rpx solid #52C41A;
  }

  .mode-text,
  .tip-text {
    font-weight: 600;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .filter-mode-indicator,
  .filter-status-tip,
  .back-button-tab,
  .exam-filter-tab {
    transition: none;
  }

  .task-status-indicator {
    animation: none;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .filter-mode-indicator {
    background-color: rgba(24, 144, 255, 0.2);
    border-color: rgba(24, 144, 255, 0.4);
  }

  .filter-status-tip {
    background-color: rgba(82, 196, 26, 0.2);
  }

  .van-tabs {
    background-color: #1f1f1f;
    box-shadow: 0 4rpx 12rpx rgba(255,255,255,0.1);
  }
}

/* 高级筛选扩展样式 */
.exam-filter-options {
  margin-top: 16rpx;
}

.exam-selection-list {
  margin-top: 12rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.exam-option-cell {
  border-left: 4rpx solid #1890FF;
  margin-bottom: 4rpx;
}

.exam-option-cell .van-cell__title {
  font-weight: 500;
  color: #1890FF;
}

.exam-option-cell .van-cell__label {
  color: #666666;
  font-size: 24rpx;
}

.quick-filter-options {
  margin-top: 16rpx;
}

.quick-filter-cell {
  border-left: 4rpx solid #52C41A;
  margin-bottom: 8rpx;
  background-color: rgba(82, 196, 26, 0.05);
}

.quick-filter-cell .van-cell__title {
  font-weight: 600;
  color: #52C41A;
}

.quick-filter-cell .van-cell__label {
  color: #666666;
  font-size: 22rpx;
  margin-top: 4rpx;
}

.quick-filter-cell:active {
  background-color: rgba(82, 196, 26, 0.1);
}

/* 筛选状态保存相关样式 */
.filter-state-section {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.saved-filter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 16rpx;
  margin-bottom: 8rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}

.saved-filter-info {
  flex: 1;
}

.saved-filter-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4rpx;
}

.saved-filter-time {
  font-size: 22rpx;
  color: #999999;
}

.saved-filter-actions {
  display: flex;
  gap: 8rpx;
}

.filter-action-btn {
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
  border: none;
  background: none;
}

.restore-btn {
  color: #1890FF;
  background-color: rgba(24, 144, 255, 0.1);
}

.delete-btn {
  color: #FF4D4F;
  background-color: rgba(255, 77, 79, 0.1);
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .exam-selection-list {
    max-height: 300rpx;
  }

  .quick-filter-cell .van-cell__title {
    font-size: 26rpx;
  }

  .quick-filter-cell .van-cell__label {
    font-size: 20rpx;
  }
}

/* 无障碍访问优化 */
.exam-option-cell,
.quick-filter-cell {
  min-height: 88rpx;
  transition: background-color 0.3s ease;
}

.exam-option-cell:focus,
.quick-filter-cell:focus {
  outline: 2rpx solid #1890FF;
  outline-offset: 2rpx;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .exam-option-cell {
    border-left-width: 6rpx;
  }

  .quick-filter-cell {
    border-left-width: 6rpx;
    background-color: rgba(82, 196, 26, 0.15);
  }

  .saved-filter-item {
    border-width: 2rpx;
  }
}

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }
  
  .stats-grid,
  .filter-section,
  .task-cell-group {
    background-color: #2d2d2d;
    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.3);
  }
  
  .stats-value,
  .task-title,
  .group-date,
  .filter-menu-title,
  .filter-section-title {
    color: #ffffff;
  }
  
  .stats-label,
  .group-count,
  .task-due,
  .time-text {
    color: #cccccc;
  }
}

/* 响应式设计 - 小屏设备优化 */
@media (max-width: 750rpx) {
  .exam-controls-section {
    padding: 12rpx;
  }

  .exam-controls-section .van-dropdown-menu {
    margin-right: 16rpx; /* 小屏设备调整间距 */
  }

  .exam-controls-section .van-dropdown-menu__bar {
    max-width: calc(100% - 140rpx) !important; /* 小屏设备调整最大宽度 */
  }

  .add-task-quick-btn {
    font-size: 24rpx !important;
    padding: 0 20rpx !important;
    min-width: 120rpx !important;
    border-radius: 28rpx !important;
    right: 12rpx !important; /* 小屏设备调整右边距 */
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .exam-controls-section .van-dropdown-menu {
    transition: none !important;
  }
}

/* ==================== 检查点弹窗样式 ==================== */

/* 弹窗基础样式 */
.checkpoint-modal {
  border-radius: 16rpx !important;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.checkpoint-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #ffffff;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.checkpoint-modal-content {
  max-height: 60vh;
  overflow-y: auto;
  padding-bottom: 16rpx;
}

/* 统计信息卡片样式 */
.checkpoint-stats-card {
  margin: 16rpx 24rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  border: 1rpx solid #f0f0f0;
}

.stats-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-main {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.stats-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #1890ff;
  transition: all 0.3s ease;
}

.stats-number.updating {
  transform: scale(1.1);
  color: #52c41a;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

.stats-detail {
  flex: 1;
  margin-left: 20rpx;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.completed-count {
  font-size: 22rpx;
  color: #52c41a;
  font-weight: 500;
  min-width: 80rpx;
}

.progress-bar {
  flex: 1;
  height: 6rpx;
  background-color: #f0f0f0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

/* 检查点列表卡片样式 */
.checkpoint-list-card {
  margin: 16rpx 24rpx;
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}

.checkpoint-item {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  border-bottom: 1rpx solid #f8f9fa;
  transition: all 0.3s ease;
  position: relative;
  animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.checkpoint-item:last-child {
  border-bottom: none;
}

.checkpoint-item:active {
  background-color: #f8f9fa;
}

.checkpoint-checkbox {
  margin-right: 16rpx;
  flex-shrink: 0;
}

.checkpoint-content {
  flex: 1;
  min-width: 0;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 6rpx;
  padding: 8rpx;
  margin: -8rpx;
}

.checkpoint-content:active {
  background-color: #f0f8ff;
}

.checkpoint-text-wrapper {
  position: relative;
  transition: all 0.3s ease;
}

.checkpoint-text-wrapper.completed-wrapper {
  opacity: 0.6;
}

.checkpoint-field {
  --van-field-padding: 12rpx 0;
  --van-field-input-text-color: #333;
  --van-field-placeholder-text-color: #999;
  --van-field-input-font-size: 28rpx;
}

.completed-wrapper .checkpoint-title {
  color: #999999;
}

/* 删除线效果 */
.completed-wrapper::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2rpx;
  background-color: #999;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 1;
}

.checkpoint-actions {
  flex-shrink: 0;
}

.delete-action {
  padding: 8rpx;
  color: #ff4d4f;
  transition: all 0.2s ease;
  border-radius: 50%;
}

.delete-action:active {
  color: #ffffff;
  background-color: #ff4d4f;
}

/* 添加检查点卡片样式 */
.add-checkpoint-card {
  margin: 16rpx 24rpx;
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}

/* 默认触发状态 */
.add-checkpoint-trigger {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-checkpoint-trigger:active {
  background-color: #f8f9fa;
}

.trigger-text {
  font-size: 28rpx;
  color: #1890ff;
  font-weight: 500;
}

/* 展开状态 */
.add-checkpoint-expanded {
  padding: 20rpx 20rpx 24rpx 20rpx;
  background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
  border: 1rpx solid #e6f7ff;
}

.add-input-section {
  margin-bottom: 16rpx;
}

.add-checkpoint-field {
  --van-field-border-color: #d9d9d9;
  --van-field-background-color: #ffffff;
  --van-field-padding: 14rpx 16rpx;
  --van-field-input-font-size: 28rpx;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 1rpx 4rpx rgba(0,0,0,0.04);
}

/* 检查点操作区域 */
.checkpoint-actions {
  display: flex;
  margin-top: 16rpx;
  background: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  overflow: hidden;
}

.checkpoint-action-button {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80rpx;
  padding: 12rpx;
}

.save-action {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.cancel-action {
  background: #f8f9fa;
  border-left: 1rpx solid #f0f0f0;
}

.save-checkpoint-btn {
  width: 100% !important;
  height: 56rpx !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  color: #ffffff !important;
  font-size: 26rpx !important;
  font-weight: 600 !important;
  box-shadow: none !important;
}

.cancel-checkpoint-btn {
  width: 100% !important;
  height: 56rpx !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  color: #666 !important;
  font-size: 26rpx !important;
  font-weight: 500 !important;
  box-shadow: none !important;
}

/* 空状态样式 */
.empty-checkpoints {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-top: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 24rpx;
  color: #cccccc;
  margin-top: 8rpx;
  line-height: 1.4;
}

/* 弹窗底部操作区域 */
.checkpoint-modal-footer {
  padding: 16rpx 24rpx 24rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #ffffff;
}

.reset-action {
  background: #f8f9fa;
  border-right: 1rpx solid #f0f0f0;
}

.complete-action {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.reset-checkpoint-btn {
  width: 100% !important;
  height: 56rpx !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  color: #666 !important;
  font-size: 26rpx !important;
  font-weight: 500 !important;
  box-shadow: none !important;
}

.complete-checkpoint-btn {
  width: 100% !important;
  height: 56rpx !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  color: #ffffff !important;
  font-size: 26rpx !important;
  font-weight: 600 !important;
  box-shadow: none !important;
}

/* 弹窗适配的响应式调整 */
@media (max-width: 750rpx) {
  .checkpoint-modal-header {
    padding: 20rpx 24rpx 12rpx;
  }

  .modal-title {
    font-size: 28rpx;
  }

  .checkpoint-stats-card,
  .checkpoint-list-card,
  .add-checkpoint-card {
    margin: 12rpx 16rpx;
  }

  .stats-number {
    font-size: 32rpx;
  }

  .checkpoint-title {
    font-size: 26rpx;
  }

  .trigger-text {
    font-size: 26rpx;
  }
}
