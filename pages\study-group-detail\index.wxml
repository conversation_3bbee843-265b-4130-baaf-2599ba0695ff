<!-- pages/study-group-detail/index.wxml -->
<van-loading wx:if="{{loading}}" size="24px" vertical>加载中...</van-loading>

<view class="container" wx:if="{{!loading}}">
  <!-- 小组信息头部 -->
  <van-card
    title="{{groupInfo.groupName}}"
    desc="{{groupInfo.examName}}"
    thumb="/images/default-group.png"
    class="group-card"
  >
    <view slot="tags" class="group-tags">
      <van-tag type="primary">{{groupInfo.currentMembers}}/{{groupInfo.maxMembers}}人</van-tag>
    </view>
    
    <view slot="footer" class="group-actions">
      <van-button 
        wx:if="{{groupInfo && groupInfo.currentMembers < groupInfo.maxMembers}}"
        type="default" 
        size="small" 
        bindtap="onInviteMembers"
        class="action-btn"
      >
        邀请成员
      </van-button>
      
      <van-button 
        wx:if="{{groupInfo}}"
        type="primary" 
        size="small" 
        bindtap="onSharePlan"
        class="action-btn"
      >
        分享计划
      </van-button>
    </view>
  </van-card>

  <!-- Tab导航 -->
  <van-tabs 
    active="{{ activeTabIndex }}" 
    bind:change="onTabChange"
    color="#1890ff"
    title-active-color="#1890ff"
    class="group-tabs"
  >
    <van-tab title="成员进度">
      <!-- 成员进度列表 -->
      <view class="member-list">
        <van-cell-group wx:if="{{memberProgress.length > 0}}">
          <van-cell 
            wx:for="{{memberProgress}}" 
            wx:key="userId"
            clickable
            bindtap="onViewMember"
            data-user-id="{{item.userId}}"
            custom-class="member-cell"
          >
            <view slot="icon" class="member-avatar-slot">
              <view class="member-avatar">
                <van-image 
                  src="{{item.userInfo.avatarUrl || '/images/default-avatar.png'}}" 
                  round 
                  width="40px" 
                  height="40px"
                />
                <view class="online-status {{item.isOnline ? 'online' : 'offline'}}"></view>
              </view>
            </view>
            
            <view slot="title" class="member-info">
              <view class="member-name">{{item.userInfo.nickName}}</view>
              <view class="member-status">
                <van-tag 
                  size="mini" 
                  color="{{item.isOnline ? '#52c41a' : '#bfbfbf'}}"
                >
                  {{item.isOnline ? '在线' : '离线'}}
                </van-tag>
              </view>
            </view>
            
            <view slot="label" class="member-stats">
              <view class="stat-row">
                <text class="stat-label">今日复习:</text>
                <text class="stat-value">{{item.todayStudyTime || '0分钟'}}</text>
              </view>
              
              <view class="stat-row">
                <text class="stat-label">任务进度:</text>
                <text class="stat-value">{{item.completedTasks || 0}}/{{item.totalTasks || 0}}</text>
              </view>
              <van-progress 
                percentage="{{(item.completedTasks || 0) / (item.totalTasks || 1) * 100}}" 
                color="#1890ff"
                class="progress-item"
              />
              
              <view class="stat-row">
                <text class="stat-label">本周进度:</text>
                <text class="stat-value">{{item.weeklyProgress || 0}}%</text>
              </view>
              <van-progress 
                percentage="{{item.weeklyProgress || 0}}" 
                color="#52c41a"
                class="progress-item"
              />
              
              <view class="stat-row">
                <text class="stat-label">连续打卡:</text>
                <text class="stat-value">{{item.consecutiveDays || 0}}天</text>
              </view>
            </view>
            
            <view slot="right-icon" class="member-actions">
              <view class="like-info">
                <van-icon name="good-job" size="16px" />
                <text class="like-count">{{item.likeCount || 0}}</text>
              </view>
            </view>
          </van-cell>
        </van-cell-group>
        
        <van-empty 
          wx:if="{{memberProgress.length === 0}}"
          image="search" 
          description="暂无成员进度数据"
        />
      </view>
    </van-tab>

    <van-tab title="共享计划">
      <!-- 共享计划列表 -->
      <view class="share-list">
        <van-cell-group wx:if="{{shares.length > 0}}">
          <van-cell 
            wx:for="{{shares}}" 
            wx:key="_id" 
            wx:for-index="index"
            custom-class="share-cell"
          >
            <view slot="icon" class="sharer-avatar-slot">
              <van-image 
                src="{{item.sharerInfo.avatarUrl || '/images/default-avatar.png'}}" 
                round 
                width="32px" 
                height="32px"
              />
            </view>
            
            <view slot="title" class="plan-header">
              <text class="sharer-name">{{item.sharerInfo.nickName}}</text>
              <text class="share-time">{{item.shareTime}}</text>
            </view>
            
            <view slot="label" class="plan-content">
              <text class="plan-title">{{item.planData.title}}</text>
              <text class="plan-desc" wx:if="{{item.planData.description}}">{{item.planData.description}}</text>
              <view class="plan-stats">
                <van-tag size="mini" color="#f0f0f0" text-color="#666">{{item.planData.totalTasks || 0}}个任务</van-tag>
                <van-tag size="mini" color="#f0f0f0" text-color="#666">预计{{item.planData.estimatedDays || 0}}天</van-tag>
              </view>
            </view>
            
            <view slot="right-icon" class="share-actions">
              <van-button 
                size="mini" 
                type="{{item.liked ? 'primary' : 'default'}}"
                bindtap="onLikePlan"
                data-share-id="{{item._id}}"
                data-index="{{index}}"
                icon="good-job"
                class="action-btn"
              >
                {{item.likeCount || 0}}
              </van-button>
              
              <van-button 
                size="mini" 
                type="default"
                bindtap="onCopyPlan"
                data-share-id="{{item._id}}"
                icon="todo-list"
                class="action-btn"
              >
                复制
              </van-button>
            </view>
          </van-cell>
        </van-cell-group>
        
        <van-empty 
          wx:if="{{shares.length === 0}}"
          image="default" 
          description="还没有共享计划"
        >
          <van-button 
            type="primary" 
            size="small" 
            bindtap="onSharePlan"
            class="empty-action-btn"
          >
            分享第一个计划
          </van-button>
        </van-empty>
      </view>
    </van-tab>

    <van-tab title="小组动态">
      <!-- 小组动态列表 -->
      <view class="activity-list">
        <van-cell-group wx:if="{{activities.length > 0}}">
          <van-cell 
            wx:for="{{activities}}" 
            wx:key="_id"
            custom-class="activity-cell"
          >
            <view slot="icon" class="activity-avatar-slot">
              <van-image 
                src="{{item.userInfo.avatarUrl || '/images/default-avatar.png'}}" 
                round 
                width="32px" 
                height="32px"
              />
            </view>
            
            <view slot="title" class="activity-content">
              <text class="activity-text">
                <text class="user-name">{{item.userInfo.nickName}}</text>
                <text wx:if="{{item.activityType === 'join_group'}}">加入了小组</text>
                <text wx:if="{{item.activityType === 'plan_share'}}">分享了计划《{{item.activityData.planTitle}}》</text>
                <text wx:if="{{item.activityType === 'like_plan'}}">点赞了计划《{{item.activityData.planTitle}}》</text>
                <text wx:if="{{item.activityType === 'copy_plan'}}">复制了计划《{{item.activityData.planTitle}}》</text>
                <text wx:if="{{item.activityType === 'task_complete'}}">完成了任务《{{item.activityData.taskTitle}}》</text>
              </text>
            </view>
            
            <view slot="label" class="activity-time">{{item.createTime}}</view>
          </van-cell>
        </van-cell-group>
        
        <van-empty 
          wx:if="{{activities.length === 0}}"
          image="network" 
          description="暂无小组动态"
        />
      </view>
    </van-tab>
  </van-tabs>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <van-button 
      type="danger" 
      size="large" 
      bindtap="onLeaveGroup"
      block
    >
      退出小组
    </van-button>
  </view>
</view>

<wxs module="Math">
  var round = function(num) {
    return Math.round(num)
  }

  module.exports = {
    round: round
  }
</wxs>
