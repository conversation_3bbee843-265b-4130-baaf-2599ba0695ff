<!--pages/splash/index.wxml-->
<view class="splash-container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="circle circle-1"></view>
    <view class="circle circle-2"></view>
    <view class="circle circle-3"></view>
  </view>

  <!-- 主要内容 -->
  <view class="content">
    <!-- 应用图标 -->
    <view class="app-icon">
      <text class="icon-text">📚</text>
    </view>
    
    <!-- 应用名称 -->
    <text class="app-name">{{appName}}</text>
    
    <!-- Slogan -->
    <text class="slogan">{{slogan}}</text>
    
    <!-- 进入按钮 -->
    <view class="button-container" wx:if="{{showButton}}">
      <van-button 
        type="primary" 
        size="large" 
        bindtap="enterApp"
        custom-class="enter-button"
        custom-style="width: 240rpx; height: 80rpx; border-radius: 40rpx; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;"
      >
        开始使用
      </van-button>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <text class="version">v1.0.0</text>
  </view>
</view> 