/**
 * 云函数版考试数据模型
 * 简化版的ExamModel，用于云函数中的数据验证和格式化
 */

class ExamModel {
  constructor(data = {}) {
    this.data = this.validate(data)
  }

  /**
   * 数据验证
   */
  validate(data) {
    const errors = []
    
    // 必填字段验证
    if (!data.title || typeof data.title !== 'string' || data.title.trim().length === 0) {
      errors.push('考试名称不能为空')
    }
    
    if (data.title && data.title.trim().length > 50) {
      errors.push('考试名称不能超过50个字符')
    }
    
    if (!data.examDate || !this.isValidDate(data.examDate)) {
      errors.push('考试日期格式不正确')
    }
    
    if (!data.examTime || !this.isValidTime(data.examTime)) {
      errors.push('考试时间格式不正确')
    }
    
    // 可选字段验证
    if (data.subject && data.subject.length > 30) {
      errors.push('考试科目不能超过30个字符')
    }
    
    if (data.location && data.location.length > 100) {
      errors.push('考试地点不能超过100个字符')
    }
    
    if (data.description && data.description.length > 500) {
      errors.push('考试描述不能超过500个字符')
    }
    
    // 枚举值验证
    if (data.type && !this.isValidExamType(data.type)) {
      errors.push('考试类型不正确')
    }
    
    if (data.importance && !this.isValidImportance(data.importance)) {
      errors.push('重要程度不正确')
    }
    
    if (data.status && !this.isValidStatus(data.status)) {
      errors.push('考试状态不正确')
    }
    
    if (errors.length > 0) {
      throw new Error(`数据验证失败: ${errors.join(', ')}`)
    }
    
    return this.format(data)
  }

  /**
   * 数据格式化
   */
  format(data) {
    const formatted = {
      title: this.trimString(data.title),
      subject: this.trimString(data.subject) || '',
      examDate: this.normalizeDate(data.examDate),
      examTime: this.normalizeTime(data.examTime),
      location: this.trimString(data.location) || '',
      description: this.trimString(data.description) || '',
      type: data.type || ExamModel.TYPE.FINAL,
      importance: data.importance || ExamModel.IMPORTANCE.MEDIUM,
      status: data.status || ExamModel.STATUS.UPCOMING,
      reminderSettings: this.normalizeReminderSettings(data.reminderSettings)
    }

    // 保留系统字段
    if (data._id) formatted._id = data._id
    if (data.userId) formatted.userId = data.userId
    if (data.createTime) formatted.createTime = data.createTime
    if (data.updateTime) formatted.updateTime = data.updateTime

    return formatted
  }

  // 验证方法
  isValidDate(dateStr) {
    if (typeof dateStr !== 'string') return false
    const regex = /^\d{4}-\d{2}-\d{2}$/
    if (!regex.test(dateStr)) return false
    const date = new Date(dateStr)
    return date instanceof Date && !isNaN(date)
  }

  isValidTime(timeStr) {
    if (typeof timeStr !== 'string') return false
    const regex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    return regex.test(timeStr)
  }

  isValidExamType(type) {
    return Object.values(ExamModel.TYPE).includes(type)
  }

  isValidImportance(importance) {
    return Object.values(ExamModel.IMPORTANCE).includes(importance)
  }

  isValidStatus(status) {
    return Object.values(ExamModel.STATUS).includes(status)
  }

  // 格式化方法
  trimString(str) {
    return typeof str === 'string' ? str.trim() : str
  }

  normalizeDate(dateStr) {
    const date = new Date(dateStr)
    return date.toISOString().split('T')[0]
  }

  normalizeTime(timeStr) {
    const [hours, minutes] = timeStr.split(':')
    return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`
  }

  normalizeReminderSettings(reminderSettings) {
    if (!Array.isArray(reminderSettings)) {
      return [ExamModel.REMINDER.ONE_DAY, ExamModel.REMINDER.THREE_DAYS]
    }
    
    const validSettings = [...new Set(reminderSettings)].filter(setting => 
      Object.values(ExamModel.REMINDER).includes(setting)
    )
    
    return validSettings.length > 0 ? validSettings : [ExamModel.REMINDER.ONE_DAY, ExamModel.REMINDER.THREE_DAYS]
  }

  // 静态方法
  static validateData(data) {
    try {
      new ExamModel(data)
      return { isValid: true, errors: [] }
    } catch (error) {
      const errors = error.message.replace('数据验证失败: ', '').split(', ')
      return { isValid: false, errors }
    }
  }

  static formatList(examList) {
    if (!Array.isArray(examList)) return []

    return examList.map(exam => {
      try {
        return new ExamModel(exam).data
      } catch (error) {
        console.error('格式化考试数据失败:', error.message, exam)
        return null
      }
    }).filter(exam => exam !== null)
  }
}

// 常量定义
ExamModel.TYPE = {
  FINAL: 'final',
  MIDTERM: 'midterm',
  QUIZ: 'quiz',
  CERTIFICATE: 'certificate',
  ENTRANCE: 'entrance',
  OTHER: 'other'
}

ExamModel.IMPORTANCE = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low'
}

ExamModel.STATUS = {
  UPCOMING: 'upcoming',
  ONGOING: 'ongoing',
  FINISHED: 'finished'
}

ExamModel.REMINDER = {
  ONE_DAY: '1day',
  THREE_DAYS: '3days',
  ONE_WEEK: '1week',
  TWO_WEEKS: '2weeks'
}

module.exports = ExamModel
