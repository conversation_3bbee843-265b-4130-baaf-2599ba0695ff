<!--pages/exam-detail/index.wxml - Vant重构版本-->
<view class="container">
  <!-- 考试头部信息卡片 -->
  <van-card 
    title="{{exam.name}}" 
    desc="{{exam.description}}"
    custom-class="exam-header-card">
    
    <!-- 考试状态和操作 -->
    <view slot="tags" class="exam-status-section">
      <van-tag 
        type="{{exam.status === 'active' ? 'primary' : 'default'}}" 
        custom-class="status-tag">
        {{exam.statusText}}
      </van-tag>
      
      <van-tag 
        type="{{exam.importance === 'high' ? 'danger' : exam.importance === 'medium' ? 'warning' : 'default'}}" 
        custom-class="importance-tag">
        {{exam.importanceText}}
      </van-tag>
    </view>

    <view slot="footer" class="exam-actions">
      <van-button 
        wx:if="{{!exam.isActive}}" 
        type="primary" 
        size="mini" 
        bind:click="setAsActiveExam">
        设为当前
      </van-button>
      
      <van-button 
        type="default" 
        size="mini" 
        bind:click="editExam"
        custom-class="action-btn">
        <van-icon name="edit" size="16" />
      </van-button>
      
      <van-button 
        type="default" 
        size="mini" 
        bind:click="showMoreActions"
        custom-class="action-btn">
        <van-icon name="ellipsis" size="16" />
      </van-button>
    </view>
  </van-card>

  <!-- 倒计时信息 -->
  <van-panel title="考试倒计时" custom-class="countdown-panel">
    <view slot="header" class="countdown-header">
      <text class="exam-date">{{exam.date}}</text>
    </view>

    <view class="countdown-content">
      <!-- 数字倒计时 -->
      <view class="countdown-display">
        <view class="countdown-item">
          <text class="countdown-number">{{countdown.days}}</text>
          <text class="countdown-label">天</text>
        </view>
        <view class="countdown-separator">:</view>
        <view class="countdown-item">
          <text class="countdown-number">{{countdown.hours}}</text>
          <text class="countdown-label">时</text>
        </view>
        <view class="countdown-separator">:</view>
        <view class="countdown-item">
          <text class="countdown-number">{{countdown.minutes}}</text>
          <text class="countdown-label">分</text>
        </view>
        <view class="countdown-separator">:</view>
        <view class="countdown-item">
          <text class="countdown-number">{{countdown.seconds}}</text>
          <text class="countdown-label">秒</text>
        </view>
      </view>

      <!-- 进度条 -->
      <view class="countdown-progress-section">
        <van-progress 
          percentage="{{exam.timeProgress}}" 
          stroke-width="8" 
          color="#1989fa"
          custom-class="time-progress" />
        <text class="progress-text">距离考试还有 {{exam.remainingDays}} 天</text>
      </view>
    </view>
  </van-panel>

  <!-- 考试科目 -->
  <van-panel 
    title="考试科目" 
    custom-class="subjects-panel"
    wx:if="{{exam.subjects && exam.subjects.length > 0}}">
    
    <view slot="header" class="subjects-header">
      <van-tag type="primary" size="mini">{{exam.subjects.length}}科</van-tag>
    </view>

    <van-collapse value="{{activeSubjects}}" bind:change="onSubjectChange">
      <van-collapse-item 
        wx:for="{{exam.subjects}}" 
        wx:key="id"
        name="{{item.id}}"
        title="{{item.name}}"
        custom-class="subject-item">
        
        <view slot="right-icon" class="subject-score">
          <text class="target-score">目标: {{item.targetScore}}</text>
          <text class="total-score">/{{item.totalScore}}</text>
        </view>

        <view class="subject-content">
          <!-- 进度条 -->
          <view class="subject-progress">
            <van-progress 
              percentage="{{item.progress}}" 
              stroke-width="6" 
              color="{{item.progressColor || '#52c41a'}}"
              custom-class="subject-progress-bar" />
            <text class="progress-text">准备进度 {{item.progress}}%</text>
          </view>

          <!-- 统计信息 -->
          <van-grid column-num="3" custom-class="subject-stats">
            <van-grid-item text="复习时长" custom-class="stat-item">
              <view slot="icon" class="stat-value">{{item.studyTime}}</view>
            </van-grid-item>
            
            <van-grid-item text="完成任务" custom-class="stat-item">
              <view slot="icon" class="stat-value">{{item.completedTasks}}</view>
            </van-grid-item>
            
            <van-grid-item text="平均效率" custom-class="stat-item">
              <view slot="icon" class="stat-value">{{item.avgEfficiency}}%</view>
            </van-grid-item>
          </van-grid>
        </view>
      </van-collapse-item>
    </van-collapse>
  </van-panel>

  <!-- 准备进度 -->
  <van-panel title="准备进度" custom-class="preparation-panel">
    <view slot="header" class="preparation-header">
      <van-tag type="success" size="mini">{{exam.overallProgress}}%</van-tag>
    </view>

    <view class="preparation-content">
      <!-- 圆形进度 -->
      <view class="progress-circle-section">
        <van-circle 
          value="{{exam.overallProgress}}" 
          size="120" 
          color="#52c41a" 
          layer-color="#f0f0f0"
          stroke-width="8"
          custom-class="overall-progress-circle">
          <view class="circle-content">
            <text class="progress-value">{{exam.overallProgress}}%</text>
            <text class="progress-label">整体进度</text>
          </view>
        </van-circle>
      </view>

      <!-- 统计数据 -->
      <van-grid column-num="2" custom-class="preparation-stats">
        <van-grid-item text="总任务数" custom-class="prep-stat-item">
          <view slot="icon" class="prep-stat-icon">📚</view>
          <view slot="text" class="prep-stat-content">
            <text class="prep-stat-value">{{exam.totalTasks}}</text>
            <text class="prep-stat-label">总任务数</text>
          </view>
        </van-grid-item>

        <van-grid-item text="已完成" custom-class="prep-stat-item">
          <view slot="icon" class="prep-stat-icon">✅</view>
          <view slot="text" class="prep-stat-content">
            <text class="prep-stat-value">{{exam.completedTasks}}</text>
            <text class="prep-stat-label">已完成</text>
          </view>
        </van-grid-item>

        <van-grid-item text="复习时长" custom-class="prep-stat-item">
          <view slot="icon" class="prep-stat-icon">⏰</view>
          <view slot="text" class="prep-stat-content">
            <text class="prep-stat-value">{{exam.totalStudyTime}}</text>
            <text class="prep-stat-label">复习时长</text>
          </view>
        </van-grid-item>

        <van-grid-item text="目标分数" custom-class="prep-stat-item">
          <view slot="icon" class="prep-stat-icon">🎯</view>
          <view slot="text" class="prep-stat-content">
            <text class="prep-stat-value">{{exam.targetScore}}</text>
            <text class="prep-stat-label">目标分数</text>
          </view>
        </van-grid-item>
      </van-grid>
    </view>
  </van-panel>

  <!-- 相关任务 -->
  <van-panel title="相关任务" custom-class="tasks-panel">
    <view slot="header" class="tasks-header">
      <van-button 
        type="primary" 
        size="mini" 
        bind:click="viewAllTasks">
        查看全部
      </van-button>
    </view>

    <!-- 任务列表 -->
    <van-cell-group wx:if="{{relatedTasks.length > 0}}" custom-class="tasks-list">
      <van-cell 
        wx:for="{{relatedTasks}}" 
        wx:key="id"
        title="{{item.title}}" 
        label="{{item.subject}} · {{item.dueDate}}"
        is-link 
        bind:click="viewTaskDetail" 
        data-id="{{item.id}}"
        custom-class="task-cell {{item.completed ? 'completed' : ''}}">
        
        <view slot="icon" class="task-status">
          <van-icon 
            name="{{item.completed ? 'success' : 'todo-list-o'}}" 
            size="20" 
            color="{{item.completed ? '#52c41a' : '#969799'}}" />
        </view>

        <view slot="right-icon" class="task-meta">
          <van-tag 
            type="{{item.priority === 'high' ? 'danger' : item.priority === 'medium' ? 'warning' : 'default'}}" 
            size="mini">
            {{item.priorityText}}
          </van-tag>
          <text class="progress-text">{{item.progress}}%</text>
        </view>
      </van-cell>
    </van-cell-group>

    <!-- 空状态 -->
    <van-empty 
      wx:else
      image="https://img.yzcdn.cn/vant/custom-empty-image.png" 
      description="暂无相关任务"
      custom-class="empty-tasks">
      <van-button 
        type="primary" 
        size="small" 
        bind:click="addTask">
        添加任务
      </van-button>
    </van-empty>
  </van-panel>

  <!-- 快捷操作 -->
  <van-panel title="快捷操作" custom-class="actions-panel">
    <van-grid column-num="3" custom-class="action-grid">
      <van-grid-item 
        text="添加任务" 
        bind:click="addTask"
        custom-class="quick-action-item">
        <van-icon slot="icon" name="add-o" size="24" color="#1989fa" />
      </van-grid-item>

      <van-grid-item 
        text="开始复习" 
        bind:click="startStudy"
        custom-class="quick-action-item">
        <van-icon slot="icon" name="play-circle-o" size="24" color="#52c41a" />
      </van-grid-item>

      <van-grid-item 
        text="设置提醒" 
        bind:click="setReminder"
        custom-class="quick-action-item">
        <van-icon slot="icon" name="bell-o" size="24" color="#ff976a" />
      </van-grid-item>

      <van-grid-item 
        text="分享" 
        bind:click="shareExam"
        custom-class="quick-action-item">
        <van-icon slot="icon" name="share-o" size="24" color="#7232dd" />
      </van-grid-item>

      <van-grid-item 
        wx:if="{{relatedTasks.length > 0}}"
        text="分享给搭子" 
        bind:click="onShareToGroup"
        custom-class="quick-action-item">
        <van-icon slot="icon" name="friends-o" size="24" color="#ff976a" />
      </van-grid-item>
    </van-grid>
  </van-panel>
</view>

<!-- 更多操作菜单 -->
<van-action-sheet 
  show="{{showMoreActions}}" 
  title="更多操作" 
  actions="{{moreActions}}" 
  bind:close="hideMoreActions"
  bind:select="onMoreActionSelect"
  custom-class="more-actions-sheet" />