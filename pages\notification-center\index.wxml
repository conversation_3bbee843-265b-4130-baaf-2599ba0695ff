<!--pages/notification-center/index.wxml-->
<view class="container">
  <!-- 通知头部 -->
  <van-card 
    title="通知中心" 
    desc="了解最新的提醒和消息"
    custom-class="header-container"
    >
    <view slot="tags">
      <van-button 
        wx:if="{{unreadCount > 0}}" 
        type="primary" 
        size="mini"
        bind:click="markAllAsRead"
        custom-class="mark-all-read-btn">
        全部已读
      </van-button>
      <van-button 
        type="default" 
        size="mini"
        icon="setting-o"
        bind:click="openNotificationSettings"
        custom-class="settings-btn">
      </van-button>
    </view>
    
    <view slot="desc">
      <van-grid 
        column-num="3" 
        border="{{false}}"
        gutter="8"
        custom-class="notification-stats"
        >
        <van-grid-item>
          <view class="stat-item">
            <text class="stat-value">{{totalCount}}</text>
            <text class="stat-label">总通知</text>
          </view>
        </van-grid-item>
        <van-grid-item>
          <view class="stat-item">
            <text class="stat-value">{{unreadCount}}</text>
            <text class="stat-label">未读</text>
          </view>
        </van-grid-item>
        <van-grid-item>
          <view class="stat-item">
            <text class="stat-value">{{todayCount}}</text>
            <text class="stat-label">今日</text>
          </view>
        </van-grid-item>
      </van-grid>
    </view>
  </van-card>

  <!-- 通知筛选 -->
  <van-card custom-class="filter-container">
    <van-tabs 
      active="{{currentFilter}}" 
      bind:change="switchFilter"
      custom-class="filter-tabs"
      >
      <van-tab 
        wx:for="{{notificationFilters}}" 
        wx:key="id"
        name="{{item.id}}"
        title="{{item.label}}"
        >
        <view slot="title">
          {{item.label}}
          <van-tag 
            wx:if="{{item.count > 0}}" 
            type="primary" 
            size="mini"
            custom-class="filter-count">
            {{item.count}}
          </van-tag>
        </view>
      </van-tab>
    </van-tabs>
  </van-card>

  <!-- 通知列表 -->
  <view class="notifications-container" wx:if="{{filteredNotifications.length > 0}}">
    <van-card 
      wx:for="{{groupedNotifications}}" 
      wx:key="date"
      custom-class="notification-group"
      >
      <view slot="title" class="group-header">
        <text class="group-date">{{item.date}}</text>
        <van-tag type="default" size="mini">{{item.notifications.length}}条</van-tag>
      </view>

      <van-cell-group slot="desc" custom-class="group-notifications">
        <van-swipe-cell 
          wx:for="{{item.notifications}}"
          wx:for-item="notification"
          wx:key="id"
          right-width="{{200}}"
          >
          <van-cell 
            title="{{notification.title}}"
            value="{{notification.time}}"
            label="{{notification.message}}"
            is-link
            custom-class="notification-item {{notification.read ? 'read' : 'unread'}}"
            bind:click="openNotification"
            data-notification="{{notification}}"
            >
            <view slot="icon" class="notification-icon-container">
              <view class="notification-icon" style="background-color: {{notification.iconBg}}">
                <text>{{notification.icon}}</text>
              </view>
              <view wx:if="{{!notification.read}}" class="unread-indicator"></view>
            </view>
            
            <view slot="label" class="notification-content">
              <text class="notification-message">{{notification.message}}</text>
              <view class="notification-actions" wx:if="{{notification.actions && notification.actions.length > 0}}">
                <van-button 
                  wx:for="{{notification.actions}}"
                  wx:for-item="action"
                  wx:key="id"
                  size="mini"
                  type="{{action.type === 'primary' ? 'primary' : 'default'}}"
                  bind:click="executeNotificationAction"
                  data-notification="{{notification}}"
                  data-action="{{action}}"
                  custom-class="notification-action">
                  {{action.label}}
                </van-button>
              </view>
            </view>
          </van-cell>
          
          <view slot="right" class="swipe-actions">
            <van-button 
              wx:if="{{!notification.read}}"
              type="primary" 
              size="small"
              bind:click="markAsRead"
              data-notification="{{notification}}"
              custom-class="swipe-action-btn">
              已读
            </van-button>
            <van-button 
              wx:if="{{notification.read}}"
              type="default" 
              size="small"
              bind:click="markAsUnread"
              data-notification="{{notification}}"
              custom-class="swipe-action-btn">
              未读
            </van-button>
            <van-button 
              type="danger" 
              size="small"
              bind:click="deleteNotification"
              data-notification="{{notification}}"
              custom-class="swipe-action-btn">
              删除
            </van-button>
          </view>
        </van-swipe-cell>
      </van-cell-group>
    </van-card>
  </view>

  <!-- 空状态 -->
  <van-empty 
    wx:if="{{filteredNotifications.length === 0}}"
    image="search"
    description="{{currentFilter === 'unread' ? '没有未读通知' : '暂无通知'}}"
    >
    <view slot="description">
      <text>{{currentFilter === 'unread' ? '所有通知都已阅读' : '开启通知提醒，不错过重要信息'}}</text>
      <van-button 
        wx:if="{{!notificationsEnabled}}" 
        type="primary" 
        size="small"
        bind:click="enableNotifications"
        custom-class="enable-notifications-btn">
        开启通知
      </van-button>
    </view>
  </van-empty>
</view>

<!-- 通知操作菜单 -->
<van-action-sheet
  show="{{showActionSheet}}"
  title="通知操作"
  actions="{{actionSheetActions}}"
  bind:close="hideActionSheet"
  bind:select="handleActionSelect"
  />

<!-- 通知设置弹窗 -->
<van-popup
  show="{{showSettingsModal}}"
  position="bottom"
  custom-style="height: 60%"
  round
  bind:close="hideSettingsModal"
  >
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">通知设置</text>
      <van-icon name="cross" bind:click="hideSettingsModal" custom-class="modal-close"/>
    </view>

    <view class="modal-body">
      <van-cell-group title="通知类型">
        <van-cell 
          wx:for="{{notificationSettings}}" 
          wx:key="type"
          title="{{item.name}}"
          label="{{item.description}}"
          >
          <van-switch 
            slot="right-icon"
            checked="{{item.enabled}}" 
            bind:change="toggleNotificationType" 
            data-type="{{item.type}}"
            />
        </van-cell>
      </van-cell-group>

      <van-divider />

      <van-cell-group title="提醒时间">
        <van-cell 
          title="免打扰时间"
          value="{{quietHours.start}} - {{quietHours.end}}"
          is-link
          bind:click="showQuietHoursSettings"
          />
      </van-cell-group>
    </view>
  </view>
</van-popup>

<!-- 免打扰时间设置弹窗 -->
<van-popup
  show="{{showQuietHoursModal}}"
  position="bottom"
  custom-style="height: 40%"
  round
  bind:close="hideQuietHoursModal"
  >
  <view class="quiet-hours-modal">
    <view class="modal-header">
      <text class="modal-title">免打扰时间</text>
      <van-icon name="cross" bind:click="hideQuietHoursModal" custom-class="modal-close"/>
    </view>
    
    <van-cell-group>
      <van-cell title="开始时间" value="{{quietHours.start}}" is-link bind:click="showStartTimePicker"/>
      <van-cell title="结束时间" value="{{quietHours.end}}" is-link bind:click="showEndTimePicker"/>
    </van-cell-group>
    
    <view class="modal-footer">
      <van-button type="primary" block bind:click="saveQuietHours">保存设置</van-button>
    </view>
  </view>
</van-popup>

<!-- 时间选择器 -->
<van-popup
  show="{{showTimePicker}}"
  position="bottom"
  bind:close="hideTimePicker"
  >
  <van-picker
    show-toolbar
    title="选择时间"
    columns="{{timeColumns}}"
    bind:confirm="onTimeConfirm"
    bind:cancel="hideTimePicker"
    />
</van-popup>