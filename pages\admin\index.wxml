<!--pages/admin/index.wxml-->
<view class="admin-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">数据管理</text>
    <text class="page-subtitle">用户数据统计与管理</text>
  </view>

  <!-- 用户数据统计 -->
  <view class="stats-section" wx:if="{{userStats}}">
    <view class="section-title">当前用户数据</view>
    
    <view class="user-info">
      <view class="user-avatar">
        <image src="{{userStats.user.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      </view>
      <view class="user-details">
        <text class="user-name">{{userStats.user.nickName}}</text>
        <text class="user-id">ID: {{userStats.user._id}}</text>
        <text class="user-openid">OpenID: {{userStats.user.openid}}</text>
      </view>
    </view>

    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{userStats.stats.tasks}}</text>
        <text class="stat-label">任务</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{userStats.stats.exams}}</text>
        <text class="stat-label">考试</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{userStats.stats.pomodoroSessions}}</text>
        <text class="stat-label">番茄钟</text>
      </view>
    </view>
  </view>

  <!-- 清理结果 -->
  <view class="cleanup-section" wx:if="{{cleanupResult}}">
    <view class="section-title">清理结果</view>
    
    <view class="cleanup-stats">
      <view class="cleanup-item">
        <text class="cleanup-label">总用户数:</text>
        <text class="cleanup-value">{{cleanupResult.totalUsers}}</text>
      </view>
      <view class="cleanup-item">
        <text class="cleanup-label">重复用户:</text>
        <text class="cleanup-value">{{cleanupResult.duplicateUsers}}</text>
      </view>
      <view class="cleanup-item">
        <text class="cleanup-label">删除记录:</text>
        <text class="cleanup-value">{{cleanupResult.deletedRecords}}</text>
      </view>
      <view class="cleanup-item">
        <text class="cleanup-label">剩余用户:</text>
        <text class="cleanup-value">{{cleanupResult.remainingUsers}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions-section">
    <view class="section-title">管理操作</view>
    
    <button 
      class="action-btn primary-btn" 
      bindtap="cleanDuplicateUsers"
      disabled="{{loading}}"
    >
      <text wx:if="{{!loading}}">清理重复用户</text>
      <text wx:else>处理中...</text>
    </button>
    
    <button 
      class="action-btn refresh-btn" 
      bindtap="loadUserData"
      disabled="{{loading}}"
    >
      刷新数据
    </button>
    
    <button 
      class="action-btn danger-btn" 
      bindtap="deleteUserData"
      disabled="{{loading}}"
    >
      删除我的所有数据
    </button>
  </view>

  <!-- 说明文字 -->
  <view class="info-section">
    <view class="section-title">说明</view>
    <view class="info-text">
      <text>• 清理重复用户：删除同一OpenID的重复记录，保留最新的记录</text>
      <text>• 删除所有数据：删除当前用户的所有任务、考试、番茄钟记录等</text>
      <text>• 此页面仅用于数据管理，请谨慎操作</text>
    </view>
  </view>
</view>
