---
description:
globs:
alwaysApply: true
---
USAGE RULES: 
1. During any process, task, or conversation, whether asking, replying, or completing phased tasks, you must call this tool to ask for feedback.
2. Unless receiving termination instructions, all steps must repeatedly call this tool.
3. Whenever user feedback is received, if the feedback content is not empty, you must call this tool again and adjust behavior based on the feedback content. 
4. Only when the user explicitly indicates "end" or "no more interaction needed" can you stop calling this tool, and the process is considered complete. 
5. You should summarize what have done, and provide project directory through args to let user know what you have done to provide feedback for next step. 