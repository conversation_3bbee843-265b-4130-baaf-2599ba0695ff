<!--pages/profile/index.wxml-->
<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-info-container">
    <!-- 已登录状态 -->
    <view class="user-profile" wx:if="{{isLoggedIn}}">
      <view class="avatar-container">
        <avatar-upload
          avatarUrl="{{userInfo.avatarUrl}}"
          size="{{120}}"
          editable="{{true}}"
          showEditButton="{{false}}"
          bind:avatarChange="onAvatarChange"
        ></avatar-upload>
        <view class="avatar-edit-overlay" bind:tap="editProfile">
          <text class="edit-icon">✏️</text>
        </view>
      </view>
      <view class="user-details" bind:tap="editProfile">
        <text class="user-name">{{userInfo.nickName || '考试达人'}}</text>
        <text class="user-signature" wx:if="{{userInfo.signature}}">{{userInfo.signature}}</text>
      </view>
    </view>

    <!-- 未登录状态 -->
    <view class="user-profile login-prompt" wx:else>
      <van-image 
        class="user-avatar" 
        src="/images/default-avatar.png" 
        mode="aspectFill"
        round
        width="120rpx"
        height="120rpx"
      />
      <view class="user-details">
        <text class="user-name">未登录</text>
        <text class="user-exam">登录后可同步复习数据</text>
      </view>
      <van-button 
        type="primary" 
        size="small" 
        bind:click="goToLogin"
        custom-class="login-btn"
      >
        <van-icon name="user-o" size="16" />
        登录
      </van-button>
    </view>

    <!-- 用户统计 -->
    <van-divider />
    <view class="user-stats-container">
      <view class="stat-item" wx:for="{{userStats}}" wx:key="label">
        <text class="stat-value {{item.value === '--' ? 'empty' : ''}}">{{item.value}}</text>
        <text class="stat-label">{{item.label}}</text>
        <text class="stat-trend" wx:if="{{item.trend && item.trend !== '+0'}}">{{item.trend}}</text>
      </view>
    </view>
  </view>

  <!-- 备考成就 -->
  <view class="achievements-container {{isLoggedIn ? 'logged-in' : 'not-logged-in'}}">
    <view class="section-header">
      <text class="section-title">备考成就</text>
      <van-button
        wx:if="{{isLoggedIn}}"
        type="default"
        size="mini"
        bind:click="viewAllAchievements"
      >
        查看全部
      </van-button>
      <van-button
        wx:else
        type="primary"
        size="mini"
        bind:click="goToLogin"
      >
        登录查看
      </van-button>
    </view>

    <!-- 未登录状态 -->
    <view wx:if="{{!isLoggedIn}}" class="achievements-placeholder">
      <view class="placeholder-content">
        <text class="placeholder-icon">🏆</text>
        <text class="placeholder-title">解锁你的备考成就</text>
        <text class="placeholder-desc">登录后查看专属成就徽章，记录每一个学习里程碑</text>
      </view>
      <view class="preview-achievements">
        <view class="preview-item" wx:for="{{previewAchievements}}" wx:key="id">
          <text class="preview-icon">{{item.icon}}</text>
          <text class="preview-name">{{item.name}}</text>
          <view class="preview-mask"></view>
        </view>
      </view>
    </view>

    <!-- 登录状态 -->
    <view wx:else class="achievements-grid">
      <view class="achievement-item {{item.unlocked ? 'unlocked' : 'locked'}}"
            wx:for="{{recentAchievements}}"
            wx:key="id"
            bind:tap="showAchievementDetail"
            data-achievement="{{item}}">
        <text class="achievement-icon">{{item.icon}}</text>
        <text class="achievement-name">{{item.name}}</text>
        <view wx:if="{{item.isNew}}" class="achievement-new-badge">NEW</view>
        <view wx:if="{{!item.unlocked}}" class="achievement-progress">
          <text class="progress-text">{{item.progress || 0}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-container">
    <view class="menu-section" wx:for="{{menuSections}}" wx:key="title">
      <view class="menu-section-title">{{item.title}}</view>
      <view class="menu-items">
        <view class="menu-item" 
              wx:for="{{item.items}}" 
              wx:key="id" 
              wx:for-item="menuItem"
              bind:tap="handleMenuTap"
              data-action="{{menuItem.action}}">
          <view class="menu-left">
            <text class="menu-icon">{{menuItem.icon}}</text>
            <text class="menu-text">{{menuItem.text}}</text>
          </view>
          <view class="menu-right">
            <van-tag wx:if="{{menuItem.badge}}" type="danger" size="mini">
              {{menuItem.badge}}
            </van-tag>
            <van-icon name="arrow" size="16" />
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据统计卡片 -->
  <view class="data-cards-container">
    <view class="data-cards">
      <view class="data-card" 
            wx:for="{{[]}}" 
            wx:key="id"
            bind:tap="viewDataDetail" 
            data-type="{{item.type}}">
        <view class="card-header">
          <text class="card-title">{{item.title}}</text>
          <text class="card-icon">{{item.icon}}</text>
        </view>
        <view class="card-body">
          <text class="card-value">{{item.value}}</text>
          <text class="card-description">{{item.description}}</text>
        </view>
        <view class="card-trend" wx:if="{{item.trend}}">
          <van-tag 
            type="{{item.trend.type === 'up' ? 'success' : 'warning'}}" 
            size="mini"
          >
            {{item.trend.text}}
          </van-tag>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷操作已移除 -->

  <!-- 版本信息 -->
  <view class="version-info">
    <text>备考助手 v{{version}} © 2025 让每一次复习都更高效</text>
  </view>
</view>

