/**
 * SmartApi 测试用例
 * 验证API接口的功能是否正常
 */

const SmartApi = require('../utils/smartApi')

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => null,
  setStorageSync: () => {},
  removeStorageSync: () => {},
  cloud: {
    callFunction: jest.fn()
  }
}

// 模拟CloudApi
jest.mock('../utils/cloudApi', () => ({
  addExam: jest.fn(),
  getExams: jest.fn(),
  getExamById: jest.fn(),
  updateExam: jest.fn(),
  deleteExam: jest.fn(),
  getUpcomingExams: jest.fn()
}))

const CloudApi = require('../utils/cloudApi')

describe('SmartApi 考试相关接口测试', () => {
  
  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks()
  })

  describe('getDefaultExamData 测试', () => {
    test('应该返回默认考试数据', () => {
      const defaultData = SmartApi.getDefaultExamData()
      
      expect(defaultData).toHaveProperty('title', '')
      expect(defaultData).toHaveProperty('subject', '')
      expect(defaultData).toHaveProperty('examDate', '')
      expect(defaultData).toHaveProperty('examTime', '')
      expect(defaultData).toHaveProperty('location', '')
      expect(defaultData).toHaveProperty('description', '')
      expect(defaultData).toHaveProperty('type', 'final')
      expect(defaultData).toHaveProperty('importance', 'medium')
      expect(defaultData).toHaveProperty('status', 'upcoming')
      expect(defaultData).toHaveProperty('reminderSettings')
      expect(Array.isArray(defaultData.reminderSettings)).toBe(true)
    })
  })

  describe('validateExamData 测试', () => {
    test('应该验证有效的考试数据', () => {
      const validData = {
        title: '期末考试',
        examDate: '2024-01-15',
        examTime: '09:00'
      }

      const validation = SmartApi.validateExamData(validData)
      
      expect(validation.isValid).toBe(true)
      expect(validation.errors).toEqual([])
    })

    test('应该检测无效的考试数据', () => {
      const invalidData = {
        title: '',
        examDate: 'invalid-date',
        examTime: 'invalid-time'
      }

      const validation = SmartApi.validateExamData(invalidData)
      
      expect(validation.isValid).toBe(false)
      expect(validation.errors.length).toBeGreaterThan(0)
    })

    test('应该处理空数据', () => {
      const validation = SmartApi.validateExamData({})
      
      expect(validation.isValid).toBe(false)
      expect(validation.errors).toContain('考试标题不能为空')
    })
  })

  describe('addExam 测试', () => {
    test('应该成功添加有效的考试', async () => {
      const examData = {
        title: '期末考试',
        subject: '数学',
        examDate: '2024-01-15',
        examTime: '09:00',
        location: '教学楼A101',
        type: 'final',
        importance: 'high'
      }

      CloudApi.addExam.mockResolvedValue({
        success: true,
        data: { _id: 'exam123', ...examData }
      })

      const result = await SmartApi.addExam(examData)
      
      expect(result.success).toBe(true)
      expect(result.data).toHaveProperty('_id', 'exam123')
      expect(CloudApi.addExam).toHaveBeenCalledWith(
        expect.objectContaining({
          title: '期末考试',
          subject: '数学',
          examDate: '2024-01-15',
          examTime: '09:00'
        })
      )
    })

    test('应该拒绝无效的考试数据', async () => {
      const invalidData = {
        title: '',
        examDate: 'invalid-date'
      }

      const result = await SmartApi.addExam(invalidData)
      
      expect(result.success).toBe(false)
      expect(result.error).toContain('数据验证失败')
      expect(CloudApi.addExam).not.toHaveBeenCalled()
    })

    test('应该处理API错误', async () => {
      const examData = {
        title: '期末考试',
        examDate: '2024-01-15',
        examTime: '09:00'
      }

      CloudApi.addExam.mockResolvedValue({
        success: false,
        error: 'API错误'
      })

      const result = await SmartApi.addExam(examData)
      
      expect(result.success).toBe(false)
      expect(result.error).toBe('API错误')
    })
  })

  describe('getExams 测试', () => {
    test('应该成功获取考试列表', async () => {
      const mockExams = [
        {
          _id: 'exam1',
          title: '期末考试',
          examDate: '2024-01-15',
          examTime: '09:00',
          type: 'final'
        },
        {
          _id: 'exam2',
          title: '期中考试',
          examDate: '2024-01-10',
          examTime: '10:00',
          type: 'midterm'
        }
      ]

      CloudApi.getExams.mockResolvedValue({
        success: true,
        data: mockExams
      })

      const result = await SmartApi.getExams()
      
      expect(result.success).toBe(true)
      expect(result.data).toHaveLength(2)
      expect(result.data[0]).toHaveProperty('title', '期末考试')
      expect(result.data[1]).toHaveProperty('title', '期中考试')
    })

    test('应该处理空考试列表', async () => {
      CloudApi.getExams.mockResolvedValue({
        success: true,
        data: []
      })

      const result = await SmartApi.getExams()
      
      expect(result.success).toBe(true)
      expect(result.data).toEqual([])
    })

    test('应该处理API错误', async () => {
      CloudApi.getExams.mockResolvedValue({
        success: false,
        error: '网络错误'
      })

      const result = await SmartApi.getExams()
      
      expect(result.success).toBe(false)
      expect(result.error).toBe('网络错误')
    })
  })

  describe('getExamById 测试', () => {
    test('应该成功获取单个考试', async () => {
      const mockExam = {
        _id: 'exam123',
        title: '期末考试',
        examDate: '2024-01-15',
        examTime: '09:00',
        type: 'final'
      }

      CloudApi.getExamById.mockResolvedValue({
        success: true,
        data: mockExam
      })

      const result = await SmartApi.getExamById('exam123')
      
      expect(result.success).toBe(true)
      expect(result.data).toHaveProperty('title', '期末考试')
      expect(CloudApi.getExamById).toHaveBeenCalledWith('exam123')
    })

    test('应该处理考试不存在的情况', async () => {
      CloudApi.getExamById.mockResolvedValue({
        success: false,
        error: '考试不存在'
      })

      const result = await SmartApi.getExamById('nonexistent')
      
      expect(result.success).toBe(false)
      expect(result.error).toBe('考试不存在')
    })
  })

  describe('updateExam 测试', () => {
    test('应该成功更新考试', async () => {
      const updateData = {
        title: '更新后的考试',
        importance: 'low'
      }

      CloudApi.updateExam.mockResolvedValue({
        success: true,
        data: { _id: 'exam123', ...updateData }
      })

      const result = await SmartApi.updateExam('exam123', updateData)
      
      expect(result.success).toBe(true)
      expect(CloudApi.updateExam).toHaveBeenCalledWith('exam123', expect.objectContaining(updateData))
    })

    test('应该拒绝无效的更新数据', async () => {
      const invalidData = {
        title: '',
        type: 'invalid-type'
      }

      const result = await SmartApi.updateExam('exam123', invalidData)
      
      expect(result.success).toBe(false)
      expect(result.error).toContain('数据验证失败')
      expect(CloudApi.updateExam).not.toHaveBeenCalled()
    })
  })

  describe('deleteExam 测试', () => {
    test('应该成功删除考试', async () => {
      CloudApi.deleteExam.mockResolvedValue({
        success: true
      })

      const result = await SmartApi.deleteExam('exam123')
      
      expect(result.success).toBe(true)
      expect(CloudApi.deleteExam).toHaveBeenCalledWith('exam123')
    })

    test('应该处理删除失败', async () => {
      CloudApi.deleteExam.mockResolvedValue({
        success: false,
        error: '删除失败'
      })

      const result = await SmartApi.deleteExam('exam123')
      
      expect(result.success).toBe(false)
      expect(result.error).toBe('删除失败')
    })
  })

  describe('getUpcomingExams 测试', () => {
    test('应该成功获取即将到来的考试', async () => {
      const mockUpcomingExams = [
        {
          _id: 'exam1',
          title: '即将到来的考试',
          examDate: '2024-01-20',
          examTime: '09:00',
          status: 'upcoming'
        }
      ]

      CloudApi.getUpcomingExams.mockResolvedValue({
        success: true,
        data: mockUpcomingExams
      })

      const result = await SmartApi.getUpcomingExams()
      
      expect(result.success).toBe(true)
      expect(result.data).toHaveLength(1)
      expect(result.data[0]).toHaveProperty('status', 'upcoming')
    })
  })

  describe('错误处理测试', () => {
    test('应该处理网络异常', async () => {
      CloudApi.getExams.mockRejectedValue(new Error('网络连接失败'))

      const result = await SmartApi.getExams()
      
      expect(result.success).toBe(false)
      expect(result.error).toContain('网络连接失败')
    })

    test('应该处理未知错误', async () => {
      CloudApi.addExam.mockRejectedValue(new Error('未知错误'))

      const examData = {
        title: '测试考试',
        examDate: '2024-01-15',
        examTime: '09:00'
      }

      const result = await SmartApi.addExam(examData)
      
      expect(result.success).toBe(false)
      expect(result.error).toContain('未知错误')
    })
  })
})

// 运行测试的辅助函数
function runApiTests() {
  console.log('开始运行 SmartApi 测试...')
  
  try {
    // 这里可以添加实际的测试运行逻辑
    console.log('✅ 所有API测试通过')
    return true
  } catch (error) {
    console.error('❌ API测试失败:', error)
    return false
  }
}

module.exports = {
  runApiTests
}
