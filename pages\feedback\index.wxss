/* pages/feedback/index.wxss */

/* 容器 */
.container {
  background-color: #F8F9FA;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 内容区域 */
.content {
  padding: 32rpx 32rpx 0 32rpx;
}

/* Vant组件自定义样式 */
.section-group {
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

/* 反馈类型样式 */
.feedback-types {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding: 16rpx 0;
}

.type-radio {
  margin-bottom: 16rpx;
}

/* 提交按钮样式 */
.submit-section {
  padding: 24rpx 32rpx;
}

.submit-button {
  width: 100% !important;
  border-radius: 12rpx !important;
}

/* 反馈历史样式 */
.history-item {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F8F9FA;
}

.history-item:last-child {
  border-bottom: none;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.history-type {
  font-size: 24rpx;
  color: #1890FF;
  font-weight: 500;
}

.history-date {
  font-size: 22rpx;
  color: #999999;
}

.history-content {
  margin-bottom: 12rpx;
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
}

.history-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
  flex: 1;
}

.expand-link {
  font-size: 22rpx;
  color: #1890FF;
  text-decoration: underline;
  cursor: pointer;
  white-space: nowrap;
}

.history-status {
  text-align: right;
}

/* 图标样式 */
.contact-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.contact-action {
  font-size: 24rpx;
  color: #1890FF;
}
