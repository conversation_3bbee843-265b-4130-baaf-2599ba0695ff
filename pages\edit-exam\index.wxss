/* pages/edit-exam/index.wxss */
/* 复用添加考试页面的样式，并添加编辑特有的样式 */

/* 容器 */
.container {
  background-color: #F8F9FA;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.header {
  background-color: #FFFFFF;
  padding: 40rpx 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  display: block;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: #7F8C8D;
}

/* 表单区域 */
.form-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.add-subject-btn {
  background-color: #1890FF;
  color: #FFFFFF;
  border: none;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
  font-weight: 500;
}

.form-input {
  width: 100%;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
}

.form-input:focus {
  border-color: #1890FF;
  background-color: #FFFFFF;
}

.form-textarea {
  width: 100%;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
  min-height: 120rpx;
}

.form-textarea:focus {
  border-color: #1890FF;
  background-color: #FFFFFF;
}

/* 考试类型选项 */
.type-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12rpx;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 12rpx;
  padding: 16rpx;
  transition: all 0.3s ease;
}

.type-option.active {
  background-color: #E6F7FF;
  border-color: #1890FF;
}

.type-icon {
  font-size: 32rpx;
}

.type-text {
  font-size: 22rpx;
  color: #333333;
}

/* 时间选择器 */
.picker-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
}

.picker-icon {
  font-size: 24rpx;
  color: #999999;
}

.time-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.time-picker {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
}

.time-separator {
  font-size: 24rpx;
  color: #666666;
}

/* 科目列表 */
.subjects-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.subject-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
}

.subject-input {
  flex: 2;
  background-color: #FFFFFF;
  border: 1rpx solid #E9ECEF;
  border-radius: 6rpx;
  padding: 8rpx 12rpx;
  font-size: 24rpx;
  color: #333333;
}

.subject-score, .subject-target {
  flex: 1;
  background-color: #FFFFFF;
  border: 1rpx solid #E9ECEF;
  border-radius: 6rpx;
  padding: 8rpx 12rpx;
  font-size: 24rpx;
  color: #333333;
  text-align: center;
}

.remove-subject-btn {
  width: 48rpx;
  height: 48rpx;
  background-color: #FF4D4F;
  color: #FFFFFF;
  border: none;
  border-radius: 50%;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.empty-subjects {
  text-align: center;
  padding: 40rpx 0;
}

.empty-text {
  font-size: 24rpx;
  color: #999999;
}

/* 重要程度选项 */
.importance-options {
  display: flex;
  gap: 16rpx;
}

.importance-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 12rpx;
  padding: 16rpx;
  transition: all 0.3s ease;
}

.importance-option.active {
  background-color: #E6F7FF;
  border-color: #1890FF;
}

.importance-icon {
  font-size: 32rpx;
}

.importance-text {
  font-size: 22rpx;
  color: #333333;
}

/* 状态选项 */
.status-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.status-option {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background-color: #F5F5F5;
  color: #666666;
  border: 1rpx solid #E9ECEF;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.status-option.active {
  background-color: #52C41A;
  color: #FFFFFF;
  border-color: #52C41A;
}

.status-icon {
  font-size: 20rpx;
}

.status-text {
  font-size: 22rpx;
}

/* 开关项 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-label {
  font-size: 26rpx;
  color: #333333;
}

/* 提醒频率 */
.reminder-frequency {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.frequency-option {
  background-color: #F5F5F5;
  color: #666666;
  border: 1rpx solid #E9ECEF;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.frequency-option.active {
  background-color: #1890FF;
  color: #FFFFFF;
  border-color: #1890FF;
}

/* 操作按钮 */
.form-actions {
  display: flex;
  gap: 12rpx;
  padding: 32rpx;
  background-color: #FFFFFF;
  position: sticky;
  bottom: 0;
  border-top: 1rpx solid #F0F0F0;
}

.cancel-btn {
  flex: 1;
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx;
  font-size: 26rpx;
}

.delete-btn {
  flex: 1;
  background-color: #FF4D4F;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx;
  font-size: 26rpx;
}

.submit-btn {
  flex: 2;
  background-color: #52C41A;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx;
  font-size: 26rpx;
}

.cancel-btn:active {
  background-color: #E6E6E6;
}

.delete-btn:active {
  background-color: #D9363E;
}

.submit-btn:active {
  background-color: #389E0D;
}

/* 科目管理样式 */
.subject-list {
  padding: 20rpx 32rpx;
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.subject-item {
  margin-bottom: 16rpx;
}

/* 添加科目图标样式 */
.add-subject-icon {
  padding: 8rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.add-subject-icon.active {
  background-color: rgba(25, 137, 250, 0.1);
}

.add-subject-icon.active:active {
  background-color: rgba(25, 137, 250, 0.2);
  transform: scale(0.95);
}

.add-subject-icon.disabled {
  opacity: 0.5;
}

/* 重要程度选择样式 */
.importance-tabs {
  padding: 0 32rpx 20rpx;
  background: #fff;
}

/* 提醒设置样式 */
.reminder-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 20rpx 32rpx;
  background: #fff;
}

.reminder-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.reminder-text {
  font-size: 28rpx;
  color: #333;
}

/* 底部操作按钮 */
.bottom-actions {
  padding: 40rpx 20rpx;
  display: flex;
  gap: 20rpx;
}

.bottom-actions .van-button {
  flex: 1;
}

/* 编辑特有样式 */
.edit-info {
  background-color: #FFF8E1;
  border: 1rpx solid #FFE082;
  border-radius: 8rpx;
  padding: 16rpx;
  margin: 20rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

/* 危险操作区域 */
.danger-zone {
  margin: 40rpx 0;
}

.danger-cell {
  --van-cell-background-color: #fff2f0;
  --van-cell-border-color: #ffccc7;
  --van-cell-text-color: #ff4444;
  --van-cell-label-color: #8c8c8c;
}

.edit-info-icon {
  font-size: 32rpx;
  color: #FF8F00;
}

.edit-info-text {
  font-size: 24rpx;
  color: #E65100;
  flex: 1;
}

/* 删除按钮特殊样式 */
.delete-btn {
  background-color: #FF4444 !important;
  border-color: #FF4444 !important;
}

.delete-btn:hover {
  background-color: #CC0000 !important;
  border-color: #CC0000 !important;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.status-upcoming {
  background-color: #52C41A;
}

.status-ongoing {
  background-color: #1890FF;
}

.status-finished {
  background-color: #8C8C8C;
}

/* 表单验证错误样式 */
.form-error {
  color: #FF4444;
  font-size: 22rpx;
  margin-top: 8rpx;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: #FFFFFF;
  padding: 40rpx;
  border-radius: 12rpx;
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #2C3E50;
  margin-top: 20rpx;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .header {
    padding: 32rpx 24rpx;
  }
  
  .page-title {
    font-size: 32rpx;
  }
  
  .bottom-actions {
    padding: 32rpx 16rpx;
    flex-direction: column;
  }
  
  .bottom-actions .van-button {
    width: 100%;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 高亮变化的字段 */
.field-changed {
  border-color: #52C41A !important;
  background-color: #F6FFED !important;
}

.field-changed .van-field__label {
  color: #52C41A !important;
}

/* 危险操作确认样式 */
.danger-action {
  background-color: #FFF2F0;
  border: 1rpx solid #FFCCC7;
  border-radius: 8rpx;
  padding: 20rpx;
  margin: 20rpx;
}

.danger-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF4444;
  margin-bottom: 12rpx;
}

.danger-desc {
  font-size: 24rpx;
  color: #8C8C8C;
  line-height: 1.5;
}

/* 保存状态指示 */
.save-status {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  background-color: #52C41A;
  color: #FFFFFF;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  z-index: 1000;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease-in-out;
}

.save-status.show {
  opacity: 1;
  transform: translateX(0);
}

.save-status.error {
  background-color: #FF4444;
}
