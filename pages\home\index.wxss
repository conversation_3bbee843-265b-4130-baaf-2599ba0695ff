/* pages/home/<USER>/

/* 页面容器 */
.page {
  height: 100vh;
  background-color: #f5f6fa;
}

.container {
  padding: 24rpx 20rpx 32rpx 20rpx;
  min-height: calc(100vh - 160rpx); /* 减去导航栏高度 */
}

/* 底部间距，确保内容不被导航栏遮挡 */
.bottom-spacer {
  height: 160rpx; /* 导航栏高度 + 安全区域 */
}

/* 优化van-cell-group样式 */
van-cell-group {
  margin: 24rpx 0;
  border-radius: 20rpx !important;
  overflow: hidden !important;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08) !important;
  --van-cell-vertical-padding: 14rpx;
  --van-cell-horizontal-padding: 24rpx;
}

/* 优化van-cell样式 */
van-cell {
  --van-cell-vertical-padding: 14rpx !important;
  --van-cell-horizontal-padding: 24rpx !important;
}

/* 优化van-divider样式 */
van-divider {
  margin: 32rpx 0 !important;
}

/* 自定义任务cell样式 */
.custom-task-cell {
  border-bottom: 1rpx solid #f5f5f5 !important;
  margin-bottom: 0 !important;
}

/* 悬浮按钮样式已移至组件内部 */

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  gap: 24rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 考试倒计时卡片 */
.countdown-card {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 107, 107, 0.3);
  position: relative;
  overflow: hidden;
}

.countdown-content {
  text-align: center;
  color: #ffffff;
}

.countdown-days {
  display: block;
  font-size: 96rpx;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.countdown-label {
  display: block;
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
  opacity: 0.95;
}

.next-exam {
  display: block;
  font-size: 26rpx;
  opacity: 0.8;
  line-height: 1.4;
}

/* 无考试状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 48rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.empty-desc {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

/* 任务状态图标 */
.task-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 52rpx;
  height: 52rpx;
  margin-right: 8rpx;
}

.status-icon {
  font-size: 36rpx;
  line-height: 1;
  padding: 6rpx;
  margin: -6rpx;
  transition: transform 0.2s ease;
}

.status-icon:active {
  transform: scale(1.1);
}

/* 任务状态选框 */
.task-checkbox {
  transform: scale(1.3);
}

/* 任务主要内容 */
.task-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding: 4rpx 0;
}

/* 任务标题行 */
.task-title-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.task-title {
  font-size: 37rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.3;
  flex-shrink: 0;
  max-width: calc(100% - 100rpx);
}

.task-title.completed {
  color: #999999;
  text-decoration: line-through;
}

.subject-tag {
  flex-shrink: 0;
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  border-radius: 8rpx;
  font-weight: 500;
  margin-left: 8rpx;
}

/* 考试名称行样式 */
.task-exam-name {
  font-size: 24rpx;
  color: #666666;
  margin-top: 4rpx;
  margin-bottom: 4rpx;
  line-height: 1.3;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 任务元信息行 */
.task-meta-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.due-time {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.3;
  flex-shrink: 0;
  font-weight: 500;
}

.priority-tag {
  flex-shrink: 0;
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

/* 检查点进度预览行样式 */
.checkpoint-preview-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8rpx;
  padding: 8rpx 12rpx;
  background-color: rgba(24, 144, 255, 0.05);
  border-radius: 6rpx;
  border: 1rpx solid rgba(24, 144, 255, 0.1);
  transition: all 0.2s ease;
}

.checkpoint-preview-line:active {
  background-color: rgba(24, 144, 255, 0.1);
  border-color: rgba(24, 144, 255, 0.3);
  transform: scale(0.98);
}

.checkpoint-progress-wrapper {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex: 1;
}

.checkpoint-progress-bar {
  flex: 1;
  height: 3rpx;
  background-color: #f0f0f0;
  border-radius: 2rpx;
  overflow: hidden;
  max-width: 100rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 2rpx;
  transition: width 0.3s ease;
}

.checkpoint-stats {
  font-size: 20rpx;
  color: #666666;
  white-space: nowrap;
}

.expand-icon {
  color: #999999;
  transition: all 0.3s ease;
}

.checkpoint-preview-line:active .expand-icon {
  color: #1890ff;
  transform: translateX(2rpx) scale(1.1);
}

/* 添加检查点引导样式 */
.add-checkpoint-guide {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 12rpx;
  margin-top: 8rpx;
  border-radius: 6rpx;
  border: 1rpx dashed rgba(24, 144, 255, 0.3);
  background-color: rgba(24, 144, 255, 0.05);
  transition: all 0.2s ease;
}

.add-checkpoint-guide:active {
  background-color: rgba(24, 144, 255, 0.1);
  border-color: rgba(24, 144, 255, 0.5);
  transform: scale(0.98);
}

.guide-text {
  flex: 1;
  font-size: 20rpx;
  color: #1890ff;
  font-weight: 500;
}

.guide-arrow {
  opacity: 0.6;
  transition: all 0.3s ease;
}

.add-checkpoint-guide:active .guide-arrow {
  opacity: 1;
  transform: translateX(2rpx);
}

/* 任务操作按钮 */
.task-action-btn {
  width: 80rpx !important;
  min-width: 80rpx !important;
  max-width: 80rpx !important;
  border-radius: 12rpx !important;
  font-weight: 500 !important;
  font-size: 24rpx !important;
  padding: 0 !important;
}

/* 更强的选择器确保按钮宽度生效 */
van-cell .task-action-btn,
.van-cell .task-action-btn {
  width: 80rpx !important;
  min-width: 80rpx !important;
  max-width: 80rpx !important;
  border-radius: 12rpx !important;
  font-weight: 500 !important;
  font-size: 24rpx !important;
  padding: 0 !important;
}

/* 针对van-button内部的样式覆盖 */
.task-action-btn .van-button,
.task-action-btn::after {
  width: 80rpx !important;
  min-width: 80rpx !important;
  max-width: 80rpx !important;
}



/* 任务信息行 */
.task-info-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.priority-tag {
  flex-shrink: 0;
}

.time-info {
  flex: 1;
  font-size: 24rpx;
  line-height: 1.3;
  min-width: 0;
}

.time-info.today {
  color: #ff4d4f;
  font-weight: 500;
}

.time-info.tomorrow {
  color: #fa8c16;
  font-weight: 500;
}

.time-info.later {
  color: #666666;
}

.time-info.completed {
  color: #52c41a;
}

/* 任务操作 */
.task-action {
  display: flex;
  align-items: center;
}

/* 添加任务入口 */
.add-icon {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx dashed #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  font-size: 24rpx;
  color: #1890ff;
  font-weight: bold;
  line-height: 1;
}

.add-text {
  font-size: 28rpx;
  color: #1890ff;
  font-weight: 500;
}

/* 空状态 */
.empty-tasks {
  text-align: center;
  padding: 80rpx 48rpx;
  color: #666666;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  margin-bottom: 48rpx;
  line-height: 1.4;
}

/* 考试板块样式 */
.exam-section {
  margin: 24rpx 0 32rpx 0;
}

.exam-swiper {
  /* 高度通过内联样式动态设置 */
  min-height: 400rpx;
}

.exam-card {
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  margin: 0 4rpx;
  padding: 32rpx 28rpx;
  display: flex;
  flex-direction: column;
  /* 使用自适应高度，让内容自然撑开 */
  min-height: 350rpx;
  position: relative;
}

.exam-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #1890ff 0%, #52c41a 100%);
}



.exam-info {
  flex: 1;
  padding-bottom: 8rpx;
}

.exam-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.exam-countdown {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #1890ff;
}

.countdown-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.countdown-number {
  font-size: 40rpx;
  font-weight: 700;
  color: #1890FF;
  margin: 0 6rpx;
}

.exam-progress {
  margin-bottom: 12rpx;
}

.progress-text {
  font-size: 22rpx;
  color: #999999;
  margin-top: 8rpx;
  display: block;
}

/* 科目信息区域 */
.subjects-section {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e8e8e8;
}

.subjects-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.subjects-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
}

.subjects-count {
  font-size: 20rpx;
  color: #999999;
  margin-left: 8rpx;
}

.subjects-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.subject-item {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  background: #ffffff;
  border-radius: 8rpx;
  border: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}

.subject-item:active {
  background: #f5f5f5;
  transform: scale(0.98);
}

.subject-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.subject-icon {
  font-size: 20rpx;
  margin-right: 8rpx;
  width: 24rpx;
  text-align: center;
}

.subject-name {
  font-size: 22rpx;
  color: #333333;
  font-weight: 500;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.subject-progress {
  font-size: 20rpx;
  color: #666666;
  margin-left: auto;
  margin-right: 8rpx;
  font-weight: 600;
}

.subject-progress-bar {
  width: 60rpx;
  height: 6rpx;
  background: #f0f0f0;
  border-radius: 3rpx;
  margin-right: 8rpx;
  overflow: hidden;
}

.subject-progress-fill {
  height: 100%;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.subject-tasks {
  font-size: 18rpx;
  color: #999999;
  flex-shrink: 0;
  min-width: 40rpx;
  text-align: right;
}

.divider {
  height: 1rpx;
  background: #f0f0f0;
  margin: 12rpx 0;
}

/* 搭子小组区域 - 参考科目进度样式 */
.partners-section {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e8e8e8;
}

.partners-content {
  display: flex;
  flex-direction: column;
}

.partners-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.partners-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
}

.partners-count {
  font-size: 20rpx;
  color: #999999;
  margin-left: 8rpx;
}

.partners-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.partner-item {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  background: #ffffff;
  border-radius: 8rpx;
  border: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}

.partner-item:active {
  background: #f5f5f5;
  transform: scale(0.98);
}

.partner-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.partner-avatar {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.partner-name {
  font-size: 22rpx;
  color: #333333;
  font-weight: 500;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.partner-progress {
  font-size: 20rpx;
  color: #666666;
  margin-left: auto;
  margin-right: 8rpx;
  font-weight: 600;
}

.partner-progress-bar {
  width: 60rpx;
  height: 6rpx;
  background: #f0f0f0;
  border-radius: 3rpx;
  margin-right: 8rpx;
  overflow: hidden;
}

.partner-progress-fill {
  height: 100%;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.partner-tasks {
  font-size: 18rpx;
  color: #999999;
  flex-shrink: 0;
  min-width: 40rpx;
  text-align: right;
}

/* 空状态样式 */
.partners-empty {
  display: flex;
  flex-direction: column;
}

.partners-create {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 12rpx;
  background: #ffffff;
  border-radius: 8rpx;
  border: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}

.partners-create:active {
  background: #f5f5f5;
  transform: scale(0.98);
}

.create-guide {
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 4rpx;
}

.create-action {
  font-size: 20rpx;
  color: #1890ff;
  font-weight: 500;
}



/* 有考试但无今日任务的引导状态 */
.empty-tasks {
  padding: 60rpx 40rpx;
  text-align: center;
  background: #ffffff;
  border-radius: 20rpx;
  margin: 24rpx 0;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
}

.empty-tasks-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.empty-tasks .empty-icon {
  font-size: 80rpx;
  margin-bottom: 8rpx;
}

.empty-tasks .empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.empty-tasks .empty-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  max-width: 500rpx;
}

.empty-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  width: 100%;
  max-width: 400rpx;
}

/* 自定义悬浮按钮位置调整 */
.custom-fab-button {
  bottom: 140rpx !important;  /* 距离底部140rpx，避开导航栏 */
  right: 40rpx !important;    /* 距离右边40rpx */
}

/* 悬浮按钮 */
.fab-wrapper {
  position: fixed;
  bottom: 140rpx;
  right: 32rpx;
  z-index: 1000;
}

.fab-menu {
  position: absolute;
  bottom: 120rpx;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  animation: slideUp 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fab-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  animation: fadeInScale 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fab-label {
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  font-size: 26rpx;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  white-space: nowrap;
  backdrop-filter: blur(10rpx);
}

.fab-button {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fab-button:active {
  transform: scale(0.95);
}

.fab-main {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background: #1890ff;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #ffffff;
  font-weight: 300;
  box-shadow: 0 12rpx 32rpx rgba(24, 144, 255, 0.4);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.fab-main::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  border-radius: 50%;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.fab-main:active::before {
  opacity: 1;
}

.fab-main.expanded {
  transform: rotate(45deg);
  background: #ff4757;
}

.fab-main:active {
  transform: scale(0.95);
}

.fab-main.expanded:active {
  transform: rotate(45deg) scale(0.95);
}

/* 动画 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 考试倒计时区域 */
.countdown-section {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;
  color: #ffffff;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  transition: all 0.2s ease;
}

.countdown-section:active {
  transform: scale(0.98);
}

.countdown-number {
  font-size: 80rpx;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 12rpx;
}

.countdown-text {
  font-size: 32rpx;
  margin-bottom: 16rpx;
  opacity: 0.9;
}

.next-exam-info {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 无考试状态 */
.no-exam-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 80rpx 48rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.create-exam-btn {
  background: #1890ff;
  color: #ffffff;
  border: none;
  border-radius: 24rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  font-weight: 600;
}

/* 今日任务区域 */
.today-tasks-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.tasks-header {
  padding: 32rpx 32rpx 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 0;
}

.tasks-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

/* 任务列表 */
.tasks-list {
  padding: 24rpx 32rpx 32rpx 32rpx;
}

.task-item {
  background: #fafafa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}

.task-item:last-child {
  margin-bottom: 0;
}

.task-item:active {
  background: #f5f5f5;
  transform: scale(0.99);
}

/* 任务第一行 */
.task-row-1 {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.subject-tag {
  font-size: 20rpx;
  color: #ffffff;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  background: #1890ff;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 1;
}

.task-status {
  flex-shrink: 0;
  margin-top: 2rpx;
}

.status-icon {
  font-size: 32rpx;
  line-height: 1;
}

.task-title-area {
  flex: 1;
  min-width: 0;
}

.task-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  word-break: break-all;
}

.task-title.completed {
  color: #999;
  text-decoration: line-through;
}

/* 任务第二行 */
.task-row-2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
  padding-left: 48rpx;
}

.task-priority {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-shrink: 0;
}

.priority-dot {
  font-size: 20rpx;
  line-height: 1;
}

.priority-dot.priority-high {
  color: #FF4D4F;
}

.priority-dot.priority-medium {
  color: #FA8C16;
}

.priority-dot.priority-low {
  color: #52C41A;
}

.priority-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 400;
}

.task-time-info {
  flex: 1;
  min-width: 0;
  margin: 0 16rpx;
}

.time-text {
  font-size: 24rpx;
  line-height: 1.3;
}

.time-text.today {
  color: #FF4D4F;
  font-weight: 500;
}

.time-text.tomorrow {
  color: #FA8C16;
  font-weight: 500;
}

.time-text.later {
  color: #666;
}

.time-text.completed {
  color: #52C41A;
}

.task-action {
  flex-shrink: 0;
}

.study-btn {
  background: #1890ff;
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  min-width: 100rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 6rpx rgba(24, 144, 255, 0.3);
}

.study-btn.completed {
  background: #f5f5f5;
  color: #999;
  box-shadow: none;
}

.study-btn:not(.completed):active {
  background: #096dd9;
  transform: scale(0.98);
}

/* 添加任务项 */
.add-task-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  gap: 20rpx;
  border-top: 2rpx dashed #e8e8e8;
  margin-top: 20rpx;
  border-radius: 0 0 16rpx 16rpx;
  background: #fafafa;
  transition: all 0.2s ease;
}

.add-task-item:active {
  background: #f0f0f0;
  transform: scale(0.99);
}

.add-task-icon {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx dashed #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: #ffffff;
}

.plus-icon {
  font-size: 24rpx;
  color: #1890ff;
  font-weight: bold;
  line-height: 1;
}

.add-task-text {
  flex: 1;
}

.add-text {
  font-size: 30rpx;
  color: #1890ff;
  font-weight: 500;
}

/* 空状态 */
.empty-tasks {
  text-align: center;
  padding: 60rpx 32rpx;
  color: #999;
}

.empty-text {
  font-size: 30rpx;
  margin-bottom: 32rpx;
  display: block;
  color: #666;
  line-height: 1.4;
}

.add-task-btn {
  background: #1890ff;
  color: #ffffff;
  border: none;
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}

.add-task-btn:active {
  background: #096dd9;
  transform: scale(0.98);
}

/* 悬浮按钮 */
.fab-container {
  position: fixed;
  bottom: 140rpx;
  right: 40rpx;
  z-index: 999;
}

.fab-menu {
  position: absolute;
  bottom: 100rpx;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  animation: slideUp 0.3s ease-out;
}

.fab-menu-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  animation: fadeIn 0.3s ease-out;
}

.fab-item-label {
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  font-size: 24rpx;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  white-space: nowrap;
}

.fab-item-button {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.fab-item-button:active {
  transform: scale(0.95);
}

.fab-main-button {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background: #1890ff;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.4);
  transition: all 0.3s ease;
}

.fab-main-button.expanded {
  transform: rotate(45deg);
}

.fab-main-button:active {
  transform: scale(0.95);
}

.fab-main-button.expanded:active {
  transform: rotate(45deg) scale(0.95);
}

/* 动画 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 容器样式 */
.container {
  background: #f8f9ff;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 整页滑动区域 */
.full-page-swiper {
  height: calc(100vh - 120rpx);
  width: 100%;
}

.page-item {
  height: 100%;
  width: 100%;
}

.page-content {
  height: 100%;
  width: 100%;
  padding: 0 24rpx;
  box-sizing: border-box;
}

/* 页面指示器 */
.page-indicator {
  text-align: center;
  padding: 20rpx 0;
}

.indicator-text {
  font-size: 24rpx;
  color: #999;
  background: #f0f0f0;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

/* 考试标题区域 */
.exam-header {
  text-align: center;
  padding: 40rpx 0 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  margin-bottom: 32rpx;
}

.exam-name {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
}

.exam-countdown {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.exam-date {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 考试统计区域 */
.exam-stats {
  padding: 0 24rpx 32rpx;
}

.progress-section {
  margin-bottom: 24rpx;
}

.progress-info {
  margin-bottom: 12rpx;
}

.progress-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.progress-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #1890ff;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 统计网格 */
.stats-grid {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 任务容器 */
.tasks-container {
  padding: 0 24rpx;
  flex: 1;
}

.tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.tasks-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.tasks-count {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

/* 任务列表 */
.tasks-list {
  margin-bottom: 32rpx;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  gap: 16rpx;
}

.task-item:last-child {
  border-bottom: none;
}

.task-status {
  flex-shrink: 0;
}

.status-icon {
  font-size: 28rpx;
}

.task-info {
  flex: 1;
  min-width: 0;
}

.task-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  line-height: 1.4;
}

.task-title.completed {
  color: #999;
  text-decoration: line-through;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.task-time {
  font-size: 22rpx;
  color: #666;
}

.task-priority {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  color: #fff;
}

.priority-high {
  background: #ff4d4f;
}

.priority-medium {
  background: #faad14;
}

.priority-low {
  background: #52c41a;
}

.task-action {
  flex-shrink: 0;
}

.study-btn {
  background: #1890ff;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  min-width: 80rpx;
}

.study-btn.completed {
  background: #f0f0f0;
  color: #999;
}

.study-btn:not(.completed):active {
  background: #096dd9;
}

/* 添加任务项 */
.add-task-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  gap: 16rpx;
  border-top: 1rpx dashed #e0e0e0;
  margin-top: 16rpx;
  cursor: pointer;
}

.add-task-item:active {
  opacity: 0.7;
}

.add-task-icon {
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  border: 2rpx dashed #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.plus-icon {
  font-size: 20rpx;
  color: #1890ff;
  font-weight: bold;
}

.add-task-text {
  flex: 1;
}

.add-text {
  font-size: 28rpx;
  color: #1890ff;
}

/* 过滤控件样式 */
.filter-controls {
  margin-bottom: 24rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.filter-section {
  margin-bottom: 16rpx;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.filter-tags {
  white-space: nowrap;
}

.filter-tag {
  display: inline-block;
  padding: 6rpx 12rpx;
  margin-right: 8rpx;
  background: #ffffff;
  border: 1rpx solid #e0e0e0;
  border-radius: 16rpx;
  font-size: 22rpx;
  color: #666;
  transition: all 0.2s ease;
}

.filter-tag.active {
  background: #1890ff;
  color: #ffffff;
  border-color: #1890ff;
}

.filter-tag:active {
  transform: scale(0.95);
}

.sort-section {
  margin-bottom: 0;
}

.sort-options {
  display: flex;
  gap: 8rpx;
}

.sort-option {
  flex: 1;
  padding: 8rpx 16rpx;
  background: #ffffff;
  border: 1rpx solid #e0e0e0;
  border-radius: 16rpx;
  font-size: 22rpx;
  color: #666;
  text-align: center;
  transition: all 0.2s ease;
}

.sort-option.active {
  background: #1890ff;
  color: #ffffff;
  border-color: #1890ff;
}

.sort-option:active {
  transform: scale(0.95);
}

.clear-filter-btn {
  background: #f0f0f0;
  color: #666;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
}

/* 空状态 */
.empty-tasks {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 24rpx;
  display: block;
}

.add-task-btn {
  background: #1890ff;
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
}

/* 无考试状态 */
.no-exam-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  text-align: center;
  padding: 0 48rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.create-exam-btn {
  background: #1890ff;
  color: #ffffff;
  border: none;
  border-radius: 24rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  font-weight: 600;
}

/* 悬浮按钮 */
.fab-container {
  position: fixed;
  bottom: 140rpx;
  right: 40rpx;
  z-index: 999;
}

.fab-menu {
  position: absolute;
  bottom: 100rpx;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  animation: slideUp 0.3s ease-out;
}

.fab-menu-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  animation: fadeIn 0.3s ease-out;
}

.fab-item-label {
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  font-size: 24rpx;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  white-space: nowrap;
}

.fab-item-button {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.fab-item-button:active {
  transform: scale(0.95);
}

.fab-main-button {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background: #1890ff;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.4);
  transition: all 0.3s ease;
}

.fab-main-button.expanded {
  transform: rotate(45deg);
}

.fab-main-button:active {
  transform: scale(0.95);
}

.fab-main-button.expanded:active {
  transform: rotate(45deg) scale(0.95);
}

/* 动画 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ==================== 检查点弹窗样式 ==================== */

/* 弹窗基础样式 */
.checkpoint-modal {
  border-radius: 16rpx !important;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.checkpoint-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #ffffff;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.checkpoint-modal-content {
  max-height: 60vh;
  overflow-y: auto;
  padding-bottom: 16rpx;
}

/* 统计信息卡片样式 */
.checkpoint-stats-card {
  margin: 16rpx 24rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  border: 1rpx solid #f0f0f0;
}

.stats-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-main {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.stats-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #1890ff;
  transition: all 0.3s ease;
}

.stats-number.updating {
  transform: scale(1.1);
  color: #52c41a;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

.stats-detail {
  flex: 1;
  margin-left: 20rpx;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.completed-count {
  font-size: 22rpx;
  color: #52c41a;
  font-weight: 500;
  min-width: 80rpx;
}

.progress-bar {
  flex: 1;
  height: 6rpx;
  background-color: #f0f0f0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

/* 检查点列表卡片样式 */
.checkpoint-list-card {
  margin: 16rpx 24rpx;
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}

.checkpoint-item {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  border-bottom: 1rpx solid #f8f9fa;
  transition: all 0.3s ease;
  position: relative;
  animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.checkpoint-item:last-child {
  border-bottom: none;
}

.checkpoint-item:active {
  background-color: #f8f9fa;
}

.checkpoint-checkbox {
  margin-right: 16rpx;
  flex-shrink: 0;
}

.checkpoint-content {
  flex: 1;
  min-width: 0;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 6rpx;
  padding: 8rpx;
  margin: -8rpx;
}

.checkpoint-content:active {
  background-color: #f0f8ff;
}

.checkpoint-text-wrapper {
  position: relative;
  transition: all 0.3s ease;
}

.checkpoint-text-wrapper.completed-wrapper {
  opacity: 0.6;
}

.checkpoint-field {
  --van-field-padding: 12rpx 0;
  --van-field-input-text-color: #333;
  --van-field-placeholder-text-color: #999;
  --van-field-input-font-size: 28rpx;
}

.completed-wrapper .checkpoint-title {
  color: #999999;
}

/* 删除线效果 */
.completed-wrapper::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2rpx;
  background-color: #999;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 1;
}

.checkpoint-actions {
  flex-shrink: 0;
}

.delete-action {
  padding: 8rpx;
  color: #ff4d4f;
  transition: all 0.2s ease;
  border-radius: 50%;
}

.delete-action:active {
  color: #ffffff;
  background-color: #ff4d4f;
}

/* 添加检查点卡片样式 */
.add-checkpoint-card {
  margin: 16rpx 24rpx;
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}

/* 默认触发状态 */
.add-checkpoint-trigger {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-checkpoint-trigger:active {
  background-color: #f8f9fa;
}

.trigger-text {
  font-size: 28rpx;
  color: #1890ff;
  font-weight: 500;
}

/* 展开状态 */
.add-checkpoint-expanded {
  padding: 20rpx 20rpx 24rpx 20rpx;
  background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
  border: 1rpx solid #e6f7ff;
}

.add-input-section {
  margin-bottom: 16rpx;
}

.add-checkpoint-field {
  --van-field-border-color: #d9d9d9;
  --van-field-background-color: #ffffff;
  --van-field-padding: 14rpx 16rpx;
  --van-field-input-font-size: 28rpx;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 1rpx 4rpx rgba(0,0,0,0.04);
}

/* 检查点操作区域 */
.checkpoint-actions {
  display: flex;
  margin-top: 16rpx;
  background: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  overflow: hidden;
}

.checkpoint-action-button {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80rpx;
  padding: 12rpx;
}

.save-action {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.cancel-action {
  background: #f8f9fa;
  border-left: 1rpx solid #f0f0f0;
}

.save-checkpoint-btn {
  width: 100% !important;
  height: 56rpx !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  color: #ffffff !important;
  font-size: 26rpx !important;
  font-weight: 600 !important;
  box-shadow: none !important;
}

.cancel-checkpoint-btn {
  width: 100% !important;
  height: 56rpx !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  color: #666 !important;
  font-size: 26rpx !important;
  font-weight: 500 !important;
  box-shadow: none !important;
}

/* 空状态样式 */
.empty-checkpoints {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-top: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 24rpx;
  color: #cccccc;
  margin-top: 8rpx;
  line-height: 1.4;
}

/* 弹窗底部操作区域 */
.checkpoint-modal-footer {
  padding: 16rpx 24rpx 24rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #ffffff;
}

.reset-action {
  background: #f8f9fa;
  border-right: 1rpx solid #f0f0f0;
}

.complete-action {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.reset-checkpoint-btn {
  width: 100% !important;
  height: 56rpx !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  color: #666 !important;
  font-size: 26rpx !important;
  font-weight: 500 !important;
  box-shadow: none !important;
}

.complete-checkpoint-btn {
  width: 100% !important;
  height: 56rpx !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  color: #ffffff !important;
  font-size: 26rpx !important;
  font-weight: 600 !important;
  box-shadow: none !important;
}

/* 弹窗适配的响应式调整 */
@media (max-width: 750rpx) {
  .checkpoint-modal-header {
    padding: 20rpx 24rpx 12rpx;
  }

  .modal-title {
    font-size: 28rpx;
  }

  .checkpoint-stats-card,
  .checkpoint-list-card,
  .add-checkpoint-card {
    margin: 12rpx 16rpx;
  }

  .stats-number {
    font-size: 32rpx;
  }

  .checkpoint-title {
    font-size: 26rpx;
  }

  .trigger-text {
    font-size: 26rpx;
  }
}
