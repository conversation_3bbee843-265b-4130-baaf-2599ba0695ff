// pages/study-group-detail/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    groupId: '',
    groupInfo: null,
    members: [],
    shares: [],
    activities: [],
    loading: true,
    currentUserId: '',
    
    // Tab切换 - 适配van-tabs
    activeTab: 'progress', // progress, shares, activities
    activeTabIndex: 0, // 0: 成员进度, 1: 共享计划, 2: 小组动态
    
    // 成员进度数据
    memberProgress: []
  },

  async onLoad(options) {
    const { groupId } = options
    if (!groupId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      wx.navigateBack()
      return
    }

    this.setData({ groupId })

    // 获取当前用户ID
    const app = getApp()
    this.setData({ currentUserId: app.globalData.openid })

    // 快速加载基本信息，延迟加载详细数据
    await this.loadBasicGroupInfo()
  },

  onShow() {
    // 页面显示时加载完整数据
    if (this.data.groupId && !this.data.groupInfo) {
      this.loadGroupDetail()
    }
  },

  // 快速加载基本小组信息
  async loadBasicGroupInfo() {
    this.setData({ loading: true })

    try {
      const result = await SmartApi.getStudyGroupDetail(this.data.groupId)

      if (result.success && result.data) {
        const { group } = result.data

        // 只设置基本信息，不加载成员进度
        this.setData({
          groupInfo: group,
          members: group.members || [],
          loading: false
        })

        // 延迟加载详细数据
        setTimeout(() => {
          this.loadDetailedData()
        }, 100)
      } else {
        wx.showToast({
          title: result.error || '加载失败',
          icon: 'none'
        })
        this.setData({ loading: false })
      }
    } catch (error) {
      console.error('加载基本小组信息失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      this.setData({ loading: false })
    }
  },

  // 加载详细数据（非阻塞）
  async loadDetailedData() {
    try {
      const result = await SmartApi.getStudyGroupDetail(this.data.groupId)

      if (result.success && result.data) {
        const { shares, activities } = result.data

        this.setData({
          shares: shares || [],
          activities: activities || []
        })

        // 加载成员进度
        await this.loadMemberProgress()
      }
    } catch (error) {
      console.error('加载详细数据失败:', error)
    }
  },

  // 加载小组详情（完整版本）
  async loadGroupDetail() {
    this.setData({ loading: true })

    try {
      const result = await SmartApi.getStudyGroupDetail(this.data.groupId)

      if (result.success && result.data) {
        const { group, shares, activities } = result.data

        console.log('小组详情数据:', group)
        console.log('当前成员数:', group.currentMembers)
        console.log('最大成员数:', group.maxMembers)

        const canInvite = group.currentMembers < group.maxMembers
        console.log('邀请按钮显示条件:', canInvite)

        this.setData({
          groupInfo: group,
          members: group.members || [],
          shares: shares || [],
          activities: activities || [],
          inviteButtonText: canInvite ? '是' : '否'
        })

        // 加载成员进度
        await this.loadMemberProgress()
      } else {
        wx.showToast({
          title: result.error || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载小组详情失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载成员进度 - 优化版本，减少超时风险
  async loadMemberProgress() {
    const { members, groupInfo } = this.data
    if (!members || !groupInfo) return

    try {
      console.log('开始加载成员进度，考试ID:', groupInfo.examId)

      // 使用已有的成员数据，避免重复查询
      const memberProgress = members.map(member => {
        // 使用搭子小组中已经计算好的进度数据
        const progress = member.progress || 0
        const totalTasks = member.totalTasks || 0
        const completedTasks = member.completedTasks || 0

        console.log(`成员 ${member.userInfo?.nickName || '未知'} 的进度:`, {
          总任务: totalTasks,
          已完成: completedTasks,
          进度: progress
        })

        return {
          userId: member.userId,
          userInfo: member.userInfo || { nickName: '用户', avatarUrl: '' },
          totalTasks: totalTasks,
          completedTasks: completedTasks,
          progress: progress,
          recentTasks: [], // 暂时为空，避免额外查询
          // 简化的模拟数据
          isOnline: Math.random() > 0.5,
          todayStudyTime: this.generateRandomStudyTime(),
          weeklyProgress: Math.min(progress + Math.floor(Math.random() * 20), 100),
          consecutiveDays: Math.floor(Math.random() * 30), // 连续打卡天数
          likeCount: Math.floor(Math.random() * 20) // 点赞数
        }
      })

      console.log('处理的成员进度:', memberProgress)
      this.setData({ memberProgress })
    } catch (error) {
      console.error('加载成员进度失败:', error)
      // 设置默认进度数据
      const memberProgress = members.map(member => ({
        userId: member.userId,
        userInfo: member.userInfo,
        totalTasks: 0,
        completedTasks: 0,
        progress: 0,
        recentTasks: []
      }))
      this.setData({ memberProgress })
    }
  },

  // 生成随机复习时长（格式化为字符串）
  generateRandomStudyTime() {
    const minutes = Math.floor(Math.random() * 300) // 0-300分钟
    if (minutes < 60) {
      return `${minutes}分钟`
    } else {
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`
    }
  },

  // 切换Tab - 适配van-tabs
  onTabChange(e) {
    const { index } = e.detail
    const tabs = ['progress', 'shares', 'activities']
    const activeTab = tabs[index]
    
    this.setData({ 
      activeTabIndex: index,
      activeTab: activeTab
    })
  },

  // 邀请新成员
  onInviteMembers() {
    const { groupInfo } = this.data

    if (!groupInfo) {
      wx.showToast({
        title: '小组信息加载中，请稍后重试',
        icon: 'none'
      })
      return
    }

    if (groupInfo.currentMembers >= groupInfo.maxMembers) {
      wx.showToast({
        title: '小组人数已满',
        icon: 'none'
      })
      return
    }

    if (!groupInfo.inviteCode) {
      wx.showToast({
        title: '邀请码不存在，请联系管理员',
        icon: 'none'
      })
      return
    }

    // 使用底部弹出的操作菜单
    wx.showActionSheet({
      itemList: ['复制邀请码', '分享给朋友'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 复制邀请码
          wx.setClipboardData({
            data: groupInfo.inviteCode,
            success: () => {
              wx.showToast({
                title: '邀请码已复制',
                icon: 'success'
              })
            }
          })
        } else if (res.tapIndex === 1) {
          // 直接触发微信分享
          this.triggerWechatShare()
        }
      },
      fail: (res) => {
        console.log('用户取消了操作')
      }
    })
  },

  // 直接触发微信分享
  triggerWechatShare() {
    // 显示分享提示，引导用户使用右上角分享
    wx.showModal({
      title: '分享邀请',
      content: '请点击右上角的"..."菜单，选择"转发"来分享给朋友',
      showCancel: true,
      cancelText: '复制链接',
      confirmText: '知道了',
      success: (res) => {
        if (res.cancel) {
          // 用户选择复制链接
          const { groupInfo } = this.data
          const shareText = `邀请你加入搭子小组"${groupInfo.groupName}"，一起备考${groupInfo.examName}！邀请码：${groupInfo.inviteCode}`
          wx.setClipboardData({
            data: shareText,
            success: () => {
              wx.showToast({
                title: '邀请信息已复制',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  },

  // 分享邀请
  shareInvite() {
    wx.showModal({
      title: '分享邀请',
      content: '选择分享方式',
      showCancel: true,
      cancelText: '转发给朋友',
      confirmText: '复制链接',
      success: (res) => {
        if (res.cancel) {
          // 转发给朋友
          wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage']
          })
        } else if (res.confirm) {
          // 复制链接
          const { groupInfo } = this.data
          const shareUrl = `pages/join-study-group/index?inviteCode=${groupInfo.inviteCode}`
          wx.setClipboardData({
            data: shareUrl,
            success: () => {
              wx.showToast({
                title: '链接已复制',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  },

  // 分享给朋友
  onShareAppMessage() {
    const { groupInfo } = this.data
    return {
      title: `一起备考${groupInfo?.examName || '考试'}吧！`,
      path: `/pages/join-study-group/index?inviteCode=${groupInfo?.inviteCode}`,
      imageUrl: '/images/share-study-group.png'
    }
  },

  // 退出小组
  onLeaveGroup() {
    wx.showModal({
      title: '确认退出',
      content: '退出后将无法查看小组信息，确定要退出吗？',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '退出中...',
            mask: true
          })

          try {
            const result = await SmartApi.leaveStudyGroup(this.data.groupId)
            
            wx.hideLoading()
            
            if (result.success) {
              wx.showToast({
                title: '已退出小组',
                icon: 'success'
              })
              
              setTimeout(() => {
                wx.navigateBack()
              }, 1500)
            } else {
              wx.showToast({
                title: result.error || '退出失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.hideLoading()
            wx.showToast({
              title: '退出失败，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 分享计划
  onSharePlan() {
    const { groupInfo } = this.data
    wx.navigateTo({
      url: `/pages/share-plan/index?groupId=${this.data.groupId}&examId=${groupInfo.examId}`
    })
  },

  // 查看成员详情
  onViewMember(e) {
    const { userId } = e.currentTarget.dataset

    if (!userId) {
      wx.showToast({
        title: '用户信息错误',
        icon: 'none'
      })
      return
    }

    // 跳转到成员详情页面
    wx.navigateTo({
      url: `/pages/member-detail/index?userId=${userId}&groupId=${this.data.groupId}`
    })
  },

  // 点赞计划
  async onLikePlan(e) {
    const { shareId, index } = e.currentTarget.dataset
    
    try {
      const result = await SmartApi.likeGroupPlan(shareId)
      
      if (result.success) {
        // 更新本地数据
        const shares = this.data.shares
        shares[index].liked = result.data.liked
        shares[index].likeCount = result.data.likeCount
        
        this.setData({ shares })
        
        wx.showToast({
          title: result.data.liked ? '已点赞' : '已取消点赞',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('点赞失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  // 复制计划
  async onCopyPlan(e) {
    const { shareId } = e.currentTarget.dataset
    
    wx.showModal({
      title: '复制计划',
      content: '确定要复制这个复习计划吗？',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '复制中...',
            mask: true
          })

          try {
            const result = await SmartApi.copyGroupPlan(shareId)
            
            wx.hideLoading()
            
            if (result.success) {
              wx.showToast({
                title: '复制成功！',
                icon: 'success'
              })
              
              // 刷新数据
              await this.loadGroupDetail()
            } else {
              wx.showToast({
                title: result.error || '复制失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.hideLoading()
            wx.showToast({
              title: '复制失败，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  }
})
