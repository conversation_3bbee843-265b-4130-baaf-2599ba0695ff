/* pages/pomodoro/index.wxss - Vant重构版本 */

/* ==================== 色彩系统定义 ==================== */
page {
  /* Z-index层级 */
  --van-popup-z-index: 10001;
  --van-action-sheet-z-index: 10001;
  --van-overlay-z-index: 10000;
  --van-dialog-z-index: 10001;
  --van-toast-z-index: 10001;

  /* 主题色彩 */
  --primary-color: #1890FF;
  --primary-light: #40A9FF;
  --primary-dark: #096DD9;
  --primary-bg: #E6F7FF;

  --success-color: #52C41A;
  --success-light: #73D13D;
  --success-dark: #389E0D;
  --success-bg: #F6FFED;

  --warning-color: #FA8C16;
  --warning-light: #FFA940;
  --warning-dark: #D46B08;
  --warning-bg: #FFF7E6;

  --error-color: #FF4D4F;
  --error-light: #FF7875;
  --error-dark: #CF1322;
  --error-bg: #FFF2F0;

  --info-color: #1890FF;
  --info-light: #40A9FF;
  --info-dark: #096DD9;
  --info-bg: #E6F7FF;

  /* 中性色彩 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8C8C8C;
  --text-quaternary: #BFBFBF;
  --text-disabled: #D9D9D9;

  --bg-primary: #FFFFFF;
  --bg-secondary: #FAFAFA;
  --bg-tertiary: #F5F5F5;
  --bg-quaternary: #F0F0F0;
  --bg-disabled: #F5F5F5;

  --border-primary: #D9D9D9;
  --border-secondary: #E8E8E8;
  --border-tertiary: #F0F0F0;

  /* 计时器专用色彩 */
  --timer-work-color: #FF6B6B;
  --timer-work-light: #FF8A8A;
  --timer-work-dark: #E55555;
  --timer-work-bg: #FFF0F0;

  --timer-break-color: #52C41A;
  --timer-break-light: #73D13D;
  --timer-break-dark: #389E0D;
  --timer-break-bg: #F6FFED;

  --timer-long-break-color: #1890FF;
  --timer-long-break-light: #40A9FF;
  --timer-long-break-dark: #096DD9;
  --timer-long-break-bg: #E6F7FF;

  /* 阴影系统 */
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  --shadow-focus: 0 0 0 4rpx rgba(24, 144, 255, 0.2);

  /* 动画时长 */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;

  /* 缓动函数 */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}



/* ==================== 弹窗层级样式 ==================== */
.sound-popup, .settings-popup, .task-action-sheet {
  z-index: 10001 !important;
}

/* 确保弹窗覆盖层也有正确的层级 */
.van-popup__overlay, .van-overlay {
  z-index: 10000 !important;
}

/* 防止滚动穿透 */
.popup-content {
  position: relative;
  z-index: 1;
}

/* ==================== 任务选择和设置区域样式 ==================== */
.task-settings-container {
  margin: 20rpx 0;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  gap: 20rpx;
}

.task-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
  cursor: pointer;
}

.task-icon {
  flex-shrink: 0;
}

.task-info {
  flex: 1;
  min-width: 0;
}

.task-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-label {
  font-size: 24rpx;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.arrow-icon {
  flex-shrink: 0;
  margin-left: 8rpx;
}

.settings-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex-shrink: 0;
}

.quick-mode-switch {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.switch-label {
  font-size: 22rpx;
  color: #666;
  white-space: nowrap;
}

.settings-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: rgba(150, 151, 153, 0.1);
  transition: all var(--duration-normal) var(--ease-out);
  cursor: pointer;
}

.settings-icon-wrapper:hover {
  background: rgba(150, 151, 153, 0.2);
  transform: scale(1.1);
}

.settings-icon-wrapper:active {
  background: rgba(150, 151, 153, 0.3);
  transform: scale(0.95);
  transition: transform var(--duration-fast) var(--ease-in);
}

/* ==================== 基础容器样式 ==================== */
.container {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: env(safe-area-inset-bottom);
  position: relative;
  transition: background var(--duration-normal) var(--ease-in-out);
}

.container.focus-mode {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  color: var(--text-primary);
}

.normal-layout {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 20rpx 80rpx 20rpx;
  animation: layoutFadeIn var(--duration-normal) var(--ease-out);
}

.focus-layout {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 20rpx 160rpx 20rpx;
}

/* ==================== Vant导航栏自定义样式 ==================== */
.pomodoro-nav-bar {
  --van-nav-bar-background-color: rgba(255, 255, 255, 0.95);
  --van-nav-bar-text-color: #333;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.pomodoro-nav-title {
  font-size: 32rpx;
  font-weight: 600;
}

.focus-nav-bar {
  --van-nav-bar-background-color: rgba(0, 0, 0, 0.3);
  --van-nav-bar-text-color: #fff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.focus-nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

/* ==================== 任务信息区域 ==================== */
.task-info-section {
  margin: 20rpx 0;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.task-info-cell {
  --van-cell-background-color: rgba(255, 255, 255, 0.95);
  --van-cell-text-color: #333;
  --van-cell-label-color: #666;
  backdrop-filter: blur(10px);
}

/* ==================== 个人签名区域 ==================== */
.signature-section {
  margin: 20rpx 0 24rpx 0;
  padding: 28rpx 36rpx;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 20rpx;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.12),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8),
    inset 0 -1rpx 0 rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.signature-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, #1890ff 0%, #52c41a 50%, #1890ff 100%);
  opacity: 0.8;
}

.signature-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  position: relative;
}

.signature-icon {
  flex-shrink: 0;
  opacity: 0.7;
}

.signature-icon-right {
  transform: scaleX(-1);
}

.signature-text {
  font-size: 32rpx;
  color: #2c3e50;
  line-height: 1.6;
  font-weight: 600;
  margin: 0 20rpx;
  text-align: center;
  letter-spacing: 1.5rpx;
  position: relative;
}

.signature-author {
  font-size: 24rpx;
  color: #8c8c8c;
  text-align: right;
  font-style: italic;
  font-weight: 400;
  letter-spacing: 0.8rpx;
  opacity: 0.8;
}

/* 专注模式下的签名样式 */
.focus-mode .signature-section {
  background: rgba(255, 255, 255, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.25);
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4),
    inset 0 -1rpx 0 rgba(255, 255, 255, 0.1);
}

.focus-mode .signature-section::before {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0.6) 100%);
}

.focus-mode .signature-text {
  color: rgba(255, 255, 255, 0.95);
}

.focus-mode .signature-author {
  color: rgba(255, 255, 255, 0.75);
}

.focus-mode .signature-icon {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* ==================== 任务选择相关样式 ==================== */
.quick-mode-switch {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.switch-label {
  font-size: 24rpx;
  color: #666;
}

.task-selector-popup {
  z-index: 10001 !important;
}

.task-selector-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.task-selector-nav {
  --van-nav-bar-background-color: #fff;
  --van-nav-bar-text-color: #333;
  border-bottom: 1px solid #eee;
}

.task-list-container {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.quick-focus-group {
  margin-bottom: 20rpx;
}

.task-list-group {
  margin-bottom: 20rpx;
}

.task-option {
  --van-cell-background-color: #fff;
  transition: all 0.3s ease;
}

.task-option.selected {
  --van-cell-background-color: #f0f9ff;
  --van-cell-text-color: #1890ff;
}

.task-option:active {
  --van-cell-background-color: #f5f5f5;
}

.empty-tasks {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
  text-align: center;
}

.empty-tasks .van-button {
  margin-top: 20rpx;
}

/* ==================== 计时器交互增强样式 ==================== */
.time-nodes {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.time-node {
  position: absolute;
  top: 10rpx;
  left: 50%;
  transform-origin: 50% 130rpx;
  pointer-events: all;
  z-index: 2;
}

.node-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: var(--bg-primary);
  border: 2rpx solid var(--border-secondary);
  transition: all var(--duration-normal) var(--ease-out);
  cursor: pointer;
  margin-left: -6rpx;
  box-shadow: var(--shadow-light);
}

.node-dot.active {
  background: var(--timer-work-color);
  border-color: var(--timer-work-color);
  transform: scale(1.2);
  box-shadow: 0 0 10rpx var(--timer-work-color);
}

.node-dot:hover {
  transform: scale(1.1);
  background: var(--bg-secondary);
  box-shadow: var(--shadow-medium);
}

.gesture-hint {
  position: absolute;
  top: -60rpx;
  left: 50%;
  transform: translateX(-50%);
  background: var(--text-primary);
  color: var(--bg-primary);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  white-space: nowrap;
  z-index: 10;
  animation: gestureHintSlide var(--duration-normal) var(--ease-out);
  box-shadow: var(--shadow-medium);
}

.hint-text {
  font-size: 24rpx;
  color: white;
}

/* ==================== 统计信息可视化样式 ==================== */
.stats-panel-container {
  margin: 20rpx 0;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.stats-header {
  padding: 20rpx 20rpx 0 20rpx;
}

.stats-content {
  padding: 20rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}

.stat-card {
  flex: 1;
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-out);
}

.stat-card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.1);
}

.stat-content {
  text-align: center;
  padding: 24rpx 16rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1890FF;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #646566;
}



/* 数据对比卡片样式 */
.comparison-cards {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 24rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.comparison-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
}

.switch-period-btn {
  border-radius: 16rpx !important;
}

.comparison-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.comparison-item {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.comparison-label {
  font-size: 24rpx;
  color: #646566;
  font-weight: 500;
}

.comparison-values {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.current-value,
.previous-value {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.value-number {
  font-size: 32rpx;
  font-weight: 700;
  color: #323233;
  margin-bottom: 4rpx;
}

.value-period {
  font-size: 20rpx;
  color: #969799;
}

.comparison-arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 0 0 120rpx;
}

.comparison-arrow.up {
  color: #52c41a;
}

.comparison-arrow.down {
  color: #ff4d4f;
}

.comparison-arrow.stable {
  color: #969799;
}

.arrow-icon {
  font-size: 28rpx;
  margin-bottom: 4rpx;
}

.trend-text {
  font-size: 20rpx;
  font-weight: 500;
}

/* 图表控制样式 */
.chart-controls {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.time-range-btn,
.export-btn,
.chart-close-btn {
  border-radius: 12rpx !important;
  min-width: 60rpx !important;
}

.export-btn {
  padding: 0 16rpx !important;
}

/* 图表增强样式 */
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #ebedf0;
}

.chart-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

.stats-tabs {
  --van-tabs-line-color: white;
  --van-tab-text-color: rgba(255, 255, 255, 0.7);
  --van-tab-active-text-color: white;
  --van-tabs-bottom-bar-color: white;
}

.stats-tab {
  font-size: 26rpx;
  padding: 0 16rpx;
}

.stats-tab-active {
  font-weight: 600;
}

.stats-content {
  padding: 20rpx;
}

.stats-grid {
  margin-bottom: 20rpx;
}

.stat-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card.clickable {
  cursor: pointer;
}

.stat-card.clickable:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stat-card.clickable:active {
  transform: scale(0.98);
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  z-index: 2;
}

.stat-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: white;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.stat-trend {
  margin-top: 8rpx;
}

.trend-tag {
  --van-tag-font-size: 20rpx;
  --van-tag-padding: 4rpx 8rpx;
}

/* 趋势图表样式 */
.trend-chart-container {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1px solid #e9ecef;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.chart-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.chart-close-btn {
  --van-button-mini-height: 48rpx;
  --van-button-mini-font-size: 22rpx;
}

.chart-area {
  display: flex;
  height: 200rpx;
  background: white;
  border-radius: 8rpx;
  padding: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.chart-y-axis {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 60rpx;
  padding-right: 16rpx;
  border-right: 1px solid #e9ecef;
}

.y-label {
  font-size: 20rpx;
  color: #666;
  text-align: right;
  line-height: 1;
}

.chart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 16rpx;
}

.chart-bars {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding-bottom: 16rpx;
  border-bottom: 1px solid #e9ecef;
}

.bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 40rpx;
}

.bar {
  width: 24rpx;
  border-radius: 4rpx 4rpx 0 0;
  transition: all 0.3s ease;
  min-height: 4rpx;
}

.focus-bar {
  background: linear-gradient(to top, #FF6B6B, #FF8E8E);
}

.bar:hover {
  opacity: 0.8;
  transform: scaleY(1.05);
}

.chart-x-axis {
  display: flex;
  justify-content: space-around;
  padding-top: 12rpx;
}

.x-label {
  font-size: 20rpx;
  color: #666;
  text-align: center;
  width: 40rpx;
}

/* ==================== 专注模式增强样式 ==================== */
.focus-nav-right {
  display: flex;
  align-items: center;
}

.exit-hint {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 10rpx;
}

.focus-motivation {
  text-align: center;
  margin: 20rpx 0;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.motivation-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  line-height: 1.4;
}

.focus-timer-circle.milestone-glow {
  animation: milestoneGlow 2s ease-in-out;
  box-shadow: 0 0 40rpx rgba(255, 255, 255, 0.5);
}

.milestone-indicator {
  position: absolute;
  bottom: -60rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  white-space: nowrap;
  animation: milestoneSlideUp 0.5s ease-out;
}

.milestone-text {
  font-weight: 600;
}

.progress-rings {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.progress-ring {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  width: calc(100% + 20rpx);
  height: calc(100% + 20rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: progressRipple 3s infinite;
}

.focus-task-card {
  margin: 20rpx 0;
  --van-card-background-color: rgba(255, 255, 255, 0.1);
  --van-card-text-color: rgba(255, 255, 255, 0.9);
  --van-card-desc-color: rgba(255, 255, 255, 0.7);
}

.task-progress {
  margin-top: 12rpx;
}

.focus-progress {
  margin-bottom: 8rpx;
}

.progress-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  display: block;
}

.focus-stats {
  display: flex;
  justify-content: space-around;
  margin: 30rpx 0;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

.focus-stats .stat-item {
  text-align: center;
  flex: 1;
}

.focus-stats .stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 4rpx;
}

.focus-stats .stat-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.focus-controls {
  display: flex;
  justify-content: center;
  gap: 40rpx;
  margin: 40rpx 0;
}

.focus-control-btn,
.focus-stop-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  min-width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
}

.focus-control-btn .btn-text,
.focus-stop-btn .btn-text {
  font-size: 22rpx;
  margin-top: 4rpx;
}

.focus-audio-control {
  margin: 30rpx 0;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

.focus-volume-slider {
  margin-bottom: 16rpx;
}

.audio-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.audio-label {
  flex: 1;
  margin-left: 12rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.sound-switch-btn {
  --van-button-mini-height: 48rpx;
  --van-button-mini-font-size: 22rpx;
  --van-button-default-background-color: rgba(255, 255, 255, 0.2);
  --van-button-default-color: rgba(255, 255, 255, 0.9);
}

/* 里程碑庆祝动画 */
.milestone-celebration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10000;
  animation: celebrationFadeIn 0.5s ease-out;
}

.celebration-content {
  text-align: center;
  padding: 60rpx 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.celebration-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 20rpx;
  animation: celebrationBounce 0.8s ease-out;
}

.celebration-message {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.celebration-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.effect-particle {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: particleFloat 3s ease-out infinite;
}

/* ==================== 主计时器区域 ==================== */
.main-timer-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 40rpx 0;
  min-height: 500rpx;
}

.timer-circle {
  width: 320rpx;
  height: 320rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  transition: all var(--duration-normal) var(--ease-in-out);
  box-shadow: var(--shadow-medium);
  position: relative;
  cursor: pointer;
  background: var(--bg-primary);
  border: 4rpx solid var(--border-tertiary);
}

.timer-circle.breathing {
  animation: breathe 4s var(--ease-in-out) infinite;
}

.timer-circle.time-node-highlight {
  animation: nodeHighlight var(--duration-slow) var(--ease-out);
}

.timer-circle:active {
  transform: scale(0.98);
  transition: transform var(--duration-fast) var(--ease-in);
}

.timer-circle:hover {
  box-shadow: var(--shadow-heavy);
  transform: translateY(-2rpx);
}

.timer-inner {
  width: 280rpx;
  height: 280rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  box-shadow: inset 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.time-display {
  font-size: 64rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', monospace;
}

.session-type {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.sound-info-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sound-tag {
  --van-tag-primary-color: #667eea;
  --van-tag-text-color: #fff;
  font-size: 20rpx;
}

/* ==================== 控制按钮 ==================== */
.primary-controls {
  display: flex;
  gap: 32rpx;
  align-items: center;
  justify-content: center;
}

.main-control-btn {
  --van-button-primary-background-color: var(--bg-primary);
  --van-button-primary-border-color: var(--bg-primary);
  --van-button-primary-color: var(--primary-color);
  --van-button-warning-background-color: var(--warning-color);
  --van-button-warning-border-color: var(--warning-color);
  --van-button-warning-color: var(--bg-primary);
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(10px);
  width: 120rpx;
  height: 120rpx;
  transition: all var(--duration-normal) var(--ease-out);
}

.main-control-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-heavy);
}

.stop-btn {
  --van-button-danger-background-color: var(--error-color);
  --van-button-danger-border-color: var(--error-color);
  --van-button-danger-color: var(--bg-primary);
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(10px);
  width: 100rpx;
  height: 100rpx;
  transition: all var(--duration-normal) var(--ease-out);
}

.stop-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-heavy);
}

.focus-mode-btn {
  --van-button-info-background-color: var(--info-color);
  --van-button-info-border-color: var(--info-color);
  --van-button-info-color: var(--bg-primary);
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(10px);
  width: 100rpx;
  height: 100rpx;
  transition: all var(--duration-normal) var(--ease-out);
}

.focus-mode-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-heavy);
}





/* ==================== 统计面板 ==================== */
.stats-panel {
  margin: 20rpx 0;
  --van-panel-background-color: rgba(255, 255, 255, 0.95);
  --van-panel-header-font-size: 28rpx;
  --van-panel-header-color: #333;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
}

.stats-grid {
  --van-grid-item-content-padding: 24rpx;
}

.stats-item {
  --van-grid-item-text-color: #666;
  --van-grid-item-text-font-size: 24rpx;
  text-align: center;
}

.stats-value {
  font-size: 40rpx;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 8rpx;
}

/* ==================== 专注模式样式 ==================== */
.focus-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.focus-timer-display {
  margin-bottom: 60rpx;
}

.focus-timer-circle {
  width: 400rpx;
  height: 400rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.3);
}

.focus-timer-circle.breathing {
  animation: focusBreath 4s ease-in-out infinite;
}

.focus-timer-inner {
  width: 360rpx;
  height: 360rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(20px);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.focus-time-text {
  font-size: 64rpx;
  font-weight: 300;
  color: #fff;
  margin-bottom: 12rpx;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', monospace;
}

.focus-session-indicator {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 4rpx;
}

.focus-task-card {
  --van-card-background-color: rgba(255, 255, 255, 0.1);
  --van-card-title-color: #fff;
  --van-card-desc-color: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  margin-bottom: 40rpx;
}

.focus-controls {
  display: flex;
  gap: 40rpx;
  align-items: center;
  justify-content: center;
}

.focus-control-btn {
  --van-button-primary-background-color: rgba(255, 255, 255, 0.2);
  --van-button-primary-border-color: rgba(255, 255, 255, 0.2);
  --van-button-primary-color: #fff;
  --van-button-warning-background-color: rgba(255, 193, 7, 0.3);
  --van-button-warning-border-color: rgba(255, 193, 7, 0.3);
  --van-button-warning-color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  width: 140rpx;
  height: 140rpx;
}

.focus-stop-btn {
  --van-button-danger-background-color: rgba(220, 53, 69, 0.3);
  --van-button-danger-border-color: rgba(220, 53, 69, 0.3);
  --van-button-danger-color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  width: 120rpx;
  height: 120rpx;
}

/* ==================== 弹窗样式 ==================== */
.sound-popup, .settings-popup {
  --van-popup-background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
}

/* ==================== 弹窗通用样式 ==================== */
.popup-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.popup-nav-bar {
  flex-shrink: 0;
  --van-nav-bar-background-color: #f7f8fa;
}

.popup-body {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx;
  background-color: #ffffff;
}

/* ==================== 声音设置弹窗 ==================== */
.sound-grid {
  --van-grid-item-content-padding: 0;
}

.sound-item {
  border-radius: 16rpx;
  overflow: hidden;
  transition: all 0.2s ease-in-out;
  border: 2rpx solid transparent;
}

.sound-item.active {
  border-color: #1989fa;
  box-shadow: 0 0 20rpx rgba(25, 137, 250, 0.3);
}

.sound-item .van-grid-item__content {
  padding: 20rpx !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.sound-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
}

.volume-slider {
  width: 300rpx;
}

/* ==================== 设置弹窗 ==================== */
.settings-popup .van-cell {
  background-color: #f7f8fa;
}

.settings-popup .van-stepper-input {
  width: 80rpx !important;
  background-color: #ffffff;
}

.settings-popup .van-switch {
  margin-left: auto;
}

/* ==================== 动画效果 ==================== */
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.2);
  }
}

@keyframes focusBreath {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.3);
  }
  50% {
    transform: scale(1.03);
    box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.4);
  }
}

@keyframes nodeHighlight {
  0% {
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  }
  50% {
    box-shadow: 0 12rpx 48rpx rgba(255, 107, 107, 0.4);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-10rpx);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes milestoneGlow {
  0%, 100% {
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  }
  50% {
    box-shadow: 0 12rpx 48rpx rgba(255, 255, 255, 0.4);
    transform: scale(1.02);
  }
}

@keyframes milestoneSlideUp {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes progressRipple {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

@keyframes celebrationFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes celebrationBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-200rpx) rotate(360deg);
    opacity: 0;
  }
}

/* ==================== 新增动画效果 ==================== */
@keyframes layoutFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gestureHintSlide {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(10rpx);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes cardSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes statsCountUp {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes slideInFromBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutToBottom {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

/* ==================== 设置界面优化样式 ==================== */
.nav-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.preset-btn {
  --van-button-mini-height: 48rpx;
  --van-button-mini-font-size: 22rpx;
}

.recommendation-banner {
  padding: 0 20rpx;
  margin-bottom: 16rpx;
}

.recommendation-notice {
  --van-notice-bar-background-color: #e6f7ff;
  --van-notice-bar-text-color: #1890ff;
}

.settings-collapse {
  background: #f8f9fa;
}

.settings-section {
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-content {
  padding: 0;
}

.settings-group {
  background: white;
}

.settings-group .van-cell {
  --van-cell-font-size: 28rpx;
  --van-cell-label-color: #666;
  --van-cell-label-font-size: 22rpx;
  --van-cell-label-line-height: 1.4;
  padding: 24rpx 20rpx;
}

.time-stepper {
  --van-stepper-button-width: 48rpx;
  --van-stepper-button-height: 48rpx;
  --van-stepper-input-width: 80rpx;
  --van-stepper-input-font-size: 26rpx;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 16rpx;
  width: 200rpx;
}

.volume-slider {
  flex: 1;
  --van-slider-bar-height: 4rpx;
  --van-slider-button-width: 32rpx;
  --van-slider-button-height: 32rpx;
}

.volume-text {
  font-size: 22rpx;
  color: #666;
  min-width: 48rpx;
  text-align: right;
}

/* 预设配置样式 */
.preset-popup {
  z-index: 10001 !important;
}

.preset-list {
  padding: 20rpx;
}

.preset-group {
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.preset-item {
  --van-cell-font-size: 28rpx;
  --van-cell-label-color: #666;
  --van-cell-label-font-size: 24rpx;
  padding: 24rpx 20rpx;
  transition: background-color 0.3s ease;
}

.preset-item:active {
  --van-cell-background-color: #f5f5f5;
}

.preset-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.preset-time {
  font-size: 22rpx;
  color: #1890ff;
  font-weight: 600;
  background: #e6f7ff;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.preset-tip {
  margin-top: 20rpx;
}

.preset-warning {
  --van-notice-bar-background-color: #fff7e6;
  --van-notice-bar-text-color: #fa8c16;
}

/* 音效分类选择样式 */
.sound-category-popup {
  z-index: 10001 !important;
}

.sound-tabs {
  --van-tabs-line-color: #1890ff;
  --van-tab-text-color: #666;
  --van-tab-active-text-color: #1890ff;
}

.sound-list {
  padding: 20rpx;
}

.sound-group {
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.sound-item {
  --van-cell-font-size: 28rpx;
  --van-cell-label-color: #666;
  --van-cell-label-font-size: 24rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
}

.sound-item.selected {
  --van-cell-background-color: #e6f7ff;
  --van-cell-text-color: #1890ff;
}

.sound-item:active {
  --van-cell-background-color: #f5f5f5;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 375px) {
  .timer-circle {
    width: 280rpx;
    height: 280rpx;
  }
  
  .timer-inner {
    width: 240rpx;
    height: 240rpx;
  }
  
  .time-display {
    font-size: 40rpx;
  }
  
  .focus-timer-circle {
    width: 320rpx;
    height: 320rpx;
  }
  
  .focus-timer-inner {
    width: 280rpx;
    height: 280rpx;
  }
  
  .focus-time-text {
    font-size: 52rpx;
  }
}

/* ==================== 暗黑模式适配 ==================== */
.container.focus-mode .sound-popup,
.container.focus-mode .settings-popup {
  --van-popup-background-color: #2d2d2d;
}

.container.focus-mode .popup-nav-bar {
  --van-nav-bar-background-color: #2d2d2d;
  --van-nav-bar-text-color: #fff;
  border-bottom: 1px solid #444;
}

.container.focus-mode .sound-item,
.container.focus-mode .stats-item {
  --van-grid-item-text-color: #fff;
}

.container.focus-mode .task-info-cell {
  --van-cell-background-color: rgba(255, 255, 255, 0.1);
  --van-cell-text-color: #fff;
  --van-cell-label-color: rgba(255, 255, 255, 0.8);
}
