/* pages/help/index.wxss */

/* 容器 */
.container {
  background-color: #F8F9FA;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 内容区域 */
.content {
  padding: 32rpx 32rpx 0 32rpx;
}

/* Vant组件自定义样式 */
.section-group {
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

/* FAQ 答案样式 */

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq-answer.show {
  max-height: 500rpx;
}

.answer-text {
  font-size: 24rpx;
  color: #5D6D7E;
  line-height: 1.6;
  padding: 16rpx 32rpx 24rpx 32rpx;
  display: block;
  background-color: #F8F9FA;
}

/* 图标样式 */
.guide-icon, .contact-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.contact-action {
  font-size: 24rpx;
  color: #1890FF;
}

/* 使用指南弹窗 */
.guide-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.guide-modal-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin: 32rpx;
  max-height: 80vh;
  overflow: hidden;
}

.guide-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.guide-modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50;
}

.guide-modal-close {
  font-size: 40rpx;
  color: #BDC3C7;
  cursor: pointer;
}

.guide-modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.guide-modal-text {
  font-size: 26rpx;
  color: #5D6D7E;
  line-height: 1.8;
  white-space: pre-line;
}
