// 登录状态检查器 - 处理登录过期和有效期管理
const LoginApi = require('./loginApi')

class LoginStatusChecker {
  // 检查登录状态并处理过期情况
  static checkLoginStatus() {
    const app = getApp()
    const loginStatus = app.checkLoginStatus()

    if (loginStatus.isLoggedIn) {
      return {
        isLoggedIn: true,
        userInfo: loginStatus.userInfo || {},
        needsUpdate: false
      }
    } else {
      // 如果是因为登录过期，静默处理，不显示提醒
      if (loginStatus.error === 'LOGIN_EXPIRED') {
        console.log('登录已过期，用户需要重新登录')
      }
      
      return {
        isLoggedIn: false,
        userInfo: {
          nickName: '考试达人',
          avatarUrl: ''
        },
        needsUpdate: true
      }
    }
  }

  // 处理登录过期的清理工作
  static handleLoginExpired() {
    return {
      isLoggedIn: false,
      userInfo: {
        nickName: '考试达人',
        avatarUrl: ''
      },
      examList: [],
      currentExamIndex: 0,
      todayTasks: [],
      completedCount: 0,
      totalCount: 0
    }
  }

  // 自动延长登录有效期（在用户活跃时调用）
  static autoExtendLoginIfNeeded() {
    const loginInfo = wx.getStorageSync('kaoba_login_info')
    if (loginInfo) {
      const now = Date.now()
      const remainingTime = loginInfo.expireTime - now
      const oneDay = 24 * 60 * 60 * 1000
      
      // 如果剩余时间不足7天，自动延长
      if (remainingTime < 7 * oneDay && remainingTime > 0) {
        LoginApi.extendLoginValidity()
        console.log('自动延长登录有效期至30天')
      }
    }
  }
}

module.exports = LoginStatusChecker