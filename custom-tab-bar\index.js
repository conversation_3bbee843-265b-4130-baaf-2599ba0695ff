// custom-tab-bar/index.js - Vant重构版本
Component({
  data: {
    selected: 0, // Vant TabBar使用数字索引
    visible: true, // 控制tabBar显示/隐藏
    list: [
      {
        pagePath: "/pages/home/<USER>",
        iconText: "🏠",
        text: "首页"
      },
      {
        pagePath: "/pages/exam-center/index",
        iconText: "📅",
        text: "考试"
      },
      {
        pagePath: "/pages/task-center/index",
        iconText: "📝",
        text: "任务"
      },
      {
        pagePath: "/pages/pomodoro/index",
        iconText: "🍅",
        text: "专注"
      },
      {
        pagePath: "/pages/profile/index",
        iconText: "👤",
        text: "我的"
      }
    ]
  },
  
  attached() {
    // 初始化默认选中状态
    this.setData({ selected: "0" })
    
    // 延迟执行，确保页面已经完全初始化
    setTimeout(() => {
      this.setSelected()
    }, 200)
  },

  ready() {
    // 组件准备就绪时再次检查状态
    console.log('TabBar ready, current selected:', this.data.selected)
    this.setSelected()
  },

  methods: {
    // 隐藏tabBar
    hide() {
      this.setData({ visible: false })
    },

    // 显示tabBar
    show() {
      this.setData({ visible: true })
    },

    // Vant TabBar的变化事件处理
    onTabChange(e) {
      console.log('TabBar change event:', e.detail)

      // Vant TabBar传递的是选中项的name属性值
      const name = e.detail
      const index = parseInt(name)

      // 确保index是有效的数字
      if (isNaN(index) || index < 0 || index >= this.data.list.length) {
        console.warn('TabBar: Invalid index received:', name, 'parsed to:', index)
        return
      }

      console.log('TabBar switching to index:', index)

      // 更新选中状态
      this.setData({
        selected: index
      })
      
      // 跳转页面
      if (this.data.list[index] && this.data.list[index].pagePath) {
        wx.switchTab({
          url: this.data.list[index].pagePath,
          fail: (err) => {
            console.error('TabBar switchTab failed:', err)
          }
        })
      }
    },

    // 兼容原有的switchTab方法
    switchTab(e) {
      const data = e.currentTarget.dataset
      const index = data.index

      if (typeof index === 'number' && index >= 0 && index < this.data.list.length) {
        this.onTabChange({
          detail: index
        })
      }
    },

    setSelected() {
      try {
        // 获取当前页面路径
        const pages = getCurrentPages()
        if (pages.length === 0) {
          // 如果没有页面，默认选中第一个
          this.setData({ selected: 0 })
          return
        }

        const currentPage = pages[pages.length - 1]
        if (!currentPage || !currentPage.route) {
          this.setData({ selected: 0 })
          return
        }

        const currentRoute = '/' + currentPage.route

        // 找到对应的tab索引
        const index = this.data.list.findIndex(item =>
          item && item.pagePath === currentRoute
        )

        if (index !== -1) {
          // 找到匹配的路由，设置对应的索引
          this.setData({
            selected: index
          })
        } else {
          // 如果当前页面不在TabBar中（如添加页面、详情页面等），不设置选中状态
          // 这样可以避免不必要的警告
          const isTabBarPage = this.data.list.some(item =>
            currentRoute.startsWith(item.pagePath.replace('/index', ''))
          )

          if (!isTabBarPage) {
            // 当前页面不是TabBar页面，保持当前状态不变
            return
          }

          // 如果是TabBar页面但找不到匹配，默认选中第一个
          this.setData({ selected: 0 })
        }
      } catch (error) {
        console.error('TabBar setSelected error:', error)
        // 出错时设置默认值
        this.setData({ selected: 0 })
      }
    }
  }
})
