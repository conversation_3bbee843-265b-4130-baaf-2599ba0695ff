// pages/help-feedback/index.js
Page({
  data: {
    // 常见问题
    faqList: [
      {
        id: 1,
        question: '如何添加考试和任务？',
        answer: '在考试中心或任务中心页面，点击右上角的"+"按钮即可添加新的考试或任务。填写相关信息后保存即可。',
        expanded: false
      },
      {
        id: 2,
        question: '番茄钟如何使用？',
        answer: '进入专注页面，选择复习模式（快速专注或任务专注），设置背景音，点击开始按钮即可。可以长按计时器进入全屏专注模式。',
        expanded: false
      },
      {
        id: 3,
        question: '如何查看复习统计？',
        answer: '在"我的"页面点击"数据中心"，或在专注页面点击底部"统计"按钮，可以查看详细的复习数据分析。',
        expanded: false
      },
      {
        id: 4,
        question: '数据会丢失吗？',
        answer: '所有数据都保存在本地，不会上传到服务器。建议定期使用"数据导出"功能备份重要数据。',
        expanded: false
      },
      {
        id: 5,
        question: '如何设置提醒？',
        answer: '在设置页面可以开启考试提醒、任务提醒等。添加考试时也可以设置具体的提醒时间。',
        expanded: false
      }
    ],

    // 使用指南
    guideList: [
      {
        id: 1,
        icon: '📚',
        title: '考试管理指南',
        description: '如何高效管理考试信息',
        content: '1. 添加考试：点击考试中心的"+"按钮\n2. 设置重要程度：选择考试的重要级别\n3. 配置提醒：设置考试前的提醒时间\n4. 查看倒计时：在首页可以看到最近考试的倒计时\n5. 准备度跟踪：通过关联任务跟踪复习进度'
      },
      {
        id: 2,
        icon: '📝',
        title: '任务管理指南',
        description: '如何科学规划复习任务',
        content: '1. 创建任务：在任务中心添加复习任务\n2. 设置优先级：根据重要性和紧急性设置\n3. 分解子任务：将大任务拆分为小任务\n4. 关联考试：将任务与相关考试关联\n5. 跟踪进度：实时查看任务完成情况'
      },
      {
        id: 3,
        icon: '🍅',
        title: '番茄钟使用指南',
        description: '如何使用番茄工作法复习',
        content: '1. 选择模式：快速专注或任务专注\n2. 设置环境：选择合适的背景音\n3. 开始专注：点击开始按钮，专注25分钟\n4. 专注模式：长按计时器进入全屏模式\n5. 休息调节：专注后进行5分钟休息'
      },
      {
        id: 4,
        icon: '📊',
        title: '数据分析指南',
        description: '如何分析复习效率',
        content: '1. 查看统计：在数据中心查看复习数据\n2. 效率分析：了解不同时间段的复习效率\n3. 科目分析：查看各科目的复习时长\n4. 趋势跟踪：观察复习习惯的变化趋势\n5. 导出数据：备份和分享复习数据'
      }
    ],

    // 反馈表单
    feedbackType: '',
    feedbackContent: '',
    contactInfo: '',
    canSubmit: false,

    // 指南弹窗
    showGuideModal: false,
    currentGuide: {}
  },

  onLoad(options) {
    this.validateFeedback()
  },

  // FAQ 相关方法
  toggleFaq(e) {
    const id = e.currentTarget.dataset.id
    const faqList = this.data.faqList.map(item => {
      if (item.id === id) {
        return { ...item, expanded: !item.expanded }
      }
      return item
    })
    this.setData({ faqList })
  },

  // 使用指南相关方法
  openGuide(e) {
    const guide = e.currentTarget.dataset.guide
    this.setData({
      currentGuide: guide,
      showGuideModal: true
    })
  },

  closeGuideModal() {
    this.setData({ showGuideModal: false })
  },

  stopPropagation() {
    // 阻止事件冒泡
  },

  // 反馈表单相关方法
  selectFeedbackType(e) {
    const type = e.currentTarget.dataset.type
    this.setData({ feedbackType: type })
    this.validateFeedback()
  },

  onFeedbackInput(e) {
    this.setData({ feedbackContent: e.detail.value })
    this.validateFeedback()
  },

  onContactInput(e) {
    this.setData({ contactInfo: e.detail.value })
  },

  validateFeedback() {
    const { feedbackType, feedbackContent } = this.data
    const canSubmit = feedbackType && feedbackContent.trim().length >= 10
    this.setData({ canSubmit })
  },

  submitFeedback() {
    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请完善反馈信息',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '提交中...'
    })

    // 模拟提交反馈
    setTimeout(() => {
      wx.hideLoading()

      // 保存反馈记录
      this.saveFeedbackRecord()

      wx.showModal({
        title: '反馈提交成功',
        content: '感谢您的反馈！我们会认真处理您的建议。',
        showCancel: false,
        confirmText: '知道了',
        success: () => {
          // 清空表单
          this.setData({
            feedbackType: '',
            feedbackContent: '',
            contactInfo: ''
          })
          this.validateFeedback()
        }
      })
    }, 1500)
  },

  saveFeedbackRecord() {
    try {
      const feedback = {
        id: Date.now().toString(),
        type: this.data.feedbackType,
        content: this.data.feedbackContent,
        contact: this.data.contactInfo,
        timestamp: new Date().toISOString()
      }

      const feedbacks = wx.getStorageSync('feedbacks') || []
      feedbacks.unshift(feedback)

      // 限制记录数量
      if (feedbacks.length > 50) {
        feedbacks.splice(50)
      }

      wx.setStorageSync('feedbacks', feedbacks)
    } catch (error) {
      console.error('保存反馈记录失败:', error)
    }
  },

  // 联系我们相关方法
  copyContact(e) {
    const text = e.currentTarget.dataset.text
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        })
      }
    })
  },

  joinGroup() {
    wx.showModal({
      title: '加入用户群',
      content: '请添加微信号：augmentcode，备注"备考助手"，我们会邀请您加入用户交流群。',
      confirmText: '复制微信号',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: 'augmentcode',
            success: () => {
              wx.showToast({
                title: '微信号已复制',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  },

  openGithub() {
    wx.setClipboardData({
      data: 'https://github.com/augmentcode/exam-helper',
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success'
        })
      }
    })
  }
})