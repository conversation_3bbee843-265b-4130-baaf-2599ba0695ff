// pages/test-invite/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    testGroupInfo: null,
    logs: []
  },

  onLoad() {
    this.addLog('邀请功能测试页面加载完成')
  },

  // 添加日志
  addLog(message) {
    const logs = this.data.logs
    const timestamp = new Date().toLocaleTimeString()
    logs.unshift(`[${timestamp}] ${message}`)
    this.setData({ logs: logs.slice(0, 20) }) // 只保留最近20条
  },

  // 创建测试小组
  async createTestGroup() {
    this.addLog('开始创建测试小组...')
    
    try {
      const result = await SmartApi.createStudyGroup(
        'test-exam-001',
        '测试考试',
        '邀请功能测试小组'
      )

      if (result.success) {
        this.setData({ testGroupInfo: result.data })
        this.addLog(`小组创建成功，邀请码: ${result.data.inviteCode}`)
      } else {
        this.addLog(`小组创建失败: ${result.error}`)
      }
    } catch (error) {
      this.addLog(`小组创建异常: ${error.message}`)
    }
  },

  // 测试验证邀请码
  async testVerifyCode() {
    if (!this.data.testGroupInfo) {
      this.addLog('请先创建测试小组')
      return
    }

    this.addLog('开始验证邀请码...')
    
    try {
      const result = await SmartApi.verifyInviteCode(this.data.testGroupInfo.inviteCode)

      if (result.success) {
        this.addLog('邀请码验证成功')
        this.addLog(`验证结果: ${JSON.stringify(result.data)}`)
      } else {
        this.addLog(`邀请码验证失败: ${result.error}`)
      }
    } catch (error) {
      this.addLog(`邀请码验证异常: ${error.message}`)
    }
  },

  // 打开邀请页面
  openInvitePage() {
    if (!this.data.testGroupInfo) {
      this.addLog('请先创建测试小组')
      return
    }

    this.addLog('跳转到邀请页面...')
    wx.navigateTo({
      url: `/pages/join-study-group/index?inviteCode=${this.data.testGroupInfo.inviteCode}`
    })
  },

  // 修复小组数据
  async fixGroupData() {
    this.addLog('开始修复小组数据...')

    try {
      const result = await SmartApi.fixGroupData()

      if (result.success) {
        this.addLog(`修复完成: 总共${result.data.totalGroups}个小组，修复了${result.data.fixedGroups}个`)
        result.data.results.forEach(item => {
          if (item.fixed) {
            this.addLog(`修复小组 ${item.groupName}: ${item.before} -> ${item.after}人`)
          }
        })
      } else {
        this.addLog(`修复失败: ${result.error}`)
      }
    } catch (error) {
      this.addLog(`修复异常: ${error.message}`)
    }
  },

  // 清理测试数据
  async cleanTestData() {
    if (!this.data.testGroupInfo) {
      this.addLog('没有测试数据需要清理')
      return
    }

    this.addLog('开始清理测试数据...')

    try {
      const result = await SmartApi.leaveStudyGroup(this.data.testGroupInfo.groupId)

      if (result.success) {
        this.setData({ testGroupInfo: null })
        this.addLog('测试数据清理成功')
      } else {
        this.addLog(`测试数据清理失败: ${result.error}`)
      }
    } catch (error) {
      this.addLog(`测试数据清理异常: ${error.message}`)
    }
  }
})
