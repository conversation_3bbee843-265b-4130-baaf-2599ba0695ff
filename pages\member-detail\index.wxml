<!-- pages/member-detail/index.wxml -->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 成员详情内容 -->
  <view class="member-detail" wx:if="{{!loading && memberInfo}}">
    <!-- 用户信息头部 -->
    <view class="member-header">
      <view class="member-avatar">
        <image src="{{memberInfo.avatarUrl || '/images/default-avatar.png'}}" class="avatar-img" />
        <view class="online-status {{memberInfo.isOnline ? 'online' : 'offline'}}"></view>
      </view>
      <view class="member-basic-info">
        <text class="member-name">{{memberInfo.nickName}}</text>
        <text class="online-text">{{memberInfo.isOnline ? '🟢 在线中' : '⚫ 离线'}}</text>
        <text class="last-active">最后复习: {{memberInfo.lastActiveTime}}</text>
      </view>
    </view>

    <!-- 复习统计 -->
    <view class="stats-section">
      <view class="section-title">📊 复习统计</view>
      
      <view class="stat-item">
        <text class="stat-label">今日复习</text>
        <text class="stat-value">{{Utils.formatStudyTime(memberProgress.todayStudyTime)}}</text>
      </view>

      <view class="stat-item">
        <text class="stat-label">本周复习</text>
        <text class="stat-value">{{Utils.formatStudyTime(memberProgress.weeklyStudyTime)}}</text>
      </view>
      
      <view class="stat-item">
        <text class="stat-label">任务进度</text>
        <text class="stat-value">{{memberProgress.tasksCompleted}}/{{memberProgress.tasksTotal}} 已完成</text>
      </view>
      <view class="progress-container">
        <view class="progress-bar task-progress">
          <view class="progress-fill" style="width: {{(memberProgress.tasksCompleted / memberProgress.tasksTotal * 100)}}%"></view>
        </view>
        <text class="progress-percent">{{Math.round(memberProgress.tasksCompleted / memberProgress.tasksTotal * 100)}}%</text>
      </view>
      
      <view class="stat-item">
        <text class="stat-label">本周进度</text>
        <text class="stat-value">{{memberProgress.weeklyProgress}}%</text>
      </view>
      <view class="progress-container">
        <view class="progress-bar weekly-progress">
          <view class="progress-fill" style="width: {{memberProgress.weeklyProgress}}%"></view>
        </view>
        <text class="progress-percent">{{memberProgress.weeklyProgress}}%</text>
      </view>
      
      <view class="stat-item">
        <text class="stat-label">连续打卡</text>
        <text class="stat-value">{{memberProgress.consecutiveDays}}天</text>
      </view>
    </view>

    <!-- 今日任务 -->
    <view class="tasks-section">
      <view class="section-title">📋 今日任务完成情况</view>
      
      <view class="task-list">
        <view class="task-item" wx:for="{{todayTasks}}" wx:key="id">
          <view class="task-status">
            <text class="task-icon">{{Utils.getTaskStatusIcon(item.status)}}</text>
          </view>
          <view class="task-content">
            <text class="task-name">{{item.name}}</text>
            <text class="task-status-text">{{Utils.getTaskStatusText(item.status)}}</text>
            
            <!-- 进行中的任务显示进度条 -->
            <view class="task-progress-container" wx:if="{{item.status === 'in_progress' && item.progress}}">
              <view class="progress-bar single-task-progress">
                <view class="progress-fill" style="width: {{item.progress}}%"></view>
              </view>
              <text class="progress-percent">{{item.progress}}%</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="empty-tasks" wx:if="{{todayTasks.length === 0}}">
        <text class="empty-text">暂无任务数据</text>
      </view>
    </view>

    <!-- 点赞区域 -->
    <view class="like-section">
      <button 
        class="like-btn {{hasLiked ? 'liked' : ''}}"
        bindtap="onLikeMember"
        disabled="{{hasLiked}}"
      >
        <text class="like-icon">👍</text>
        <text class="like-text">{{hasLiked ? '已点赞' : '点赞鼓励'}}</text>
      </button>
      <text class="like-count">已有{{likeCount}}人为TA点赞</text>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:if="{{!loading && !memberInfo}}">
    <text class="error-text">加载失败，请重试</text>
    <button class="retry-btn" bindtap="loadMemberDetail">重新加载</button>
  </view>
</view>

<wxs module="Utils">
  var round = function(num) {
    return Math.round(num)
  }

  var formatStudyTime = function(minutes) {
    if (minutes < 60) {
      return minutes + '分钟'
    } else {
      var hours = Math.floor(minutes / 60)
      var mins = minutes % 60
      return mins > 0 ? hours + '小时' + mins + '分钟' : hours + '小时'
    }
  }

  var getTaskStatusText = function(status) {
    if (status === 'completed') return '已完成'
    if (status === 'in_progress') return '进行中'
    if (status === 'not_started') return '未开始'
    return '未知状态'
  }

  var getTaskStatusIcon = function(status) {
    if (status === 'completed') return '✅'
    if (status === 'in_progress') return '⏳'
    if (status === 'not_started') return '❌'
    return '❓'
  }

  module.exports = {
    round: round,
    formatStudyTime: formatStudyTime,
    getTaskStatusText: getTaskStatusText,
    getTaskStatusIcon: getTaskStatusIcon
  }
</wxs>
