// pages/member-detail/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    userId: '',
    groupId: '',
    memberInfo: null,
    memberProgress: null,
    todayTasks: [],
    loading: true,
    likeCount: 0,
    hasLiked: false
  },

  async onLoad(options) {
    const { userId, groupId } = options
    if (!userId || !groupId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      wx.navigateBack()
      return
    }

    this.setData({ userId, groupId })
    await this.loadMemberDetail()
  },

  // 加载成员详情
  async loadMemberDetail() {
    this.setData({ loading: true })

    try {
      // 模拟API调用，实际应该调用真实API
      const result = await this.getMockMemberDetail()
      
      if (result.success && result.data) {
        const { memberInfo, progress, tasks, likeInfo } = result.data
        
        this.setData({
          memberInfo,
          memberProgress: progress,
          todayTasks: tasks || [],
          likeCount: likeInfo.count || 0,
          hasLiked: likeInfo.hasLiked || false
        })
      } else {
        wx.showToast({
          title: result.error || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载成员详情失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 模拟数据（实际开发中应该调用真实API）
  async getMockMemberDetail() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            memberInfo: {
              userId: this.data.userId,
              nickName: '张三',
              avatarUrl: '/images/default-avatar.png',
              isOnline: true,
              lastActiveTime: '10分钟前'
            },
            progress: {
              todayStudyTime: 150, // 分钟
              weeklyStudyTime: 1200, // 分钟
              tasksCompleted: 8,
              tasksTotal: 10,
              weeklyProgress: 85,
              consecutiveDays: 7
            },
            tasks: [
              { id: 1, name: '数学-函数与导数', status: 'completed' },
              { id: 2, name: '英语-阅读理解练习', status: 'completed' },
              { id: 3, name: '政治-马原第三章', status: 'completed' },
              { id: 4, name: '数学-极限计算', status: 'in_progress', progress: 50 },
              { id: 5, name: '英语-作文练习', status: 'not_started' }
            ],
            likeInfo: {
              count: 12,
              hasLiked: false
            }
          }
        })
      }, 500)
    })
  },

  // 点赞功能
  async onLikeMember() {
    if (this.data.hasLiked) {
      wx.showToast({
        title: '已经点过赞了',
        icon: 'none'
      })
      return
    }

    try {
      // 模拟API调用
      const result = await this.mockLikeMember()
      
      if (result.success) {
        this.setData({
          likeCount: this.data.likeCount + 1,
          hasLiked: true
        })
        
        wx.showToast({
          title: '点赞成功',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: result.error || '点赞失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('点赞失败:', error)
      wx.showToast({
        title: '点赞失败，请重试',
        icon: 'none'
      })
    }
  },

  // 模拟点赞API
  async mockLikeMember() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true })
      }, 300)
    })
  },

  // 格式化复习时长
  formatStudyTime(minutes) {
    if (minutes < 60) {
      return `${minutes}分钟`
    } else {
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`
    }
  },

  // 获取任务状态文本
  getTaskStatusText(status) {
    switch (status) {
      case 'completed': return '已完成'
      case 'in_progress': return '进行中'
      case 'not_started': return '未开始'
      default: return '未知状态'
    }
  },

  // 获取任务状态图标
  getTaskStatusIcon(status) {
    switch (status) {
      case 'completed': return '✅'
      case 'in_progress': return '⏳'
      case 'not_started': return '❌'
      default: return '❓'
    }
  }
})
