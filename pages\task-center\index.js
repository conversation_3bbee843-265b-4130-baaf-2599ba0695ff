// pages/task-center/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    taskStats: [
      { label: '总计划', value: '0' },
      { label: '今日', value: '0' },
      { label: '已完成', value: '0' },
      { label: '完成率', value: '0%' }
    ],
    filterOptions: [],
    currentFilter: 'all',
    activeTab: 0,

    // 考试筛选相关字段
    examFilterMode: false,        // 考试筛选模式开关
    examFilterOptions: [],       // 考试筛选选项
    availableExams: [],          // 可用考试列表
    selectedExamId: null,        // 当前选中的考试ID
    basicFilterOptions: [],      // 基础筛选选项备份

    // 考试下拉菜单相关字段
    examDropdownValue: '',       // 下拉菜单当前选中值
    examDropdownOptions: [],     // 下拉菜单选项列表
    showExamDropdown: false,     // 下拉菜单显示状态

    tasks: [],
    filteredTasks: [],
    groupedTasks: [],
    showActionSheet: false,
    selectedTask: null,
    taskActions: [],

    // 性能优化相关
    checkpointCache: {}, // 检查点数据缓存
    loadedCheckpoints: [], // 已加载的检查点任务ID数组

    // 检查点弹窗相关
    showCheckpointModal: false,
    checkpointModalData: {
      taskId: '',
      taskTitle: '',
      subtasks: [],
      completedCount: 0
    },
    newCheckpointTitle: '',
    showAddCheckpointInput: false,

    showFilterMenu: false,
    currentSort: 'dueDate',
    selectedPriorities: [],
    sortOptions: [],
    priorityOptions: [],

    // 高级筛选扩展字段
    advancedExamFilterEnabled: false,    // 高级筛选中的考试筛选开关
    selectedExamsInAdvanced: [],         // 高级筛选中选中的考试列表
    quickFilterOptions: [],              // 快捷筛选选项
    savedFilterStates: [],               // 保存的筛选状态
  },

  onLoad(options) {
    console.log('任务中心页面加载，参数:', options)

    // 保存页面参数供后续使用
    this.pageOptions = options || {}

    // 处理URL参数
    this.handleUrlParameters(options)

    this.initPage()
  },

  // 处理URL参数
  handleUrlParameters(options) {
    if (!options) return

    const { examId, examName, autoFilter } = options

    console.log('处理URL参数:', { examId, examName, autoFilter })

    // 验证所有参数
    const validation = this.validateUrlParameters(options)

    if (!validation.isValid) {
      console.warn('URL参数验证失败:', validation.errors)
      this.showParameterError(validation.errors.join(', '))
      return
    }

    // 如果有examId参数，设置待处理的考试筛选参数
    if (examId) {
      this.pendingExamFilter = {
        examId: examId,
        examName: examName || null,
        autoFilter: autoFilter === 'true',
        fromUrl: true
      }
      console.log('设置待处理的考试筛选:', this.pendingExamFilter)
    }
  },

  // 验证examId参数
  validateExamId(examId) {
    // 检查examId格式（假设是字符串且不为空）
    if (!examId || typeof examId !== 'string' || examId.trim() === '') {
      return false
    }

    // 检查长度限制
    if (examId.length > 100) {
      return false
    }

    // 检查是否包含非法字符（可根据实际需求调整）
    const invalidChars = /[<>\"'&]/
    if (invalidChars.test(examId)) {
      return false
    }

    return true
  },

  // 验证所有URL参数
  validateUrlParameters(options) {
    const errors = []

    if (options.examId) {
      if (!this.validateExamId(options.examId)) {
        errors.push('考试ID格式无效')
      }
    }

    if (options.examName) {
      if (typeof options.examName !== 'string' || options.examName.length > 50) {
        errors.push('考试名称格式无效')
      }
    }

    if (options.autoFilter) {
      if (!['true', 'false'].includes(options.autoFilter)) {
        errors.push('自动筛选参数无效')
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  },

  // 显示参数错误提示
  showParameterError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  },

  onShow() {
    // 检查全局状态中的待处理考试筛选参数
    this.checkGlobalPendingExamFilter()

    this.loadData()
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      })
    }
  },

  // 检查全局状态中的待处理考试筛选参数
  checkGlobalPendingExamFilter() {
    const app = getApp()
    if (app.globalData && app.globalData.pendingExamFilter) {
      console.log('发现全局待处理考试筛选:', app.globalData.pendingExamFilter)

      // 设置为当前页面的待处理参数
      this.pendingExamFilter = app.globalData.pendingExamFilter

      // 清除全局状态
      delete app.globalData.pendingExamFilter

      console.log('已转移全局考试筛选参数到当前页面')
    }
  },

  onPullDownRefresh() {
    this.loadData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 初始化页面
  initPage() {
    this.initFilterOptions()
    this.initTaskActions()
    this.initSortOptions()
    this.loadSavedFilterStates() // 加载保存的筛选状态
    this.initDemoStats() // 先初始化演示数据
    this.loadData()
  },

  // 初始化演示统计数据
  initDemoStats() {
    const taskStats = [
      { label: '总计划', value: '0' },
      { label: '今日', value: '0' },
      { label: '已完成', value: '0' },
      { label: '完成率', value: '0%' }
    ]

    this.setData({ taskStats }, () => {
      console.log('统计数据已设置:', this.data.taskStats)
    })
    console.log('初始化演示统计数据:', taskStats)
  },

  // 初始化筛选选项
  initFilterOptions() {
    const basicFilterOptions = [
      { value: 'all', label: '全部', count: 0 },
      { value: 'today', label: '今天', count: 0 },
      { value: 'pending', label: '待完成', count: 0 },
      { value: 'completed', label: '已完成', count: 0 },
      { value: 'overdue', label: '已逾期', count: 0 }
    ]

    // 设置默认activeTab
    const activeTab = basicFilterOptions.findIndex(option => option.value === this.data.currentFilter)
    this.setData({
      filterOptions: basicFilterOptions,
      basicFilterOptions: basicFilterOptions, // 保存基础筛选选项备份
      activeTab: activeTab >= 0 ? activeTab : 0
    })
  },

  // 初始化考试筛选选项
  initExamFilterOptions() {
    const { availableExams } = this.data
    const examFilterOptions = [
      { value: 'back_to_basic', label: '← 返回', count: 0, isBackButton: true }
    ]

    // 添加考试选项
    availableExams.forEach(exam => {
      examFilterOptions.push({
        value: exam.id,
        label: exam.name,
        count: exam.taskCount || 0,
        examId: exam.id
      })
    })

    this.setData({ examFilterOptions })
  },

  // 初始化考试下拉选项
  initExamDropdownOptions() {
    const { availableExams } = this.data

    console.log('初始化考试下拉选项，可用考试数量:', availableExams.length)

    // 创建下拉选项数组，格式：[{text: '显示文本', value: '值'}]
    const examDropdownOptions = []

    // 第一项：全部考试选项（用于清除筛选）
    examDropdownOptions.push({
      text: '全部考试',
      value: ''
    })

    // 添加考试选项
    availableExams.forEach(exam => {
      const taskCountText = exam.taskCount > 0 ? ` (${exam.taskCount}个任务)` : ' (0个任务)'
      examDropdownOptions.push({
        text: exam.name + taskCountText, // 恢复完整名称，让用户在下拉时能看清楚
        value: exam.id
      })
    })

    console.log('生成的下拉选项:', examDropdownOptions.length, '个选项')

    this.setData({ examDropdownOptions })
  },

  // 更新考试下拉选项（当任务数量变化时）
  updateExamDropdownOptions() {
    // 重新生成下拉选项以更新任务数量
    this.initExamDropdownOptions()
  },

  // 更新考试筛选选项的任务计数
  updateExamFilterCounts() {
    const { availableExams, examFilterOptions, tasks } = this.data

    // 重新计算每个考试的任务数量
    const updatedExams = availableExams.map(exam => {
      const taskCount = tasks.filter(task =>
        task.examId === exam.id || task.examName === exam.name
      ).length

      return { ...exam, taskCount }
    })

    // 更新考试筛选选项的计数
    const updatedExamFilterOptions = examFilterOptions.map(option => {
      if (option.isBackButton) {
        return option
      }

      const exam = updatedExams.find(e => e.id === option.examId)
      return {
        ...option,
        count: exam ? exam.taskCount : 0
      }
    })

    this.setData({
      availableExams: updatedExams,
      examFilterOptions: updatedExamFilterOptions
    })

    // 同时更新下拉选项的任务计数
    this.updateExamDropdownOptions()
  },

  // 切换到考试筛选模式
  switchToExamFilter() {
    const { examFilterOptions, availableExams } = this.data

    // 检查是否有可用的考试数据
    if (availableExams.length === 0) {
      wx.showToast({
        title: '暂无考试数据',
        icon: 'none',
        duration: 2000
      })
      return
    }

    console.log('切换到考试筛选模式，可用考试:', availableExams.length)

    this.setData({
      examFilterMode: true,
      filterOptions: examFilterOptions,
      activeTab: 0,
      currentFilter: 'back_to_basic',
      selectedExamId: null
    })

    // 显示切换提示
    wx.showToast({
      title: '已切换到考试筛选',
      icon: 'success',
      duration: 1500
    })

    // 触发筛选更新和状态同步
    this.filterTasks()
    this.syncFilterState()
  },

  // 切换回基础筛选模式
  switchToBasicFilter() {
    console.log('切换回基础筛选模式')

    this.setData({
      examFilterMode: false,
      filterOptions: this.data.basicFilterOptions,
      selectedExamId: null,
      currentFilter: 'all',
      activeTab: 0
    })

    // 显示切换提示
    wx.showToast({
      title: '已返回基础筛选',
      icon: 'success',
      duration: 1500
    })

    // 触发筛选更新和状态同步
    this.filterTasks()
    this.syncFilterState()
  },

  // 重置考试筛选状态
  resetExamFilterState() {
    this.setData({
      examFilterMode: false,
      examFilterOptions: [],
      availableExams: [],
      selectedExamId: null
    })
  },

  // 验证筛选状态的一致性
  validateFilterState() {
    const { examFilterMode, filterOptions, basicFilterOptions, examFilterOptions } = this.data

    if (examFilterMode) {
      // 考试筛选模式下，应该使用examFilterOptions
      if (filterOptions !== examFilterOptions) {
        console.warn('考试筛选模式状态不一致，自动修复')
        this.setData({ filterOptions: examFilterOptions })
      }
    } else {
      // 基础筛选模式下，应该使用basicFilterOptions
      if (filterOptions !== basicFilterOptions) {
        console.warn('基础筛选模式状态不一致，自动修复')
        this.setData({ filterOptions: basicFilterOptions })
      }
    }
  },

  // 获取当前筛选模式信息
  getCurrentFilterMode() {
    const { examFilterMode, selectedExamId, currentFilter } = this.data

    return {
      mode: examFilterMode ? 'exam' : 'basic',
      examFilterMode,
      selectedExamId,
      currentFilter,
      isExamSelected: !!selectedExamId
    }
  },

  // 调试方法：验证数据结构扩展
  debugExamFilterStructure() {
    const {
      examFilterMode,
      examFilterOptions,
      availableExams,
      selectedExamId,
      basicFilterOptions,
      filterOptions
    } = this.data

    console.log('📊 考试筛选数据结构验证:', {
      examFilterMode,
      examFilterOptionsLength: examFilterOptions.length,
      availableExamsLength: availableExams.length,
      selectedExamId,
      basicFilterOptionsLength: basicFilterOptions.length,
      filterOptionsLength: filterOptions.length,
      hasExamFilterOption: filterOptions.some(option => option.isExamFilter)
    })

    return true
  },

  // 调试方法：验证考试数据加载
  debugExamDataLoading() {
    const { availableExams, examFilterOptions } = this.data

    console.log('📚 考试数据加载验证:', {
      availableExamsCount: availableExams.length,
      examFilterOptionsCount: examFilterOptions.length,
      examsWithTasks: availableExams.filter(exam => exam.taskCount > 0).length,
      examDetails: availableExams.map(exam => ({
        id: exam.id,
        name: exam.name,
        taskCount: exam.taskCount,
        status: exam.status
      }))
    })

    return true
  },

  // 调试方法：验证筛选模式切换
  debugFilterModeSwitch() {
    const filterMode = this.getCurrentFilterMode()
    const { filterOptions, examFilterOptions, basicFilterOptions } = this.data

    console.log('🔄 筛选模式切换验证:', {
      currentMode: filterMode,
      filterOptionsLength: filterOptions.length,
      examFilterOptionsLength: examFilterOptions.length,
      basicFilterOptionsLength: basicFilterOptions.length,
      isStateConsistent: filterMode.examFilterMode ?
        (filterOptions === examFilterOptions) :
        (filterOptions === basicFilterOptions),
      switchFunctions: {
        switchToExamFilter: typeof this.switchToExamFilter === 'function',
        switchToBasicFilter: typeof this.switchToBasicFilter === 'function',
        syncFilterState: typeof this.syncFilterState === 'function'
      }
    })

    return true
  },

  // 调试方法：验证考试筛选核心逻辑
  debugExamFilterLogic() {
    const { tasks, availableExams, examFilterMode, selectedExamId } = this.data

    console.log('🔍 考试筛选核心逻辑验证:', {
      totalTasks: tasks.length,
      availableExams: availableExams.length,
      examFilterMode,
      selectedExamId,
      tasksWithExamId: tasks.filter(task => task.examId).length,
      tasksWithExamName: tasks.filter(task => task.examName).length
    })

    // 测试每个考试的任务筛选
    availableExams.forEach(exam => {
      const filteredTasks = this.filterTasksByExam(tasks, exam.id)
      console.log(`考试 "${exam.name}" (${exam.id}) 的任务:`, {
        taskCount: filteredTasks.length,
        taskIds: filteredTasks.map(t => t.id),
        taskTitles: filteredTasks.map(t => t.title)
      })
    })

    return true
  },

  // 测试方法：模拟筛选模式切换
  testFilterModeSwitch() {
    console.log('🧪 开始测试筛选模式切换...')

    // 记录初始状态
    const initialMode = this.getCurrentFilterMode()
    console.log('初始状态:', initialMode)

    // 测试切换到考试筛选模式
    this.switchToExamFilter()
    const examMode = this.getCurrentFilterMode()
    console.log('切换到考试筛选后:', examMode)

    // 测试切换回基础筛选模式
    setTimeout(() => {
      this.switchToBasicFilter()
      const basicMode = this.getCurrentFilterMode()
      console.log('切换回基础筛选后:', basicMode)

      console.log('✅ 筛选模式切换测试完成')
    }, 2000)

    return true
  },

  // 测试方法：验证完整筛选功能
  testCompleteFilterFunction() {
    console.log('🧪 开始测试完整筛选功能...')

    const { tasks, availableExams } = this.data

    // 测试基础筛选
    console.log('测试基础筛选:')
    const basicFilters = ['all', 'today', 'pending', 'completed', 'overdue']
    basicFilters.forEach(filter => {
      const filtered = this.filterTasksByBasicFilter(tasks, filter)
      console.log(`  ${filter}: ${filtered.length} 个任务`)
    })

    // 测试考试筛选
    console.log('测试考试筛选:')
    availableExams.slice(0, 3).forEach(exam => {
      const filtered = this.filterTasksByExam(tasks, exam.id)
      console.log(`  ${exam.name}: ${filtered.length} 个任务`)
    })

    // 测试组合筛选
    if (availableExams.length > 0) {
      console.log('测试组合筛选:')
      const firstExam = availableExams[0]
      const combined = this.filterTasksWithCombination(tasks, firstExam.id, 'pending')
      console.log(`  ${firstExam.name} + 待完成: ${combined.length} 个任务`)
    }

    // 测试性能缓存
    console.log('测试性能缓存:')
    const cacheKey = this.generateFilterCacheKey()
    console.log(`  缓存键: ${cacheKey}`)

    console.log('✅ 完整筛选功能测试完成')
    return true
  },

  // UI优化方法：显示筛选反馈
  showFilterFeedback(filterType, count) {
    let message = ''

    switch (filterType) {
      case 'exam':
        const { availableExams, selectedExamId } = this.data
        const exam = availableExams.find(e => e.id === selectedExamId)
        message = exam ? `找到 ${count} 个"${exam.name}"相关任务` : `找到 ${count} 个考试任务`
        break
      case 'today':
        message = `找到 ${count} 个今日任务`
        break
      case 'pending':
        message = `找到 ${count} 个待完成任务`
        break
      case 'completed':
        message = `找到 ${count} 个已完成任务`
        break
      case 'overdue':
        message = `找到 ${count} 个逾期任务`
        break
      default:
        message = `找到 ${count} 个任务`
        break
    }

    if (count === 0) {
      message = message.replace('找到 0 个', '暂无')
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 1500
    })
  },

  // UI优化方法：更新筛选状态显示
  updateFilterStatusDisplay() {
    const { examFilterMode, selectedExamId, currentFilter, filteredTasks } = this.data

    // 更新页面标题
    if (examFilterMode && selectedExamId) {
      const { availableExams } = this.data
      const exam = availableExams.find(e => e.id === selectedExamId)
      if (exam) {
        wx.setNavigationBarTitle({
          title: `${exam.name} - 任务中心`
        })
      }
    } else {
      wx.setNavigationBarTitle({
        title: '任务中心'
      })
    }

    // 显示筛选反馈
    const filterType = examFilterMode ? 'exam' : currentFilter
    this.showFilterFeedback(filterType, filteredTasks.length)
  },

  // UI优化方法：筛选动画效果
  animateFilterChange() {
    // 添加筛选切换的视觉反馈（已移除震动效果）
    const query = wx.createSelectorQuery()
    query.select('.filter-section').boundingClientRect()
    query.exec((res) => {
      if (res[0]) {
        // 移除震动反馈，保留其他视觉效果
        console.log('筛选切换动画效果触发')
      }
    })
  },

  // 调试方法：验证UI优化功能
  debugUIOptimizations() {
    console.log('🎨 UI优化功能验证:', {
      filterModeIndicator: !!this.data.examFilterMode,
      filterStatusTip: !!this.data.selectedExamId,
      uiMethods: {
        showFilterFeedback: typeof this.showFilterFeedback === 'function',
        updateFilterStatusDisplay: typeof this.updateFilterStatusDisplay === 'function',
        animateFilterChange: typeof this.animateFilterChange === 'function'
      },
      currentFilterState: {
        examFilterMode: this.data.examFilterMode,
        selectedExamId: this.data.selectedExamId,
        currentFilter: this.data.currentFilter,
        activeTab: this.data.activeTab
      }
    })

    // 测试筛选反馈
    this.showFilterFeedback('test', 5)

    return true
  },

  // 调试方法：验证URL参数支持功能
  debugUrlParameterSupport() {
    console.log('🔗 URL参数支持功能验证:', {
      pageOptions: this.pageOptions,
      pendingExamFilter: this.pendingExamFilter,
      urlMethods: {
        handleUrlParameters: typeof this.handleUrlParameters === 'function',
        validateUrlParameters: typeof this.validateUrlParameters === 'function',
        processPendingExamFilter: typeof this.processPendingExamFilter === 'function',
        syncFilterStateToUrl: typeof this.syncFilterStateToUrl === 'function',
        generateShareUrl: typeof this.generateShareUrl === 'function'
      },
      currentState: {
        examFilterMode: this.data.examFilterMode,
        selectedExamId: this.data.selectedExamId,
        currentFilter: this.data.currentFilter
      }
    })

    // 测试URL生成
    const shareUrl = this.generateShareUrl()
    console.log('当前状态的分享URL:', shareUrl)

    // 测试参数验证
    const testParams = {
      examId: 'test-exam-123',
      examName: '测试考试',
      autoFilter: 'true'
    }
    const validation = this.validateUrlParameters(testParams)
    console.log('参数验证测试:', { testParams, validation })

    return true
  },

  // 调试方法：验证高级筛选功能集成
  debugAdvancedFilterIntegration() {
    const {
      advancedExamFilterEnabled,
      selectedExamsInAdvanced,
      quickFilterOptions,
      savedFilterStates,
      availableExams
    } = this.data

    console.log('🔧 高级筛选功能集成验证:', {
      advancedExamFilterEnabled,
      selectedExamsInAdvanced,
      quickFilterOptionsCount: quickFilterOptions.length,
      savedFilterStatesCount: savedFilterStates.length,
      availableExamsCount: availableExams.length,
      advancedMethods: {
        toggleExamFilterInAdvanced: typeof this.toggleExamFilterInAdvanced === 'function',
        toggleExamInAdvanced: typeof this.toggleExamInAdvanced === 'function',
        applyQuickFilter: typeof this.applyQuickFilter === 'function',
        filterTasksByMultipleExams: typeof this.filterTasksByMultipleExams === 'function',
        saveCurrentFilterState: typeof this.saveCurrentFilterState === 'function',
        restoreFilterState: typeof this.restoreFilterState === 'function'
      }
    })

    // 测试快捷筛选选项
    console.log('快捷筛选选项:', quickFilterOptions.map(option => ({
      value: option.value,
      label: option.label,
      description: option.description
    })))

    // 测试多考试筛选
    if (availableExams.length > 0) {
      const testExamIds = availableExams.slice(0, 2).map(exam => exam.id)
      const testResult = this.filterTasksByMultipleExams(this.data.tasks, testExamIds)
      console.log('多考试筛选测试:', {
        testExamIds,
        resultCount: testResult.length
      })
    }

    return true
  },

  // 调试方法：测试考试简称生成算法
  debugExamShortNameGeneration() {
    const testCases = [
      '2024年国家公务员考试',
      '软件设计师资格考试',
      '英语四级考试',
      '上半年教师资格证考试',
      '全国计算机等级考试',
      '驾驶证科目一考试',
      '会计从业资格考试',
      '短名称',
      '这是一个非常非常长的考试名称用来测试截取功能'
    ]

    console.log('🧪 考试简称生成算法测试:')
    testCases.forEach(testCase => {
      const shortName = this.generateExamShortName(testCase)
      console.log(`"${testCase}" → "${shortName}"`)
    })

    return true
  },

  // 调试方法：验证考试下拉选项数据
  debugExamDropdownOptions() {
    const { examDropdownOptions, availableExams } = this.data

    console.log('🔍 考试下拉选项数据验证:')
    console.log('可用考试数量:', availableExams.length)
    console.log('下拉选项数量:', examDropdownOptions.length)

    examDropdownOptions.forEach((option, index) => {
      console.log(`选项${index}: "${option.text}" -> "${option.value}"`)
    })

    // 验证数据格式
    const isValidFormat = examDropdownOptions.every(option =>
      option.hasOwnProperty('text') && option.hasOwnProperty('value')
    )

    console.log('数据格式验证:', isValidFormat ? '✅ 正确' : '❌ 错误')

    return {
      examCount: availableExams.length,
      optionCount: examDropdownOptions.length,
      isValidFormat,
      options: examDropdownOptions
    }
  },

  // 调试方法：测试下拉菜单选择事件
  debugExamDropdownSelection() {
    const { availableExams, examDropdownValue, selectedExamId } = this.data

    console.log('🎯 下拉菜单选择事件测试:')
    console.log('当前状态:', {
      examDropdownValue,
      selectedExamId,
      availableExamsCount: availableExams.length
    })

    // 模拟选择第一个考试
    if (availableExams.length > 0) {
      const testExamId = availableExams[0].id
      console.log('模拟选择考试:', testExamId)

      // 模拟事件对象
      const mockEvent = { detail: testExamId }
      this.onExamDropdownChange(mockEvent)
    }

    // 模拟清除选择
    setTimeout(() => {
      console.log('模拟清除选择')
      const mockClearEvent = { detail: '' }
      this.onExamDropdownChange(mockClearEvent)
    }, 2000)

    return true
  },

  // 初始化任务操作
  initTaskActions() {
    const taskActions = [
      { name: '查看详情', action: 'viewDetail' },
      { name: '编辑计划', action: 'edit' },
      { name: '开始复习', action: 'start' },
      { name: '标记完成', action: 'complete' },
      { name: '删除计划', action: 'delete', color: '#ee0a24' }
    ]

    this.setData({ taskActions })
  },

  // 初始化排序选项
  initSortOptions() {
    const sortOptions = [
      { value: 'dueDate', label: '按截止时间' },
      { value: 'priority', label: '按优先级' },
      { value: 'createTime', label: '按创建时间' },
      { value: 'progress', label: '按完成进度' }
    ]

    const priorityOptions = [
      { value: 'high', label: '高优先级' },
      { value: 'medium', label: '中优先级' },
      { value: 'low', label: '低优先级' }
    ]

    const quickFilterOptions = [
      {
        value: 'urgent_tasks',
        label: '紧急任务',
        description: '高优先级且即将到期的任务'
      },
      {
        value: 'today_high_priority',
        label: '今日重点',
        description: '今天的高优先级任务'
      },
      {
        value: 'overdue_important',
        label: '逾期重要',
        description: '已逾期的重要任务'
      },
      {
        value: 'exam_related_pending',
        label: '考试待办',
        description: '考试相关的待完成任务'
      },
      {
        value: 'quick_wins',
        label: '快速完成',
        description: '简单易完成的任务'
      }
    ]

    this.setData({ sortOptions, priorityOptions, quickFilterOptions })
  },

  // 加载数据
  async loadData() {
    // 检查登录状态
    const app = getApp()
    const loginStatus = app.checkLoginStatus()

    if (!loginStatus.isLoggedIn) {
      console.log('用户未登录，显示空状态')
      // 设置空状态数据
      this.setData({
        tasks: [],
        taskStats: [
          { label: '总计划', value: '0' },
          { label: '今日', value: '0' },
          { label: '已完成', value: '0' },
          { label: '完成率', value: '0%' }
        ]
      })
      this.filterTasks()
      this.updateFilterCounts()
      return
    }

    await Promise.all([
      this.loadTasks(), // 加载任务列表
      this.loadTaskStats(), // 并行加载统计数据
      this.loadAvailableExams() // 并行加载考试数据
    ])

    // 处理待处理的考试筛选参数
    this.processPendingExamFilter()
  },

  // 处理待处理的考试筛选参数
  processPendingExamFilter() {
    if (!this.pendingExamFilter) return

    const { examId, examName, autoFilter } = this.pendingExamFilter
    console.log('处理待处理的考试筛选:', this.pendingExamFilter)

    // 验证考试是否存在
    const { availableExams } = this.data
    let targetExam = availableExams.find(exam => exam.id === examId)

    // 如果通过ID没找到，尝试通过名称查找
    if (!targetExam && examName) {
      targetExam = availableExams.find(exam => exam.name === examName)
    }

    if (targetExam) {
      console.log('找到目标考试:', targetExam)

      // 自动切换到考试筛选模式
      this.autoSwitchToExamFilter(targetExam.id, autoFilter)

      // 显示跳转成功提示
      wx.showToast({
        title: `已切换到"${targetExam.name}"`,
        icon: 'success',
        duration: 2000
      })
    } else {
      console.warn('未找到指定的考试:', { examId, examName })
      this.showParameterError('指定的考试不存在')
    }

    // 清除待处理参数
    this.pendingExamFilter = null
  },

  // 自动切换到考试筛选模式
  autoSwitchToExamFilter(examId, autoFilter = true) {
    console.log('自动切换到考试筛选模式:', { examId, autoFilter })

    if (!autoFilter) {
      // 如果不自动筛选，只是设置选中的考试ID
      this.setData({ selectedExamId: examId })
      return
    }

    // 确保考试筛选选项已初始化
    this.initExamFilterOptions()

    // 切换到考试筛选模式
    this.setData({
      examFilterMode: true,
      filterOptions: this.data.examFilterOptions,
      selectedExamId: examId,
      currentFilter: examId,
      activeTab: this.findExamTabIndex(examId)
    })

    // 触发筛选
    this.filterTasks()
    this.syncFilterState()
  },

  // 查找考试在筛选选项中的索引
  findExamTabIndex(examId) {
    const { examFilterOptions } = this.data
    const index = examFilterOptions.findIndex(option => option.examId === examId)
    return index >= 0 ? index : 0
  },

  // 同步筛选状态到URL（用于分享或书签）
  syncFilterStateToUrl() {
    const { examFilterMode, selectedExamId, currentFilter } = this.data

    if (examFilterMode && selectedExamId) {
      const { availableExams } = this.data
      const exam = availableExams.find(e => e.id === selectedExamId)

      if (exam) {
        const params = {
          examId: selectedExamId,
          examName: exam.name,
          autoFilter: 'true'
        }

        console.log('当前筛选状态可用于分享:', params)

        // 返回可分享的URL参数
        return params
      }
    }

    return null
  },

  // 生成分享链接
  generateShareUrl() {
    const filterParams = this.syncFilterStateToUrl()

    if (filterParams) {
      const baseUrl = '/pages/task-center/index'
      const paramString = Object.entries(filterParams)
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&')

      return `${baseUrl}?${paramString}`
    }

    return '/pages/task-center/index'
  },

  // 分享当前筛选状态
  shareCurrentFilter() {
    const filterParams = this.syncFilterStateToUrl()

    if (filterParams) {
      const { availableExams, selectedExamId } = this.data
      const exam = availableExams.find(e => e.id === selectedExamId)

      return {
        title: `${exam.name} - 任务中心`,
        path: this.generateShareUrl(),
        imageUrl: '/images/share-task-center.png' // 需要添加分享图片
      }
    }

    return {
      title: '任务中心 - 备考助手',
      path: '/pages/task-center/index'
    }
  },

  // 加载复习统计
  async loadTaskStats() {
    try {
      // 从后端API获取任务统计数据
      const result = await SmartApi.getTaskStats()
      
      if (result.success && result.data) {
        const stats = result.data
        const taskStats = [
          { label: '总计划', value: stats.total?.toString() || '0' },
          { label: '今日', value: stats.today?.toString() || '0' },
          { label: '已完成', value: stats.completed?.toString() || '0' },
          { label: '完成率', value: stats.total > 0 ? `${Math.round((stats.completed / stats.total) * 100)}%` : '0%' }
        ]

        this.setData({ taskStats })
        console.log('任务统计数据加载成功:', taskStats)
        console.log('详细统计:', {
          总任务: stats.total,
          今日任务: stats.today,
          今日已完成: stats.todayCompleted,
          总完成: stats.completed,
          待完成: stats.pending,
          逾期: stats.overdue
        })
      } else {
        // 如果API调用失败，使用本地计算的统计
        this.loadLocalTaskStats()
      }
    } catch (error) {
      console.error('加载任务统计失败:', error)
      // 使用本地计算的统计作为备选方案
      this.loadLocalTaskStats()
    }
  },

  // 本地计算统计数据（作为API失败时的备选方案）
  loadLocalTaskStats() {
    const { tasks } = this.data
    const totalTasks = tasks.length
    const completedTasks = tasks.filter(task => task.completed).length
    
    // 计算今日任务
    const today = new Date()
    const todayTasks = tasks.filter(task => {
      if (!task.dueDate) return false
      const dueDate = new Date(task.dueDate)
      return dueDate.toDateString() === today.toDateString()
    }).length
    
    const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0

    const taskStats = [
      { label: '总计划', value: totalTasks.toString() },
      { label: '今日', value: todayTasks.toString() },
      { label: '已完成', value: completedTasks.toString() },
      { label: '完成率', value: `${completionRate}%` }
    ]

    this.setData({ taskStats })
    console.log('使用本地计算的任务统计:', taskStats)
  },

  // 加载任务列表
  async loadTasks() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      // 使用SmartApi从云数据库获取任务
      const result = await SmartApi.getTasks({}, 50, 0)

      wx.hideLoading()

      if (result.success) {
        // 转换数据格式以适配页面显示
        const tasks = result.data.map(task => ({
          id: task._id || task.id,
          title: task.title,
          subject: task.subject,
          examName: task.examName || '',
          priority: task.priority || 'medium',
          priorityText: this.getPriorityText(task.priority),
          status: task.completed ? 'completed' : 'pending',
          statusText: task.completed ? '已完成' : '进行中',
          completed: task.completed || false,
          dueDate: this.formatDueDate(task.dueDate, task.dueTime),
          estimatedTime: task.estimatedDuration || '未设置',
          progress: task.progress || 0,
          subtasks: task.subtasks || [],
          completedSubtasks: task.subtasks ? task.subtasks.filter(sub => sub.completed).length : 0,
          showSubtasks: false,
          showCheckpoints: false, // 检查点快速展开状态
          completionTime: task.completedTime ? this.formatTime(task.completedTime) : '',
          description: task.description || ''
        }))

        this.setData({ tasks })
        this.filterTasks()
        this.updateFilterCounts()
        // 统计数据已在loadData中并行加载，这里不需要重复调用
      } else {
        console.error('加载任务失败:', result.error)
        wx.showToast({
          title: '加载任务失败',
          icon: 'none'
        })
        this.setData({ tasks: [] })
        // 如果任务加载失败，使用本地计算的统计（空数据）
        this.loadLocalTaskStats()
      }
    } catch (error) {
      wx.hideLoading()
      console.error('加载任务异常:', error)
      wx.showToast({
        title: '加载任务失败',
        icon: 'none'
      })
      this.setData({ tasks: [] })
      // 如果任务加载异常，使用本地计算的统计（空数据）
      this.loadLocalTaskStats()
    }
  },

  // 加载可用考试数据
  async loadAvailableExams() {
    try {
      console.log('开始加载考试数据...')

      // 调用SmartApi获取考试列表
      const result = await SmartApi.getExams({}, 100, 0) // 获取最多100个考试

      if (result.success && Array.isArray(result.data)) {
        console.log('考试数据加载成功:', result.data.length, '个考试')

        // 格式化考试数据
        const availableExams = result.data.map(exam => this.formatExamData(exam))

        // 计算每个考试的任务数量
        const examsWithTaskCount = await this.calculateExamTaskCounts(availableExams)

        this.setData({ availableExams: examsWithTaskCount })

        // 初始化考试筛选选项
        this.initExamFilterOptions()

        // 初始化考试下拉选项
        this.initExamDropdownOptions()

        console.log('考试数据处理完成:', examsWithTaskCount.length, '个考试')
      } else {
        console.error('加载考试失败:', result.error)
        this.setData({ availableExams: [] })
      }
    } catch (error) {
      console.error('加载考试异常:', error)
      this.setData({ availableExams: [] })
    }
  },

  // 格式化考试数据
  formatExamData(exam) {
    return {
      id: exam._id || exam.id,
      name: exam.title || exam.name || '未命名考试',
      subject: exam.subject || '',
      date: exam.examDate || '',
      time: exam.examTime || '',
      status: exam.status || 'preparing',
      importance: exam.importance || 'medium',
      type: exam.type || 'final',
      taskCount: 0 // 初始化任务计数
    }
  },

  // 生成考试简称
  generateExamShortName(examName) {
    if (!examName || typeof examName !== 'string') {
      return '未命名考试'
    }

    // 如果考试名称长度≤6个字符，直接返回
    if (examName.length <= 6) {
      return examName
    }

    // 智能截取：去掉年份、地区等修饰词，保留核心名称
    let shortName = examName
      .replace(/^\d{4}年?/, '')           // 移除年份（如"2024年"）
      .replace(/^(上|下)半年/, '')        // 移除半年标识
      .replace(/^(全国|国家|省|市|地区)/, '') // 移除地区标识
      .replace(/(考试|测试|检测)$/, '')    // 移除后缀
      .trim()

    // 如果处理后的名称仍然过长，截取前4个字符并添加省略号
    if (shortName.length > 6) {
      shortName = shortName.substring(0, 4) + '...'
    }

    // 如果处理后为空，返回原名称的前6个字符
    if (!shortName) {
      shortName = examName.substring(0, 6)
    }

    return shortName
  },

  // 根据考试ID获取考试名称
  getExamNameById(examId) {
    if (!examId) return ''

    const { availableExams } = this.data
    const exam = availableExams.find(e => e.id === examId)
    return exam ? exam.name : ''
  },

  // 根据考试ID获取考试简称
  getExamShortName(examId) {
    const examName = this.getExamNameById(examId)
    return this.generateExamShortName(examName)
  },

  // 获取筛选状态文本（用于显示组合筛选信息）
  getFilterStatusText() {
    const { examDropdownValue, selectedExamId, currentFilter } = this.data

    console.log('获取筛选状态文本:', { examDropdownValue, selectedExamId, currentFilter })

    // 确定当前选中的考试
    const currentExamId = examDropdownValue || selectedExamId
    if (!currentExamId) {
      console.log('没有选中考试，返回空文本')
      return ''
    }

    // 获取考试名称
    const examName = this.getExamNameById(currentExamId)
    if (!examName) {
      console.log('未找到考试名称，examId:', currentExamId)
      return '正在显示：选中考试的任务'
    }

    const examShortName = this.generateExamShortName(examName)
    console.log('考试简称:', examShortName)

    // 获取基础筛选描述
    const filterLabels = {
      'all': '',
      'today': '今天的',
      'pending': '待完成的',
      'completed': '已完成的',
      'overdue': '已逾期的'
    }

    const filterPrefix = filterLabels[currentFilter] || ''

    // 组合显示文本
    let statusText = ''
    if (filterPrefix) {
      statusText = `正在显示：${filterPrefix}${examShortName}任务`
    } else {
      statusText = `正在显示：${examShortName}相关任务`
    }

    console.log('最终状态文本:', statusText)
    return statusText
  },

  // 计算每个考试的任务数量
  async calculateExamTaskCounts(exams) {
    const { tasks } = this.data

    return exams.map(exam => {
      // 统计关联到该考试的任务数量
      const taskCount = tasks.filter(task =>
        task.examId === exam.id || task.examName === exam.name
      ).length

      return {
        ...exam,
        taskCount
      }
    })
  },

  // 刷新考试数据
  async refreshExamData() {
    try {
      console.log('刷新考试数据...')
      await this.loadAvailableExams()
      console.log('考试数据刷新完成')
    } catch (error) {
      console.error('刷新考试数据失败:', error)
    }
  },

  // 获取考试数据（带缓存）
  async getExamData(examId) {
    const { availableExams } = this.data
    let exam = availableExams.find(e => e.id === examId)

    if (!exam) {
      // 如果本地没有找到，尝试刷新数据
      await this.refreshExamData()
      const { availableExams: refreshedExams } = this.data
      exam = refreshedExams.find(e => e.id === examId)
    }

    return exam
  },

  // 获取优先级文本
  getPriorityText(priority) {
    const priorityMap = {
      'high': '高优先级',
      'medium': '中优先级',
      'low': '低优先级'
    }
    return priorityMap[priority] || '中优先级'
  },

  // 格式化截止日期
  formatDueDate(dueDate, dueTime) {
    if (!dueDate) return '未设置'

    const date = new Date(dueDate)
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    let dateStr = ''
    if (date.toDateString() === today.toDateString()) {
      dateStr = '今天'
    } else if (date.toDateString() === tomorrow.toDateString()) {
      dateStr = '明天'
    } else {
      dateStr = `${date.getMonth() + 1}/${date.getDate()}`
    }

    if (dueTime) {
      dateStr += ` ${dueTime}`
    }

    return dateStr
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return ''

    const date = new Date(timeStr)
    const now = new Date()
    const diffMs = now - date
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return '刚刚'
    if (diffMins < 60) return `${diffMins}分钟前`
    if (diffHours < 24) return `${diffHours}小时前`
    if (diffDays < 7) return `${diffDays}天前`

    return `${date.getMonth() + 1}/${date.getDate()}`
  },

  // 筛选任务
  filterTasks() {
    const { tasks, currentFilter, examFilterMode, selectedExamId, examDropdownValue } = this.data

    console.log('开始筛选任务:', {
      currentFilter,
      examFilterMode,
      selectedExamId,
      examDropdownValue,
      totalTasks: tasks.length
    })

    // 尝试从缓存获取结果
    const cacheKey = this.generateFilterCacheKey()
    const cachedResults = this.getCachedFilterResults(cacheKey)

    if (cachedResults) {
      console.log('使用缓存的筛选结果')
      this.setData({ filteredTasks: cachedResults })
      this.groupTasks()
      return
    }

    let filteredTasks = tasks

    // 组合筛选模式：基础筛选 + 考试筛选

    // 第一步：应用考试筛选（如果有选择考试）
    if (examDropdownValue && examDropdownValue !== '') {
      filteredTasks = this.filterTasksByExam(filteredTasks, examDropdownValue)
      console.log('应用考试筛选:', examDropdownValue, '筛选后任务数:', filteredTasks.length)
    }
    else if (examFilterMode && selectedExamId) {
      // 兼容传统考试筛选模式
      filteredTasks = this.filterTasksByExam(filteredTasks, selectedExamId)
      console.log('应用传统考试筛选:', selectedExamId, '筛选后任务数:', filteredTasks.length)
    }

    // 第二步：应用基础筛选（除了"全部"）
    if (currentFilter !== 'all' && currentFilter !== 'back_to_basic') {
      filteredTasks = this.filterTasksByBasicFilter(filteredTasks, currentFilter)
      console.log('应用基础筛选:', currentFilter, '最终任务数:', filteredTasks.length)
    }

    // 应用高级筛选条件（优先级、排序等）
    filteredTasks = this.applyAdvancedFilters(filteredTasks)

    console.log('筛选结果:', {
      filteredCount: filteredTasks.length,
      mode: examFilterMode ? 'exam' : 'basic'
    })

    // 缓存筛选结果
    this.cacheFilterResults(cacheKey, filteredTasks)

    this.setData({ filteredTasks })
    this.groupTasks()

    // UI优化：更新筛选状态显示
    this.updateFilterStatusDisplay()
  },

  // 按考试筛选任务
  filterTasksByExam(tasks, examId) {
    console.log('按考试筛选任务:', { examId, totalTasks: tasks.length })

    const filteredTasks = tasks.filter(task => {
      // 通过examId精确匹配
      if (task.examId === examId) {
        return true
      }

      // 通过examName模糊匹配（备用方案）
      if (task.examName) {
        const { availableExams } = this.data
        const exam = availableExams.find(e => e.id === examId)
        if (exam && task.examName === exam.name) {
          return true
        }
      }

      return false
    })

    console.log('考试筛选结果:', {
      examId,
      matchedTasks: filteredTasks.length,
      taskIds: filteredTasks.map(t => t.id)
    })

    return filteredTasks
  },

  // 基础筛选逻辑（从原filterTasks方法提取）
  filterTasksByBasicFilter(tasks, filter) {
    console.log('基础筛选:', { filter, totalTasks: tasks.length })

    let filteredTasks = tasks

    switch (filter) {
      case 'today':
        filteredTasks = tasks.filter(task => task.dueDate && task.dueDate.includes('今天'))
        break
      case 'pending':
        filteredTasks = tasks.filter(task => !task.completed)
        break
      case 'completed':
        filteredTasks = tasks.filter(task => task.completed)
        break
      case 'overdue':
        // 实现逾期逻辑
        filteredTasks = this.filterOverdueTasks(tasks)
        break
      default:
        filteredTasks = tasks
        break
    }

    console.log('基础筛选结果:', {
      filter,
      matchedTasks: filteredTasks.length
    })

    return filteredTasks
  },

  // 筛选逾期任务
  filterOverdueTasks(tasks) {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    return tasks.filter(task => {
      if (task.completed) return false

      if (task.dueDate) {
        try {
          // 解析截止日期
          const dueDate = new Date(task.dueDate)
          return dueDate < today
        } catch (error) {
          console.warn('解析截止日期失败:', task.dueDate, error)
          return false
        }
      }

      return false
    })
  },

  // 组合筛选：考试筛选 + 其他条件
  filterTasksWithCombination(tasks, examId, additionalFilter) {
    console.log('组合筛选:', { examId, additionalFilter })

    // 首先按考试筛选
    let filteredTasks = this.filterTasksByExam(tasks, examId)

    // 然后应用额外的筛选条件
    if (additionalFilter && additionalFilter !== 'all') {
      filteredTasks = this.filterTasksByBasicFilter(filteredTasks, additionalFilter)
    }

    console.log('组合筛选结果:', {
      examId,
      additionalFilter,
      finalCount: filteredTasks.length
    })

    return filteredTasks
  },

  // 应用高级筛选条件
  applyAdvancedFilters(tasks) {
    const { currentSort, selectedPriorities } = this.data
    let filteredTasks = [...tasks]

    // 按优先级筛选
    if (selectedPriorities.length > 0) {
      filteredTasks = filteredTasks.filter(task =>
        selectedPriorities.includes(task.priority)
      )
    }

    // 按排序条件排序
    if (currentSort) {
      filteredTasks = this.sortTasks(filteredTasks, currentSort)
    }

    return filteredTasks
  },

  // 任务排序
  sortTasks(tasks, sortBy) {
    const sortedTasks = [...tasks]

    switch (sortBy) {
      case 'dueDate':
        sortedTasks.sort((a, b) => {
          if (!a.dueDate) return 1
          if (!b.dueDate) return -1
          return new Date(a.dueDate) - new Date(b.dueDate)
        })
        break
      case 'priority':
        const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 }
        sortedTasks.sort((a, b) => {
          return (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0)
        })
        break
      case 'createTime':
        sortedTasks.sort((a, b) => {
          return new Date(b.createTime) - new Date(a.createTime)
        })
        break
      default:
        break
    }

    return sortedTasks
  },

  // 性能优化：缓存筛选结果
  cacheFilterResults(cacheKey, results) {
    if (!this.filterCache) {
      this.filterCache = new Map()
    }

    // 限制缓存大小，避免内存泄漏
    if (this.filterCache.size > 10) {
      const firstKey = this.filterCache.keys().next().value
      this.filterCache.delete(firstKey)
    }

    this.filterCache.set(cacheKey, {
      results,
      timestamp: Date.now()
    })
  },

  // 获取缓存的筛选结果
  getCachedFilterResults(cacheKey) {
    if (!this.filterCache) {
      return null
    }

    const cached = this.filterCache.get(cacheKey)
    if (!cached) {
      return null
    }

    // 缓存有效期5分钟
    const isExpired = Date.now() - cached.timestamp > 5 * 60 * 1000
    if (isExpired) {
      this.filterCache.delete(cacheKey)
      return null
    }

    return cached.results
  },

  // 生成筛选缓存键
  generateFilterCacheKey() {
    const { currentFilter, examFilterMode, selectedExamId, examDropdownValue, selectedPriorities, currentSort } = this.data

    return JSON.stringify({
      currentFilter,
      examFilterMode,
      selectedExamId,
      examDropdownValue,
      selectedPriorities,
      currentSort,
      taskCount: this.data.tasks.length
    })
  },

  // 按日期分组任务
  groupTasks() {
    const { filteredTasks } = this.data
    const groups = {}

    filteredTasks.forEach(task => {
      let dateKey = '其他'

      if (task.dueDate) {
        if (task.dueDate.includes('今天')) {
          dateKey = '今天'
        } else if (task.dueDate.includes('明天')) {
          dateKey = '明天'
        } else {
          dateKey = task.dueDate.split(' ')[0] || '其他'
        }
      } else if (task.completed && task.completionTime) {
        if (task.completionTime.includes('今天')) {
          dateKey = '今天'
        }
      }

      if (!groups[dateKey]) {
        groups[dateKey] = []
      }
      groups[dateKey].push(task)
    })

    const groupedTasks = Object.keys(groups).map(date => ({
      date,
      tasks: groups[date]
    }))

    this.setData({ groupedTasks })
  },

  // 分享功能
  onShareAppMessage() {
    return this.shareCurrentFilter()
  },

  onShareTimeline() {
    return this.shareCurrentFilter()
  },

  // 更新筛选计数
  updateFilterCounts() {
    const { tasks, filterOptions, basicFilterOptions, examDropdownValue, selectedExamId } = this.data

    // 确定当前选择的考试ID（优先使用examDropdownValue）
    const currentExamId = examDropdownValue || selectedExamId

    // 根据当前选择的考试过滤任务（使用与filterTasks相同的逻辑）
    const filteredTasks = !currentExamId || currentExamId === ''
      ? tasks
      : this.filterTasksByExam(tasks, currentExamId)

    console.log('更新筛选计数，当前考试:', currentExamId, '过滤后任务数:', filteredTasks.length)
    console.log('当前basicFilterOptions:', JSON.stringify(basicFilterOptions))

    // 计算各种筛选的任务数量
    const calculateCount = (filterValue) => {
      let count = 0
      switch (filterValue) {
        case 'all':
          count = filteredTasks.length
          break
        case 'today':
          count = filteredTasks.filter(task => task.dueDate && task.dueDate.includes('今天')).length
          break
        case 'pending':
          count = filteredTasks.filter(task => !task.completed).length
          break
        case 'completed':
          count = filteredTasks.filter(task => task.completed).length
          break
        case 'overdue':
          count = this.getOverdueTasks(filteredTasks).length
          break
        default:
          count = 0
      }
      console.log(`筛选 ${filterValue} 的任务数量: ${count}`)
      return count
    }

    // 更新basicFilterOptions（始终更新，因为WXML中使用的是这个）
    if (basicFilterOptions && basicFilterOptions.length > 0) {
      console.log('更新basicFilterOptions，当前长度:', basicFilterOptions.length)

      const updatedBasicOptions = basicFilterOptions.map(option => {
        const count = calculateCount(option.value)
        console.log(`更新选项 ${option.label}(${option.value}) 的计数: ${count}`)
        return {
          ...option,
          count: count
        }
      })

      console.log('更新后的basicFilterOptions:', JSON.stringify(updatedBasicOptions))
      this.setData({ basicFilterOptions: updatedBasicOptions })

      // 检查更新后的数据
      setTimeout(() => {
        console.log('setData后的basicFilterOptions:', JSON.stringify(this.data.basicFilterOptions))
      }, 100)
    }

    // 更新filterOptions（如果存在且当前处于考试筛选模式）
    if (filterOptions && filterOptions.length > 0) {
      console.log('更新filterOptions，当前长度:', filterOptions.length)

      const updatedOptions = filterOptions.map(option => {
        const count = calculateCount(option.value)
        return {
          ...option,
          count: count
        }
      })

      this.setData({ filterOptions: updatedOptions })
    }

    console.log('筛选计数已更新:', {
      totalTasks: tasks.length,
      pending: calculateCount('pending'),
      completed: calculateCount('completed'),
      today: calculateCount('today')
    })

    // 同时更新考试筛选计数
    this.updateExamFilterCounts()
  },

  // 切换筛选标签 (与考试中心保持一致)
  onTabChange(e) {
    const activeTab = e.detail.index
    const { basicFilterOptions } = this.data
    const currentFilter = basicFilterOptions[activeTab].value
    const selectedOption = basicFilterOptions[activeTab]

    console.log('筛选标签切换:', { activeTab, currentFilter, selectedOption })

    // 判断是否点击了"按考试"标签
    if (selectedOption.isExamFilter) {
      console.log('切换到考试筛选模式')
      this.switchToExamFilter()
      return
    }

    // 判断是否点击了"返回"按钮
    if (selectedOption.isBackButton) {
      console.log('返回基础筛选模式')
      this.switchToBasicFilter()
      return
    }

    // 判断是否是考试筛选（通过examId识别）
    if (selectedOption.examId) {
      console.log('选择考试筛选:', selectedOption.label)
      this.setData({
        activeTab,
        currentFilter,
        selectedExamId: selectedOption.examId
      })
      this.filterTasks()
      return
    }

    // 默认的基础筛选逻辑（保持原有逻辑不变）
    this.setData({
      activeTab,
      currentFilter
    })
    this.filterTasks()
  },

  // 考试下拉菜单选择事件处理
  onExamDropdownChange(e) {
    const examId = e.detail

    console.log('考试下拉菜单选择变化:', { examId })

    // 更新下拉菜单选中值
    this.setData({ examDropdownValue: examId })

    // 处理选择逻辑
    if (!examId || examId === '') {
      // 选择了"全部考试"，清除考试筛选
      console.log('清除考试筛选')
      this.clearExamFilter()
    } else {
      // 选择了具体考试，应用考试筛选
      console.log('应用考试筛选:', examId)
      this.applyExamFilter(examId)
    }
  },

  // 清除考试筛选（保持基础筛选不变）
  clearExamFilter() {
    this.setData({
      selectedExamId: null,
      examDropdownValue: ''
      // 注意：不重置currentFilter和activeTab，保持基础筛选状态
    })

    // 触发筛选更新
    this.filterTasks()

    // 更新筛选计数
    this.updateFilterCounts()

    // 用户反馈
    wx.showToast({
      title: '已清除考试筛选',
      icon: 'success',
      duration: 1500
    })

    console.log('考试筛选已清除，保持基础筛选:', this.data.currentFilter)
  },

  // 应用考试筛选
  applyExamFilter(examId) {
    const { availableExams } = this.data
    const selectedExam = availableExams.find(exam => exam.id === examId)

    if (!selectedExam) {
      console.error('未找到选中的考试:', examId)
      wx.showToast({
        title: '考试不存在',
        icon: 'error'
      })
      return
    }

    // 更新筛选状态
    this.setData({
      selectedExamId: examId,
      examDropdownValue: examId
    })

    // 触发筛选更新
    this.filterTasks()

    // 更新筛选计数
    this.updateFilterCounts()

    // 用户反馈
    const shortName = this.generateExamShortName(selectedExam.name)
    wx.showToast({
      title: `已筛选：${shortName}`,
      icon: 'success',
      duration: 2000
    })

    console.log('考试筛选已应用:', {
      examId,
      examName: selectedExam.name,
      shortName
    })
  },

  // 获取逾期任务
  getOverdueTasks(tasks) {
    const today = new Date()
    today.setHours(0, 0, 0, 0) // 设置为今天的开始时间

    return tasks.filter(task => {
      // 已完成的任务不算逾期
      if (task.completed) return false

      // 没有截止日期的任务不算逾期
      if (!task.dueDate) return false

      // 如果截止日期包含"今天"，不算逾期
      if (task.dueDate.includes('今天')) return false

      // 如果截止日期包含"明天"或之后的日期，不算逾期
      if (task.dueDate.includes('明天') || task.dueDate.includes('后天')) return false

      // 如果截止日期是日期格式（如"5/20"），需要解析并比较
      const dateMatch = task.dueDate.match(/(\d+)\/(\d+)/)
      if (dateMatch) {
        const month = parseInt(dateMatch[1]) - 1 // 月份从0开始
        const day = parseInt(dateMatch[2])

        const dueDate = new Date()
        dueDate.setMonth(month)
        dueDate.setDate(day)
        dueDate.setHours(0, 0, 0, 0)

        // 如果截止日期在今天之前，则算逾期
        return dueDate < today
      }

      // 其他情况（如"昨天"、"前天"等）算逾期
      return task.dueDate.includes('昨天') ||
             task.dueDate.includes('前天') ||
             task.dueDate.includes('逾期')
    })
  },

  // 同步筛选状态
  syncFilterState() {
    // 验证状态一致性
    this.validateFilterState()

    // 更新筛选计数
    this.updateFilterCounts()

    // 记录当前状态
    const filterMode = this.getCurrentFilterMode()
    console.log('当前筛选状态:', filterMode)
  },

  // 切换筛选 (保留兼容)
  switchFilter(e) {
    const filter = e.detail.name || e.currentTarget.dataset.filter
    this.setData({ currentFilter: filter })
    this.filterTasks()
  },

  // 切换任务状态
  async toggleTask(e) {
    const taskId = e.currentTarget.dataset.id
    const task = this.data.tasks.find(t => t.id === taskId)

    if (!task) return

    const newCompleted = !task.completed

    try {
      // 使用SmartApi更新任务状态
      const result = await SmartApi.completeTask(taskId, newCompleted)

      if (result.success) {
        // 更新本地数据
        const tasks = this.data.tasks.map(t => {
          if (t.id === taskId) {
            t.completed = newCompleted
            t.status = newCompleted ? 'completed' : 'pending'
            t.statusText = newCompleted ? '已完成' : '进行中'
            if (newCompleted) {
              t.completionTime = '刚刚'
            }
          }
          return t
        })

        // 同时更新filteredTasks和groupedTasks
        const filteredTasks = this.data.filteredTasks.map(t => {
          if (t.id === taskId) {
            t.completed = newCompleted
            t.status = newCompleted ? 'completed' : 'pending'
            t.statusText = newCompleted ? '已完成' : '进行中'
            if (newCompleted) {
              t.completionTime = '刚刚'
            }
          }
          return t
        })

        const groupedTasks = this.data.groupedTasks.map(group => ({
          ...group,
          tasks: group.tasks.map(t => {
            if (t.id === taskId) {
              t.completed = newCompleted
              t.status = newCompleted ? 'completed' : 'pending'
              t.statusText = newCompleted ? '已完成' : '进行中'
              if (newCompleted) {
                t.completionTime = '刚刚'
              }
            }
            return t
          })
        }))

        this.setData({
          tasks,
          filteredTasks,
          groupedTasks
        })

        // 重新过滤和更新统计
        this.filterTasks()
        this.updateFilterCounts()
        this.loadTaskStats()

        wx.showToast({
          title: newCompleted ? '复习完成！' : '重新开始复习',
          icon: 'success'
        })

        // 如果是完成任务，更新统计数据和检查成就
        if (newCompleted) {
          await this.updateTaskCompletionStats(task)
          await this.checkTaskAchievements()
        }
      } else {
        wx.showToast({
          title: result.error || '操作失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('更新任务状态失败:', error)
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    }
  },

  // 切换子任务显示
  async toggleSubtasks(e) {
    const taskId = e.currentTarget.dataset.id

    // 如果是首次展开且未加载过检查点，则懒加载
    const task = this.data.tasks.find(t => t.id === taskId)
    if (!task.showSubtasks && !this.data.loadedCheckpoints.includes(taskId)) {
      await this.lazyLoadCheckpoints(taskId)
    }

    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.showSubtasks = !task.showSubtasks
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()
  },

  // 懒加载检查点数据
  async lazyLoadCheckpoints(taskId) {
    // 检查缓存
    if (this.data.checkpointCache[taskId]) {
      const tasks = this.data.tasks.map(task => {
        if (task.id === taskId) {
          task.subtasks = this.data.checkpointCache[taskId]
          task.completedSubtasks = task.subtasks.filter(s => s.completed).length
        }
        return task
      })
      this.setData({ tasks })
      return
    }

    try {
      // 标记为已加载，避免重复尝试
      const loadedCheckpoints = [...this.data.loadedCheckpoints]
      if (!loadedCheckpoints.includes(taskId)) {
        loadedCheckpoints.push(taskId)
      }

      this.setData({ loadedCheckpoints })

      console.log('检查点懒加载完成（使用现有数据）:', taskId)
    } catch (error) {
      console.error('懒加载检查点失败:', error)
      // 使用现有数据，不影响用户体验
    }
  },

  // 快速展开检查点
  quickExpandCheckpoints(e) {
    console.log('quickExpandCheckpoints 被调用')
    const taskId = e.currentTarget.dataset.id
    console.log('任务ID:', taskId)

    // 获取当前数据
    const { tasks, filteredTasks, groupedTasks } = this.data

    // 更新tasks数组
    const updatedTasks = tasks.map(task => {
      if (task.id === taskId) {
        task.showCheckpoints = !task.showCheckpoints
        console.log('切换检查点展开状态为:', task.showCheckpoints)
      }
      return task
    })

    // 更新filteredTasks数组
    const updatedFilteredTasks = filteredTasks.map(task => {
      if (task.id === taskId) {
        task.showCheckpoints = !task.showCheckpoints
      }
      return task
    })

    // 更新groupedTasks数组
    const updatedGroupedTasks = groupedTasks.map(group => ({
      ...group,
      tasks: group.tasks.map(task => {
        if (task.id === taskId) {
          task.showCheckpoints = !task.showCheckpoints
        }
        return task
      })
    }))

    // 清除筛选缓存
    if (this.filterCache) {
      this.filterCache.clear()
    }

    // 一次性更新所有相关数据
    this.setData({
      tasks: updatedTasks,
      filteredTasks: updatedFilteredTasks,
      groupedTasks: updatedGroupedTasks
    }, () => {
      console.log('setData 回调执行，数据已更新')
      // 强制触发页面重新渲染
      wx.nextTick(() => {
        console.log('nextTick 执行，页面应该已重新渲染')
      })
    })

    const task = updatedTasks.find(t => t.id === taskId)
    console.log('最终状态:', task ? task.showCheckpoints : '任务未找到')

    // 显示操作反馈
    wx.showToast({
      title: task.showCheckpoints ? '已展开检查点' : '已收起检查点',
      icon: 'success',
      duration: 1000
    })
  },

  // 打开检查点弹窗
  async openCheckpointModal(e) {
    const taskId = e.currentTarget.dataset.id
    console.log('打开检查点弹窗，任务ID:', taskId)

    // 查找对应的任务
    const task = this.data.tasks.find(t => t.id === taskId)
    if (!task) {
      console.error('未找到任务:', taskId)
      wx.showToast({
        title: '任务不存在',
        icon: 'error'
      })
      return
    }

    // 如果是首次打开且未加载过检查点，则懒加载
    if (!this.data.loadedCheckpoints.includes(taskId) && (!task.subtasks || task.subtasks.length === 0)) {
      console.log('首次打开，尝试懒加载检查点数据')
      await this.lazyLoadCheckpoints(taskId)
      // 重新获取任务数据
      const updatedTask = this.data.tasks.find(t => t.id === taskId)
      if (updatedTask) {
        task.subtasks = updatedTask.subtasks
        task.completedSubtasks = updatedTask.completedSubtasks
      }
    }

    // 设置弹窗数据，确保数据完整性
    const subtasks = task.subtasks || []
    const completedCount = subtasks.filter(item => item.completed).length

    const checkpointModalData = {
      taskId: taskId,
      taskTitle: task.title,
      subtasks: [...subtasks], // 深拷贝避免引用问题
      completedCount: completedCount
    }

    this.setData({
      showCheckpointModal: true,
      checkpointModalData: checkpointModalData,
      showAddCheckpointInput: false,
      newCheckpointTitle: ''
    })

    console.log('检查点弹窗已打开，数据:', checkpointModalData)
  },

  // 关闭检查点弹窗
  closeCheckpointModal() {
    console.log('关闭检查点弹窗')

    this.setData({
      showCheckpointModal: false,
      showAddCheckpointInput: false,
      newCheckpointTitle: ''
    })
  },

  // 更新检查点统计（弹窗中）
  updateCheckpointModalStats() {
    const { checkpointModalData } = this.data
    const completedCount = checkpointModalData.subtasks.filter(item => item.completed).length

    this.setData({
      'checkpointModalData.completedCount': completedCount
    })

    // 添加统计更新的视觉反馈
    this.triggerStatsAnimation()

    console.log('检查点统计已更新:', completedCount, '/', checkpointModalData.subtasks.length)
  },

  // 触发统计数字动画
  triggerStatsAnimation() {
    // 这里可以添加统计数字的动画效果
    // 由于小程序限制，我们通过短暂的样式变化来实现反馈
    setTimeout(() => {
      console.log('统计动画触发')
    }, 100)
  },

  // 切换检查点状态（弹窗中）
  toggleCheckpointInModal(e) {
    const index = parseInt(e.currentTarget.dataset.index)
    const checkpointModalData = { ...this.data.checkpointModalData }

    if (checkpointModalData.subtasks[index]) {
      checkpointModalData.subtasks[index].completed = !checkpointModalData.subtasks[index].completed

      this.setData({ checkpointModalData })

      // 更新统计
      this.updateCheckpointModalStats()

      // 同步到主任务列表
      this.syncCheckpointToTaskList()

      console.log('检查点状态已切换:', checkpointModalData.subtasks[index])
    }
  },

  // 删除检查点（弹窗中）
  removeCheckpointInModal(e) {
    const index = parseInt(e.currentTarget.dataset.index)
    const checkpointModalData = { ...this.data.checkpointModalData }

    if (checkpointModalData.subtasks[index]) {
      const removedItem = checkpointModalData.subtasks[index]
      const updatedSubtasks = [...checkpointModalData.subtasks]
      updatedSubtasks.splice(index, 1)

      checkpointModalData.subtasks = updatedSubtasks

      this.setData({ checkpointModalData })

      // 更新统计
      this.updateCheckpointModalStats()

      // 同步到主任务列表
      this.syncCheckpointToTaskList()

      wx.showToast({
        title: '检查点已删除',
        icon: 'success',
        duration: 1000
      })

      console.log('检查点已删除:', removedItem)
    }
  },

  // 切换添加检查点输入框显示状态
  toggleAddCheckpointInput() {
    console.log('切换添加检查点输入框显示状态，当前状态:', this.data.showAddCheckpointInput)
    this.setData({
      showAddCheckpointInput: true,
      newCheckpointTitle: ''
    }, () => {
      console.log('setData完成，新状态:', this.data.showAddCheckpointInput)
    })
  },

  // 取消添加检查点
  cancelAddCheckpoint() {
    this.setData({
      showAddCheckpointInput: false,
      newCheckpointTitle: ''
    })
  },

  // 更新新检查点标题
  updateNewCheckpointTitle(e) {
    const newTitle = e.detail.value || e.detail
    this.setData({
      newCheckpointTitle: newTitle
    })
  },

  // 添加检查点（弹窗中）
  addCheckpointInModal() {
    const { newCheckpointTitle, checkpointModalData } = this.data
    const title = newCheckpointTitle.trim()

    if (!title) {
      wx.showToast({
        title: '请输入检查点内容',
        icon: 'none'
      })
      return
    }

    // 检查是否重复
    const isDuplicate = checkpointModalData.subtasks.some(item => item.title === title)
    if (isDuplicate) {
      wx.showToast({
        title: '检查点已存在',
        icon: 'none'
      })
      return
    }

    // 添加新检查点
    const newCheckpoint = {
      id: `checkpoint_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: title,
      completed: false
    }

    const updatedModalData = { ...checkpointModalData }
    updatedModalData.subtasks.push(newCheckpoint)

    this.setData({
      checkpointModalData: updatedModalData,
      newCheckpointTitle: '',
      showAddCheckpointInput: false
    })

    // 更新统计
    this.updateCheckpointModalStats()

    // 同步到主任务列表
    this.syncCheckpointToTaskList()

    wx.showToast({
      title: '检查点已添加',
      icon: 'success'
    })

    console.log('新检查点已添加:', newCheckpoint)
  },

  // 批量完成所有检查点
  completeAllCheckpoints() {
    const checkpointModalData = { ...this.data.checkpointModalData }
    let changedCount = 0

    checkpointModalData.subtasks.forEach(subtask => {
      if (!subtask.completed) {
        subtask.completed = true
        changedCount++
      }
    })

    if (changedCount === 0) {
      wx.showToast({
        title: '所有检查点已完成',
        icon: 'none',
        duration: 1500
      })
      return
    }

    this.setData({ checkpointModalData })
    this.updateCheckpointModalStats()
    this.syncCheckpointToTaskList()

    wx.showToast({
      title: `已完成 ${changedCount} 个检查点`,
      icon: 'success',
      duration: 1500
    })

    console.log('批量完成检查点:', changedCount)
  },

  // 批量重置所有检查点
  resetAllCheckpoints() {
    const checkpointModalData = { ...this.data.checkpointModalData }
    let changedCount = 0

    checkpointModalData.subtasks.forEach(subtask => {
      if (subtask.completed) {
        subtask.completed = false
        changedCount++
      }
    })

    if (changedCount === 0) {
      wx.showToast({
        title: '所有检查点已重置',
        icon: 'none',
        duration: 1500
      })
      return
    }

    this.setData({ checkpointModalData })
    this.updateCheckpointModalStats()
    this.syncCheckpointToTaskList()

    wx.showToast({
      title: `已重置 ${changedCount} 个检查点`,
      icon: 'success',
      duration: 1500
    })

    console.log('批量重置检查点:', changedCount)
  },

  // 更新检查点内容（弹窗中）
  updateCheckpointInModal(e) {
    const index = parseInt(e.currentTarget.dataset.index)
    const newTitle = e.detail.value || e.detail
    const checkpointModalData = { ...this.data.checkpointModalData }

    if (checkpointModalData.subtasks[index]) {
      checkpointModalData.subtasks[index].title = newTitle
      this.setData({ checkpointModalData })

      // 同步到任务列表和服务器
      this.syncCheckpointToTaskList()

      console.log('检查点内容已更新:', newTitle)
    }
  },

  // 同步检查点数据到任务列表
  syncCheckpointToTaskList() {
    const { checkpointModalData } = this.data
    const { taskId, subtasks, completedCount } = checkpointModalData

    console.log('开始同步检查点数据到任务列表:', taskId)

    // 更新tasks数组中的检查点数据
    const updatedTasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.subtasks = [...subtasks]
        task.completedSubtasks = completedCount
        console.log('任务检查点数据已更新:', task.id, completedCount + '/' + subtasks.length)
      }
      return task
    })

    // 更新filteredTasks数组
    const updatedFilteredTasks = this.data.filteredTasks.map(task => {
      if (task.id === taskId) {
        task.subtasks = [...subtasks]
        task.completedSubtasks = completedCount
      }
      return task
    })

    // 更新groupedTasks数组
    const updatedGroupedTasks = this.data.groupedTasks.map(group => ({
      ...group,
      tasks: group.tasks.map(task => {
        if (task.id === taskId) {
          task.subtasks = [...subtasks]
          task.completedSubtasks = completedCount
        }
        return task
      })
    }))

    // 一次性更新所有相关数据
    this.setData({
      tasks: updatedTasks,
      filteredTasks: updatedFilteredTasks,
      groupedTasks: updatedGroupedTasks
    })

    // 更新缓存
    this.updateCheckpointCache(taskId, subtasks)

    // 防抖更新到服务器
    this.debouncedSyncToServer(taskId, subtasks)

    console.log('检查点数据同步完成')
  },

  // 快速完成下一个检查点
  quickCompleteNext(e) {
    const taskId = e.currentTarget.dataset.id
    let completedSubtask = null

    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        // 找到第一个未完成的检查点
        for (let i = 0; i < task.subtasks.length; i++) {
          if (!task.subtasks[i].completed) {
            task.subtasks[i].completed = true
            completedSubtask = task.subtasks[i]
            break
          }
        }
        task.completedSubtasks = task.subtasks.filter(s => s.completed).length
      }
      return task
    })

    if (completedSubtask) {
      this.setData({ tasks })
      this.filterTasks()

      wx.showToast({
        title: `已完成：${completedSubtask.title}`,
        icon: 'success',
        duration: 1500
      })
    } else {
      wx.showToast({
        title: '所有检查点已完成',
        icon: 'none',
        duration: 1000
      })
    }
  },

  // 切换批量操作模式
  toggleBatchMode(e) {
    const taskId = e.currentTarget.dataset.id
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.batchMode = !task.batchMode
        // 重置所有检查点的选择状态
        if (task.subtasks) {
          task.subtasks = task.subtasks.map(subtask => ({
            ...subtask,
            selected: false
          }))
        }
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()

    wx.showToast({
      title: tasks.find(t => t.id === taskId).batchMode ? '已开启批量模式' : '已关闭批量模式',
      icon: 'success',
      duration: 1000
    })
  },

  // 切换批量操作模式
  toggleBatchMode(e) {
    const taskId = e.currentTarget.dataset.id
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.batchMode = !task.batchMode
        // 重置所有检查点的选择状态
        if (task.subtasks) {
          task.subtasks = task.subtasks.map(subtask => ({
            ...subtask,
            selected: false
          }))
        }
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()

    wx.showToast({
      title: tasks.find(t => t.id === taskId).batchMode ? '已开启批量模式' : '已关闭批量模式',
      icon: 'success',
      duration: 1000
    })
  },

  // 切换检查点选择状态
  toggleSubtaskSelection(e) {
    const { taskId, subtaskId } = e.currentTarget.dataset
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.subtasks = task.subtasks.map(subtask => {
          if (subtask.id === subtaskId) {
            subtask.selected = !subtask.selected
          }
          return subtask
        })
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()
  },

  // 全选检查点
  selectAllSubtasks(e) {
    const taskId = e.currentTarget.dataset.id
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        const allSelected = task.subtasks.every(subtask => subtask.selected)
        task.subtasks = task.subtasks.map(subtask => ({
          ...subtask,
          selected: !allSelected
        }))
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()

    const task = tasks.find(t => t.id === taskId)
    const allSelected = task.subtasks.every(subtask => subtask.selected)
    wx.showToast({
      title: allSelected ? '已全选' : '已取消全选',
      icon: 'success',
      duration: 1000
    })
  },

  // 批量完成检查点
  batchCompleteSubtasks(e) {
    const taskId = e.currentTarget.dataset.id
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        let completedCount = 0
        task.subtasks = task.subtasks.map(subtask => {
          if (subtask.selected) {
            subtask.completed = true
            subtask.selected = false
            completedCount++
          }
          return subtask
        })
        task.completedSubtasks = task.subtasks.filter(s => s.completed).length
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()

    const task = tasks.find(t => t.id === taskId)
    const selectedCount = task.subtasks.filter(s => s.selected).length
    if (selectedCount === 0) {
      wx.showToast({
        title: '请先选择检查点',
        icon: 'none',
        duration: 1500
      })
    } else {
      wx.showToast({
        title: `已完成 ${selectedCount} 个检查点`,
        icon: 'success',
        duration: 1500
      })
    }
  },

  // 批量重置检查点
  batchResetSubtasks(e) {
    const taskId = e.currentTarget.dataset.id
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        let resetCount = 0
        task.subtasks = task.subtasks.map(subtask => {
          if (subtask.selected) {
            subtask.completed = false
            subtask.selected = false
            resetCount++
          }
          return subtask
        })
        task.completedSubtasks = task.subtasks.filter(s => s.completed).length
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()

    const task = tasks.find(t => t.id === taskId)
    const selectedCount = task.subtasks.filter(s => s.selected).length
    if (selectedCount === 0) {
      wx.showToast({
        title: '请先选择检查点',
        icon: 'none',
        duration: 1500
      })
    } else {
      wx.showToast({
        title: `已重置 ${selectedCount} 个检查点`,
        icon: 'success',
        duration: 1500
      })
    }
  },

  // 切换检查点选择状态
  toggleSubtaskSelection(e) {
    const { taskId, subtaskId } = e.currentTarget.dataset
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.subtasks = task.subtasks.map(subtask => {
          if (subtask.id === subtaskId) {
            subtask.selected = !subtask.selected
          }
          return subtask
        })
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()
  },

  // 全选检查点
  selectAllSubtasks(e) {
    const taskId = e.currentTarget.dataset.id
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        const allSelected = task.subtasks.every(subtask => subtask.selected)
        task.subtasks = task.subtasks.map(subtask => ({
          ...subtask,
          selected: !allSelected
        }))
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()

    const task = tasks.find(t => t.id === taskId)
    const allSelected = task.subtasks.every(subtask => subtask.selected)
    wx.showToast({
      title: allSelected ? '已全选' : '已取消全选',
      icon: 'success',
      duration: 1000
    })
  },

  // 批量完成检查点
  batchCompleteSubtasks(e) {
    const taskId = e.currentTarget.dataset.id
    let selectedCount = 0

    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.subtasks = task.subtasks.map(subtask => {
          if (subtask.selected) {
            subtask.completed = true
            subtask.selected = false
            selectedCount++
          }
          return subtask
        })
        task.completedSubtasks = task.subtasks.filter(s => s.completed).length
      }
      return task
    })

    if (selectedCount === 0) {
      wx.showToast({
        title: '请先选择检查点',
        icon: 'none',
        duration: 1500
      })
      return
    }

    this.setData({ tasks })
    this.filterTasks()

    wx.showToast({
      title: `已完成 ${selectedCount} 个检查点`,
      icon: 'success',
      duration: 1500
    })
  },

  // 批量重置检查点
  batchResetSubtasks(e) {
    const taskId = e.currentTarget.dataset.id
    let selectedCount = 0

    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.subtasks = task.subtasks.map(subtask => {
          if (subtask.selected) {
            subtask.completed = false
            subtask.selected = false
            selectedCount++
          }
          return subtask
        })
        task.completedSubtasks = task.subtasks.filter(s => s.completed).length
      }
      return task
    })

    if (selectedCount === 0) {
      wx.showToast({
        title: '请先选择检查点',
        icon: 'none',
        duration: 1500
      })
      return
    }

    this.setData({ tasks })
    this.filterTasks()

    wx.showToast({
      title: `已重置 ${selectedCount} 个检查点`,
      icon: 'success',
      duration: 1500
    })
  },

  // 切换子任务状态
  toggleSubtask(e) {
    const { taskId, subtaskId } = e.currentTarget.dataset
    let toggledSubtask = null

    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.subtasks = task.subtasks.map(subtask => {
          if (subtask.id === subtaskId) {
            subtask.completed = !subtask.completed
            toggledSubtask = subtask
          }
          return subtask
        })
        task.completedSubtasks = task.subtasks.filter(s => s.completed).length
        // 移除进度计算：检查点状态不影响任务进度
        // task.progress = Math.round((task.completedSubtasks / task.subtasks.length) * 100)

        // 更新缓存
        this.updateCheckpointCache(taskId, task.subtasks)
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()

    // 防抖更新到服务器
    this.debouncedUpdateCheckpoint(taskId, subtaskId, toggledSubtask.completed)

    // 提供用户反馈（无震动）
    if (toggledSubtask) {
      wx.showToast({
        title: toggledSubtask.completed ? '检查点已完成' : '检查点已重置',
        icon: 'success',
        duration: 1000
      })
    }
  },

  // 更新检查点缓存
  updateCheckpointCache(taskId, subtasks) {
    const checkpointCache = { ...this.data.checkpointCache }
    checkpointCache[taskId] = [...subtasks]
    this.setData({ checkpointCache })
  },

  // 防抖更新检查点到服务器
  debouncedUpdateCheckpoint: (() => {
    let timeouts = {}
    return function(taskId, subtaskId, completed) {
      // 清除之前的定时器
      if (timeouts[subtaskId]) {
        clearTimeout(timeouts[subtaskId])
      }

      // 设置新的定时器
      timeouts[subtaskId] = setTimeout(async () => {
        try {
          await SmartApi.updateSubtaskStatus(taskId, subtaskId, completed)
          delete timeouts[subtaskId]
        } catch (error) {
          console.error('更新检查点状态失败:', error)
          // 可以在这里添加重试逻辑
        }
      }, 500) // 500ms防抖
    }
  })(),

  // 显示任务操作菜单
  showTaskActions(e) {
    const task = e.currentTarget.dataset.task
    this.setData({
      selectedTask: task,
      showActionSheet: true
    })
  },

  // 隐藏操作菜单
  hideActionSheet() {
    this.setData({
      showActionSheet: false,
      selectedTask: null
    })
  },

  // 显示筛选菜单
  showFilterMenu() {
    this.setData({ showFilterMenu: true })
  },

  // 隐藏筛选菜单
  hideFilterMenu() {
    this.setData({ showFilterMenu: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 执行任务操作
  executeTaskAction(e) {
    const action = e.detail.action || e.currentTarget.dataset.action
    const task = this.data.selectedTask

    this.hideActionSheet()

    switch (action) {
      case 'viewDetail':
        this.navigateToTaskDetail({ currentTarget: { dataset: { id: task.id } } })
        break
      case 'edit':
        this.editTask({ currentTarget: { dataset: { id: task.id } } })
        break
      case 'start':
        this.startTask({ currentTarget: { dataset: { id: task.id } } })
        break
      case 'complete':
        this.toggleTask({ currentTarget: { dataset: { id: task.id } } })
        break
      case 'delete':
        this.deleteTask(task)
        break
    }
  },

  // 页面跳转和操作方法

  viewTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id
    // 切换任务详情展开状态
    const tasks = this.data.tasks
    const updatedTasks = tasks.map(task => {
      if (task.id === taskId) {
        return { ...task, showDetails: !task.showDetails }
      }
      return task
    })
    this.setData({ tasks: updatedTasks })
    this.filterTasks()
  },

  navigateToTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/task-form/index?mode=edit&id=${taskId}`
    })
  },

  addTask() {
    // 检查登录状态
    const app = getApp()
    const loginStatus = app.checkLoginStatus()

    if (!loginStatus.isLoggedIn) {
      this.showLoginPrompt('添加任务需要先登录')
      return
    }

    // 获取当前选中的考试ID
    const { examDropdownValue } = this.data
    let url = '/pages/task-form/index?mode=add'

    // 如果有选中的考试，将考试ID作为参数传递
    if (examDropdownValue && examDropdownValue !== '') {
      url += `&examId=${examDropdownValue}`
      console.log('传递考试ID到添加任务页面:', examDropdownValue)
    }

    wx.navigateTo({
      url: url
    })
  },

  // 显示登录提示
  showLoginPrompt(message = '该功能需要先登录') {
    wx.navigateTo({
      url: '/pages/login/index'
    })
  },

  editTask(e) {
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/task-form/index?mode=edit&id=' + taskId
    })
  },

  startTask(e) {
    const taskId = e.currentTarget.dataset.id
    console.log('开始复习任务:', taskId)

    // 查找任务信息
    const task = this.data.tasks.find(t => t.id === taskId)
    if (!task) {
      wx.showToast({
        title: '任务不存在',
        icon: 'error'
      })
      return
    }

    // 存储任务信息到本地存储（与首页保持一致的格式）
    wx.setStorageSync('currentTaskId', taskId)
    wx.setStorageSync('currentExamId', task.examId)
    wx.setStorageSync('currentTaskTitle', task.title)
    wx.setStorageSync('currentExamName', task.examName)

    console.log('任务信息已存储:', { taskId, examId: task.examId, title: task.title, examName: task.examName })

    // 跳转到番茄钟页面
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })

    // 显示成功提示
    wx.showToast({
      title: '开始复习：' + task.title,
      icon: 'success',
      duration: 2000
    })
  },

  startPomodoro(e) {
    const taskId = e.currentTarget.dataset.id
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  deleteTask(task) {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除任务"${task.title}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          const tasks = this.data.tasks.filter(t => t.id !== task.id)
          this.setData({ tasks })
          this.filterTasks()
          this.updateFilterCounts()
          this.loadTaskStats()

          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 获取空状态标题和消息
  getEmptyTitle() {
    const { currentFilter } = this.data
    const titles = {
      all: '还没有复习计划',
      today: '今天没有复习计划',
      pending: '没有待完成的复习',
      completed: '还没有完成的复习',
      overdue: '没有逾期的复习'
    }
    return titles[currentFilter] || '暂无数据'
  },

  getEmptyMessage() {
    const { currentFilter, examDropdownValue, availableExams } = this.data

    // 获取当前选中的考试名称
    let selectedExamName = ''
    if (examDropdownValue && availableExams.length > 0) {
      const selectedExam = availableExams.find(exam => exam.id === examDropdownValue)
      selectedExamName = selectedExam ? selectedExam.name : ''
    }

    const messages = {
      all: selectedExamName ?
        `为「${this.generateExamShortName(selectedExamName)}」制定复习计划` :
        '制定你的第一个复习计划',
      today: '今天的复习都完成了',
      pending: '所有复习都已完成',
      completed: '完成一些复习来查看记录',
      overdue: '保持良好的备考习惯'
    }
    return messages[currentFilter] || '暂无相关数据'
  },

  // 获取任务卡片中考试名称的简称
  getTaskExamShortName(examName) {
    if (!examName || typeof examName !== 'string') {
      return ''
    }

    // 任务卡片中的考试名称更短，最多4个字符
    if (examName.length <= 4) {
      return examName
    }

    // 智能截取：去掉年份、地区等修饰词，保留核心名称
    let shortName = examName
      .replace(/^\d{4}年?/, '')           // 移除年份
      .replace(/^(上|下)半年/, '')        // 移除半年标识
      .replace(/^(全国|国家|省|市|地区)/, '') // 移除地区标识
      .replace(/(考试|测试|检测)$/, '')    // 移除后缀
      .trim()

    // 如果处理后的名称仍然过长，截取前3个字符并添加省略号
    if (shortName.length > 4) {
      shortName = shortName.substring(0, 3) + '...'
    }

    // 如果处理后为空，返回原名称的前4个字符
    if (!shortName) {
      shortName = examName.substring(0, 4)
    }

    return shortName
  },

  // 获取任务卡片中考试名称的显示名称（用于标题下方的灰色文本）
  getTaskExamDisplayName(examName) {
    if (!examName || typeof examName !== 'string') {
      return ''
    }

    // 超过10个字符时截断
    if (examName.length > 10) {
      return examName.substring(0, 10) + '...'
    }

    return examName
  },

  // 更新任务完成统计
  async updateTaskCompletionStats(task) {
    try {
      const statsData = {
        studyTime: 0, // 任务完成不直接增加复习时长
        completedTasks: 1,
        pomodoroSessions: 0,
        efficiency: 80, // 完成任务的基础效率
        subject: task.subject || '其他'
      }

      await SmartApi.updateDailyStats(statsData)
    } catch (error) {
      console.error('更新任务完成统计失败:', error)
    }
  },

  // 检查任务相关成就
  async checkTaskAchievements() {
    try {
      await SmartApi.checkAchievements('task_complete', 1)
    } catch (error) {
      console.error('检查任务成就失败:', error)
    }
  },

  // 筛选菜单相关方法
  changeSort(e) {
    const sort = e.currentTarget.dataset.sort
    this.setData({ currentSort: sort })
    this.filterTasks()
  },

  togglePriority(e) {
    const priority = e.currentTarget.dataset.priority
    let { selectedPriorities } = this.data
    
    if (selectedPriorities.includes(priority)) {
      selectedPriorities = selectedPriorities.filter(p => p !== priority)
    } else {
      selectedPriorities.push(priority)
    }
    
    this.setData({ selectedPriorities })
  },

  // 高级筛选中切换考试筛选开关
  toggleExamFilterInAdvanced(e) {
    const enabled = e.detail || !this.data.advancedExamFilterEnabled
    console.log('切换高级筛选中的考试筛选:', enabled)

    this.setData({
      advancedExamFilterEnabled: enabled,
      selectedExamsInAdvanced: enabled ? this.data.selectedExamsInAdvanced : []
    })
  },

  // 高级筛选中切换考试选择
  toggleExamInAdvanced(e) {
    const examId = e.currentTarget.dataset.examId
    let { selectedExamsInAdvanced } = this.data

    console.log('切换考试选择:', examId)

    if (selectedExamsInAdvanced.includes(examId)) {
      selectedExamsInAdvanced = selectedExamsInAdvanced.filter(id => id !== examId)
    } else {
      selectedExamsInAdvanced.push(examId)
    }

    this.setData({ selectedExamsInAdvanced })
  },

  // 应用快捷筛选
  applyQuickFilter(e) {
    const filterType = e.currentTarget.dataset.filter
    console.log('应用快捷筛选:', filterType)

    // 重置所有筛选条件
    this.resetFilters()

    // 根据快捷筛选类型设置相应的筛选条件
    switch (filterType) {
      case 'urgent_tasks':
        this.setData({
          selectedPriorities: ['high'],
          currentFilter: 'pending'
        })
        break
      case 'today_high_priority':
        this.setData({
          selectedPriorities: ['high'],
          currentFilter: 'today'
        })
        break
      case 'overdue_important':
        this.setData({
          selectedPriorities: ['high', 'medium'],
          currentFilter: 'overdue'
        })
        break
      case 'exam_related_pending':
        this.setData({
          advancedExamFilterEnabled: true,
          selectedExamsInAdvanced: this.data.availableExams.map(exam => exam.id),
          currentFilter: 'pending'
        })
        break
      case 'quick_wins':
        this.setData({
          selectedPriorities: ['low'],
          currentFilter: 'pending'
        })
        break
    }

    // 应用筛选并关闭弹窗
    this.applyFilters()

    // 显示快捷筛选反馈
    const quickFilter = this.data.quickFilterOptions.find(option => option.value === filterType)
    if (quickFilter) {
      wx.showToast({
        title: `已应用"${quickFilter.label}"`,
        icon: 'success',
        duration: 1500
      })
    }
  },

  resetFilters() {
    console.log('重置所有筛选条件')

    this.setData({
      currentSort: 'dueDate',
      selectedPriorities: [],
      currentFilter: 'all',
      // 重置高级筛选扩展字段
      advancedExamFilterEnabled: false,
      selectedExamsInAdvanced: []
    })

    this.filterTasks()

    wx.showToast({
      title: '筛选条件已重置',
      icon: 'success',
      duration: 1000
    })
  },

  applyFilters() {
    console.log('应用筛选条件')

    const {
      tasks,
      examFilterMode,
      selectedExamId,
      currentFilter,
      selectedPriorities,
      currentSort,
      advancedExamFilterEnabled,
      selectedExamsInAdvanced
    } = this.data

    let filteredTasks = tasks

    // 第一步：基础筛选或考试筛选
    if (examFilterMode && selectedExamId) {
      // 考试筛选模式（来自基础筛选标签）
      filteredTasks = this.filterTasksByExam(tasks, selectedExamId)
    } else if (advancedExamFilterEnabled && selectedExamsInAdvanced.length > 0) {
      // 高级筛选中的考试筛选
      filteredTasks = this.filterTasksByMultipleExams(tasks, selectedExamsInAdvanced)
    } else if (currentFilter !== 'all' && currentFilter !== 'back_to_basic') {
      // 基础筛选模式
      filteredTasks = this.filterTasksByBasicFilter(tasks, currentFilter)
    }

    // 第二步：应用高级筛选条件（优先级、排序等）
    filteredTasks = this.applyAdvancedFilters(filteredTasks)

    console.log('最终筛选结果:', {
      originalCount: tasks.length,
      filteredCount: filteredTasks.length,
      examFilterMode,
      selectedExamId,
      currentFilter,
      selectedPriorities,
      currentSort,
      advancedExamFilterEnabled,
      selectedExamsInAdvanced
    })

    this.setData({ filteredTasks })
    this.groupTasks()
    this.hideFilterMenu()
  },

  // 按多个考试筛选任务
  filterTasksByMultipleExams(tasks, examIds) {
    console.log('按多个考试筛选任务:', { examIds, totalTasks: tasks.length })

    const filteredTasks = tasks.filter(task => {
      // 检查任务是否属于任何一个选中的考试
      return examIds.some(examId => {
        // 通过examId精确匹配
        if (task.examId === examId) {
          return true
        }

        // 通过examName模糊匹配（备用方案）
        if (task.examName) {
          const { availableExams } = this.data
          const exam = availableExams.find(e => e.id === examId)
          if (exam && task.examName === exam.name) {
            return true
          }
        }

        return false
      })
    })

    console.log('多考试筛选结果:', {
      examIds,
      matchedTasks: filteredTasks.length,
      taskIds: filteredTasks.map(t => t.id)
    })

    return filteredTasks
  },

  // 保存当前筛选状态
  saveCurrentFilterState(name) {
    const {
      currentFilter,
      selectedPriorities,
      currentSort,
      examFilterMode,
      selectedExamId,
      advancedExamFilterEnabled,
      selectedExamsInAdvanced
    } = this.data

    const filterState = {
      id: Date.now().toString(),
      name: name || `筛选状态_${new Date().toLocaleString()}`,
      timestamp: Date.now(),
      state: {
        currentFilter,
        selectedPriorities: [...selectedPriorities],
        currentSort,
        examFilterMode,
        selectedExamId,
        advancedExamFilterEnabled,
        selectedExamsInAdvanced: [...selectedExamsInAdvanced]
      }
    }

    let { savedFilterStates } = this.data
    savedFilterStates.push(filterState)

    // 限制保存的状态数量
    if (savedFilterStates.length > 10) {
      savedFilterStates = savedFilterStates.slice(-10)
    }

    this.setData({ savedFilterStates })

    // 保存到本地存储
    wx.setStorageSync('task_center_saved_filters', savedFilterStates)

    console.log('保存筛选状态:', filterState)

    wx.showToast({
      title: '筛选状态已保存',
      icon: 'success',
      duration: 1500
    })
  },

  // 恢复筛选状态
  restoreFilterState(stateId) {
    const { savedFilterStates } = this.data
    const filterState = savedFilterStates.find(state => state.id === stateId)

    if (!filterState) {
      wx.showToast({
        title: '筛选状态不存在',
        icon: 'none',
        duration: 1500
      })
      return
    }

    console.log('恢复筛选状态:', filterState)

    const { state } = filterState
    this.setData({
      currentFilter: state.currentFilter,
      selectedPriorities: [...state.selectedPriorities],
      currentSort: state.currentSort,
      examFilterMode: state.examFilterMode,
      selectedExamId: state.selectedExamId,
      advancedExamFilterEnabled: state.advancedExamFilterEnabled,
      selectedExamsInAdvanced: [...state.selectedExamsInAdvanced]
    })

    // 应用恢复的筛选状态
    this.filterTasks()

    wx.showToast({
      title: `已恢复"${filterState.name}"`,
      icon: 'success',
      duration: 1500
    })
  },

  // 加载保存的筛选状态
  loadSavedFilterStates() {
    try {
      const savedFilterStates = wx.getStorageSync('task_center_saved_filters') || []
      this.setData({ savedFilterStates })
      console.log('加载保存的筛选状态:', savedFilterStates.length, '个')
    } catch (error) {
      console.error('加载筛选状态失败:', error)
    }
  },

  // 删除保存的筛选状态
  deleteSavedFilterState(stateId) {
    let { savedFilterStates } = this.data
    savedFilterStates = savedFilterStates.filter(state => state.id !== stateId)

    this.setData({ savedFilterStates })
    wx.setStorageSync('task_center_saved_filters', savedFilterStates)

    wx.showToast({
      title: '筛选状态已删除',
      icon: 'success',
      duration: 1000
    })
  },

  // ==================== 检查点服务器同步方法 ====================

  // 同步检查点数据到服务器
  async syncCheckpointToServer(taskId, subtasks, retryCount = 0) {
    const maxRetries = 2

    try {
      console.log('开始同步检查点数据到服务器:', taskId, retryCount > 0 ? `(重试 ${retryCount}/${maxRetries})` : '')
      console.log('要同步的检查点数据:', subtasks)

      // 过滤空的检查点
      const filteredSubtasks = subtasks.filter(item =>
        item.title && item.title.trim()
      )

      // 准备更新数据（参考task-form的实现）
      const updates = {
        subtasks: filteredSubtasks,
        completedSubtasks: filteredSubtasks.filter(item => item.completed).length,
        totalSubtasks: filteredSubtasks.length
      }

      console.log('格式化后的更新数据:', JSON.stringify(updates, null, 2))

      const result = await SmartApi.updateTask(taskId, updates)
      console.log('服务器返回结果:', JSON.stringify(result, null, 2))

      if (result && result.success) {
        console.log('检查点数据同步成功:', taskId)
        wx.showToast({
          title: '数据已保存',
          icon: 'success',
          duration: 1000
        })
        return { success: true }
      } else {
        console.error('检查点数据同步失败:', result ? result.error : '未知错误')

        // 如果是网络错误且还有重试次数，则重试
        if (retryCount < maxRetries && this.isRetryableError(result ? result.error : '网络错误')) {
          console.log('准备重试同步...')
          await this.delay(1000 * (retryCount + 1)) // 递增延迟
          return await this.syncCheckpointToServer(taskId, subtasks, retryCount + 1)
        }

        return { success: false, error: result ? result.error : '同步失败' }
      }
    } catch (error) {
      console.error('检查点数据同步异常:', error)

      // 如果是网络错误且还有重试次数，则重试
      if (retryCount < maxRetries && this.isRetryableError(error.message)) {
        console.log('准备重试同步...')
        await this.delay(1000 * (retryCount + 1)) // 递增延迟
        return await this.syncCheckpointToServer(taskId, subtasks, retryCount + 1)
      }

      return { success: false, error: error.message }
    }
  },

  // 判断是否为可重试的错误
  isRetryableError(errorMessage) {
    const retryableErrors = ['网络错误', 'timeout', 'Network Error', '请求超时', 'connection']
    return retryableErrors.some(error =>
      errorMessage && errorMessage.toLowerCase().includes(error.toLowerCase())
    )
  },

  // 延迟工具方法
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  // 防抖更新检查点到服务器
  debouncedSyncToServer(taskId, subtasks) {
    // 清除之前的定时器
    if (this.syncTimeouts && this.syncTimeouts[taskId]) {
      clearTimeout(this.syncTimeouts[taskId])
    }

    // 初始化定时器对象
    if (!this.syncTimeouts) {
      this.syncTimeouts = {}
    }

    // 设置新的定时器
    this.syncTimeouts[taskId] = setTimeout(async () => {
      try {
        console.log('开始防抖同步检查点到服务器:', taskId)
        const result = await this.syncCheckpointToServer(taskId, subtasks)
        if (result.success) {
          console.log('检查点数据同步成功')
        } else {
          console.warn('检查点数据同步失败:', result.error)
          wx.showToast({
            title: '数据同步失败',
            icon: 'none',
            duration: 2000
          })
        }
        delete this.syncTimeouts[taskId]
      } catch (error) {
        console.error('防抖同步检查点失败:', error)
        delete this.syncTimeouts[taskId]
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none',
          duration: 2000
        })
      }
    }, 1000) // 1秒防抖
  },

  // 立即同步检查点到服务器（用于关闭弹窗时）
  async forceSyncCheckpointToServer() {
    const { checkpointModalData } = this.data
    if (!checkpointModalData.taskId) {
      return
    }

    try {
      console.log('强制同步检查点数据到服务器')
      const result = await this.syncCheckpointToServer(
        checkpointModalData.taskId,
        checkpointModalData.subtasks
      )

      if (result.success) {
        console.log('强制同步成功')
      } else {
        console.warn('强制同步失败:', result.error)
        // 可以显示用户提示
        wx.showToast({
          title: '数据同步失败',
          icon: 'none',
          duration: 2000
        })
      }
    } catch (error) {
      console.error('强制同步异常:', error)
    }
  }
})