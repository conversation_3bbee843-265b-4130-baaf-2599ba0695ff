<!--pages/search/index.wxml-->
<view class="container">
  <!-- 搜索头部 -->
  <view class="search-header">
    <view class="search-bar">
      <view class="search-input-container">
        <text class="search-icon">🔍</text>
        <input class="search-input"
               placeholder="搜索任务、考试、笔记..."
               value="{{searchQuery}}"
               bindinput="onSearchInput"
               bindconfirm="performSearch"
               focus="{{autoFocus}}"
               confirm-type="search"/>
        <button class="clear-btn" wx:if="{{searchQuery}}" bindtap="clearSearch">×</button>
      </view>
      <button class="cancel-btn" bindtap="cancelSearch">取消</button>
    </view>

    <view class="search-filters" wx:if="{{showFilters}}">
      <button class="filter-item {{filter.active ? 'active' : ''}}"
              wx:for="{{searchFilters}}"
              wx:key="id"
              bindtap="toggleFilter"
              data-filter="{{item.id}}">
        {{item.label}}
      </button>
    </view>
  </view>

  <!-- 搜索内容 -->
  <view class="search-content">
    <!-- 搜索建议 -->
    <view class="suggestions-container" wx:if="{{!searchQuery && !hasSearched}}">
      <!-- 最近搜索 -->
      <view class="recent-searches" wx:if="{{recentSearches.length > 0}}">
        <view class="section-header">
          <text class="section-title">最近搜索</text>
          <button class="clear-recent-btn" bindtap="clearRecentSearches">清空</button>
        </view>

        <view class="recent-list">
          <button class="recent-item"
                  wx:for="{{recentSearches}}"
                  wx:key="query"
                  bindtap="selectRecentSearch"
                  data-query="{{item.query}}">
            <text class="recent-icon">🕐</text>
            <text class="recent-text">{{item.query}}</text>
          </button>
        </view>
      </view>

      <!-- 热门搜索 -->
      <view class="hot-searches">
        <text class="section-title">热门搜索</text>

        <view class="hot-tags">
          <button class="hot-tag"
                  wx:for="{{hotSearches}}"
                  wx:key="*this"
                  bindtap="selectHotSearch"
                  data-tag="{{item}}">
            {{item}}
          </button>
        </view>
      </view>

      <!-- 搜索提示 -->
      <view class="search-tips">
        <text class="tips-title">搜索提示</text>
        <view class="tips-list">
          <text class="tip-item">• 可以搜索任务标题、描述内容</text>
          <text class="tip-item">• 可以搜索考试名称、科目</text>
          <text class="tip-item">• 可以搜索复习笔记内容</text>
          <text class="tip-item">• 支持模糊搜索和关键词匹配</text>
        </view>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view class="search-results" wx:if="{{hasSearched}}">
      <view class="results-header">
        <text class="results-count">找到 {{searchResults.total}} 个结果</text>
        <button class="filter-toggle-btn" bindtap="toggleFilters">
          <text class="filter-icon">⚙️</text>
          <text>筛选</text>
        </button>
      </view>

      <!-- 结果列表 -->
      <view class="results-list" wx:if="{{searchResults.items.length > 0}}">
        <view class="result-item"
              wx:for="{{searchResults.items}}"
              wx:key="id"
              bindtap="openSearchResult"
              data-item="{{item}}">

          <view class="result-icon">
            <text class="icon-emoji">{{item.icon}}</text>
          </view>

          <view class="result-content">
            <view class="result-header">
              <text class="result-title">{{item.title}}</text>
              <text class="result-type">{{item.type}}</text>
            </view>

            <text class="result-description" wx:if="{{item.description}}">{{item.description}}</text>

            <view class="result-meta">
              <text class="result-date" wx:if="{{item.date}}">{{item.date}}</text>
              <text class="result-status"
                    wx:if="{{item.status}}"
                    style="color: {{item.statusColor}}; background-color: {{item.statusBg}}">
                {{item.status}}
              </text>
            </view>
          </view>

          <text class="result-arrow">›</text>
        </view>
      </view>

      <!-- 无结果 -->
      <view class="no-results" wx:if="{{searchResults.items.length === 0}}">
        <text class="no-results-icon">🔍</text>
        <text class="no-results-title">没有找到相关内容</text>
        <text class="no-results-subtitle">试试其他关键词吧</text>

        <view class="search-suggestions-inline">
          <text class="suggestions-label">建议搜索：</text>
          <view class="suggestions-tags">
            <button class="suggestion-tag"
                    wx:for="{{searchSuggestions}}"
                    wx:key="*this"
                    bindtap="selectHotSearch"
                    data-tag="{{item}}">
              {{item}}
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>