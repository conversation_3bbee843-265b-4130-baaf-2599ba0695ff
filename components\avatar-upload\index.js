// components/avatar-upload/index.js
const SmartApi = require('../../utils/smartApi')

Component({
  properties: {
    // 当前头像URL
    avatarUrl: {
      type: String,
      value: '',
      observer: function(newVal) {
        // 确保avatarUrl始终是字符串
        if (newVal === null || newVal === undefined) {
          this.setData({
            avatarUrl: ''
          })
        }
      }
    },
    // 头像大小
    size: {
      type: Number,
      value: 120
    },
    // 是否可编辑
    editable: {
      type: Boolean,
      value: true
    },
    // 是否显示编辑按钮
    showEditButton: {
      type: Boolean,
      value: true
    }
  },

  data: {
    uploading: false,
    defaultAvatar: '/images/default-avatar.png'
  },

  methods: {
    // 选择头像
    chooseAvatar() {
      if (!this.properties.editable || this.data.uploading) {
        return
      }

      const that = this
      
      wx.showActionSheet({
        itemList: ['从相册选择', '拍照', '删除头像'],
        success(res) {
          if (res.tapIndex === 0) {
            // 从相册选择
            that.chooseFromAlbum()
          } else if (res.tapIndex === 1) {
            // 拍照
            that.takePhoto()
          } else if (res.tapIndex === 2) {
            // 删除头像
            that.deleteAvatar()
          }
        }
      })
    },

    // 从相册选择
    chooseFromAlbum() {
      const that = this

      // 优先使用 wx.chooseMedia，失败时降级到 wx.chooseImage
      if (wx.chooseMedia) {
        wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: ['album'],
          maxDuration: 30,
          camera: 'back',
          success(res) {
            console.log('chooseMedia成功:', res)
            const tempFilePath = res.tempFiles[0].tempFilePath
            that.uploadAvatar(tempFilePath)
          },
          fail(error) {
            console.error('chooseMedia失败，降级到chooseImage:', error)
            // 降级到 chooseImage
            that.chooseImageFallback('album')
          }
        })
      } else {
        // 直接使用 chooseImage
        this.chooseImageFallback('album')
      }
    },

    // 拍照
    takePhoto() {
      const that = this

      // 优先使用 wx.chooseMedia，失败时降级到 wx.chooseImage
      if (wx.chooseMedia) {
        wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: ['camera'],
          maxDuration: 30,
          camera: 'back',
          success(res) {
            console.log('chooseMedia成功:', res)
            const tempFilePath = res.tempFiles[0].tempFilePath
            that.uploadAvatar(tempFilePath)
          },
          fail(error) {
            console.error('chooseMedia失败，降级到chooseImage:', error)
            // 降级到 chooseImage
            that.chooseImageFallback('camera')
          }
        })
      } else {
        // 直接使用 chooseImage
        this.chooseImageFallback('camera')
      }
    },

    // 降级方案：使用 chooseImage
    chooseImageFallback(sourceType) {
      const that = this

      wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: [sourceType],
        success(res) {
          console.log('chooseImage成功:', res)
          const tempFilePath = res.tempFilePaths[0]
          that.uploadAvatar(tempFilePath)
        },
        fail(error) {
          console.error('chooseImage失败:', error)
          wx.showToast({
            title: sourceType === 'camera' ? '拍照失败' : '选择图片失败',
            icon: 'none'
          })
        }
      })
    },

    // 上传头像
    async uploadAvatar(filePath) {
      console.log('开始上传头像:', filePath)

      this.setData({ uploading: true })

      let loadingShown = false

      try {
        // 检查文件路径
        if (!filePath) {
          throw new Error('文件路径为空')
        }

        // 显示加载提示
        wx.showLoading({
          title: '检查文件...',
          mask: true
        })
        loadingShown = true

        // 检查文件信息
        const fileInfo = await new Promise((resolve, reject) => {
          wx.getFileInfo({
            filePath: filePath,
            success: resolve,
            fail: reject
          })
        })

        console.log('文件信息:', fileInfo)

        // 检查文件大小（限制为5MB）
        if (fileInfo.size > 5 * 1024 * 1024) {
          throw new Error('图片文件过大，请选择小于5MB的图片')
        }

        // 更新加载提示
        wx.showLoading({
          title: '上传中...',
          mask: true
        })

        console.log('调用SmartApi.uploadAvatar')
        const result = await SmartApi.uploadAvatar(filePath)
        console.log('上传结果:', JSON.stringify(result, (key, value) => {
          if (typeof value === 'symbol') {
            return `[Symbol: ${value.toString()}]`
          }
          return value
        }))

        // 隐藏加载提示
        if (loadingShown) {
          wx.hideLoading()
          loadingShown = false
        }

        if (result.success) {
          // 触发父组件事件
          this.triggerEvent('avatarChange', {
            avatarUrl: result.data.avatarUrl,
            success: true
          })

          wx.showToast({
            title: '头像上传成功',
            icon: 'success'
          })
        } else {
          console.error('上传失败:', result.error)
          wx.showToast({
            title: result.error || '上传失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('上传头像异常:', error)

        // 确保隐藏加载提示
        if (loadingShown) {
          wx.hideLoading()
          loadingShown = false
        }

        wx.showToast({
          title: `上传失败: ${error.message || '未知错误'}`,
          icon: 'none'
        })
      } finally {
        // 最终确保隐藏加载提示
        if (loadingShown) {
          wx.hideLoading()
        }
        this.setData({ uploading: false })
      }
    },

    // 删除头像
    async deleteAvatar() {
      if (!this.properties.avatarUrl || this.properties.avatarUrl === this.data.defaultAvatar) {
        wx.showToast({
          title: '没有可删除的头像',
          icon: 'none'
        })
        return
      }

      const that = this
      
      wx.showModal({
        title: '确认删除',
        content: '确定要删除当前头像吗？',
        success: async (res) => {
          if (res.confirm) {
            wx.showLoading({
              title: '删除中...',
              mask: true
            })

            try {
              const result = await SmartApi.deleteAvatar(that.properties.avatarUrl)
              
              if (result.success) {
                // 触发父组件事件
                that.triggerEvent('avatarChange', {
                  avatarUrl: '',
                  success: true
                })
                
                wx.showToast({
                  title: '头像删除成功',
                  icon: 'success'
                })
              } else {
                wx.showToast({
                  title: result.error || '删除失败',
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('删除头像失败:', error)
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              })
            } finally {
              wx.hideLoading()
            }
          }
        }
      })
    },

    // 预览头像
    previewAvatar() {
      const avatarUrl = this.properties.avatarUrl || this.data.defaultAvatar
      
      if (avatarUrl && avatarUrl !== this.data.defaultAvatar) {
        wx.previewImage({
          urls: [avatarUrl],
          current: avatarUrl
        })
      }
    }
  }
})
