/**
 * 数据备份和恢复工具
 * 用于在数据迁移前后进行数据备份和恢复
 */

const CloudApi = require('./cloudApi')

class DataBackup {
  constructor() {
    this.backupMetadata = {
      version: '1.0.0',
      timestamp: null,
      examCount: 0,
      taskCount: 0,
      userCount: 0,
      checksum: null
    }
  }

  /**
   * 创建完整数据备份
   * @param {string} backupName - 备份名称
   * @returns {Object} 备份结果
   */
  async createFullBackup(backupName) {
    console.log('开始创建数据备份...')
    
    try {
      const timestamp = new Date().toISOString()
      const backupData = {
        metadata: {
          ...this.backupMetadata,
          name: backupName || `backup_${timestamp}`,
          timestamp: timestamp,
          type: 'full'
        },
        data: {}
      }

      // 备份考试数据
      console.log('备份考试数据...')
      const examResult = await CloudApi.getExams()
      if (examResult.success) {
        backupData.data.exams = examResult.data
        backupData.metadata.examCount = examResult.data.length
      }

      // 备份任务数据
      console.log('备份任务数据...')
      const taskResult = await CloudApi.getTasks()
      if (taskResult.success) {
        backupData.data.tasks = taskResult.data
        backupData.metadata.taskCount = taskResult.data.length
      }

      // 备份用户数据
      console.log('备份用户数据...')
      const userResult = await CloudApi.getUserProfile()
      if (userResult.success) {
        backupData.data.user = userResult.data
        backupData.metadata.userCount = 1
      }

      // 备份复习小组数据
      console.log('备份复习小组数据...')
      const groupResult = await CloudApi.getMyStudyGroups()
      if (groupResult.success) {
        backupData.data.studyGroups = groupResult.data
      }

      // 计算校验和
      backupData.metadata.checksum = this.calculateChecksum(backupData.data)

      // 保存备份到本地存储
      const backupKey = `backup_${timestamp}`
      wx.setStorageSync(backupKey, backupData)

      // 保存备份索引
      this.updateBackupIndex(backupKey, backupData.metadata)

      console.log('数据备份完成:', backupData.metadata)

      return {
        success: true,
        backupKey: backupKey,
        metadata: backupData.metadata,
        message: '数据备份创建成功'
      }

    } catch (error) {
      console.error('创建数据备份失败:', error)
      return {
        success: false,
        error: error.message,
        message: '数据备份创建失败'
      }
    }
  }

  /**
   * 创建考试数据备份
   * @param {string} backupName - 备份名称
   * @returns {Object} 备份结果
   */
  async createExamBackup(backupName) {
    console.log('开始创建考试数据备份...')
    
    try {
      const timestamp = new Date().toISOString()
      const backupData = {
        metadata: {
          name: backupName || `exam_backup_${timestamp}`,
          timestamp: timestamp,
          type: 'exam_only',
          version: '1.0.0'
        },
        data: {}
      }

      // 备份考试数据
      const examResult = await CloudApi.getExams()
      if (examResult.success) {
        backupData.data.exams = examResult.data
        backupData.metadata.examCount = examResult.data.length
        backupData.metadata.checksum = this.calculateChecksum(backupData.data)

        // 保存备份
        const backupKey = `exam_backup_${timestamp}`
        wx.setStorageSync(backupKey, backupData)
        this.updateBackupIndex(backupKey, backupData.metadata)

        return {
          success: true,
          backupKey: backupKey,
          metadata: backupData.metadata,
          message: '考试数据备份创建成功'
        }
      } else {
        throw new Error('获取考试数据失败')
      }

    } catch (error) {
      console.error('创建考试数据备份失败:', error)
      return {
        success: false,
        error: error.message,
        message: '考试数据备份创建失败'
      }
    }
  }

  /**
   * 恢复数据
   * @param {string} backupKey - 备份键
   * @param {Object} options - 恢复选项
   * @returns {Object} 恢复结果
   */
  async restoreData(backupKey, options = {}) {
    console.log('开始恢复数据...')
    
    try {
      // 获取备份数据
      const backupData = wx.getStorageSync(backupKey)
      if (!backupData) {
        throw new Error('备份数据不存在')
      }

      // 验证备份数据
      const validation = this.validateBackup(backupData)
      if (!validation.valid) {
        throw new Error(`备份数据验证失败: ${validation.errors.join(', ')}`)
      }

      const restoreResults = {
        exams: { success: false, count: 0 },
        tasks: { success: false, count: 0 },
        user: { success: false, count: 0 },
        studyGroups: { success: false, count: 0 }
      }

      // 恢复考试数据
      if (backupData.data.exams && (options.restoreExams !== false)) {
        console.log('恢复考试数据...')
        const examResult = await this.restoreExams(backupData.data.exams, options)
        restoreResults.exams = examResult
      }

      // 恢复任务数据
      if (backupData.data.tasks && options.restoreTasks) {
        console.log('恢复任务数据...')
        const taskResult = await this.restoreTasks(backupData.data.tasks, options)
        restoreResults.tasks = taskResult
      }

      // 恢复用户数据
      if (backupData.data.user && options.restoreUser) {
        console.log('恢复用户数据...')
        const userResult = await this.restoreUser(backupData.data.user, options)
        restoreResults.user = userResult
      }

      // 恢复复习小组数据
      if (backupData.data.studyGroups && options.restoreStudyGroups) {
        console.log('恢复复习小组数据...')
        const groupResult = await this.restoreStudyGroups(backupData.data.studyGroups, options)
        restoreResults.studyGroups = groupResult
      }

      console.log('数据恢复完成:', restoreResults)

      return {
        success: true,
        results: restoreResults,
        metadata: backupData.metadata,
        message: '数据恢复完成'
      }

    } catch (error) {
      console.error('数据恢复失败:', error)
      return {
        success: false,
        error: error.message,
        message: '数据恢复失败'
      }
    }
  }

  /**
   * 恢复考试数据
   * @param {Array} exams - 考试数据
   * @param {Object} options - 恢复选项
   * @returns {Object} 恢复结果
   */
  async restoreExams(exams, options = {}) {
    try {
      let successCount = 0
      const errors = []

      for (const exam of exams) {
        try {
          if (options.overwrite) {
            // 覆盖模式：先删除再添加
            if (exam._id) {
              await CloudApi.deleteExam(exam._id)
            }
          }

          const result = await CloudApi.addExam(exam)
          if (result.success) {
            successCount++
          } else {
            errors.push(`恢复考试 ${exam.title || exam.name} 失败: ${result.error}`)
          }
        } catch (error) {
          errors.push(`恢复考试 ${exam.title || exam.name} 异常: ${error.message}`)
        }
      }

      return {
        success: successCount > 0,
        count: successCount,
        total: exams.length,
        errors: errors
      }

    } catch (error) {
      return {
        success: false,
        count: 0,
        total: exams.length,
        errors: [error.message]
      }
    }
  }

  /**
   * 恢复任务数据
   * @param {Array} tasks - 任务数据
   * @param {Object} options - 恢复选项
   * @returns {Object} 恢复结果
   */
  async restoreTasks(tasks, options = {}) {
    try {
      let successCount = 0
      const errors = []

      for (const task of tasks) {
        try {
          if (options.overwrite && task._id) {
            await CloudApi.deleteTask(task._id)
          }

          const result = await CloudApi.addTask(task)
          if (result.success) {
            successCount++
          } else {
            errors.push(`恢复任务 ${task.title} 失败: ${result.error}`)
          }
        } catch (error) {
          errors.push(`恢复任务 ${task.title} 异常: ${error.message}`)
        }
      }

      return {
        success: successCount > 0,
        count: successCount,
        total: tasks.length,
        errors: errors
      }

    } catch (error) {
      return {
        success: false,
        count: 0,
        total: tasks.length,
        errors: [error.message]
      }
    }
  }

  /**
   * 恢复用户数据
   * @param {Object} user - 用户数据
   * @param {Object} options - 恢复选项
   * @returns {Object} 恢复结果
   */
  async restoreUser(user, options = {}) {
    try {
      const result = await CloudApi.updateUserProfile(user)
      return {
        success: result.success,
        count: result.success ? 1 : 0,
        total: 1,
        errors: result.success ? [] : [result.error]
      }
    } catch (error) {
      return {
        success: false,
        count: 0,
        total: 1,
        errors: [error.message]
      }
    }
  }

  /**
   * 恢复复习小组数据
   * @param {Array} studyGroups - 复习小组数据
   * @param {Object} options - 恢复选项
   * @returns {Object} 恢复结果
   */
  async restoreStudyGroups(studyGroups, options = {}) {
    // 复习小组数据通常不需要恢复，因为涉及多用户
    return {
      success: true,
      count: 0,
      total: studyGroups.length,
      errors: ['复习小组数据跳过恢复（涉及多用户）']
    }
  }

  /**
   * 验证备份数据
   * @param {Object} backupData - 备份数据
   * @returns {Object} 验证结果
   */
  validateBackup(backupData) {
    const validation = {
      valid: true,
      errors: []
    }

    // 检查基本结构
    if (!backupData.metadata) {
      validation.valid = false
      validation.errors.push('缺少备份元数据')
    }

    if (!backupData.data) {
      validation.valid = false
      validation.errors.push('缺少备份数据')
    }

    // 检查校验和
    if (backupData.metadata && backupData.metadata.checksum) {
      const currentChecksum = this.calculateChecksum(backupData.data)
      if (currentChecksum !== backupData.metadata.checksum) {
        validation.valid = false
        validation.errors.push('数据校验和不匹配，可能已损坏')
      }
    }

    return validation
  }

  /**
   * 计算数据校验和
   * @param {Object} data - 数据对象
   * @returns {string} 校验和
   */
  calculateChecksum(data) {
    const str = JSON.stringify(data)
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(16)
  }

  /**
   * 更新备份索引
   * @param {string} backupKey - 备份键
   * @param {Object} metadata - 备份元数据
   */
  updateBackupIndex(backupKey, metadata) {
    try {
      const backupIndex = wx.getStorageSync('backup_index') || []
      backupIndex.push({
        key: backupKey,
        ...metadata
      })
      
      // 保持最近10个备份的索引
      if (backupIndex.length > 10) {
        const oldBackups = backupIndex.splice(0, backupIndex.length - 10)
        // 删除旧备份
        oldBackups.forEach(backup => {
          wx.removeStorageSync(backup.key)
        })
      }
      
      wx.setStorageSync('backup_index', backupIndex)
    } catch (error) {
      console.error('更新备份索引失败:', error)
    }
  }

  /**
   * 获取备份列表
   * @returns {Array} 备份列表
   */
  getBackupList() {
    try {
      return wx.getStorageSync('backup_index') || []
    } catch (error) {
      console.error('获取备份列表失败:', error)
      return []
    }
  }

  /**
   * 删除备份
   * @param {string} backupKey - 备份键
   * @returns {boolean} 删除结果
   */
  deleteBackup(backupKey) {
    try {
      wx.removeStorageSync(backupKey)
      
      // 从索引中移除
      const backupIndex = wx.getStorageSync('backup_index') || []
      const updatedIndex = backupIndex.filter(backup => backup.key !== backupKey)
      wx.setStorageSync('backup_index', updatedIndex)
      
      return true
    } catch (error) {
      console.error('删除备份失败:', error)
      return false
    }
  }
}

}

module.exports = DataBackup
