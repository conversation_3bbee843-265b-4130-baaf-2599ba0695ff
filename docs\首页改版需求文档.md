# 备考助手微信小程序 - 首页改版优化需求文档 v2.0

## 📋 项目背景
基于用户体验优化和功能整合需求，对备考助手微信小程序首页进行重新设计，采用整页滑动的交互方式，将考试信息和任务列表完全融合，提升信息展示效率和用户操作便捷性。

## 🎯 改版目标
1. **信息融合**：将考试信息和任务列表完全融合到统一页面
2. **交互统一**：采用整页左右滑动的交互方式
3. **体验流畅**：简化操作流程，提升滑动体验
4. **视觉现代**：扁平化设计风格，去除卡片概念

## 🏗️ 新的布局结构

### 整页滑动设计
```
┌─────────────────────────────────────────┐
│ 📚 考研数学                    1/3      │
│ 2024年12月23日 还有45天                  │
│ ████████░░ 准备度 80%                   │
│ 📊 本周: 12h  完成: 15/20  👥 3人       │
├─────────────────────────────────────────┤
│ 📋 今日任务 (3/8)                       │
│                                         │
│ ⭕ 高等数学-极限专题    [开始复习]        │
│ ✅ 线性代数-矩阵运算    (已完成)         │
│ ⭕ 概率论-随机变量      [开始复习]        │
│ ⭕ 英语单词背诵        [开始复习]        │
│ ✅ 政治马原理论        (已完成)         │
│                                         │
│  [添加任务]                             │
│                                         │
│ ← 滑动查看其他考试 →                     │
└─────────────────────────────────────────┘
```

**核心特性：**
- **整页滑动**：每个考试占据整个页面，左右滑动切换
- **信息融合**：考试信息和任务列表在同一页面
- **垂直滚动**：页面内容支持垂直滚动
- **指示器**：顶部显示当前考试位置（如 1/3）
- **流畅动画**：原生swiper组件保证滑动流畅

### 页面结构详细设计

#### 1. 考试信息区域 (页面顶部)
- **考试标题**：大字体显示考试名称
- **考试日期**：显示具体日期和倒计时
- **进度信息**：准备度进度条
- **统计信息**：本周复习时长、任务完成情况、搭子人数
- **页面指示器**：显示当前考试位置（如 1/3）

#### 2. 任务列表区域 (页面主体)
- **任务标题**：显示今日任务数量
- **任务列表**：垂直滚动的任务列表
- **任务状态**：完成/未完成状态图标
- **快速操作**：开始复习按钮
- **底部操作**：查看全部任务、添加任务

#### 3. 悬浮快捷操作 (保留)
- **添加任务**：快速添加复习任务
- **添加考试**：创建新的考试计划
- **开始专注**：启动番茄钟专注模式

## 🎨 设计规范

### 视觉风格
- **扁平化设计**：去除卡片阴影，采用扁平化风格
- **渐变背景**：每个考试页面使用不同的渐变背景
- **图标系统**：统一的emoji图标风格
- **字体层次**：清晰的信息层次结构

### 交互体验
- **整页滑动**：左右滑动切换考试页面
- **垂直滚动**：页面内容支持垂直滚动
- **快速操作**：一键开始复习、查看详情
- **状态反馈**：操作结果及时反馈

## 🔧 技术实现要点

### 数据结构调整
- 将任务数据合并到考试数据中
- 每个考试对象包含其对应的任务列表
- 支持考试-任务数据联动加载

### 核心组件实现
```xml
<swiper class="full-page-swiper" bindchange="onPageChange">
  <swiper-item wx:for="{{exams}}" wx:key="id">
    <scroll-view scroll-y class="page-content">
      <!-- 考试信息区域 -->
      <!-- 任务列表区域 -->
    </scroll-view>
  </swiper-item>
</swiper>
```

### 布局实现
- 使用整页swiper布局
- 每个swiper-item包含完整的考试+任务页面
- 内部使用scroll-view支持垂直滚动

### 组件结构
```
home/
├── index.wxml    # 整页滑动结构
├── index.wxss    # 扁平化样式
├── index.js      # 数据融合逻辑
└── index.json    # 页面配置
```

## 📱 用户场景

### 主要使用流程
1. **进入首页** → 查看当前考试的完整信息和任务
2. **切换考试** → 左右滑动查看不同考试页面
3. **浏览任务** → 在页面内垂直滚动查看所有任务
4. **开始复习** → 点击任务的"开始复习"按钮
5. **快速操作** → 使用页面内按钮或悬浮按钮进行操作

### 空状态处理
- **无考试时**：显示创建考试引导页面
- **无任务时**：显示添加任务引导区域
- **无搭子时**：在统计信息中显示创建搭子入口

## ✅ 验收标准

### 功能完整性
- [ ] 整页滑动功能正常
- [ ] 考试数据和任务数据正确融合
- [ ] 垂直滚动功能正常
- [ ] 页面指示器正确显示
- [ ] 所有按钮功能正常

### 视觉效果
- [ ] 扁平化设计风格统一
- [ ] 渐变背景美观协调
- [ ] 信息层次清晰易读
- [ ] 响应式适配不同屏幕

### 交互体验
- [ ] 整页滑动流畅自然
- [ ] 垂直滚动响应及时
- [ ] 页面加载速度快
- [ ] 操作反馈清晰

## 🚀 后续优化方向
1. **手势增强**：支持快速手势操作
2. **动画优化**：页面切换动画效果
3. **个性化背景**：每个考试自定义背景色
4. **智能排序**：根据紧急程度自动排序任务

---

**文档版本**：v2.0
**创建时间**：2025-01-02
**最后更新**：2025-01-02
**更新内容**：采用整页滑动设计，融合考试信息和任务列表
