// 头像上传云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action, data } = event

  console.log('uploadAvatar云函数调用:', { action, data, openid: wxContext.OPENID })

  try {
    // 检查参数
    if (!action) {
      return { success: false, error: 'action参数不能为空' }
    }

    if (!wxContext.OPENID) {
      return { success: false, error: '用户未登录' }
    }

    switch (action) {
      case 'uploadAvatar':
        return await uploadAvatar(wxContext.OPENID, data || {})
      case 'deleteAvatar':
        return await deleteAvatar(wxContext.OPENID, data || {})
      case 'getUploadUrl':
        return await getUploadUrl(wxContext.OPENID, data || {})
      default:
        return { success: false, error: `未知操作: ${action}` }
    }
  } catch (error) {
    console.error('头像上传云函数错误:', error)
    return { success: false, error: error.message || '云函数执行失败' }
  }
}

// 上传头像
async function uploadAvatar(openid, { fileID, fileName, fileSize }) {
  try {
    console.log('开始处理头像上传:', { openid, fileID, fileName, fileSize })

    // 检查参数
    if (!fileID) {
      return { success: false, error: 'fileID不能为空' }
    }

    // 获取用户信息（同时获取用户ID）
    console.log('查询用户信息, openid:', openid)
    const userResult = await db.collection('users')
      .where({ openid: openid })
      .get()

    console.log('用户查询结果:', userResult)

    if (!userResult.data || userResult.data.length === 0) {
      return { success: false, error: '用户不存在' }
    }

    // 如果有多个用户匹配，这是数据库问题，需要报错
    if (userResult.data.length > 1) {
      console.error('发现重复用户数据:', userResult.data.length, '条记录')
      return { success: false, error: `数据异常：发现${userResult.data.length}个重复用户，请联系管理员处理` }
    }

    const user = userResult.data[0]
    const userId = user._id
    const oldAvatarUrl = user.avatarUrl

    console.log('找到用户:', user.nickName, 'ID:', userId, '旧头像:', oldAvatarUrl)
    console.log('更新用户头像URL:', fileID)

    // 使用文档ID进行精确更新，避免影响其他用户
    const updateResult = await db.collection('users')
      .doc(userId)  // 使用文档ID，确保只更新这一个用户
      .update({
        data: {
          avatarUrl: fileID,
          updateTime: new Date()
        }
      })

    console.log('用户头像更新结果:', updateResult)

    // 如果有旧头像且不是默认头像，删除旧头像
    if (oldAvatarUrl && oldAvatarUrl.startsWith('cloud://') && oldAvatarUrl !== fileID) {
      try {
        await cloud.deleteFile({
          fileList: [oldAvatarUrl]
        })
        console.log('删除旧头像成功:', oldAvatarUrl)
      } catch (deleteError) {
        console.error('删除旧头像失败:', deleteError)
        // 不影响主流程，继续执行
      }
    }

    console.log('头像上传成功:', fileID)

    const result = {
      success: true,
      data: {
        avatarUrl: String(fileID), // 确保是字符串
        fileName: String(fileName || 'avatar.jpg'),
        fileSize: Number(fileSize || 0),
        uploadTime: new Date().toISOString()
      }
    }

    console.log('uploadAvatar返回结果:', JSON.stringify(result))
    return result
  } catch (error) {
    console.error('上传头像失败:', error)
    return { success: false, error: error.message || '上传头像失败' }
  }
}

// 删除头像
async function deleteAvatar(openid, { fileID }) {
  try {
    console.log('开始删除头像:', { openid, fileID })
    
    // 获取用户信息（同时获取用户ID）
    console.log('查询用户信息, openid:', openid)
    const userResult = await db.collection('users')
      .where({ openid: openid })
      .get()

    if (!userResult.data || userResult.data.length === 0) {
      return { success: false, error: '用户不存在' }
    }

    // 如果有多个用户匹配，这是数据库问题，需要报错
    if (userResult.data.length > 1) {
      console.error('发现重复用户数据:', userResult.data.length, '条记录')
      return { success: false, error: `数据异常：发现${userResult.data.length}个重复用户，请联系管理员处理` }
    }

    const user = userResult.data[0]
    const userId = user._id

    console.log('找到用户:', user.nickName, 'ID:', userId)

    // 删除云存储文件
    const deleteResult = await cloud.deleteFile({
      fileList: [fileID]
    })

    // 使用文档ID进行精确更新，避免影响其他用户
    await db.collection('users')
      .doc(userId)  // 使用文档ID，确保只更新这一个用户
      .update({
        data: {
          avatarUrl: '',
          updateTime: new Date()
        }
      })

    console.log('删除头像成功:', fileID)
    
    return {
      success: true,
      data: {
        deletedFileID: fileID,
        deleteResult: deleteResult
      }
    }
  } catch (error) {
    console.error('删除头像失败:', error)
    return { success: false, error: error.message }
  }
}

// 获取上传URL（用于直接上传到云存储）
async function getUploadUrl(openid, { fileName, fileType }) {
  try {
    console.log('获取上传URL:', { openid, fileName, fileType })

    // 检查参数
    if (!openid) {
      return { success: false, error: 'openid不能为空' }
    }

    if (!fileName) {
      fileName = 'avatar.jpg' // 默认文件名
    }

    // 获取用户信息（同时获取用户ID）
    console.log('查询用户信息, openid:', openid)
    const userResult = await db.collection('users')
      .where({ openid: openid })
      .get()

    if (!userResult.data || userResult.data.length === 0) {
      return { success: false, error: '用户不存在' }
    }

    // 如果有多个用户匹配，这是数据库问题，需要报错
    if (userResult.data.length > 1) {
      console.error('发现重复用户数据:', userResult.data.length, '条记录')
      return { success: false, error: `数据异常：发现${userResult.data.length}个重复用户，请联系管理员处理` }
    }

    const user = userResult.data[0]
    const userId = user._id

    console.log('找到用户:', user.nickName, 'ID:', userId)

    // 生成唯一的文件名
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substr(2, 9)
    const fileExtension = fileName.includes('.') ? fileName.split('.').pop() : 'jpg'
    const cloudPath = `avatars/${userId}/${timestamp}_${randomStr}.${fileExtension}`

    console.log('生成云存储路径:', cloudPath)

    const result = {
      success: true,
      data: {
        cloudPath: String(cloudPath) // 确保是字符串
      }
    }

    console.log('getUploadUrl返回结果:', JSON.stringify(result))
    return result
  } catch (error) {
    console.error('获取上传URL失败:', error)
    return { success: false, error: error.message }
  }
}
