// pages/edit-exam/index.js
const SmartApi = require('../../utils/smartApi')
const ExamModel = require('../../models/ExamModel')

Page({
  data: {
    examId: '',
    examForm: {
      title: '',
      subject: [],
      examDate: '',
      examTime: '',
      location: '',
      description: '',
      type: 'final',
      importance: 'medium',
      status: 'upcoming',
      reminderSettings: ['1day', '3days']
    },

    // 重要程度选项
    importanceOptions: ['一般', '重要', '非常重要'],
    selectedImportanceIndex: 1,

    // 考试类型选项
    typeOptions: [
      { label: '📚 期末考试', value: 'final' },
      { label: '📖 期中考试', value: 'midterm' },
      { label: '📝 小测验', value: 'quiz' },
      { label: '🏆 资格考试', value: 'certificate' },
      { label: '🎓 入学考试', value: 'entrance' },
      { label: '📋 其他', value: 'other' }
    ],

    // van-picker需要的列数据格式
    typeColumns: [],

    // 考试状态选项
    statusOptions: [
      { label: '即将到来', value: 'upcoming' },
      { label: '进行中', value: 'ongoing' },
      { label: '已结束', value: 'finished' }
    ],

    // van-picker需要的状态列数据格式
    statusColumns: [],

    // 提醒设置选项
    reminderOptions: [
      { value: '1day', label: '考试前1天', checked: true },
      { value: '3days', label: '考试前3天', checked: true },
      { value: '1week', label: '考试前1周', checked: false },
      { value: '2weeks', label: '考试前2周', checked: false }
    ],

    canSave: false,
    loading: false,
    titleErrorMessage: '',
    newSubject: '',
    canAddSubject: false,
    typeLabel: '',
    statusLabel: '',

    // 弹窗控制
    showDatePopup: false,
    showTimePopup: false,
    showTypePopup: false,
    showStatusPopup: false,
    currentDate: new Date().getTime(),
    currentTime: '09:00',
    minDate: new Date().getTime()
  },

  onLoad(options) {
    console.log('=== 编辑考试页面onLoad ===')
    console.log('页面参数 options:', JSON.stringify(options, null, 2))
    
    const examId = options.id
    console.log('从options中获取的examId:', examId)
    console.log('examId类型:', typeof examId)
    
    if (examId) {
      console.log('examId有效，设置到data中')
      this.setData({ examId })
      this.initPage()
      this.loadExamData(examId)
    } else {
      console.error('错误：examId为空或undefined')
      wx.showToast({
        title: '考试ID不能为空',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  initPage() {
    const typeColumns = this.data.typeOptions.map(option => option.label)
    const statusColumns = this.data.statusOptions.map(option => option.label)

    this.setData({
      typeColumns: typeColumns,
      statusColumns: statusColumns
    })
  },

  async loadExamData(examId) {
    console.log('=== 开始加载考试数据 ===')
    console.log('传入的examId:', examId)
    console.log('examId类型:', typeof examId)
    console.log('examId是否为空:', !examId)

    wx.showLoading({
      title: '加载中...'
    })

    try {
      console.log('调用 SmartApi.getExamById, 参数:', examId)
      const result = await SmartApi.getExamById(examId)

      console.log('SmartApi.getExamById 返回结果:', JSON.stringify(result, null, 2))

      wx.hideLoading()

      if (result.success && result.data) {
        const examData = result.data
        console.log('加载到的考试数据:', JSON.stringify(examData, null, 2))

        if (typeof examData.subject === 'string') {
          console.log('将科目从字符串转换为数组:', examData.subject)
          examData.subject = examData.subject ? [examData.subject] : []
        } else if (!Array.isArray(examData.subject)) {
          console.log('科目不是数组格式，重置为空数组')
          examData.subject = []
        }

        const importanceMap = { 'low': 0, 'medium': 1, 'high': 2 }
        const selectedImportanceIndex = importanceMap[examData.importance] || 1
        console.log('重要程度映射:', examData.importance, '->', selectedImportanceIndex)

        const reminderOptions = this.data.reminderOptions.map(option => ({
          ...option,
          checked: examData.reminderSettings && examData.reminderSettings.includes(option.value)
        }))
        console.log('提醒设置:', examData.reminderSettings)

        const typeOption = this.data.typeOptions.find(opt => opt.value === examData.type)
        const statusOption = this.data.statusOptions.find(opt => opt.value === examData.status)
        const typeLabel = typeOption ? typeOption.label : '📚 期末考试'
        const statusLabel = statusOption ? statusOption.label : '即将到来'
        console.log('类型标签:', examData.type, '->', typeLabel)
        console.log('状态标签:', examData.status, '->', statusLabel)

        const finalData = {
          examForm: examData,
          selectedImportanceIndex: selectedImportanceIndex,
          reminderOptions: reminderOptions,
          typeLabel: typeLabel,
          statusLabel: statusLabel,
          originalExam: JSON.parse(JSON.stringify(examData))
        }

        console.log('即将设置到页面的数据keys:', Object.keys(finalData))

        this.setData(finalData)

        this.validateForm()
        console.log('考试数据加载完成，当前examId:', this.data.examId)
      } else {
        console.error('加载考试数据失败:', result.error)
        console.error('API返回的完整结果:', result)
        wx.showToast({
          title: result.error || '加载考试数据失败',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      wx.hideLoading()
      console.error('加载考试数据异常:', error)
      console.error('异常详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })
      wx.showToast({
        title: '加载考试数据失败',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
    console.log('=== 加载考试数据操作结束 ===')
  },

  onTitleChange(e) {
    const value = e.detail || ''
    this.setData({
      'examForm.title': value
    })
    this.validateForm()
  },

  onNewSubjectChange(e) {
    const value = e.detail || ''
    const canAddSubject = value.trim().length > 0
    this.setData({
      newSubject: value,
      canAddSubject: canAddSubject
    })
  },

  addSubject() {
    if (!this.data.canAddSubject) return

    const newSubject = this.data.newSubject.trim()
    if (!newSubject) return

    const currentSubjects = this.data.examForm.subject || []

    if (currentSubjects.includes(newSubject)) {
      wx.showToast({
        title: '科目已存在',
        icon: 'none'
      })
      return
    }

    if (currentSubjects.length >= 10) {
      wx.showToast({
        title: '最多只能添加10个科目',
        icon: 'none'
      })
      return
    }

    const updatedSubjects = [...currentSubjects, newSubject]
    this.setData({
      'examForm.subject': updatedSubjects,
      newSubject: '',
      canAddSubject: false
    })
    this.validateForm()
  },

  removeSubject(e) {
    const index = e.currentTarget.dataset.index
    const currentSubjects = this.data.examForm.subject || []
    const updatedSubjects = currentSubjects.filter((_, i) => i !== index)
    this.setData({
      'examForm.subject': updatedSubjects
    })
    this.validateForm()
  },

  onLocationChange(e) {
    const value = e.detail || ''
    this.setData({
      'examForm.location': value
    })
    this.validateForm()
  },

  onDescriptionChange(e) {
    const value = e.detail || ''
    this.setData({
      'examForm.description': value
    })
    this.validateForm()
  },

  onImportanceChange(e) {
    const index = e.detail.index
    const importanceMap = ['low', 'medium', 'high']
    this.setData({
      'examForm.importance': importanceMap[index],
      selectedImportanceIndex: index
    })
    this.validateForm()
  },

  onReminderChange(e) {
    const value = e.currentTarget.dataset.value
    const checked = e.detail

    const reminderOptions = this.data.reminderOptions.map(option => {
      if (option.value === value) {
        return { ...option, checked: checked }
      }
      return option
    })

    const selectedReminders = reminderOptions
      .filter(option => option.checked)
      .map(option => option.value)

    this.setData({
      reminderOptions: reminderOptions,
      'examForm.reminderSettings': selectedReminders
    })
    this.validateForm()
  },

  showDatePicker() {
    this.setData({ showDatePopup: true })
  },

  closeDatePicker() {
    this.setData({ showDatePopup: false })
  },

  onDateConfirm(event) {
    const date = new Date(event.detail)
    const formattedDate = this.formatDate(date)
    this.setData({
      'examForm.examDate': formattedDate,
      showDatePopup: false
    })
    this.validateForm()
  },

  showTimePicker() {
    this.setData({ showTimePopup: true })
  },

  closeTimePicker() {
    this.setData({ showTimePopup: false })
  },

  onTimeConfirm(event) {
    const time = event.detail
    this.setData({
      'examForm.examTime': time,
      showTimePopup: false
    })
    this.validateForm()
  },

  showTypePicker() {
    this.setData({ showTypePopup: true })
  },

  closeTypePicker() {
    this.setData({ showTypePopup: false })
  },

  onTypeConfirm(event) {
    const selectedIndex = event.detail.index
    const selectedType = this.data.typeOptions[selectedIndex]
    this.setData({
      'examForm.type': selectedType.value,
      typeLabel: selectedType.label,
      showTypePopup: false
    })
    this.validateForm()
  },

  showStatusPicker() {
    this.setData({ showStatusPopup: true })
  },

  closeStatusPicker() {
    this.setData({ showStatusPopup: false })
  },

  onStatusConfirm(event) {
    const selectedIndex = event.detail.index
    const selectedStatus = this.data.statusOptions[selectedIndex]
    this.setData({
      'examForm.status': selectedStatus.value,
      statusLabel: selectedStatus.label,
      showStatusPopup: false
    })
    this.validateForm()
  },

  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  validateForm() {
    const { title, examDate, examTime } = this.data.examForm
    const canSave = title.trim().length > 0 && examDate && examTime

    let titleErrorMessage = ''
    if (!title.trim()) {
      titleErrorMessage = '考试名称不能为空'
    } else if (title.trim().length > 50) {
      titleErrorMessage = '考试名称不能超过50个字符'
    }

    this.setData({
      canSave: canSave,
      titleErrorMessage: titleErrorMessage
    })
  },

  async saveExam() {
    console.log('=== 开始保存考试 ===')
    console.log('当前页面examId:', this.data.examId)
    console.log('当前表单数据:', JSON.stringify(this.data.examForm, null, 2))
    console.log('canSave状态:', this.data.canSave)

    if (!this.data.canSave) {
      console.log('表单验证失败，无法保存')
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      })
      return
    }

    if (!this.data.examId) {
      console.error('错误：examId为空')
      wx.showToast({
        title: '考试ID不存在，无法保存',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    wx.showLoading({
      title: '保存中...'
    })

    try {
      const examData = {
        ...this.data.examForm,
        id: this.data.examId,
        updatedAt: new Date().toISOString()
      }

      // 准备更新数据（移除系统字段）
      const updates = { ...examData }
      delete updates._id
      delete updates.userId
      delete updates.createTime
      delete updates.updateTime
      delete updates.id  // 也删除我们添加的id字段

      console.log('准备发送的更新数据:', JSON.stringify(updates, null, 2))

      // 调用API前的日志
      console.log('调用 SmartApi.updateExam, 参数:', {
        examId: this.data.examId,
        hasUpdates: !!updates,
        updateKeys: Object.keys(updates)
      })

      const result = await SmartApi.updateExam(this.data.examId, updates)

      console.log('SmartApi.updateExam 返回结果:', JSON.stringify(result, null, 2))

      wx.hideLoading()

      if (result.success) {
        console.log('保存成功')
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })

        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        console.error('保存失败，错误信息:', result.error)
        console.error('完整错误对象:', result)
        wx.showToast({
          title: result.error || '保存失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('保存考试异常:', error)
      console.error('异常详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
      console.log('=== 保存考试操作结束 ===')
    }
  },

  // 确认删除考试
  confirmDeleteExam() {
    wx.showModal({
      title: '删除确认',
      content: '确定要删除这个考试吗？\n删除后无法恢复，相关的复习任务也会受到影响。',
      confirmText: '确认删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.deleteExam()
        }
      }
    })
  },

  async deleteExam() {

    wx.showLoading({
      title: '删除中...'
    })

    try {
      const deleteResult = await SmartApi.deleteExam(this.data.examId)

      wx.hideLoading()

      if (deleteResult.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })

        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: deleteResult.error || '删除失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('删除考试失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  },

  cancelEdit() {
    wx.navigateBack()
  }
})