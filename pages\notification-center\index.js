// pages/notification-center/index.js
Page({
  data: {
    totalCount: 0,
    unreadCount: 0,
    todayCount: 0,

    currentFilter: 'all',
    notificationFilters: [
      { id: 'all', label: '全部', count: 0 },
      { id: 'unread', label: '未读', count: 0 },
      { id: 'exam', label: '考试提醒', count: 0 },
      { id: 'task', label: '任务提醒', count: 0 },
      { id: 'system', label: '系统通知', count: 0 }
    ],

    allNotifications: [],
    filteredNotifications: [],
    groupedNotifications: [],

    showActionSheet: false,
    selectedNotification: {},
    actionSheetActions: [],

    showSettingsModal: false,
    showQuietHoursModal: false,
    showTimePicker: false,
    timePickerType: '', // 'start' or 'end'
    timeColumns: [],
    notificationsEnabled: true,

    notificationSettings: [
      {
        type: 'exam_reminder',
        name: '考试提醒',
        description: '考试前的倒计时提醒',
        enabled: true
      },
      {
        type: 'task_deadline',
        name: '任务截止',
        description: '任务即将到期的提醒',
        enabled: true
      },
      {
        type: 'study_reminder',
        name: '复习提醒',
        description: '定时复习提醒',
        enabled: true
      },
      {
        type: 'achievement',
        name: '成就通知',
        description: '解锁新成就时的通知',
        enabled: true
      },
      {
        type: 'system_update',
        name: '系统更新',
        description: '应用更新和功能通知',
        enabled: false
      }
    ],

    quietHours: {
      start: '22:00',
      end: '08:00'
    }
  },

  onLoad() {
    this.loadNotifications()
    this.loadNotificationSettings()
    this.initTimeColumns()
  },

  onShow() {
    this.refreshNotifications()
  },

  // 初始化时间选择器数据
  initTimeColumns() {
    const hours = Array.from({ length: 24 }, (_, i) => {
      const hour = i.toString().padStart(2, '0')
      return { text: hour, value: hour }
    })
    
    const minutes = Array.from({ length: 60 }, (_, i) => {
      const minute = i.toString().padStart(2, '0')
      return { text: minute, value: minute }
    })
    
    this.setData({
      timeColumns: [hours, minutes]
    })
  },

  // 加载通知数据
  loadNotifications() {
    try {
      // 从本地存储加载通知
      const notifications = wx.getStorageSync('notifications') || []

      // 如果没有通知，生成模拟数据
      if (notifications.length === 0) {
        this.generateMockNotifications()
      } else {
        this.setData({ allNotifications: notifications })
        this.updateNotificationStats()
        this.filterNotifications()
      }

    } catch (error) {
      console.error('加载通知失败:', error)
      this.generateMockNotifications()
    }
  },

  // 生成模拟通知数据
  generateMockNotifications() {
    const mockNotifications = [
      {
        id: 'notif_001',
        type: 'exam_reminder',
        title: '考试提醒',
        message: '距离"2025年考研"还有156天，记得按时复习哦！',
        icon: '📅',
        iconBg: '#E6F7FF',
        time: '2小时前',
        date: '今天',
        read: false,
        actions: [
          { id: 'view_exam', label: '查看考试', type: 'primary' },
          { id: 'dismiss', label: '忽略', type: 'secondary' }
        ]
      },
      {
        id: 'notif_002',
        type: 'task_deadline',
        title: '任务截止提醒',
        message: '任务"数学高数第一章复习"将在今天18:00截止',
        icon: '⏰',
        iconBg: '#FFF7E6',
        time: '4小时前',
        date: '今天',
        read: false,
        actions: [
          { id: 'view_task', label: '查看任务', type: 'primary' }
        ]
      },
      {
        id: 'notif_003',
        type: 'achievement',
        title: '成就解锁',
        message: '恭喜！你解锁了"坚持达人"成就，连续复习15天！',
        icon: '🏆',
        iconBg: '#F6FFED',
        time: '昨天 20:30',
        date: '昨天',
        read: true,
        actions: [
          { id: 'view_achievement', label: '查看成就', type: 'primary' }
        ]
      },
      {
        id: 'notif_004',
        type: 'study_reminder',
        title: '复习提醒',
        message: '该开始今天的复习计划了，加油！',
        icon: '📚',
        iconBg: '#F9F0FF',
        time: '昨天 09:00',
        date: '昨天',
        read: true
      },
      {
        id: 'notif_005',
        type: 'system_update',
        title: '功能更新',
        message: '新增成就系统功能，快来体验吧！',
        icon: '🎉',
        iconBg: '#FFF1F0',
        time: '3天前',
        date: '3天前',
        read: true
      }
    ]

    // 保存到本地存储
    try {
      wx.setStorageSync('notifications', mockNotifications)
    } catch (error) {
      console.error('保存通知失败:', error)
    }

    this.setData({ allNotifications: mockNotifications })
    this.updateNotificationStats()
    this.filterNotifications()
  },

  // 更新通知统计
  updateNotificationStats() {
    const notifications = this.data.allNotifications
    const today = new Date().toDateString()

    const totalCount = notifications.length
    const unreadCount = notifications.filter(n => !n.read).length
    const todayCount = notifications.filter(n => {
      // 简化实现，实际应该比较日期
      return n.date === '今天'
    }).length

    // 更新筛选器计数
    const filters = this.data.notificationFilters.map(filter => {
      let count = 0
      switch (filter.id) {
        case 'all':
          count = totalCount
          break
        case 'unread':
          count = unreadCount
          break
        case 'exam':
          count = notifications.filter(n => n.type === 'exam_reminder').length
          break
        case 'task':
          count = notifications.filter(n => n.type === 'task_deadline').length
          break
        case 'system':
          count = notifications.filter(n => n.type === 'system_update').length
          break
      }
      return { ...filter, count }
    })

    this.setData({
      totalCount,
      unreadCount,
      todayCount,
      notificationFilters: filters
    })
  },

  // 筛选通知
  filterNotifications() {
    const allNotifications = this.data.allNotifications
    const currentFilter = this.data.currentFilter

    let filteredNotifications = allNotifications

    if (currentFilter === 'unread') {
      filteredNotifications = allNotifications.filter(n => !n.read)
    } else if (currentFilter !== 'all') {
      // 根据类型筛选
      const typeMap = {
        'exam': 'exam_reminder',
        'task': 'task_deadline',
        'system': 'system_update'
      }
      const targetType = typeMap[currentFilter] || currentFilter
      filteredNotifications = allNotifications.filter(n => n.type === targetType)
    }

    const groupedNotifications = this.groupNotificationsByDate(filteredNotifications)

    this.setData({
      filteredNotifications,
      groupedNotifications
    })
  },

  // 按日期分组通知
  groupNotificationsByDate(notifications) {
    const grouped = {}
    
    notifications.forEach(notification => {
      const date = notification.date
      if (!grouped[date]) {
        grouped[date] = []
      }
      grouped[date].push(notification)
    })

    return Object.keys(grouped).map(date => ({
      date,
      notifications: grouped[date]
    }))
  },

  // 切换筛选器 - 适配van-tabs
  switchFilter(e) {
    const filter = e.detail.name
    this.setData({ currentFilter: filter })
    this.filterNotifications()
  },

  // 全部标记为已读
  markAllAsRead() {
    const notifications = this.data.allNotifications.map(n => ({
      ...n,
      read: true
    }))

    this.updateNotifications(notifications)
    
    wx.showToast({
      title: '全部标记为已读',
      icon: 'success'
    })
  },

  // 打开通知详情
  openNotification(e) {
    const notification = e.currentTarget.dataset.notification
    
    // 如果是未读通知，标记为已读
    if (!notification.read) {
      this.markNotificationAsRead(notification.id)
    }

    // 处理通知点击
    this.handleNotificationAction(notification)
  },

  // 处理通知动作
  handleNotificationAction(notification) {
    switch (notification.type) {
      case 'exam_reminder':
        wx.navigateTo({ url: '/pages/exam-detail/index' })
        break
      case 'task_deadline':
        wx.navigateTo({ url: '/pages/task-center/index' })
        break
      case 'achievement':
        wx.navigateTo({ url: '/pages/achievement-system/index' })
        break
      case 'study_reminder':
        wx.navigateTo({ url: '/pages/pomodoro/index' })
        break
      default:
        wx.showToast({ title: '查看通知详情', icon: 'none' })
    }
  },

  // 显示通知操作菜单
  showNotificationActions(e) {
    const notification = e.currentTarget.dataset.notification
    
    const actions = [
      { name: 'read', text: notification.read ? '标记为未读' : '标记为已读' },
      { name: 'delete', text: '删除通知' },
      { name: 'mute', text: '屏蔽此类通知' }
    ]

    this.setData({
      showActionSheet: true,
      selectedNotification: notification,
      actionSheetActions: actions
    })
  },

  // 隐藏操作菜单
  hideActionSheet() {
    this.setData({ showActionSheet: false })
  },

  // 处理操作菜单选择
  handleActionSelect(e) {
    const action = e.detail.name
    const notification = this.data.selectedNotification

    switch (action) {
      case 'read':
        if (notification.read) {
          this.markNotificationAsUnread(notification.id)
        } else {
          this.markNotificationAsRead(notification.id)
        }
        break
      case 'delete':
        this.deleteNotificationById(notification.id)
        break
      case 'mute':
        this.muteNotificationType(notification.type)
        break
    }

    this.hideActionSheet()
  },

  // 标记为已读
  markAsRead(e) {
    const notification = e.currentTarget.dataset.notification
    this.markNotificationAsRead(notification.id)
  },

  // 标记为未读
  markAsUnread(e) {
    const notification = e.currentTarget.dataset.notification
    this.markNotificationAsUnread(notification.id)
  },

  // 删除通知
  deleteNotification(e) {
    const notification = e.currentTarget.dataset.notification
    this.deleteNotificationById(notification.id)
  },

  // 屏蔽通知类型
  muteNotificationType(type) {
    wx.showModal({
      title: '屏蔽通知',
      content: `确定要屏蔽 ${this.getNotificationTypeName(type)} 类型的通知吗？`,
      success: (res) => {
        if (res.confirm) {
          // 更新通知设置
          const settings = this.data.notificationSettings.map(setting => {
            if (setting.type === type) {
              return { ...setting, enabled: false }
            }
            return setting
          })
          
          this.setData({ notificationSettings: settings })
          this.saveNotificationSettings()
          
          wx.showToast({ title: '已屏蔽该类型通知', icon: 'success' })
        }
      }
    })
  },

  // 执行通知动作
  executeNotificationAction(e) {
    const action = e.currentTarget.dataset.action
    const notification = e.currentTarget.dataset.notification

    switch (action.id) {
      case 'view_exam':
        wx.navigateTo({ url: '/pages/exam-detail/index' })
        break
      case 'view_task':
        wx.navigateTo({ url: '/pages/task-center/index' })
        break
      case 'view_achievement':
        wx.navigateTo({ url: '/pages/achievement-system/index' })
        break
      case 'dismiss':
        this.markNotificationAsRead(notification.id)
        break
      default:
        wx.showToast({ title: action.label, icon: 'none' })
    }
  },

  // 根据ID标记通知为已读
  markNotificationAsRead(notificationId) {
    const notifications = this.data.allNotifications.map(n => {
      if (n.id === notificationId) {
        return { ...n, read: true }
      }
      return n
    })
    
    this.updateNotifications(notifications)
  },

  // 根据ID标记通知为未读
  markNotificationAsUnread(notificationId) {
    const notifications = this.data.allNotifications.map(n => {
      if (n.id === notificationId) {
        return { ...n, read: false }
      }
      return n
    })
    
    this.updateNotifications(notifications)
  },

  // 根据ID删除通知
  deleteNotificationById(notificationId) {
    wx.showModal({
      title: '删除通知',
      content: '确定要删除这条通知吗？',
      success: (res) => {
        if (res.confirm) {
          const notifications = this.data.allNotifications.filter(n => n.id !== notificationId)
          this.updateNotifications(notifications)
          
          wx.showToast({ title: '通知已删除', icon: 'success' })
        }
      }
    })
  },

  // 更新通知数据
  updateNotifications(notifications) {
    try {
      wx.setStorageSync('notifications', notifications)
    } catch (error) {
      console.error('保存通知失败:', error)
    }

    this.setData({ allNotifications: notifications })
    this.updateNotificationStats()
    this.filterNotifications()
  },

  // 获取通知类型名称
  getNotificationTypeName(type) {
    const typeMap = {
      'exam_reminder': '考试提醒',
      'task_deadline': '任务截止',
      'study_reminder': '复习提醒',
      'achievement': '成就通知',
      'system_update': '系统更新'
    }
    return typeMap[type] || '未知类型'
  },

  // 打开通知设置
  openNotificationSettings() {
    this.setData({ showSettingsModal: true })
  },

  // 隐藏设置弹窗
  hideSettingsModal() {
    this.setData({ showSettingsModal: false })
  },

  // 切换通知类型开关
  toggleNotificationType(e) {
    const type = e.currentTarget.dataset.type
    const enabled = e.detail
    
    const settings = this.data.notificationSettings.map(setting => {
      if (setting.type === type) {
        return { ...setting, enabled }
      }
      return setting
    })
    
    this.setData({ notificationSettings: settings })
    this.saveNotificationSettings()
  },

  // 显示免打扰时间设置
  showQuietHoursSettings() {
    this.setData({ showQuietHoursModal: true })
  },

  // 隐藏免打扰时间设置
  hideQuietHoursModal() {
    this.setData({ showQuietHoursModal: false })
  },

  // 显示开始时间选择器
  showStartTimePicker() {
    this.setData({ 
      showTimePicker: true, 
      timePickerType: 'start' 
    })
  },

  // 显示结束时间选择器
  showEndTimePicker() {
    this.setData({ 
      showTimePicker: true, 
      timePickerType: 'end' 
    })
  },

  // 隐藏时间选择器
  hideTimePicker() {
    this.setData({ showTimePicker: false })
  },

  // 时间选择确认
  onTimeConfirm(e) {
    const [hour, minute] = e.detail.value
    const time = `${hour}:${minute}`
    const type = this.data.timePickerType
    
    const quietHours = { ...this.data.quietHours }
    if (type === 'start') {
      quietHours.start = time
    } else {
      quietHours.end = time
    }
    
    this.setData({ quietHours })
    this.hideTimePicker()
  },

  // 保存免打扰时间设置
  saveQuietHours() {
    this.saveNotificationSettings()
    this.hideQuietHoursModal()
    wx.showToast({ title: '设置已保存', icon: 'success' })
  },

  // 加载通知设置
  loadNotificationSettings() {
    try {
      const settings = wx.getStorageSync('notificationSettings')
      if (settings) {
        this.setData({ 
          notificationSettings: settings.types || this.data.notificationSettings,
          quietHours: settings.quietHours || this.data.quietHours
        })
      }
    } catch (error) {
      console.error('加载通知设置失败:', error)
    }
  },

  // 保存通知设置
  saveNotificationSettings() {
    try {
      const settings = {
        types: this.data.notificationSettings,
        quietHours: this.data.quietHours
      }
      wx.setStorageSync('notificationSettings', settings)
    } catch (error) {
      console.error('保存通知设置失败:', error)
    }
  },

  // 启用通知
  enableNotifications() {
    wx.showModal({
      title: '开启通知',
      content: '是否允许考吧小程序向您发送通知？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ notificationsEnabled: true })
          wx.showToast({ title: '通知已开启', icon: 'success' })
        }
      }
    })
  },

  // 刷新通知
  refreshNotifications() {
    this.loadNotifications()
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshNotifications()
    wx.stopPullDownRefresh()
  }
})