/* pages/exam-center/index.wxss */

/* CSS变量定义 */
:root {
  --urgent-red: #FF4D4F;
  --brand-blue: #1890FF;
  --success-green: #52C41A;
  --warning-orange: #FA8C16;
  --neutral-gray: #8C8C8C;
  --bg-gray: #F7F8FA;
}

/* 页面容器 */
.container {
  padding: 20rpx;
  padding-bottom: 140rpx; /* 为底部导航栏和按钮预留足够空间 */
  background-color: #f7f8fa;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 顶部统计区域 */
.stats-section {
  margin-bottom: 20rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}

.stat-card {
  flex: 1;
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

/* 统计卡片悬停效果 */
.stat-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}



.stat-content {
  text-align: center;
  padding: 24rpx 16rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1890FF;
  display: block;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}



.stat-label {
  font-size: 22rpx;
  color: #646566;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20rpx;
}



/* 考试列表区域 */
.exams-section {
  margin-bottom: 40rpx;
}

.exam-item-wrapper {
  margin-bottom: 16rpx;
  position: relative;
  overflow: hidden;
}

/* 滑动容器 */
.swipe-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

/* 考试卡片主体 */
.exam-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  padding: 24rpx;
  transition: transform 0.3s ease;
  position: relative;
}

/* 考试卡片顶部渐变线 */
.exam-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #1890ff 0%, #52c41a 100%);
  border-radius: 16rpx 16rpx 0 0;
}

/* 紧急考试卡片样式 */
.exam-card--urgent {
  border-left: 6rpx solid var(--urgent-red);
  box-shadow: 0 4rpx 16rpx rgba(255, 77, 79, 0.3);
  background: linear-gradient(135deg, #ffffff 0%, #fff5f5 100%);
}



/* 考试卡片头部 */
.exam-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.exam-title-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.urgent-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.exam-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
}

/* 已完成考试标题样式 */
.exam-title--completed {
  color: #8C8C8C !important;
}

/* 考试标签区域 */
.exam-tags {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
}

.exam-type-tag,
.importance-tag,
.status-tag {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #fff;
  font-weight: 500;
}

.type-icon,
.importance-icon {
  font-size: 18rpx;
}

.type-text,
.importance-text,
.status-tag-text {
  font-size: 20rpx;
  line-height: 1;
}

/* 考试时间文本 */
.exam-datetime-text {
  font-size: 22rpx;
  color: #8C8C8C;
  font-weight: 500;
  margin-left: 8rpx;
}

/* 头部操作区域 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}



.status-toggle-section {
  /* 移除了 margin-left */
}

/* 三个点菜单按钮 */
.menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.menu-button:active {
  background-color: rgba(140, 140, 140, 0.1);
  transform: scale(0.9);
}

/* 添加考试区域 */
.add-exam-section {
  margin-top: 32rpx;
  margin-bottom: 160rpx; /* 为底部导航栏预留足够空间 */
  padding: 0 16rpx;
}

.add-exam-button {
  height: 88rpx !important;
  font-size: 28rpx !important;
  font-weight: 600 !important;
  border-radius: 16rpx !important;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2) !important;
}

/* 突出提示行 */
.exam-alert-section {
  text-align: center;
  margin: 16rpx 0;
  padding: 12rpx 16rpx;
  background-color: rgba(24, 144, 255, 0.05);
  border-radius: 12rpx;
  border-left: 4rpx solid currentColor;
}

.exam-alert-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #1890FF;
  line-height: 1.3;
}

/* 效率徽章 */
.efficiency-badge {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  background-color: #F0F0F0;
  border-radius: 8rpx;
  margin-left: 8rpx;
}



/* 小组信息区域 */
.group-section {
  background-color: #F8F9FF;
  border-radius: 12rpx;
  padding: 16rpx;
  margin: 16rpx 0;
  border-left: 4rpx solid var(--brand-blue);
}

/* 小组概览 */
.group-overview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 4rpx 0;
}

.group-overview:active {
  background-color: rgba(24, 144, 255, 0.05);
  border-radius: 8rpx;
}

/* 小组概览内容 */
.group-summary {
  display: flex;
  align-items: center;
  flex: 1;
}

.group-title {
  font-size: 24rpx;
  color: #262626;
  font-weight: 500;
  margin-right: 12rpx;
}

/* 成员头像组 */
.group-members-avatars {
  display: flex;
  align-items: center;
}

.member-avatar {
  position: relative;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

/* 空位显示 */
.empty-slot {
  margin-left: -8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: #F0F0F0;
  border: 2rpx dashed #1890FF;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.empty-slot:active {
  background-color: #E6F7FF;
  transform: scale(0.95);
}

.add-icon {
  font-size: 20rpx;
  color: #1890FF;
  font-weight: bold;
}

/* 小组切换按钮 */
.group-toggle {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.toggle-text {
  font-size: 22rpx;
  color: #8C8C8C;
}

/* 小组统计行 */
.group-stats-line {
  margin-top: 8rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.group-stats {
  font-size: 22rpx;
  color: #8C8C8C;
  flex: 1;
}

/* 排名变化图标 */
.rank-change {
  display: flex;
  align-items: center;
  margin-left: 8rpx;
}

.rank-change-icon {
  font-size: 24rpx;
  font-weight: 600;
  animation: rankBounce 0.6s ease-out;
}

/* 排名变化动画 */
@keyframes rankBounce {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 小组详情展开 */
.group-detail {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  margin-top: 12rpx;
}

.group-detail--show {
  max-height: 2000rpx;
}

/* 成员项目 */
.member-item {
  padding: 12rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.member-item:last-child {
  border-bottom: none;
}

.member-info {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.member-avatar-large {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: 12rpx;
  border: 2rpx solid #fff;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.member-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.member-name {
  font-size: 24rpx;
  color: #262626;
  font-weight: 500;
  margin-bottom: 2rpx;
}

.member-progress-text {
  font-size: 20rpx;
  color: #8C8C8C;
}

.online-indicator {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.online-dot {
  font-size: 16rpx;
  color: #52C41A;
}

.online-text {
  font-size: 18rpx;
  color: #52C41A;
}

.member-progress-bar {
  margin-bottom: 8rpx;
}

/* 小组操作按钮 */
.group-actions {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid #F0F0F0;
  align-items: center;
  min-height: 64rpx;
}

.group-action-btn {
  flex: 1;
  min-width: 0; /* 防止flex项目溢出 */
}

.invite-btn-wrapper {
  flex: 1;
  display: flex;
}

/* 小组满员提示 */
.group-full-tip {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  background-color: rgba(82, 196, 26, 0.1);
  border-radius: 16rpx;
  border: 1rpx solid #52C41A;
}

.full-text {
  font-size: 22rpx;
  color: #52C41A;
  font-weight: 500;
}

/* 科目进度区域样式 */
.subjects-section {
  margin-top: 16rpx;
  border-radius: 12rpx;
  background-color: #FAFAFA;
  overflow: hidden;
}

/* 科目概览 */
.subjects-overview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 16rpx;
  background-color: #F5F5F5;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.subjects-overview:active {
  background-color: #E8E8E8;
}

.subjects-summary {
  display: flex;
  align-items: center;
  flex: 1;
}

.subjects-title {
  font-size: 24rpx;
  color: #262626;
  font-weight: 500;
  margin-right: 12rpx;
}

.subjects-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  align-items: center;
}

.subject-compact-item {
  font-size: 22rpx;
  font-weight: 500;
  color: #1890FF;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  line-height: 1.2;
}

.subject-more-item {
  font-size: 22rpx;
  font-weight: 500;
  color: #8C8C8C;
  background-color: rgba(140, 140, 140, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  line-height: 1.2;
}

.subjects-toggle {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.toggle-text {
  font-size: 22rpx;
  color: #8C8C8C;
}

/* 科目详情 */
.subjects-detail {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background-color: #FAFAFA;
}

.subjects-detail--show {
  max-height: 1000rpx;
}

.subject-item {
  padding: 12rpx 16rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.subject-item:last-child {
  border-bottom: none;
}

.subject-info {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.subject-icon {
  font-size: 20rpx;
  margin-right: 8rpx;
  width: 24rpx;
  text-align: center;
}

.subject-name {
  font-size: 22rpx;
  color: #262626;
  flex: 1;
}

.subject-progress-text {
  font-size: 20rpx;
  color: #8C8C8C;
  font-weight: 600;
}

.subject-progress-bar {
  margin-bottom: 6rpx;
}

.subject-tasks {
  text-align: right;
}

.tasks-text {
  font-size: 18rpx;
  color: #BFBFBF;
}

/* 创建搭子小组引导样式 */
.group-create-section {
  margin-top: 16rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2rpx dashed #1890FF;
  padding: 16rpx;
  cursor: pointer;
}

.create-info {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.create-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  opacity: 0.8;
}

.create-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.create-title {
  font-size: 26rpx;
  color: #1890FF;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.create-desc {
  font-size: 22rpx;
  color: #1890FF;
  opacity: 0.7;
}

.create-action {
  display: flex;
  justify-content: flex-end;
}

.create-button {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 8rpx 16rpx;
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 16rpx;
  border: 1rpx solid #1890FF;
}

.create-button-text {
  font-size: 22rpx;
  color: #1890FF;
  font-weight: 500;
}
/* 左滑操作区域 */
.swipe-actions {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  z-index: 1;
}

.swipe-action {
  width: 120rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
}

.swipe-action--edit {
  background-color: var(--brand-blue);
}

.swipe-action--delete {
  background-color: var(--urgent-red);
}

.swipe-action-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.swipe-action-text {
  font-size: 20rpx;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes blink {
  0%, 100% {
    background-color: var(--urgent-red);
  }
  50% {
    background-color: #FF7875;
  }
}

/* 紧急按钮样式（移除动画） */
.urgent-button {
  background-color: var(--urgent-red) !important;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.4) !important;
}



.exam-date {
  font-size: 24rpx;
  color: #969799;
}

/* 进度区域 */
.progress-section {
  padding: 12rpx 0;
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6rpx;
}

.progress-label {
  font-size: 24rpx;
  color: #646566;
}

.progress-text {
  font-size: 22rpx;
  color: #1890FF;
  font-weight: 600;
}

/* 倒计时区域 */
.countdown-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 8rpx 0;
  margin-bottom: 12rpx;
}

.countdown-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60rpx;
}

.countdown-number {
  font-size: 26rpx;
  font-weight: 700;
  color: #1890FF;
  line-height: 1;
}

.countdown-unit {
  font-size: 20rpx;
  color: #969799;
  margin-top: 2rpx;
}



/* 空状态区域 */
.empty-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500rpx;
  margin-bottom: 40rpx;
  padding: 40rpx 20rpx;
}

.empty-container {
  text-align: center;
  max-width: 600rpx;
  width: 100%;
}

.empty-icon {
  margin-bottom: 32rpx;
}

.empty-emoji {
  font-size: 120rpx;
  line-height: 1;
  animation: emptyIconFloat 3s ease-in-out infinite;
}

/* 空状态图标漂浮动画 */
@keyframes emptyIconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

.empty-content {
  margin-bottom: 40rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.empty-message {
  font-size: 26rpx;
  color: #8C8C8C;
  line-height: 1.6;
  white-space: pre-line;
}

.empty-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}

.empty-action-button {
  width: 320rpx !important;
  height: 88rpx !important;
  font-size: 30rpx !important;
  font-weight: 600 !important;
  border-radius: 16rpx !important;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3) !important;
}

/* 快速操作区域 */
.empty-quick-actions {
  width: 100%;
}

.quick-action-title {
  font-size: 24rpx;
  color: #8C8C8C;
  display: block;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.quick-action-grid {
  display: flex;
  justify-content: center;
  gap: 24rpx;
}

.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.quick-action-item:active {
  transform: scale(0.96);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.quick-action-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
  display: block;
}

.quick-action-text {
  font-size: 22rpx;
  color: #646566;
  font-weight: 500;
}

/* 悬浮按钮样式已移除，现在使用列表底部的添加按钮 */

/* Vant 组件自定义样式 */

/* 标签页 */
.van-tabs {
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
}

/* 卡片组件 */
.van-card {
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
  overflow: hidden;
}

.van-card__content {
  padding: 24rpx !important;
}

.van-card__title {
  font-size: 30rpx;
  font-weight: 600;
  color: #323233;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.van-card__desc {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #969799;
}

/* 标签样式 */
.van-tag {
  font-size: 20rpx;
}

/* 进度条 */
.van-progress {
  margin: 6rpx 0;
}

/* 单元格组 */
.van-cell-group {
  background-color: transparent;
}

.van-cell {
  padding: 0;
  background-color: transparent;
}

/* 按钮样式调整 */
.van-button--small {
  height: 52rpx;
  font-size: 22rpx;
  padding: 0 12rpx;
}

.van-button--plain {
  border: 1rpx solid #ebedf0;
}

.van-button--plain.van-button--primary {
  border-color: #1890FF;
}

/* 操作面板 */
.van-action-sheet__header {
  font-size: 32rpx;
  font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .stats-value {
    font-size: 28rpx;
  }

  .stats-label {
    font-size: 20rpx;
  }



  .van-card__content {
    padding: 20rpx !important;
  }
}
