/**
 * 兼容性检查工具
 * 检查系统中的数据格式兼容性问题
 */

const ExamDataMigration = require('./examDataMigration')
const ExamModel = require('../models/ExamModel')

class CompatibilityChecker {
  constructor() {
    this.issues = []
    this.warnings = []
    this.recommendations = []
  }

  /**
   * 检查考试数据兼容性
   * @param {Array} examList - 考试数据列表
   * @returns {Object} 检查结果
   */
  checkExamCompatibility(examList) {
    console.log('开始检查考试数据兼容性...')
    
    this.resetResults()
    
    const results = {
      total: examList.length,
      compatible: 0,
      needsMigration: 0,
      hasIssues: 0,
      details: []
    }

    examList.forEach((exam, index) => {
      const checkResult = this.checkSingleExam(exam, index)
      results.details.push(checkResult)

      if (checkResult.compatible) {
        results.compatible++
      } else if (checkResult.needsMigration) {
        results.needsMigration++
      } else {
        results.hasIssues++
      }
    })

    return {
      success: true,
      results,
      issues: this.issues,
      warnings: this.warnings,
      recommendations: this.generateCompatibilityRecommendations(results)
    }
  }

  /**
   * 检查单个考试数据
   * @param {Object} exam - 考试数据
   * @param {number} index - 索引
   * @returns {Object} 检查结果
   */
  checkSingleExam(exam, index) {
    const result = {
      index,
      id: exam._id || exam.id,
      title: exam.name || exam.title,
      compatible: false,
      needsMigration: false,
      issues: [],
      warnings: [],
      fieldStatus: {}
    }

    try {
      // 检查是否需要迁移
      if (ExamDataMigration.needsMigration(exam)) {
        result.needsMigration = true
        result.warnings.push('数据格式需要迁移')
        
        // 检查具体的字段问题
        this.checkFieldCompatibility(exam, result)
      } else {
        // 检查新格式数据的完整性
        try {
          const examModel = new ExamModel(exam)
          result.compatible = true
          result.fieldStatus = this.getFieldStatus(exam, 'compatible')
        } catch (error) {
          result.issues.push(`数据验证失败: ${error.message}`)
          result.fieldStatus = this.getFieldStatus(exam, 'invalid')
        }
      }
    } catch (error) {
      result.issues.push(`检查异常: ${error.message}`)
    }

    // 记录全局问题和警告
    result.issues.forEach(issue => {
      this.issues.push(`考试 ${index + 1} (${result.title}): ${issue}`)
    })
    
    result.warnings.forEach(warning => {
      this.warnings.push(`考试 ${index + 1} (${result.title}): ${warning}`)
    })

    return result
  }

  /**
   * 检查字段兼容性
   * @param {Object} exam - 考试数据
   * @param {Object} result - 检查结果对象
   */
  checkFieldCompatibility(exam, result) {
    const fieldChecks = [
      { old: 'name', new: 'title', required: true },
      { old: 'date', new: 'examDate', required: true },
      { old: 'time', new: 'examTime', required: true },
      { old: 'notes', new: 'description', required: false },
      { old: 'subjects', new: 'subject', required: false, special: 'array_to_string' }
    ]

    fieldChecks.forEach(check => {
      const hasOld = exam.hasOwnProperty(check.old)
      const hasNew = exam.hasOwnProperty(check.new)

      if (hasOld && !hasNew) {
        result.fieldStatus[check.old] = 'needs_migration'
        if (check.required) {
          result.warnings.push(`必填字段 ${check.old} 需要迁移为 ${check.new}`)
        }
      } else if (!hasOld && !hasNew && check.required) {
        result.issues.push(`缺少必填字段 ${check.new}`)
        result.fieldStatus[check.new] = 'missing'
      } else if (hasNew) {
        result.fieldStatus[check.new] = 'compatible'
      }

      // 特殊字段检查
      if (check.special === 'array_to_string' && hasOld) {
        if (Array.isArray(exam[check.old]) && exam[check.old].length > 1) {
          result.warnings.push(`多个科目将合并为单个科目: ${exam[check.old].join(', ')}`)
        }
      }
    })

    // 检查废弃字段
    const deprecatedFields = ['targetScore', 'isActive', 'startTime', 'endTime', 'reminderEnabled', 'reminderFrequency']
    deprecatedFields.forEach(field => {
      if (exam.hasOwnProperty(field)) {
        result.warnings.push(`字段 ${field} 已废弃，将在迁移时移除`)
        result.fieldStatus[field] = 'deprecated'
      }
    })
  }

  /**
   * 获取字段状态
   * @param {Object} exam - 考试数据
   * @param {string} defaultStatus - 默认状态
   * @returns {Object} 字段状态对象
   */
  getFieldStatus(exam, defaultStatus) {
    const status = {}
    const standardFields = ['title', 'subject', 'examDate', 'examTime', 'location', 'description', 'type', 'importance', 'status', 'reminderSettings']
    
    standardFields.forEach(field => {
      status[field] = exam.hasOwnProperty(field) ? defaultStatus : 'missing'
    })

    return status
  }

  /**
   * 生成兼容性建议
   * @param {Object} results - 检查结果
   * @returns {Array} 建议列表
   */
  generateCompatibilityRecommendations(results) {
    const recommendations = []

    if (results.needsMigration > 0) {
      recommendations.push({
        type: 'migration',
        priority: 'high',
        message: `有 ${results.needsMigration} 个考试需要数据迁移`,
        action: '运行数据迁移脚本',
        command: 'node scripts/migrateExamData.js'
      })
    }

    if (results.hasIssues > 0) {
      recommendations.push({
        type: 'error',
        priority: 'critical',
        message: `有 ${results.hasIssues} 个考试存在数据问题`,
        action: '检查并修复数据问题',
        details: this.issues.slice(0, 5) // 显示前5个问题
      })
    }

    if (results.compatible === results.total) {
      recommendations.push({
        type: 'success',
        priority: 'info',
        message: '所有考试数据都兼容新格式',
        action: '可以安全使用新的考试模型'
      })
    }

    const multiSubjectWarnings = this.warnings.filter(w => w.includes('多个科目'))
    if (multiSubjectWarnings.length > 0) {
      recommendations.push({
        type: 'info',
        priority: 'medium',
        message: `有 ${multiSubjectWarnings.length} 个考试包含多个科目`,
        action: '迁移后请检查科目信息是否正确'
      })
    }

    return recommendations
  }

  /**
   * 检查API兼容性
   * @returns {Object} API兼容性检查结果
   */
  checkApiCompatibility() {
    const apiChecks = [
      {
        name: 'SmartApi.getExams',
        description: '获取考试列表API',
        compatible: true,
        notes: '已集成自动数据迁移'
      },
      {
        name: 'SmartApi.getExamById',
        description: '获取单个考试API',
        compatible: true,
        notes: '已集成自动数据迁移'
      },
      {
        name: 'SmartApi.addExam',
        description: '添加考试API',
        compatible: true,
        notes: '使用ExamModel验证'
      },
      {
        name: 'SmartApi.updateExam',
        description: '更新考试API',
        compatible: true,
        notes: '使用ExamModel验证'
      },
      {
        name: 'SmartApi.deleteExam',
        description: '删除考试API',
        compatible: true,
        notes: '无需修改'
      }
    ]

    return {
      success: true,
      apis: apiChecks,
      summary: {
        total: apiChecks.length,
        compatible: apiChecks.filter(api => api.compatible).length,
        incompatible: apiChecks.filter(api => !api.compatible).length
      }
    }
  }

  /**
   * 检查页面兼容性
   * @returns {Object} 页面兼容性检查结果
   */
  checkPageCompatibility() {
    const pageChecks = [
      {
        name: 'pages/home/<USER>',
        description: '首页',
        compatible: true,
        notes: '已更新字段映射'
      },
      {
        name: 'pages/exam-center/index',
        description: '考试中心',
        compatible: true,
        notes: '字段使用正确'
      },
      {
        name: 'pages/exam-detail/index',
        description: '考试详情',
        compatible: true,
        notes: '已更新字段映射'
      },
      {
        name: 'pages/add-exam/index',
        description: '新增考试',
        compatible: true,
        notes: '已重构使用新模型'
      },
      {
        name: 'pages/edit-exam/index',
        description: '编辑考试',
        compatible: true,
        notes: '已重构使用新模型'
      },
      {
        name: 'pages/task-center/index',
        description: '任务中心',
        compatible: true,
        notes: '字段使用正确'
      }
    ]

    return {
      success: true,
      pages: pageChecks,
      summary: {
        total: pageChecks.length,
        compatible: pageChecks.filter(page => page.compatible).length,
        incompatible: pageChecks.filter(page => !page.compatible).length
      }
    }
  }

  /**
   * 生成完整的兼容性报告
   * @param {Array} examList - 考试数据列表
   * @returns {Object} 完整报告
   */
  generateFullReport(examList) {
    const dataCompatibility = this.checkExamCompatibility(examList)
    const apiCompatibility = this.checkApiCompatibility()
    const pageCompatibility = this.checkPageCompatibility()

    return {
      timestamp: new Date().toISOString(),
      summary: {
        dataCompatible: dataCompatibility.results.compatible === dataCompatibility.results.total,
        apiCompatible: apiCompatibility.summary.incompatible === 0,
        pageCompatible: pageCompatibility.summary.incompatible === 0,
        overallCompatible: dataCompatibility.results.compatible === dataCompatibility.results.total &&
                          apiCompatibility.summary.incompatible === 0 &&
                          pageCompatibility.summary.incompatible === 0
      },
      data: dataCompatibility,
      api: apiCompatibility,
      pages: pageCompatibility,
      recommendations: [
        ...dataCompatibility.recommendations,
        ...this.generateSystemRecommendations()
      ]
    }
  }

  /**
   * 生成系统级建议
   * @returns {Array} 系统建议
   */
  generateSystemRecommendations() {
    return [
      {
        type: 'maintenance',
        priority: 'medium',
        message: '建议定期运行兼容性检查',
        action: '设置定期检查任务'
      },
      {
        type: 'backup',
        priority: 'high',
        message: '执行数据迁移前请备份数据',
        action: '创建数据备份'
      },
      {
        type: 'testing',
        priority: 'medium',
        message: '迁移后建议进行功能测试',
        action: '运行测试用例'
      }
    ]
  }

  /**
   * 重置检查结果
   */
  resetResults() {
    this.issues = []
    this.warnings = []
    this.recommendations = []
  }
}

module.exports = CompatibilityChecker
