// pages/exam-center/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    examStats: [
      { label: '进行中', value: '0' },
      { label: '平均进度', value: '0%' },
      { label: '最近考试', value: '0天' }
    ],
    filterTabs: [],
    currentFilter: 'all',
    activeTab: 0,
    exams: [],
    filteredExams: [],
    examGroups: {}, // 存储考试对应的小组信息
    showActionSheet: false,
    selectedExam: null,
    examActions: []
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.loadData()
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      })
    }
  },

  onPullDownRefresh() {
    this.loadData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 初始化页面
  initPage() {
    this.initFilterTabs()
    this.initExamActions()
    this.loadData()
  },

  // 初始化演示统计数据
  initDemoStats() {
    const examStats = [
      { label: '总考试', value: '8' },
      { label: '备考中', value: '5' },
      { label: '已完成', value: '2' },
      { label: '即将到来', value: '1' }
    ]

    this.setData({ examStats }, () => {
      console.log('统计数据已设置:', this.data.examStats)
    })
    console.log('初始化演示统计数据:', examStats)
  },

  // 初始化筛选标签 - 简化为三个基本标签
  initFilterTabs() {
    const filterTabs = [
      { value: 'all', label: '全部', count: 0 },
      { value: 'preparing', label: '备考中', count: 0 },
      { value: 'completed', label: '已完成', count: 0 }
    ]

    this.setData({ filterTabs })
  },

  // 初始化考试操作
  initExamActions() {
    const examActions = [
      { name: '🚀 开始复习', action: 'startStudy' },
      { name: '📋 查看任务', action: 'viewTasks' },
      { name: '👥 备考小组', action: 'viewGroup' },
      { name: '📄 查看详情', action: 'viewDetail' },
      { name: '✏️ 编辑考试', action: 'edit' },
      { name: '📤 分享考试', action: 'share' },
      { name: '🗑️ 删除考试', action: 'delete', color: '#ff4444' }
    ]

    this.setData({ examActions })
  },

  // 加载数据
  async loadData() {
    // 检查登录状态
    const app = getApp()
    const loginStatus = app.checkLoginStatus()

    if (!loginStatus.isLoggedIn) {
      console.log('用户未登录，显示空状态')
      // 设置空状态数据
      this.setData({
        exams: [],
        examStats: [
          { label: '总考试', value: '0' },
          { label: '本月考试', value: '0' },
          { label: '已完成', value: '0' },
          { label: '备考中', value: '0' }
        ]
      })
      return
    }

    await Promise.all([
      this.loadExamStats(), // 并行加载统计数据
      this.loadExams() // 并行加载考试列表
    ])

    // 加载完考试后，再加载小组信息和科目信息
    if (this.data.exams.length > 0) {
      // 先加载小组信息，再加载科目信息，避免数据竞态
      await this.loadExamGroups()
      await this.loadExamSubjects()
    }
  },

  // 加载考试统计
  async loadExamStats() {
    // 直接使用本地计算的统计数据，确保与筛选数据一致
    console.log('使用本地统计数据，确保与筛选数据一致')
    this.loadLocalExamStats()
  },

  // 本地计算统计数据（新的三项统计）
  loadLocalExamStats() {
    const { exams } = this.data
    const now = new Date()

    console.log('📊 统计数据计算 - 所有考试:', exams.map(e => ({ name: e.name, status: e.status })))

    // 计算进行中的考试数量（备考中 + 即将到来）
    const preparingExams = exams.filter(exam => {
      const match = exam.status === 'preparing' || exam.status === 'upcoming'
      console.log(`统计 - 考试 "${exam.name}" 状态: ${exam.status}, 匹配进行中: ${match}`)
      return match
    })

    console.log('📊 统计数据 - 进行中考试:', preparingExams.map(e => ({ name: e.name, status: e.status })))

    // 计算平均进度
    let avgProgress = 0
    if (exams.length > 0) {
      const totalProgress = exams.reduce((sum, exam) => sum + (exam.preparationProgress || 0), 0)
      avgProgress = Math.round(totalProgress / exams.length)
    }

    // 计算最近考试天数
    let nearestDays = 999
    if (exams.length > 0) {
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const futureDays = exams
        .map(exam => {
          const examDate = new Date(exam.date)
          const examDateOnly = new Date(examDate.getFullYear(), examDate.getMonth(), examDate.getDate())
          return Math.floor((examDateOnly - today) / (1000 * 60 * 60 * 24))
        })
        .filter(days => days >= 0) // 只考虑未来的考试

      if (futureDays.length > 0) {
        nearestDays = Math.min(...futureDays)
      }
    }

    // 根据紧急程度设置颜色
    const nearestColor = nearestDays <= 3 ? '#FF4D4F' : nearestDays <= 7 ? '#FA8C16' : '#1890FF'
    const avgColor = avgProgress >= 80 ? '#52C41A' : avgProgress >= 60 ? '#1890FF' : '#FA8C16'

    const examStats = [
      { label: '进行中', value: preparingExams.length.toString(), color: '#1890FF' },
      { label: '平均进度', value: `${avgProgress}%`, color: avgColor },
      { label: '最近考试', value: nearestDays === 999 ? '无' : `${nearestDays}天`, color: nearestColor }
    ]

    this.setData({ examStats })
    console.log('使用新的三项统计:', examStats)
  },
  // 加载考试对应的小组信息
  async loadExamGroups() {
    const { exams } = this.data
    if (exams.length === 0) return

    try {
      const examIds = exams.map(exam => exam.id)
      // 使用正确的API方法名
      const result = await SmartApi.getBatchStudyGroupsByExam(examIds)

      if (result.success) {
        this.setData({ examGroups: result.data || {} })
        // 只添加小组信息，不重新格式化整个考试数据
        const updatedExams = exams.map(exam => {
          const examId = exam.id
          const groupData = result.data[examId]
          if (groupData) {
            const progressData = this.formatGroupProgressText(groupData)
            const groupInfo = {
              id: groupData.id,
              name: groupData.name,
              memberCount: groupData.memberCount || 0,
              members: groupData.members || [],
              averageProgress: groupData.averageProgress || 0,
              myRank: groupData.myRank || 0,
              isActive: groupData.isActive || false,
              membersText: this.formatGroupMembersText(groupData),
              progressText: progressData.text || progressData,
              rankBadge: progressData.rankBadge || '',
              rankColor: progressData.rankColor || '#8C8C8C',
              rankChangeIcon: progressData.rankChangeIcon || '',
              rankChangeColor: progressData.rankChangeColor || '#8C8C8C'
            }
            return { ...exam, groupInfo }
          }
          return exam
        })
        this.setData({ exams: updatedExams })
        this.filterExams()
        console.log('小组信息加载成功:', result.data)
      } else {
        console.log('小组信息加载失败:', result.error)
      }
    } catch (error) {
      console.error('加载小组信息异常:', error)
    }
  },





  // 计算准备进度（为演示数据生成合理的进度）
  calculatePreparationProgress(examId, timeDiff) {
    // 如果考试已经结束，进度为100%
    if (timeDiff < 0) {
      return 100
    }

    // 根据考试ID生成一个固定的随机数种子，确保每次显示的进度都一样
    let seed = 0
    for (let i = 0; i < examId.length; i++) {
      seed += examId.charCodeAt(i)
    }

    // 根据剩余天数调整进度基数
    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
    let baseProgress = 0

    if (days > 30) {
      baseProgress = 15 + (seed % 20) // 15-35%
    } else if (days > 7) {
      baseProgress = 35 + (seed % 30) // 35-65%
    } else if (days > 0) {
      baseProgress = 65 + (seed % 25) // 65-90%
    } else {
      baseProgress = 90 + (seed % 10) // 90-100%
    }

    return Math.min(100, baseProgress)
  },

  // 获取真实的准备进度
  getActualPreparationProgress(exam) {
    try {
      console.log(`🔍 检查考试 "${exam.title || exam.name}" 的进度数据:`, {
        preparationProgress: exam.preparationProgress,
        totalTasksCount: exam.totalTasksCount,
        completedTasksCount: exam.completedTasksCount,
        examData: exam
      })

      // 优先使用云函数返回的真实进度数据
      if (typeof exam.preparationProgress === 'number' && exam.preparationProgress >= 0) {
        console.log(`✅ 考试 "${exam.title || exam.name}" 使用真实进度:`, exam.preparationProgress + '%')
        return exam.preparationProgress
      }

      // 如果有任务数据，基于任务完成情况计算
      if (exam.totalTasksCount && exam.completedTasksCount !== undefined) {
        const progress = exam.totalTasksCount > 0 ?
          Math.round((exam.completedTasksCount / exam.totalTasksCount) * 100) : 0
        console.log(`📊 考试 "${exam.title || exam.name}" 基于任务计算进度:`, progress + '%',
          `(${exam.completedTasksCount}/${exam.totalTasksCount})`)
        return progress
      }

      // 最后才使用模拟数据
      const timeDiff = new Date(exam.examDate || exam.date).getTime() - new Date().getTime()
      const simulatedProgress = this.calculatePreparationProgress(exam._id || exam.id, timeDiff)
      console.log(`⚠️ 考试 "${exam.title || exam.name}" 使用模拟进度:`, simulatedProgress + '%')
      return simulatedProgress
    } catch (error) {
      console.error('❌ 获取进度数据失败:', error, exam)
      // 出错时返回默认值
      return 0
    }
  },

  // 加载考试列表
  async loadExams() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      // 先尝试使用带分析数据的API，如果失败则使用普通API
      let result
      try {
        result = await SmartApi.getExamsWithAnalytics()
        console.log('使用分析API获取考试数据成功')
      } catch (analyticsError) {
        console.warn('分析API失败，使用普通API:', analyticsError)
        result = await SmartApi.getExams({}, 20, 0)
      }

      wx.hideLoading()

      if (result.success) {
        console.log('原始考试数据:', result.data)
        console.log('原始考试数据长度:', result.data ? result.data.length : 0)

        // 转换数据格式以适配页面显示
        const exams = result.data.map(exam => {
          console.log('处理考试:', exam.title || exam.name, exam)
          return this.formatExamData(exam)
        })

        console.log('格式化后的考试数据:', exams)
        console.log('格式化后的考试数据长度:', exams.length)

        this.setData({ exams })
        this.filterExams()
        this.updateFilterCounts()
        this.loadLocalExamStats() // 使用本地统计数据，确保与筛选数据一致
      } else {
        console.error('加载考试失败:', result.error)
        wx.showToast({
          title: '加载考试失败',
          icon: 'none'
        })
        this.setData({ exams: [] })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('加载考试异常:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      this.setData({ exams: [] })
    }
  },

  // 格式化考试数据 - 增强版本
  formatExamData(exam, includeGroupInfo = false) {
    // 安全的日期处理
    let examDate
    let timeDiff = 0
    let days = 0
    let hours = 0
    let minutes = 0

    try {
      // 尝试多个可能的日期字段
      const dateValue = exam.examDate || exam.date
      
      if (!dateValue) {
        console.warn('考试日期为空:', exam)
        examDate = new Date()
        examDate.setDate(examDate.getDate() + 30) // 默认30天后
      } else {
        examDate = new Date(dateValue)
        
        // 检查日期是否有效
        if (isNaN(examDate.getTime())) {
          console.warn('无效的考试日期:', dateValue, exam)
          examDate = new Date()
          examDate.setDate(examDate.getDate() + 30) // 默认30天后
        }
      }

      // 为了准确计算天数差，将两个日期都重置为当天的00:00:00
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const examDateOnly = new Date(examDate.getFullYear(), examDate.getMonth(), examDate.getDate())

      timeDiff = examDateOnly.getTime() - today.getTime()

      // 计算倒计时（按自然日计算）
      days = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
      hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))

      // 确保计算结果不是NaN
      if (isNaN(days)) days = 30
      if (isNaN(hours)) hours = 0
      if (isNaN(minutes)) minutes = 0

    } catch (error) {
      console.error('日期处理出错:', error, exam)
      // 使用默认值
      examDate = new Date()
      examDate.setDate(examDate.getDate() + 30)
      const now = new Date()
      timeDiff = examDate.getTime() - now.getTime()
      days = 30
      hours = 0
      minutes = 0
    }

    // 确定状态 - 优先使用数据库中的状态，否则根据时间计算
    let status = exam.status || 'preparing'
    let statusText = '备考中'
    let progressColor = '#1890FF'

    // 如果数据库没有状态或状态为空，根据时间计算
    if (!exam.status) {
      if (timeDiff < 0) {
        status = 'past'  // 已过期（而非已完成）
        statusText = '已过期'
        progressColor = '#999'
      } else if (days <= 7) {
        status = 'upcoming'
        statusText = '即将到来'
        progressColor = '#52C41A'
      } else if (days <= 30) {
        status = 'preparing'
        statusText = '备考中'
        progressColor = '#FA8C16'
      }
    } else {
      // 根据数据库状态设置显示文本和颜色
      switch (status) {
        case 'completed':
          statusText = '已完成'
          progressColor = '#52C41A'
          break
        case 'past':
          statusText = '已过期'
          progressColor = '#999'
          break
        case 'upcoming':
          statusText = '即将到来'
          progressColor = '#52C41A'
          break
        case 'preparing':
        default:
          statusText = '备考中'
          progressColor = '#1890FF'
          break
      }
    }

    // 新增：紧急度计算
    const daysLeft = Math.max(0, days)
    const isUrgent = daysLeft <= 3 && timeDiff > 0

    // 新增：倒计时颜色和文案
    let countdownColor = '#1890FF'
    let countdownText = ''
    if (timeDiff <= 0) {
      countdownText = `已过期${Math.abs(days)}天`
      countdownColor = '#999999'
    } else if (daysLeft === 0) {
      countdownText = '今天考试'
      countdownColor = '#8B0000'
    } else if (daysLeft <= 3) {
      countdownText = `还有${daysLeft}天`
      countdownColor = '#FF4D4F'
    } else if (daysLeft <= 7) {
      countdownText = `还有${daysLeft}天`
      countdownColor = '#FA8C16'
    } else {
      countdownText = `还有${daysLeft}天`
      countdownColor = '#1890FF'
    }

    // 额外的安全检查
    if (isNaN(daysLeft)) {
      console.warn('daysLeft 计算异常，使用默认值')
      countdownText = '日期异常'
      countdownColor = '#999999'
    }

    // 新增：小组信息处理
    let groupInfo = null
    const examId = exam._id || exam.id
    if (includeGroupInfo && this.data.examGroups && this.data.examGroups[examId]) {
      const group = this.data.examGroups[examId]
      const progressData = this.formatGroupProgressText(group)
      groupInfo = {
        id: group.id,
        name: group.name,
        memberCount: group.memberCount || 0,
        members: group.members || [],
        averageProgress: group.averageProgress || 0,
        myRank: group.myRank || 0,
        isActive: group.isActive || false,
        membersText: this.formatGroupMembersText(group),
        progressText: progressData.text || progressData,
        rankBadge: progressData.rankBadge || '',
        rankColor: progressData.rankColor || '#8C8C8C',
        rankChangeIcon: progressData.rankChangeIcon || '',
        rankChangeColor: progressData.rankChangeColor || '#8C8C8C'
      }
    }

    // 新增：快速状态切换按钮文案
    let statusButtonText = ''
    let canToggleStatus = false
    if (status === 'preparing') {
      statusButtonText = '✓完成'
      canToggleStatus = true
    } else if (status === 'completed') {
      statusButtonText = '恢复'
      canToggleStatus = true
    }

    // 新增：心理激励文案系统
    let motivationText = ''
    let motivationColor = ''
    let showMotivation = false
    
    // 根据不同条件生成激励文案
    if (daysLeft <= 3 && timeDiff > 0 && status !== 'completed') {
      // 考试临近的激励
      motivationText = '⏰ 考试临近，冲刺阶段！'
      motivationColor = '#FA8C16'
      showMotivation = true
    } else if (groupInfo && groupInfo.averageProgress > 0) {
      // 基于小组进度的激励
      const currentProgress = exam.preparationProgress || this.calculatePreparationProgress(exam._id || exam.id, timeDiff)
      const avgProgress = groupInfo.averageProgress
      
      if (currentProgress > avgProgress + 10) {
        motivationText = '🎉 进度领先，保持优势！'
        motivationColor = '#52C41A'
        showMotivation = true
      } else if (currentProgress < avgProgress - 10) {
        motivationText = '⚠️ 进度落后，加油追赶！'
        motivationColor = '#FF4D4F'
        showMotivation = true
      } else if (groupInfo.myRank === 1 && groupInfo.memberCount > 1) {
        motivationText = '🥇 排名第一，继续加油！'
        motivationColor = '#52C41A'
        showMotivation = true
      }
    } else if (exam.preparationProgress && exam.preparationProgress < 30 && daysLeft <= 7 && status !== 'completed') {
      // 进度严重滞后的警告
      motivationText = '🚨 进度严重滞后，抓紧时间！'
      motivationColor = '#FF4D4F'
      showMotivation = true
    }

    return {
      id: exam._id || exam.id,
      name: exam.title || exam.name,
      subject: exam.subject || '',
      type: exam.type || 'other',
      date: this.formatDisplayDate(examDate), // 使用格式化的显示日期
      time: exam.examTime || exam.time || '',
      location: exam.location || '',
      description: exam.description || '',
      status: status,
      statusText: statusText,
      preparationProgress: this.getActualPreparationProgress(exam),
      progressColor: progressColor,
      countdown: timeDiff > 0 ? [
        { value: Math.max(0, days), unit: '天' },
        { value: Math.max(0, hours), unit: '时' },
        { value: Math.max(0, minutes), unit: '分' }
      ] : [
        { value: 0, unit: '天' },
        { value: 0, unit: '时' },
        { value: 0, unit: '分' }
      ],
      subjects: [], // 科目进度信息将通过loadExamSubjects异步加载
      showSubjects: false, // 控制科目详情展开状态
      showGroupDetail: false, // 控制小组详情展开状态
      // 新增字段
      daysLeft,
      isUrgent,
      countdownColor,
      countdownText,
      alertMessage: this.generateAlertMessage(daysLeft, status, exam),
      groupInfo,
      statusButtonText,
      canToggleStatus,
    // 考试类型和重要性标签
    ...this.generateExamLabels(exam, status)
    }
  },

  // 加载考试科目进度信息
  async loadExamSubjects() {
    const { exams } = this.data
    if (exams.length === 0) return

    try {
      console.log('开始加载考试科目信息...')
      
      // 为每个考试获取科目统计
      const subjectPromises = exams.map(async (exam) => {
        try {
          const result = await SmartApi.getExamSubjectStats(exam.id)
          return {
            examId: exam.id,
            subjects: result.success ? result.data : []
          }
        } catch (error) {
          console.error(`获取考试 ${exam.id} 科目统计失败:`, error)
          return {
            examId: exam.id,
            subjects: []
          }
        }
      })

      const subjectResults = await Promise.all(subjectPromises)
      
      // 构建科目数据映射
      const examSubjects = {}
      subjectResults.forEach(result => {
        examSubjects[result.examId] = result.subjects
      })

      // 更新考试数据，只添加科目信息，保持其他所有属性不变
      // 直接在现有数据基础上更新，避免数据竞态
      const currentExams = this.data.exams
      const updatedExams = currentExams.map(exam => {
        const newSubjects = examSubjects[exam.id] || []
        const processedSubjects = this.processSubjectsForDisplay(newSubjects)
        return {
          ...exam,
          subjects: newSubjects,
          ...processedSubjects
        }
      })

      this.setData({ exams: updatedExams })
      this.filterExams() // 重新筛选以更新filteredExams

      console.log('科目信息加载完成:', examSubjects)
      
      // 调试：检查更新后的数据是否包含小组信息
      const hasGroupInfo = updatedExams.some(exam => exam.groupInfo)
      console.log('科目更新后是否保留小组信息:', hasGroupInfo)
    } catch (error) {
      console.error('加载科目信息失败:', error)
    }
  },

  // 生成考试提示信息
  generateAlertMessage(daysLeft, status, exam) {
    if (status === 'completed') {
      return '考试已完成'
    }

    if (status === 'expired') {
      return '考试已过期'
    }

    // 根据剩余天数生成不同的提示信息
    if (daysLeft < 0) {
      return '考试已过期'
    } else if (daysLeft === 0) {
      return '今日考试，加油！'
    } else if (daysLeft === 1) {
      return '明日考试，最后冲刺！'
    } else if (daysLeft <= 3) {
      return '考试临近，冲刺阶段！'
    } else if (daysLeft <= 7) {
      return '一周内考试，抓紧复习'
    } else if (daysLeft <= 14) {
      return '两周内考试，稳步推进'
    } else if (daysLeft <= 30) {
      return '一月内考试，制定计划'
    } else {
      return '充足时间，从容准备'
    }
  },

  // 处理科目显示逻辑，限制显示数量
  processSubjectsForDisplay(subjects) {
    const maxDisplaySubjects = 2 // 最多显示2个科目

    if (!subjects || subjects.length === 0) {
      return {
        displaySubjects: [],
        hasMoreSubjects: false,
        moreSubjectsCount: 0
      }
    }

    const displaySubjects = subjects.slice(0, maxDisplaySubjects)
    const hasMoreSubjects = subjects.length > maxDisplaySubjects
    const moreSubjectsCount = subjects.length - maxDisplaySubjects

    return {
      displaySubjects,
      hasMoreSubjects,
      moreSubjectsCount
    }
  },

  // 格式化显示日期
  formatDisplayDate(date) {
    try {
      if (!date || isNaN(date.getTime())) {
        return '日期待定'
      }

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      
      return `${year}-${month}-${day}`
    } catch (error) {
      console.error('日期格式化出错:', error)
      return '日期异常'
    }
  },

  // 生成考试标签数据（基于真实数据）
  generateExamLabels(exam, status) {
    // 1. 考试类型处理 - 直接使用真实数据，不推断
    const examType = exam.type || 'other' // 使用真实的type字段
    console.log('生成考试标签 - 考试类型:', examType, '考试数据:', exam.name || exam.title)
    const typeConfig = this.getExamTypeConfig(examType)
    
    // 2. 重要性处理 - 直接使用真实数据，不推断
    const importance = exam.importance || 'medium' // 使用真实的importance字段
    const importanceConfig = this.getImportanceConfig(importance)

    // 3. 状态标签处理
    const statusTagConfig = this.getStatusTagConfig(exam, status)

    return {
      // 考试类型标签
      examTypeName: typeConfig.name,
      examTypeIcon: typeConfig.icon,
      examTypeColor: typeConfig.color,
      
      // 重要性标签
      importanceText: importanceConfig.text,
      importanceColor: importanceConfig.color,
      
      // 状态标签（可选）
      statusTag: statusTagConfig.text,
      statusTagColor: statusTagConfig.color
    }
  },



  // 获取考试类型配置（与创建考试页面一致）
  getExamTypeConfig(examType) {
    const configs = {
      final: { name: '期末考试', icon: '📚', color: '#1890FF' },
      midterm: { name: '期中考试', icon: '📖', color: '#722ED1' },
      quiz: { name: '小测验', icon: '📝', color: '#FA8C16' },
      certificate: { name: '资格考试', icon: '🏆', color: '#52C41A' },
      entrance: { name: '入学考试', icon: '🎓', color: '#F5222D' },
      other: { name: '其他', icon: '📋', color: '#8C8C8C' }
    }
    
    return configs[examType] || configs.other
  },

  // 获取重要性配置（与创建考试页面一致）
  getImportanceConfig(importance) {
    const configs = {
      low: { text: '一般', color: '#8C8C8C' },
      medium: { text: '重要', color: '#FA8C16' },
      high: { text: '非常重要', color: '#F5222D' }
    }
    
    return configs[importance] || configs.medium
  },

  // 获取状态标签配置
  getStatusTagConfig(exam, status) {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const examDate = new Date(exam.examDate || exam.date)
    const examDateOnly = new Date(examDate.getFullYear(), examDate.getMonth(), examDate.getDate())
    const daysLeft = Math.floor((examDateOnly - today) / (1000 * 60 * 60 * 24))
    
    // 根据状态和时间生成特殊标签
    if (status === 'completed') {
      return { text: null, color: null } // 已完成不显示额外标签
    }
    
    if (daysLeft <= 1 && daysLeft >= 0) {
      return { text: '明日考试', color: '#F5222D' }
    }
    
    if (daysLeft <= 3 && daysLeft > 1) {
      return { text: '临近考试', color: '#FA8C16' }
    }
    
    if (exam.isRetake) {
      return { text: '补考', color: '#722ED1' }
    }
    
    if (exam.isOptional) {
      return { text: '选修', color: '#52C41A' }
    }
    
    return { text: null, color: null }
  },



  // 格式化小组成员文本
  formatGroupMembersText(group) {
    if (!group || !group.members || group.members.length === 0) {
      return '暂无成员'
    }

    const memberNames = group.members.map(member => member.nickname || member.name || '匿名')
    const totalCount = group.memberCount || group.members.length

    if (memberNames.length <= 2) {
      return `${memberNames.join('、')} (${totalCount}人小组)`
    } else {
      return `${memberNames.slice(0, 2).join('、')} 等${totalCount}人`
    }
  },

  // 格式化小组进度文本
  formatGroupProgressText(group) {
    if (!group) return ''

    const avgProgress = group.averageProgress || 0
    const myRank = group.myRank || 0
    const totalMembers = group.memberCount || 0

    if (totalMembers === 0) return ''

    // 获取排名徽章
    let rankBadge = ''
    let rankColor = '#8C8C8C'
    
    if (myRank === 1) {
      rankBadge = '🥇'
      rankColor = '#FFD700'
    } else if (myRank === 2) {
      rankBadge = '🥈'  
      rankColor = '#C0C0C0'
    } else if (myRank === 3) {
      rankBadge = '🥉'
      rankColor = '#CD7F32'
    }

    // 检查排名变化（这里可以根据历史数据计算，暂时使用模拟逻辑）
    const previousRank = group.previousRank || myRank
    let rankChangeIcon = ''
    let rankChangeColor = ''
    
    if (previousRank > myRank) {
      rankChangeIcon = '↗'
      rankChangeColor = '#52C41A'
    } else if (previousRank < myRank) {
      rankChangeIcon = '↘'
      rankChangeColor = '#FF4D4F'
    } else {
      rankChangeIcon = '→'
      rankChangeColor = '#1890FF'
    }

    return {
      text: `小组平均进度: ${avgProgress}% | 我的排名: ${rankBadge} ${myRank}/${totalMembers}`,
      rankBadge,
      rankColor,
      rankChangeIcon,
      rankChangeColor,
      myRank,
      totalMembers,
      avgProgress
    }
  },

  // 更新筛选计数
  updateFilterCounts() {
    const { exams, filterTabs, currentFilter } = this.data

    console.log('🏷️ 筛选计数 - 所有考试:', exams.map(e => ({ name: e.name, status: e.status })))

    const updatedTabs = filterTabs.map(tab => {
      let count = 0
      if (tab.value === 'all') {
        count = exams.length
      } else if (tab.value === 'preparing') {
        // 备考中包括preparing和upcoming状态，与顶部统计保持一致
        const matchingExams = exams.filter(exam => {
          const match = exam.status === 'preparing' || exam.status === 'upcoming'
          console.log(`筛选 - 考试 "${exam.name}" 状态: ${exam.status}, 匹配备考中: ${match}`)
          return match
        })
        count = matchingExams.length
        console.log('🏷️ 筛选计数 - 备考中考试:', matchingExams.map(e => ({ name: e.name, status: e.status })))
      } else {
        const matchingExams = exams.filter(exam => {
          const match = exam.status === tab.value
          console.log(`筛选 - 考试 "${exam.name}" 状态: ${exam.status}, 匹配${tab.label}: ${match}`)
          return match
        })
        count = matchingExams.length
        console.log(`🏷️ 筛选计数 - ${tab.label}考试:`, matchingExams.map(e => ({ name: e.name, status: e.status })))
      }
      return { ...tab, count }
    })

    // 更新当前选中的标签索引
    const activeTab = updatedTabs.findIndex(tab => tab.value === currentFilter)

    this.setData({
      filterTabs: updatedTabs,
      activeTab: activeTab >= 0 ? activeTab : 0
    })
  },

  // 筛选考试
  filterExams() {
    const { exams, currentFilter } = this.data
    console.log('开始筛选考试，原始数据:', exams)
    console.log('当前筛选条件:', currentFilter)

    let filteredExams = exams

    if (currentFilter !== 'all') {
      if (currentFilter === 'preparing') {
        // 备考中包括preparing和upcoming状态，与顶部统计保持一致
        filteredExams = exams.filter(exam => {
          const match = exam.status === 'preparing' || exam.status === 'upcoming'
          console.log(`考试 "${exam.name}" 状态: ${exam.status}, 匹配备考中: ${match}`)
          return match
        })
      } else {
        filteredExams = exams.filter(exam => {
          const match = exam.status === currentFilter
          console.log(`考试 "${exam.name}" 状态: ${exam.status}, 匹配${currentFilter}: ${match}`)
          return match
        })
      }
    }

    console.log('筛选后的考试:', filteredExams)

    // 智能排序：紧急考试置顶，然后按状态和日期排序
    filteredExams.sort((a, b) => {
      // 首先按紧急程度排序
      if (a.isUrgent !== b.isUrgent) {
        return b.isUrgent - a.isUrgent // 紧急的排在前面
      }

      const now = new Date()
      const aDate = new Date(a.date)
      const bDate = new Date(b.date)

      // 定义状态优先级：数字越小优先级越高
      const getStatusPriority = (status, date) => {
        switch (status) {
          case 'upcoming': return 1      // 即将到来 - 最高优先级
          case 'preparing': return 2     // 备考中
          case 'completed': return 3     // 已完成 - 排在下面
          case 'past': return 4          // 已过期 - 最低优先级
          default:
            // 如果没有明确状态，根据日期判断
            return date < now ? 4 : 2
        }
      }

      const aPriority = getStatusPriority(a.status, aDate)
      const bPriority = getStatusPriority(b.status, bDate)

      // 按状态优先级排序
      if (aPriority !== bPriority) {
        return aPriority - bPriority
      }

      // 同状态下按日期排序
      if (aPriority <= 2) {
        // 活跃状态（即将到来、备考中）：按日期升序（最近的在前）
        return aDate - bDate
      } else {
        // 非活跃状态（已完成、已过期）：按日期降序（最近的在前）
        return bDate - aDate
      }
    })

    this.setData({ filteredExams })
    console.log('考试智能排序完成，排序规则：即将到来 > 备考中 > 已完成 > 已过期')
    console.log('排序后的考试:', filteredExams.map((exam, index) => ({
      序号: index + 1,
      考试名称: exam.name,
      考试日期: exam.date,
      状态: exam.status,
      状态文本: exam.statusText,
      排序说明:
        exam.status === 'upcoming' ? '即将到来(优先级最高)' :
        exam.status === 'preparing' ? '备考中(优先级较高)' :
        exam.status === 'completed' ? '已完成(排在下面)' :
        exam.status === 'past' ? '已过期(优先级最低)' : '未知状态'
    })))
  },

  // 切换筛选标签
  onTabChange(e) {
    const activeTab = e.detail.index
    const filterTabs = this.data.filterTabs
    const currentFilter = filterTabs[activeTab].value

    this.setData({
      activeTab,
      currentFilter
    })
    this.filterExams()
  },

  // 切换筛选 (保留兼容)
  switchFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ currentFilter: filter })
    this.filterExams()
  },
  // 快速状态切换
  async quickToggleStatus(e) {
    e.stopPropagation() // 阻止事件冒泡

    const examId = e.currentTarget.dataset.id
    if (!examId) {
      console.error('考试ID不存在')
      return
    }

    const exam = this.data.exams.find(e => e.id === examId)
    if (!exam) {
      console.error('找不到对应的考试')
      return
    }

    // 只允许备考中和已完成状态之间切换
    if (exam.status !== 'preparing' && exam.status !== 'completed') {
      wx.showToast({
        title: '该状态不支持快速切换',
        icon: 'none'
      })
      return
    }

    const newStatus = exam.status === 'preparing' ? 'completed' : 'preparing'
    const newStatusText = newStatus === 'completed' ? '已完成' : '备考中'

    try {


      // 立即更新UI
      const updatedExams = this.data.exams.map(e => {
        if (e.id === examId) {
          return {
            ...e,
            status: newStatus,
            statusText: newStatusText,
            statusButtonText: newStatus === 'preparing' ? '✓完成' : '恢复'
          }
        }
        return e
      })

      this.setData({ exams: updatedExams })
      this.filterExams()
      this.updateFilterCounts()
      this.loadLocalExamStats() // 更新统计数据

      // 显示成功提示
      wx.showToast({
        title: `已标记为${newStatusText}`,
        icon: 'success'
      })

      // 同步到云端
      const result = await SmartApi.updateExam(examId, { status: newStatus })
      if (!result.success) {
        console.error('状态同步失败:', result.error)
        // 如果同步失败，可以选择回滚本地状态或显示错误提示
      }

    } catch (error) {
      console.error('状态切换失败:', error)
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    }
  },



  // 统计卡片点击事件
  onStatCardTap(e) {
    const type = e.currentTarget.dataset.type
    const cardElement = e.currentTarget

    // 添加点击效果
    this.addStatCardClickEffect(cardElement)



    switch (type) {
      case '进行中':
        // 筛选到备考中状态
        this.setData({
          currentFilter: 'preparing',
          activeTab: 1
        })
        this.filterExams()
        // 显示操作成功提示
        wx.showToast({
          title: '已筛选进行中',
          icon: 'success',
          duration: 1500
        })
        break
      case '平均进度':
        // 显示进度详情
        this.showProgressDetail()
        break
      case '最近考试':
        // 跳转到最近的考试
        const nearestExam = this.findNearestExam()
        if (nearestExam) {
          this.viewExamDetail({ currentTarget: { dataset: { id: nearestExam.id } } })
        } else {
          wx.showToast({
            title: '暂无即将到来的考试',
            icon: 'none'
          })
        }
        break
    }
  },

  // 添加统计卡片点击效果
  addStatCardClickEffect(cardElement) {
    // 这里在小程序中无法直接操作DOM，但可以通过数据绑定来实现
    // 暂时使用简单的反馈机制
    console.log('统计卡片点击效果触发')
  },

  // 显示进度详情
  showProgressDetail() {
    const { exams } = this.data
    if (exams.length === 0) {
      wx.showModal({
        title: '进度详情',
        content: '暂无考试数据',
        showCancel: false
      })
      return
    }

    // 计算详细进度信息
    const totalExams = exams.length
    const completedExams = exams.filter(exam => exam.status === 'completed').length
    const preparingExams = exams.filter(exam => exam.status === 'preparing').length
    const avgProgress = Math.round(
      exams.reduce((sum, exam) => sum + (exam.preparationProgress || 0), 0) / totalExams
    )

    const content = `总考试数: ${totalExams}个\n已完成: ${completedExams}个\n备考中: ${preparingExams}个\n平均进度: ${avgProgress}%\n\n继续加油！💪`

    wx.showModal({
      title: '📊 详细进度分析',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 查找最近的考试
  findNearestExam() {
    const { exams } = this.data
    const now = new Date()

    const futureExams = exams
      .filter(exam => new Date(exam.date) >= now)
      .sort((a, b) => new Date(a.date) - new Date(b.date))

    return futureExams.length > 0 ? futureExams[0] : null
  },

  // 查看小组详情
  viewStudyGroup(e) {
    // 安全地阻止事件冒泡
    if (e && e.stopPropagation && typeof e.stopPropagation === 'function') {
      e.stopPropagation()
    }
    
    const examId = e.currentTarget.dataset.id

    if (!examId) {
      wx.showToast({
        title: '考试ID不存在',
        icon: 'none'
      })
      return
    }

    // 从考试数据中获取小组ID
    const exam = this.data.exams.find(exam => exam.id === examId)
    const groupId = exam && exam.groupInfo && exam.groupInfo.id

    if (!groupId) {
      wx.showToast({
        title: '该考试暂无小组信息',
        icon: 'none'
      })
      return
    }

    console.log('跳转到小组详情页面，小组ID:', groupId)

    // 跳转到小组详情页面
    wx.navigateTo({
      url: `/pages/study-group-detail/index?groupId=${groupId}`,
      success: () => {
        console.log('成功跳转到小组详情页面')
      },
      fail: (error) => {
        console.error('跳转失败:', error)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  },

  // 阻止事件冒泡
  stopPropagation(e) {
    e.stopPropagation()
  },
  // 获取空状态标题
  getEmptyTitle() {
    const { currentFilter } = this.data
    const titles = {
      all: '🎓 开启你的备考之旅',
      upcoming: '📅 暂无即将到来的考试',
      preparing: '📖 暂无备考中的考试',
      completed: '🎉 暂无已完成的考试',
      past: '⏰ 暂无已过期的考试'
    }
    return titles[currentFilter] || '暂无数据'
  },

  // 获取空状态消息
  getEmptyMessage() {
    const { currentFilter } = this.data
    const messages = {
      all: '添加你的第一个考试，开始高效备考之旅！\n制定复习计划，与同学组成备考搭子小组，\n让学习更有动力 💪',
      upcoming: '所有考试都在准备中，继续努力！\n合理安排时间，稳步推进复习进度',
      preparing: '当前没有正在备考的考试\n添加新考试或查看其他状态的考试',
      completed: '还没有完成的考试\n继续努力，向目标前进！',
      past: '没有已过期的考试\n说明你的时间管理很棒！'
    }
    return messages[currentFilter] || '暂无相关数据'
  },

  // 获取空状态操作建议
  getEmptyActionText() {
    const { currentFilter } = this.data
    const actions = {
      all: '立即添加考试',
      upcoming: '查看全部考试',
      preparing: '添加新考试',
      completed: '查看备考中',
      past: '查看全部考试'
    }
    return actions[currentFilter] || '查看全部'
  },

  // 空状态操作处理
  onEmptyAction() {
    const { currentFilter } = this.data
    
    switch (currentFilter) {
      case 'all':
      case 'preparing':
        this.addExam()
        break
      case 'upcoming':
      case 'completed':
      case 'past':
        this.setData({
          currentFilter: 'all',
          activeTab: 0
        })
        this.filterExams()
        break
    }
  },



  // 显示考试操作菜单
  showExamActions(exam) {
    // 如果是事件对象，提取数据
    if (exam && exam.currentTarget) {
      exam = exam.currentTarget.dataset.exam
    }

    this.setData({
      selectedExam: exam,
      showActionSheet: true
    })
  },

  // 隐藏操作菜单
  hideActionSheet() {
    this.setData({
      showActionSheet: false,
      selectedExam: null
    })
  },

  // 切换科目详情显示
  toggleSubjects(e) {
    // 安全地阻止事件冒泡
    if (e && e.stopPropagation && typeof e.stopPropagation === 'function') {
      e.stopPropagation()
    }

    const examId = e.currentTarget.dataset.id
    if (!examId) return

    // 更新指定考试的showSubjects状态
    const updatedExams = this.data.filteredExams.map(exam => {
      if (exam.id === examId) {
        return { ...exam, showSubjects: !exam.showSubjects }
      }
      return exam
    })

    // 同时更新原始exams数据
    const updatedOriginExams = this.data.exams.map(exam => {
      if (exam.id === examId) {
        return { ...exam, showSubjects: !exam.showSubjects }
      }
      return exam
    })

    this.setData({
      filteredExams: updatedExams,
      exams: updatedOriginExams
    })
  },

  // 切换小组详情显示
  toggleGroupDetail(e) {
    // 安全地阻止事件冒泡
    if (e && e.stopPropagation && typeof e.stopPropagation === 'function') {
      e.stopPropagation()
    }

    const examId = e.currentTarget.dataset.id
    if (!examId) return

    // 更新指定考试的showGroupDetail状态
    const updatedExams = this.data.filteredExams.map(exam => {
      if (exam.id === examId) {
        return { ...exam, showGroupDetail: !exam.showGroupDetail }
      }
      return exam
    })

    // 同时更新原始exams数据
    const updatedOriginExams = this.data.exams.map(exam => {
      if (exam.id === examId) {
        return { ...exam, showGroupDetail: !exam.showGroupDetail }
      }
      return exam
    })

    this.setData({
      filteredExams: updatedExams,
      exams: updatedOriginExams
    })
  },

  // 邀请成员到小组
  inviteToGroup(e) {
    // 安全地阻止事件冒泡
    if (e && e.stopPropagation && typeof e.stopPropagation === 'function') {
      e.stopPropagation()
    }

    const examId = e.currentTarget.dataset.id
    const exam = this.data.exams.find(exam => exam.id === examId)
    
    if (!exam || !exam.groupInfo) {
      wx.showToast({
        title: '小组信息异常',
        icon: 'none'
      })
      return
    }

    // 显示邀请方式选择
    wx.showActionSheet({
      itemList: ['生成邀请码', '微信分享邀请', '复制邀请链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.generateInviteCode(exam.groupInfo.id)
            break
          case 1:
            this.shareGroupInvite(exam.groupInfo.id, exam.name)
            break
          case 2:
            this.copyInviteLink(exam.groupInfo.id)
            break
        }
      }
    })
  },

  // 生成邀请码
  generateInviteCode(groupId) {
    // 模拟生成邀请码
    const inviteCode = `KB${groupId.substr(-6).toUpperCase()}`
    
    wx.showModal({
      title: '邀请码生成成功',
      content: `邀请码：${inviteCode}\n\n分享给同学，让他们在「加入小组」页面输入邀请码即可加入。`,
      confirmText: '复制邀请码',
      cancelText: '知道了',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: inviteCode,
            success: () => {
              wx.showToast({
                title: '邀请码已复制',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  },

  // 微信分享邀请
  shareGroupInvite(groupId, examName) {
    wx.showToast({
      title: '微信分享功能开发中',
      icon: 'none'
    })
  },

  // 复制邀请链接
  copyInviteLink(groupId) {
    const inviteLink = `https://kaoba.cn/group/join?id=${groupId}`
    
    wx.setClipboardData({
      data: inviteLink,
      success: () => {
        wx.showToast({
          title: '邀请链接已复制',
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
  },

  // 创建备考搭子小组
  createStudyGroup(e) {
    // 安全地阻止事件冒泡
    if (e && e.stopPropagation && typeof e.stopPropagation === 'function') {
      e.stopPropagation()
    }

    const examId = e.currentTarget.dataset.id
    const examName = e.currentTarget.dataset.examName

    if (!examId) {
      wx.showToast({
        title: '考试信息异常',
        icon: 'none'
      })
      return
    }

    // 显示创建确认
    wx.showModal({
      title: '创建备考搭子小组',
      content: `为「${examName}」创建备考搭子小组，和同学一起高效备考？`,
      confirmText: '立即创建',
      cancelText: '暂不创建',
      success: async (res) => {
        if (res.confirm) {
          await this.doCreateStudyGroup(examId, examName)
        }
      }
    })
  },

  // 执行创建小组操作
  async doCreateStudyGroup(examId, examName) {
    wx.showLoading({
      title: '创建中...',
      mask: true
    })

    try {
      console.log('开始创建小组，考试ID:', examId, '考试名称:', examName)
      
      const result = await SmartApi.createStudyGroup(examId, examName)
      
      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: '小组创建成功！',
          icon: 'success',
          duration: 2000
        })

        // 重新加载小组信息
        await this.loadExamGroups()

        // 显示邀请提示
        setTimeout(() => {
          wx.showModal({
            title: '🎉 小组创建成功',
            content: '现在可以邀请同学加入小组，一起高效备考！',
            confirmText: '查看小组',
            cancelText: '稍后邀请',
            success: (res) => {
              if (res.confirm) {
                // 跳转到小组详情页面
                this.viewStudyGroup({ 
                  currentTarget: { 
                    dataset: { id: examId } 
                  } 
                })
              }
            }
          })
        }, 1000)

      } else {
        wx.showToast({
          title: result.error || '创建失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('创建小组失败:', error)
      wx.showToast({
        title: '创建失败，请重试',
        icon: 'none'
      })
    }
  },

  // 阻止事件冒泡
  stopPropagation(e) {
    // 安全地阻止事件冒泡到父级元素
    if (e && e.stopPropagation && typeof e.stopPropagation === 'function') {
      e.stopPropagation()
    }
  },

  // 操作菜单选择
  onActionSelect(e) {
    const action = e.detail.action
    const exam = this.data.selectedExam

    this.hideActionSheet()

    switch (action) {
      case 'startStudy':
        this.startStudyForExam({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'viewTasks':
        this.viewExamTasks({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'viewGroup':
        this.viewStudyGroup({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'viewDetail':
        this.viewExamDetail({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'edit':
        this.editExam({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'share':
        this.shareExam(exam)
        break
      case 'delete':
        this.deleteExam(exam)
        break
    }
  },

  // 执行考试操作 (保留兼容)
  executeExamAction(e) {
    const action = e.currentTarget.dataset.action
    const exam = this.data.selectedExam

    this.hideActionSheet()

    switch (action) {
      case 'startStudy':
        this.startStudyForExam({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'viewTasks':
        this.viewExamTasks({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'viewGroup':
        this.viewStudyGroup({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'viewDetail':
        this.viewExamDetail({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'edit':
        this.editExam({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'share':
        this.shareExam(exam)
        break
      case 'delete':
        this.deleteExam(exam)
        break
    }
  },

  // 页面跳转和操作方法

  viewExamDetail(e) {
    console.log('点击考试卡片:', e)
    const examId = e.currentTarget.dataset.id
    console.log('考试ID:', examId)

    if (!examId) {
      wx.showToast({
        title: '考试ID不存在',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: '/pages/exam-detail/index?id=' + examId
    })
  },

  addExam() {
    // 检查登录状态
    const app = getApp()
    const loginStatus = app.checkLoginStatus()

    if (!loginStatus.isLoggedIn) {
      this.showLoginPrompt('添加考试需要先登录')
      return
    }

    wx.navigateTo({
      url: '/pages/add-exam/index'
    })
  },

  // 显示登录提示
  showLoginPrompt(message = '该功能需要先登录') {
    wx.navigateTo({
      url: '/pages/login/index'
    })
  },

  editExam(e) {
    const examId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/edit-exam/index?id=' + examId
    })
  },

  startStudyForExam(e) {
    const examId = e.currentTarget.dataset.id
    
    // 检查是否为紧急考试，提供特殊反馈
    const exam = this.data.exams.find(exam => exam.id === examId)
    if (exam && exam.isUrgent) {
      // 紧急考试的强化反馈
      wx.vibrateLong() // 长震动
      
      // 显示紧急提示
      wx.showToast({
        title: '🔥 进入紧急复习模式',
        icon: 'none',
        duration: 2000
      })
      
      console.log('触发紧急考试复习模式')
    } else {
      // 普通震动
      wx.vibrateShort()
    }
    
    // 跳转到任务中心，筛选该考试的任务
    this.navigateToTaskCenterWithExam(examId)
  },

  // 跳转到任务中心并设置考试筛选
  navigateToTaskCenterWithExam(examId) {
    const exam = this.data.exams.find(e => e.id === examId)

    if (!exam) {
      console.warn('未找到指定的考试:', examId)
      wx.switchTab({
        url: '/pages/task-center/index'
      })
      return
    }

    console.log('跳转到任务中心，考试:', exam.name)

    // 构建URL参数
    const params = new URLSearchParams({
      examId: examId,
      examName: exam.name || exam.title,
      autoFilter: 'true'
    })

    // 由于switchTab不支持参数，我们需要使用其他方式
    // 先存储参数到全局状态
    const app = getApp()
    app.globalData = app.globalData || {}
    app.globalData.pendingExamFilter = {
      examId: examId,
      examName: exam.name || exam.title,
      autoFilter: true,
      fromPage: 'exam-center'
    }

    console.log('设置全局待处理考试筛选:', app.globalData.pendingExamFilter)

    // 跳转到任务中心
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  viewExamTasks(e) {
    const examId = e.currentTarget.dataset.id
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  shareExam(exam) {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })

    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    })
  },

  async deleteExam(exam) {
    const result = await wx.showModal({
      title: '确认删除',
      content: `确定要删除考试"${exam.name}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#FF4D4F'
    })

    if (!result.confirm) return

    wx.showLoading({
      title: '删除中...',
      mask: true
    })

    try {
      // 使用SmartApi删除考试
      const deleteResult = await SmartApi.deleteExam(exam.id)

      wx.hideLoading()

      if (deleteResult.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })

        // 重新加载数据
        this.loadData()
      } else {
        wx.showToast({
          title: deleteResult.error || '删除失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('删除考试失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  }
})
