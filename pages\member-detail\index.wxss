/* pages/member-detail/index.wxss */
.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 成员头部信息 */
.member-header {
  background: white;
  padding: 40rpx 32rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.member-avatar {
  position: relative;
  margin-right: 24rpx;
}

.avatar-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid #f0f0f0;
}

.online-status {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 12rpx;
  border: 4rpx solid white;
}

.online-status.online {
  background: #52c41a;
}

.online-status.offline {
  background: #bfbfbf;
}

.member-basic-info {
  flex: 1;
}

.member-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.online-text {
  font-size: 28rpx;
  color: #52c41a;
  display: block;
  margin-bottom: 8rpx;
}

.last-active {
  font-size: 24rpx;
  color: #999;
  display: block;
}

/* 复习统计区域 */
.stats-section {
  background: white;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
}

.stat-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 进度条样式 */
.progress-container {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  gap: 16rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.task-progress .progress-fill {
  background: #1890ff;
}

.weekly-progress .progress-fill {
  background: #52c41a;
}

.single-task-progress .progress-fill {
  background: #fa8c16;
}

.progress-percent {
  font-size: 24rpx;
  color: #666;
  min-width: 60rpx;
  text-align: right;
}

/* 任务列表区域 */
.tasks-section {
  background: white;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.task-list {
  margin-top: 16rpx;
}

.task-item {
  display: flex;
  align-items: flex-start;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.task-item:last-child {
  border-bottom: none;
}

.task-status {
  margin-right: 16rpx;
  padding-top: 4rpx;
}

.task-icon {
  font-size: 32rpx;
}

.task-content {
  flex: 1;
}

.task-name {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.task-status-text {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 8rpx;
}

.task-progress-container {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.task-progress-container .progress-bar {
  height: 8rpx;
}

.task-progress-container .progress-percent {
  font-size: 20rpx;
  min-width: 50rpx;
}

.empty-tasks {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 点赞区域 */
.like-section {
  background: white;
  padding: 32rpx;
  text-align: center;
}

.like-btn {
  width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  margin: 0 auto 16rpx;
  transition: all 0.3s ease;
}

.like-btn:not(.liked) {
  background: #1890ff;
  color: white;
}

.like-btn.liked {
  background: #f0f0f0;
  color: #999;
}

.like-btn:active:not(.liked) {
  background: #096dd9;
  transform: scale(0.98);
}

.like-icon {
  font-size: 24rpx;
}

.like-text {
  font-size: 28rpx;
}

.like-count {
  font-size: 24rpx;
  color: #666;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  gap: 24rpx;
}

.error-text {
  font-size: 28rpx;
  color: #999;
}

.retry-btn {
  width: 200rpx;
  height: 80rpx;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
}
