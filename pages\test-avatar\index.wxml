<!--pages/test-avatar/index.wxml-->
<view class="test-container">
  <view class="test-header">
    <text class="test-title">头像上传测试</text>
  </view>

  <!-- 当前头像显示 -->
  <view class="avatar-section">
    <text class="section-title">当前头像</text>
    <image 
      class="test-avatar" 
      src="{{avatarUrl || '/images/default-avatar.png'}}" 
      mode="aspectFill"
    ></image>
    <text class="avatar-url">{{avatarUrl || '暂无头像'}}</text>
  </view>

  <!-- 测试按钮 -->
  <view class="test-buttons">
    <button 
      class="test-btn primary" 
      bindtap="testChooseImage"
      disabled="{{uploading}}"
    >
      {{uploading ? '上传中...' : '选择并上传图片'}}
    </button>
    
    <button 
      class="test-btn secondary" 
      bindtap="testCloudFunction"
    >
      测试云函数调用
    </button>
    
    <button
      class="test-btn secondary"
      bindtap="testUserInfo"
    >
      查看用户信息
    </button>

    <button
      class="test-btn secondary"
      bindtap="testSimpleCloudFunction"
    >
      简化云函数测试
    </button>
  </view>

  <!-- 使用头像上传组件 -->
  <view class="component-section">
    <text class="section-title">头像上传组件测试</text>
    <avatar-upload
      avatarUrl="{{avatarUrl}}"
      size="{{120}}"
      editable="{{true}}"
      showEditButton="{{true}}"
      bind:avatarChange="onAvatarChange"
    ></avatar-upload>
  </view>

  <!-- 错误日志 -->
  <view class="error-log-section" wx:if="{{errorLog.length > 0}}">
    <text class="section-title">错误日志</text>
    <view class="error-item" wx:for="{{errorLog}}" wx:key="time">
      <text class="error-time">{{item.time}}</text>
      <text class="error-message">{{item.error}}</text>
    </view>
  </view>
</view>
