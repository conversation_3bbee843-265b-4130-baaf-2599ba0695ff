/* pages/test-avatar/index.wxss */
.test-container {
  padding: 32rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.test-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.avatar-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.test-avatar {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  border: 4rpx solid #f0f0f0;
  margin-bottom: 16rpx;
}

.avatar-url {
  display: block;
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.test-buttons {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.test-btn {
  width: 100%;
  margin-bottom: 24rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.test-btn:last-child {
  margin-bottom: 0;
}

.primary {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
}

.secondary {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: white;
}

.test-btn[disabled] {
  background: #d9d9d9 !important;
  color: #999 !important;
}

.component-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  text-align: center;
}

.error-log-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-top: 32rpx;
}

.error-item {
  padding: 16rpx;
  margin-bottom: 16rpx;
  background: #fff2f0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff4d4f;
}

.error-time {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.error-message {
  display: block;
  font-size: 28rpx;
  color: #ff4d4f;
  word-break: break-all;
}
