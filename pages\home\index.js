// pages/home/<USER>
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    // 考试板块数据
    examList: [],
    currentExamIndex: 0,

    // 今日任务聚合
    todayTasks: [],
    completedCount: 0,
    totalCount: 0,

    // 用户信息
    userInfo: null,

    // 登录状态
    isLoggedIn: false,

    // 加载状态
    loading: true,



    // 悬浮按钮 - 已迁移到原生实现，不再需要
    /*
    fabButtons: [
      {
        label: '添加任务',
        icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAQAAAAAYLlVAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QAAKqNIzIAAAAHdElNRQfhBAQLCR5MtjrbAAAAjUlEQVRo3u3ZMRKAIAxEUbDirp4nXnctFFDHBtDQ/O1Nnk6aHUMgZCBKMkmmNAtgOmL9M+IQQGVM95zljy8DAAAAAAAAAAAAAACALsDZcppSx7Q+WdtUvA5xffUtrjeA8/qQ21S9gc15/3Nfzw0M5O0G2kM5BQAAAAAAAAAAAAAAQGk33q0qZ/p/Q/JFdmei9usomnwIAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE3LTA0LTA0VDExOjA5OjMwKzA4OjAw1U4c3wAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNy0wNC0wNFQxMTowOTozMCswOjAwpBOkYwAAAABJRU5ErkJggg=='
      },
      {
        label: '添加考试',
        icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAQAAAAAYLlVAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QAAKqNIzIAAAAHdElNRQfhBAQLCR5MtjrbAAAAjUlEQVRo3u3ZMRKAIAxEUbDirp4nXnctFFDHBtDQ/O1Nnk6aHUMgZCBKMkmmNAtgOmL9M+IQQGVM95zljy8DAAAAAAAAAAAAAACALsDZcppSx7Q+WdtUvA5xffUtrjeA8/qQ21S9gc15/3Nfzw0M5O0G2kM5BQAAAAAAAAAAAAAAQGk33q0qZ/p/Q/JFdmei9usomnwIAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE3LTA0LTA0VDExOjA5OjMwKzA4OjAw1U4c3wAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNy0wNC0wNFQxMTowOTozMCswOjAwpBOkYwAAAABJRU5ErkJggg=='
      },
      {
        label: '开始专注',
        icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAQAAAAAYLlVAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QAAKqNIzIAAAAHdElNRQfhBAQLCR5MtjrbAAAAjUlEQVRo3u3ZMRKAIAxEUbDirp4nXnctFFDHBtDQ/O1Nnk6aHUMgZCBKMkmmNAtgOmL9M+IQQGVM95zljy8DAAAAAAAAAAAAAACALsDZcppSx7Q+WdtUvA5xffUtrjeA8/qQ21S9gc15/3Nfzw0M5O0G2kM5BQAAAAAAAAAAAAAAQGk33q0qZ/p/Q/JFdmei9usomnwIAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE3LTA0LTA0VDExOjA5OjMwKzA4OjAw1U4c3wAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNy0wNC0wNFQxMTowOTozMCswODowMKQTpGMAAAAASUVORK5CYII='
      }
    ],
    */

    // 加载状态
    loading: false,

    // 检查点弹窗相关
    showCheckpointModal: false,
    checkpointModalData: {
      taskId: '',
      taskTitle: '',
      subtasks: [],
      completedCount: 0
    },
    newCheckpointTitle: '',
    showAddCheckpointInput: false,

    // 性能优化相关
    checkpointCache: {}, // 检查点数据缓存
    loadedCheckpoints: [] // 已加载的检查点任务ID数组
  },

  onLoad() {
    // 初始化检查点相关数据
    this.initCheckpointData()

    // 检查登录状态（不强制跳转）
    this.checkLoginStatus()

    // 初始化页面数据
    this.initPage()

    // 加载数据
    this.loadData()
  },

  // 初始化检查点相关数据
  initCheckpointData() {
    // 初始化检查点缓存
    this.setData({
      checkpointCache: {},
      loadedCheckpoints: [],
      showCheckpointModal: false,
      checkpointModalData: {
        taskId: '',
        taskTitle: '',
        subtasks: [],
        completedCount: 0
      },
      newCheckpointTitle: '',
      showAddCheckpointInput: false
    })

    console.log('检查点数据初始化完成')
  },

  // 验证和修复任务数据的检查点字段
  validateTaskCheckpointData(tasks) {
    return tasks.map(task => {
      // 确保任务包含检查点相关字段
      if (!task.subtasks) {
        task.subtasks = []
      }

      if (typeof task.completedSubtasks !== 'number') {
        task.completedSubtasks = task.subtasks ? task.subtasks.filter(subtask => subtask.completed).length : 0
      }

      // 验证subtasks数组的完整性
      if (Array.isArray(task.subtasks)) {
        task.subtasks = task.subtasks.map((subtask, index) => ({
          id: subtask.id || `subtask_${index}`,
          title: subtask.title || '',
          completed: Boolean(subtask.completed),
          createdAt: subtask.createdAt || new Date().toISOString(),
          updatedAt: subtask.updatedAt || new Date().toISOString()
        }))
      }

      return task
    })
  },

  // 检查登录状态（不强制跳转）
  checkLoginStatus() {
    const app = getApp()
    const loginStatus = app.checkLoginStatus()

    if (loginStatus.isLoggedIn) {
      // 用户已登录，更新用户信息
      this.setData({
        isLoggedIn: true,
        userInfo: loginStatus.userInfo || {}
      })
    } else {
      // 用户未登录，清空所有数据
      console.log('检测到用户未登录，清空所有页面数据')
      this.setData({
        isLoggedIn: false,
        userInfo: {
          nickName: '考试达人',
          avatarUrl: ''
        },
        examList: [],
        currentExamIndex: 0,
        todayTasks: [],
        completedCount: 0,
        totalCount: 0
      })
    }

    return loginStatus.isLoggedIn
  },

  async onShow() {
    // 更新TabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      })
    }

    // 检查登录状态（会自动清空数据如果未登录）
    const isLoggedIn = this.checkLoginStatus()

    // 只有登录用户才加载数据
    if (isLoggedIn) {
      // 检查数据是否已初始化
      const app = getApp()
      if (app.globalData.dbInitialized) {
        this.refreshData()
      } else {
        // 如果数据未初始化，延迟加载数据
        setTimeout(() => {
          this.refreshData()
        }, 2000)
      }
    }
  },

  async onPullDownRefresh() {
    console.log('🔄 用户触发下拉刷新')

    try {
      // 移除触觉反馈
      // wx.vibrateShort({
      //   type: 'light'
      // })

      await this.refreshData()

      // 刷新成功提示
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1000
      })

      console.log('✅ 下拉刷新完成')
    } catch (error) {
      console.error('❌ 下拉刷新失败:', error)
      wx.showToast({
        title: '刷新失败，请重试',
        icon: 'none',
        duration: 2000
      })
    } finally {
      // 停止下拉刷新动画
      wx.stopPullDownRefresh()
    }
  },

  // 初始化页面
  initPage() {
    this.loadUserInfo()
    this.initFabMenu()
  },

  // 加载用户信息
  loadUserInfo() {
    // 先检查登录状态
    const app = getApp()
    const loginStatus = app.checkLoginStatus()
    
    if (loginStatus.isLoggedIn && loginStatus.userInfo) {
      // 用户已登录，使用真实用户信息
      this.setData({ 
        userInfo: loginStatus.userInfo,
        isLoggedIn: true
      })
    } else {
      // 用户未登录，使用默认信息
      this.setData({ 
        userInfo: {
          nickName: '考试达人',
          avatarUrl: ''
        },
        isLoggedIn: false
      })
    }
  },

  // 加载今日数据
  async loadData() {
    this.setData({ loading: true })

    try {
      // 显示加载提示
      wx.showLoading({
        title: '加载中...',
        mask: true
      })

      // 加载今日数据
      await this.loadTodayData()

      wx.hideLoading()
    } catch (error) {
      console.error('加载数据失败:', error)
      wx.hideLoading()

      // 显示错误提示
      wx.showModal({
        title: '加载失败',
        content: '数据加载失败，请检查网络连接后重试',
        showCancel: true,
        cancelText: '稍后再试',
        confirmText: '重新加载',
        success: (res) => {
          if (res.confirm) {
            // 用户选择重新加载
            this.loadData()
          }
        }
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载考试数据
  async loadExams() {
    try {
      // 使用真实API获取考试数据
      const result = await SmartApi.getExams()

      if (result.success && result.data && result.data.length > 0) {
        // 在前端过滤掉当天之前的考试
        const now = new Date()
        now.setHours(0, 0, 0, 0) // 设置为当天0点，这样当天的考试也会显示
        const filteredExams = result.data.filter(exam => {
          const examDate = new Date(exam.examDate)
          return examDate >= now
        })

        if (filteredExams.length === 0) {
          console.log('没有当天及以后的考试数据，使用测试数据')
          // 跳转到使用测试数据的逻辑
          const daysInfo = this.calculateDaysLeft('2025-12-23')
          const testExams = [{
            id: 'test_exam_1',
            name: '2025年考研',
            date: '2025-12-23',
            daysLeft: daysInfo.daysLeft,
            isToday: daysInfo.isToday,
            preparationProgress: 65,
            weeklyStudyTime: '12h',
            completedTasks: 15,
            totalTasks: 20,
            studyStreak: 7,
            studyGroup: null
          }]

          this.setData({
            exams: testExams,
            currentExam: testExams[0],
            currentExamIndex: 0
          })
          return
        }

        // 处理考试数据，添加UI需要的字段
        const exams = await Promise.all(filteredExams.map(async exam => {
          // 获取该考试的搭子信息
          const studyGroup = await this.loadStudyGroupForExam(exam._id)

          // 获取该考试的科目统计信息
          const subjectStatsResult = await SmartApi.getExamSubjectStats(exam._id)
          const subjectStats = subjectStatsResult.success ? subjectStatsResult.data : []

          const daysInfo = this.calculateDaysLeft(exam.examDate)
          return {
            id: exam._id,
            name: exam.title, // 使用标准字段名
            date: exam.examDate, // 使用标准字段名
            daysLeft: daysInfo.daysLeft,
            isToday: daysInfo.isToday,
            preparationProgress: exam.preparationProgress || 0,
            weeklyStudyTime: this.formatStudyTime(exam.weeklyStudyTime || 0),
            completedTasks: 0, // 初始化为0，后续动态计算
            totalTasks: 0, // 初始化为0，后续动态计算
            studyStreak: exam.studyStreak || 0,
            studyGroup: studyGroup,
            tasks: [], // 初始化为空数组，后续动态加载
            subjectStats: subjectStats, // 科目统计信息
            hasSubjects: subjectStats.length > 0 // 是否有科目信息
          }
        }))

        // 按日期排序
        exams.sort((a, b) => new Date(a.date) - new Date(b.date))

        this.setData({
          exams,
          currentExam: exams[0] || {},
          currentExamIndex: 0
        })

        // 加载第一个考试的任务
        if (exams.length > 0) {
          console.log('初始加载第一个考试的任务，examId:', exams[0].id)
          const firstExamTasks = await this.loadTasksForExam(exams[0].id)

          // 获取科目选项
          const subjectOptions = [...new Set(firstExamTasks.map(task => task.subject).filter(Boolean))]

          // 更新第一个考试的任务数据
          const updatedExams = exams.map((exam, index) => {
            if (index === 0) {
              return {
                ...exam,
                tasks: firstExamTasks,
                subjectOptions: subjectOptions,
                filteredTasks: firstExamTasks, // 初始时显示所有任务
                completedTasks: firstExamTasks.filter(task => task.completed).length,
                totalTasks: firstExamTasks.length
              }
            }
            return exam
          })

          this.setData({
            exams: updatedExams,
            currentExam: updatedExams[0]
          })

          console.log('初始加载完成，第一个考试的任务:', firstExamTasks)
          console.log('初始加载完成，过滤后的任务:', firstExamTasks)
        }
      } else {
        // 没有考试数据，使用测试数据
        console.log('没有考试数据，使用测试数据')
        const daysInfo = this.calculateDaysLeft('2025-12-23')
        const testExams = [{
          id: 'test_exam_1',
          name: '2025年考研',
          date: '2025-12-23',
          daysLeft: daysInfo.daysLeft,
          isToday: daysInfo.isToday,
          preparationProgress: 65,
          weeklyStudyTime: '12h',
          completedTasks: 15,
          totalTasks: 20,
          studyStreak: 7,
          studyGroup: null
        }]

        this.setData({
          exams: testExams,
          currentExam: testExams[0],
          currentExamIndex: 0
        })
      }
    } catch (error) {
      console.error('加载考试数据失败:', error)

      // 加载失败时使用测试数据
      const daysInfo = this.calculateDaysLeft('2025-12-23')
      const testExams = [{
        id: 'test_exam_1',
        name: '2025年考研',
        date: '2025-12-23',
        daysLeft: daysInfo.daysLeft,
        isToday: daysInfo.isToday,
        preparationProgress: 65,
        weeklyStudyTime: '12h',
        completedTasks: 15,
        totalTasks: 20,
        studyStreak: 7,
        studyGroup: null
      }]

      this.setData({
        exams: testExams,
        currentExam: testExams[0],
        currentExamIndex: 0
      })
    }
  },

  // 加载今日数据（新首页专用）
  async loadTodayData() {
    try {
      const startTime = Date.now()
      console.log('🚀 开始加载今日数据')

      // 检查用户登录状态
      if (!this.data.isLoggedIn) {
        console.log('用户未登录，跳过数据加载')
        this.setData({
          examList: [],
          currentExamIndex: 0,
          todayTasks: [],
          completedCount: 0,
          totalCount: 0
        })
        return
      }

      // 1. 加载所有考试数据
      const examResult = await SmartApi.getExams()

      if (!examResult.success || !examResult.data || examResult.data.length === 0) {
        console.log('没有考试数据')
        this.setData({
          examList: [],
          currentExamIndex: 0,
          todayTasks: [],
          completedCount: 0,
          totalCount: 0
        })
        return
      }

      // 在前端过滤掉当天之前的考试
      const now = new Date()
      now.setHours(0, 0, 0, 0) // 设置为当天0点，这样当天的考试也会显示
      const filteredExams = examResult.data.filter(exam => {
        const examDate = new Date(exam.examDate)
        return examDate >= now
      })

      if (filteredExams.length === 0) {
        console.log('没有当天及以后的考试数据')
        this.setData({
          examList: [],
          currentExamIndex: 0,
          todayTasks: [],
          completedCount: 0,
          totalCount: 0
        })
        return
      }

      const exams = filteredExams
      console.log('加载到考试数据:', exams.length, '个')

      // 2. 并行加载今日任务和搭子信息
      console.log('🚀 开始加载今日任务和搭子信息')

      const examIds = exams.map(exam => exam._id)

      // 获取当前用户信息用于调试
      const app = getApp()
      const userInfo = app.globalData.userInfo || {}
      const userId = userInfo._id || ''
      const openid = app.globalData.openid || ''

      console.log('🔍 用户身份信息详情:', {
        hasUserInfo: !!userInfo,
        userId: userId,
        openid: openid,
        userInfoKeys: Object.keys(userInfo),
        isLoggedIn: this.data.isLoggedIn
      })

      // 云函数会自动验证用户身份，这里只做基本的登录状态检查
      if (!this.data.isLoggedIn) {
        console.log('⚠️ 用户未登录，无法加载任务')
        this.setData({
          todayTasks: [],
          completedCount: 0,
          totalCount: 0
        })
        return
      }

      console.log('👤 当前用户ID:', userId)
      console.log('🔑 当前OpenID:', openid)

      // 并行获取今日所有任务和搭子信息
      const [todayTasksResult, batchGroupsResult] = await Promise.all([
        SmartApi.getTasks({
          todayOnly: true
          // 注意：不需要传递userId，云函数会自动使用当前登录用户的ID
        }, 100), // 直接获取今日所有任务，不限制考试
        SmartApi.getBatchStudyGroupsByExam(examIds)
      ])

      console.log('📝 今日任务结果:', todayTasksResult.success ? '成功' : '失败')
      console.log('👥 批量搭子结果:', batchGroupsResult.success ? '成功' : '失败')

      // 详细的任务查询结果日志
      console.log('🔍 任务查询详细结果:', {
        success: todayTasksResult.success,
        error: todayTasksResult.error,
        dataLength: todayTasksResult.data ? todayTasksResult.data.length : 0,
        data: todayTasksResult.data,
        requestUserId: userId,
        requestFilter: { todayOnly: true, userId: userId }
      })

      let allTodayTasks = todayTasksResult.success ? todayTasksResult.data || [] : []
      const groupsByExam = batchGroupsResult.success ? batchGroupsResult.data : {}
      
      // 检查任务数据完整性
      console.log('📝 任务数据完整性检查:', {
        taskCount: allTodayTasks.length,
        taskSample: allTodayTasks.slice(0, 2).map(task => ({
          id: task._id,
          title: task.title,
          userId: task.userId,
          hasUserId: !!task.userId
        }))
      })
      
      // 如果没有任务，尝试修复用户ID问题
      if (allTodayTasks.length === 0) {
        console.log('⚠️ 没有加载到任务，尝试修复用户ID问题')
        try {
          const fixResult = await SmartApi.fixTaskUserIds()
          console.log('修复结果:', fixResult)
          
          if (fixResult.success && fixResult.fixedCount > 0) {
            console.log('✅ 修复了任务用户ID，重新获取任务')
            // 重新获取任务
            const retryResult = await SmartApi.getTasks({
              todayOnly: true
              // 注意：不需要传递userId，云函数会自动使用当前登录用户的ID
            }, 100)
            if (retryResult.success && retryResult.data) {
              allTodayTasks = retryResult.data
            }
          }
        } catch (error) {
          console.error('修复任务用户ID失败:', error)
        }
      }
      
      // 添加调试信息
      console.log('👥 搭子小组数据详情:', JSON.stringify(groupsByExam, null, 2))
      console.log('👥 搭子小组加载状态:', {
        success: batchGroupsResult.success,
        hasData: Object.keys(groupsByExam).length > 0,
        examIds: examIds
      })

      // 3. 处理每个考试的数据
      const examList = []

      for (const exam of exams) {
        try {
          // 获取该考试的搭子信息
          const studyGroup = groupsByExam[exam._id] || null

          // 搭子小组调试信息
          console.log(`👥 考试 "${exam.title}" 的搭子信息:`, {
            examId: exam._id,
            hasGroup: !!studyGroup,
            groupData: studyGroup
          })

          // 计算考试基本信息
          const daysInfo = this.calculateDaysLeft(exam.examDate)
          const dateText = this.formatExamDate(exam.examDate)

          // 计算该考试相关的任务数量（从今日任务中筛选）
          const examRelatedTasks = allTodayTasks.filter(task => task.examId === exam._id)
          const completedTasks = examRelatedTasks.filter(task => task.completed).length
          const totalTasks = examRelatedTasks.length
          const progressPercent = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0

          // 获取该考试的科目统计信息
          const subjectStatsResult = await SmartApi.getExamSubjectStats(exam._id)
          const subjectStats = subjectStatsResult.success ? subjectStatsResult.data : []

          // 添加到考试列表
          const examItem = {
            id: exam._id,
            name: exam.title,
            date: exam.examDate,
            daysLeft: daysInfo.daysLeft,
            isToday: daysInfo.isToday,
            dateText: dateText,
            completedTasks: completedTasks,
            totalTasks: totalTasks,
            progressPercent: progressPercent,
            hasPartner: !!studyGroup,
            bestPartnerProgress: studyGroup ? (studyGroup.bestProgress || 0) : 0,
            studyGroup: studyGroup,
            topMembers: studyGroup ? this.getTopMembers(studyGroup.members) : [],
            subjectStats: subjectStats, // 科目统计信息
            hasSubjects: subjectStats.length > 0 // 是否有科目信息
          }
          
          console.log(`👥 考试 "${exam.title}" 最终数据:`, {
            hasPartner: examItem.hasPartner,
            bestPartnerProgress: examItem.bestPartnerProgress,
            studyGroupId: studyGroup ? studyGroup.id : null,
            relatedTasks: totalTasks
          })
          
          examList.push(examItem)

        } catch (error) {
          console.error(`处理考试 "${exam.title}" 数据失败:`, error)

          // 添加基本的考试数据
          const daysInfo = this.calculateDaysLeft(exam.examDate)
          examList.push({
            id: exam._id,
            name: exam.title,
            date: exam.examDate,
            daysLeft: daysInfo.daysLeft,
            isToday: daysInfo.isToday,
            dateText: this.formatExamDate(exam.examDate),
            completedTasks: 0,
            totalTasks: 0,
            progressPercent: 0,
            hasPartner: false,
            bestPartnerProgress: 0,
            studyGroup: null,
            subjectStats: [], // 空的科目统计
            hasSubjects: false // 没有科目信息
          })
        }
      }

      // 4. 格式化今日任务数据
      const formattedTodayTasks = allTodayTasks.map(task => {
        // 如果任务有关联考试，获取考试信息
        const relatedExam = exams.find(exam => exam._id === task.examId)
        const examSubject = relatedExam ? this.getExamSubjectName(relatedExam) : null
        const finalSubject = examSubject || task.subject || '通用'
        
        // 处理检查点数据
        const subtasks = task.subtasks || []
        const completedSubtasks = subtasks.filter(subtask => subtask.completed).length

        return {
          id: task._id,
          title: task.title,
          subject: task.subject || '',
          estimatedTime: task.estimatedTime ? `${task.estimatedTime}分钟` : '',
          priority: task.priority || 'medium',
          priorityText: this.getPriorityText(task.priority),
          dueTime: task.dueTime || '',
          completed: task.completed || false,
          examId: task.examId || null,
          examName: relatedExam ? relatedExam.title : '独立任务',
          examSubject: finalSubject,
          subjectColor: this.getSubjectColor(examSubject || task.subject),
          urgencyLevel: this.getUrgencyLevel(task.dueTime),
          dueTimeText: this.formatDueTime(task.dueTime),
          completedTime: task.completedTime ? this.formatTime(task.completedTime) : '',
          // 检查点相关字段
          subtasks: subtasks,
          completedSubtasks: completedSubtasks
        }
      })

      const loadTime = Date.now() - startTime
      console.log(`🎉 数据加载完成! 用时: ${loadTime}ms`)
      console.log(`📚 加载到 ${examList.length} 个考试`)
      console.log(`📝 加载到 ${formattedTodayTasks.length} 个今日任务`)
      console.log(`⚡ 云函数调用次数: 2次 (examManager + taskManager)`)

      // 5. 验证和修复任务数据的检查点字段
      const validatedTasks = this.validateTaskCheckpointData(formattedTodayTasks)

      // 6. 按照设计文档的规则排序任务
      const sortedTasks = this.sortTodayTasks(validatedTasks)

      // 6. 按日期排序考试列表
      examList.sort((a, b) => new Date(a.date) - new Date(b.date))

      // 7. 计算统计信息
      const completedCount = sortedTasks.filter(task => task.completed).length
      const totalCount = sortedTasks.length

      // 7. 计算动态高度
      const swiperHeight = this.calculateSwiperHeight(examList)

      // 8. 更新页面数据
      this.setData({
        examList: examList,
        currentExamIndex: 0,
        todayTasks: sortedTasks,
        completedCount: completedCount,
        totalCount: totalCount,
        swiperHeight: swiperHeight
      })

      console.log('今日数据加载完成')
      console.log('考试板块:', examList.length, '个考试')
      console.log('今日任务:', totalCount, '个，已完成', completedCount, '个')

    } catch (error) {
      console.error('加载今日数据失败:', error)

      // 设置空状态
      this.setData({
        examList: [],
        currentExamIndex: 0,
        todayTasks: [],
        completedCount: 0,
        totalCount: 0
      })
    }
  },

  // 添加测试数据
  async addTestData() {
    try {
      wx.showLoading({
        title: '添加测试数据...',
        mask: true
      })

      console.log('🔄 开始添加测试数据')

      // 1. 添加测试考试数据
      const testExam = {
        title: '2024年考研数学',
        subject: '数学',
        examDate: '2024-12-23',
        examTime: '08:30',
        location: '考试中心',
        description: '2024年全国硕士研究生招生考试数学科目',
        type: 'graduate',
        status: 'upcoming',
        priority: 'high',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      console.log('📚 正在添加考试数据...')
      const examResult = await SmartApi.addExam(testExam)
      console.log('📚 考试添加结果:', examResult)

      if (examResult.success && examResult.data) {
        const examId = examResult.data._id || examResult.data.id
        console.log('📚 获取到考试ID:', examId)

        // 2. 添加测试任务数据
        const testTasks = [
          {
            title: '高等数学-极限专题复习',
            description: '复习函数极限的定义、性质和计算方法',
            subject: '高等数学',
            examId: examId,
            priority: 'high',
            estimatedTime: 120,
            dueDate: new Date().toISOString().split('T')[0],
            dueTime: '20:00',
            completed: false,
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            title: '线性代数-矩阵运算练习',
            description: '练习矩阵的基本运算和性质',
            subject: '线性代数',
            examId: examId,
            priority: 'medium',
            estimatedTime: 90,
            dueDate: new Date().toISOString().split('T')[0],
            dueTime: '21:30',
            completed: false,
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            title: '概率论-随机变量复习',
            description: '复习离散型和连续型随机变量',
            subject: '概率论',
            examId: examId,
            priority: 'medium',
            estimatedTime: 60,
            dueDate: new Date().toISOString().split('T')[0],
            dueTime: '19:00',
            completed: true,
            completedTime: new Date().toISOString(),
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ]

        console.log('📝 正在添加任务数据...')
        for (const task of testTasks) {
          const taskResult = await SmartApi.addTask(task)
          console.log('📝 任务添加结果:', taskResult)
        }

        wx.hideLoading()
        wx.showToast({
          title: '测试数据添加成功',
          icon: 'success',
          duration: 2000
        })

        // 重新加载数据
        setTimeout(() => {
          this.loadTodayData()
        }, 2000)

      } else {
        throw new Error(examResult.error || '添加考试失败')
      }

    } catch (error) {
      console.error('❌ 添加测试数据失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '添加测试数据失败',
        icon: 'error',
        duration: 2000
      })
    }
  },



  // 修复考试用户关联
  async fixExamUserAssociation() {
    try {
      wx.showLoading({
        title: '修复数据关联...',
        mask: true
      })

      console.log('🔧 开始修复考试用户关联')

      // 1. 获取当前用户ID
      console.log('👤 获取当前用户信息...')
      const userResult = await wx.cloud.callFunction({
        name: 'login',
        data: {}
      })

      if (!userResult.result.success) {
        throw new Error('获取用户信息失败')
      }

      const currentUserId = userResult.result.data.userId
      console.log('👤 当前用户ID:', currentUserId)

      // 2. 获取所有考试数据
      console.log('📚 获取所有考试数据...')
      const examsResult = await wx.cloud.database().collection('exams').get()
      console.log('📚 找到考试数量:', examsResult.data.length)

      // 3. 更新考试数据的userId
      let updateCount = 0
      for (const exam of examsResult.data) {
        if (exam.userId !== currentUserId) {
          console.log(`🔧 更新考试 "${exam.title}" 的用户关联...`)
          await wx.cloud.database().collection('exams').doc(exam._id).update({
            data: {
              userId: currentUserId,
              updateTime: new Date()
            }
          })
          updateCount++
        }
      }

      console.log(`✅ 成功更新 ${updateCount} 个考试的用户关联`)

      // 4. 同样处理任务数据
      console.log('📝 获取所有任务数据...')
      const tasksResult = await wx.cloud.database().collection('tasks').get()
      console.log('📝 找到任务数量:', tasksResult.data.length)

      let taskUpdateCount = 0
      for (const task of tasksResult.data) {
        if (task.userId !== currentUserId) {
          console.log(`🔧 更新任务 "${task.title}" 的用户关联...`)
          await wx.cloud.database().collection('tasks').doc(task._id).update({
            data: {
              userId: currentUserId,
              updateTime: new Date()
            }
          })
          taskUpdateCount++
        }
      }

      console.log(`✅ 成功更新 ${taskUpdateCount} 个任务的用户关联`)

      wx.hideLoading()
      wx.showToast({
        title: `关联修复成功！更新了${updateCount}个考试和${taskUpdateCount}个任务`,
        icon: 'success',
        duration: 3000
      })

      // 重新加载数据
      setTimeout(() => {
        this.loadTodayData()
      }, 3000)

    } catch (error) {
      console.error('❌ 修复用户关联失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '修复失败: ' + error.message,
        icon: 'error',
        duration: 3000
      })
    }
  },

  // 处理考试板块数据
  async processExamListData(exams) {
    const examList = []

    for (let i = 0; i < exams.length; i++) {
      const exam = exams[i]
      try {
        // 计算考试基本信息
        const daysInfo = this.calculateDaysLeft(exam.examDate)
        const dateText = this.formatExamDate(exam.examDate)

        // 加载该考试的任务数据
        const tasks = await this.loadTasksForExam(exam._id)
        const completedTasks = tasks.filter(task => task.completed).length
        const totalTasks = tasks.length
        const progressPercent = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0

        // 加载搭子信息
        const studyGroup = await this.loadStudyGroupForExam(exam._id)

        // 获取该考试的科目统计信息
        const subjectStatsResult = await SmartApi.getExamSubjectStats(exam._id)
        const subjectStats = subjectStatsResult.success ? subjectStatsResult.data : []

        const examData = {
          id: exam._id,
          name: exam.title, // 使用标准字段名
          date: exam.examDate, // 使用标准字段名
          daysLeft: daysInfo.daysLeft,
          isToday: daysInfo.isToday,
          dateText: dateText,
          completedTasks: completedTasks,
          totalTasks: totalTasks,
          progressPercent: progressPercent,
          hasPartner: !!studyGroup,
          bestPartnerProgress: studyGroup ? (studyGroup.bestProgress || 0) : 0,
          studyGroup: studyGroup,
          topMembers: studyGroup ? studyGroup.topMembers : [],
          subjectStats: subjectStats, // 科目统计信息
          hasSubjects: subjectStats.length > 0 // 是否有科目信息
        }

        console.log(`考试 ${i} (${exam.title}):`, examData)
        examList.push(examData)
      } catch (error) {
        console.error(`处理考试 ${exam.title} 数据失败:`, error)

        // 如果加载失败，使用基本信息
        const daysInfo = this.calculateDaysLeft(exam.examDate)
        const examData = {
          id: exam._id,
          name: exam.title, // 使用标准字段名
          date: exam.examDate, // 使用标准字段名
          daysLeft: daysInfo.daysLeft,
          isToday: daysInfo.isToday,
          dateText: this.formatExamDate(exam.examDate),
          completedTasks: 0,
          totalTasks: 0,
          progressPercent: 0,
          hasPartner: false,
          bestPartnerProgress: 0,
          studyGroup: null,
          subjectStats: [], // 空的科目统计
          hasSubjects: false // 没有科目信息
        }
        examList.push(examData)
      }
    }

    // 按日期排序
    examList.sort((a, b) => new Date(a.date) - new Date(b.date))

    return examList
  },

  // 格式化考试日期
  formatExamDate(dateStr) {
    const date = new Date(dateStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 加载考试的搭子小组信息
  async loadStudyGroupForExam(examId) {
    try {
      console.log('加载考试搭子信息:', examId)

      // 调用搭子小组API
      const result = await SmartApi.getStudyGroupByExam(examId)
      console.log('搭子小组API返回结果:', result)

      if (result.success && result.data) {
        const studyGroup = result.data
        console.log('搭子小组数据:', studyGroup)
        console.log('搭子成员数据:', studyGroup.members)

        // 计算最快搭子进度
        let bestProgress = 0
        if (studyGroup.members && studyGroup.members.length > 0) {
          bestProgress = Math.max(...studyGroup.members.map(member => member.progress || 0))
        }

        // 获取前三名进度最快的搭子
        const topMembers = this.getTopMembers(studyGroup.members)
        console.log('处理后的topMembers:', topMembers)

        return {
          id: studyGroup._id,
          name: studyGroup.name,
          members: studyGroup.members,
          topMembers: topMembers,
          bestProgress: bestProgress
        }
      } else {
        console.log('没有找到搭子小组数据或API调用失败')
        
        // 临时添加测试数据来验证显示效果
        console.log('生成测试搭子数据用于验证显示效果')
        const testMembers = [
          {
            userId: 'test1',
            nickname: '学霸小王',
            avatar: 'https://via.placeholder.com/40x40/1890FF/FFFFFF?text=王',
            completedTasks: 15,
            totalTasks: 20
          },
          {
            userId: 'test2', 
            nickname: '努力小李',
            avatar: 'https://via.placeholder.com/40x40/52C41A/FFFFFF?text=李',
            completedTasks: 12,
            totalTasks: 20
          },
          {
            userId: 'test3',
            nickname: '勤奋小张',
            avatar: 'https://via.placeholder.com/40x40/FF976A/FFFFFF?text=张',
            completedTasks: 10,
            totalTasks: 20
          }
        ]
        
        const topMembers = this.getTopMembers(testMembers)
        console.log('测试topMembers:', topMembers)
        
        return {
          id: 'test_group',
          name: '测试搭子小组',
          members: testMembers,
          topMembers: topMembers,
          bestProgress: 75
        }
      }

      return null
    } catch (error) {
      console.error('加载搭子信息失败:', error)
      return null
    }
  },

  // 获取前三名进度最快的搭子成员
  getTopMembers(members) {
    console.log('getTopMembers 输入参数:', members)
    
    if (!members || members.length === 0) {
      console.log('没有搭子成员数据')
      return []
    }

    // 按任务完成率从高到低排序，取前三名
    const sortedMembers = [...members]
      .sort((a, b) => {
        const progressA = (a.completedTasks || 0) / Math.max(a.totalTasks || 1, 1)
        const progressB = (b.completedTasks || 0) / Math.max(b.totalTasks || 1, 1)
        return progressB - progressA
      })
      .slice(0, 3)

    console.log('排序后的前三名成员:', sortedMembers)

    // 为每个成员添加头像和任务完成情况
    const result = sortedMembers.map((member, index) => {
      const completedTasks = member.completedTasks || 0
      const totalTasks = member.totalTasks || 1 // 避免除零错误
      const progressPercent = Math.round((completedTasks / totalTasks) * 100)
      
      // 处理头像：优先级 userInfo.avatarUrl > avatar > avatarUrl > 默认头像
      let avatarUrl = ''
      if (member.userInfo && member.userInfo.avatarUrl) {
        // 使用从数据库获取的真实用户头像
        avatarUrl = member.userInfo.avatarUrl
        console.log(`成员 ${index + 1} 使用数据库头像:`, avatarUrl)
      } else if (member.avatar) {
        avatarUrl = member.avatar
        console.log(`成员 ${index + 1} 使用备用头像:`, avatarUrl)
      } else if (member.avatarUrl) {
        avatarUrl = member.avatarUrl
        console.log(`成员 ${index + 1} 使用原始头像:`, avatarUrl)
      } else {
        // 使用本地默认头像
        avatarUrl = '/images/default-avatar.png'
        console.log(`成员 ${index + 1} 使用默认头像`)
      }
      
      // 处理昵称：优先级 userInfo.nickName > nickname > name > 默认名称
      const displayName = member.userInfo?.nickName || member.nickname || member.name || `搭子${index + 1}`

      // 详细的昵称调试信息
      console.log(`成员 ${index + 1} 昵称处理:`)
      console.log(`  - member.userInfo?.nickName: "${member.userInfo?.nickName}"`)
      console.log(`  - member.nickname: "${member.nickname}"`)
      console.log(`  - member.name: "${member.name}"`)
      console.log(`  - 最终显示名称: "${displayName}"`)
      
      const memberData = {
        id: member.userId || member.id || `member_${index}`,
        name: displayName,
        avatar: avatarUrl,
        completedTasks: completedTasks,
        totalTasks: totalTasks,
        progressPercent: progressPercent
      }
      
      console.log(`成员 ${index + 1} 处理结果:`, memberData)
      console.log(`  - 原始数据:`, member)
      console.log(`  - userInfo:`, member.userInfo)
      console.log(`  - 最终头像:`, avatarUrl)
      return memberData
    })

    console.log('最终返回的topMembers:', result)
    return result
  },

  // 计算最近考试信息
  calculateNearestExam(exams) {
    const today = new Date()
    const upcomingExams = exams
      .map(exam => {
        const daysInfo = this.calculateDaysLeft(exam.examDate)
        return {
          ...exam,
          examDate: new Date(exam.examDate), // 使用标准字段名
          daysLeft: daysInfo.daysLeft,
          isToday: daysInfo.isToday
        }
      })
      .filter(exam => exam.daysLeft >= 0) // 只考虑未来的考试
      .sort((a, b) => a.daysLeft - b.daysLeft) // 按天数排序

    const nearest = upcomingExams[0] || null
    const next = upcomingExams[1] || null

    return {
      nearest: nearest,
      daysLeft: nearest ? nearest.daysLeft : 0,
      next: next
    }
  },

  // 按照设计文档排序今日任务
  sortTodayTasks(tasks) {
    return tasks.sort((a, b) => {
      // 1. 完成状态：未完成优先
      if (a.completed !== b.completed) {
        return a.completed ? 1 : -1
      }

      // 2. 截止时间：越紧急越靠前
      const timeA = this.getTaskUrgencyScore(a.dueTime)
      const timeB = this.getTaskUrgencyScore(b.dueTime)
      if (timeA !== timeB) {
        return timeB - timeA // 分数高的排前面
      }

      // 3. 重要程度：高 > 中 > 低
      const priorityA = this.getPriorityScore(a.priority)
      const priorityB = this.getPriorityScore(b.priority)
      return priorityB - priorityA
    })
  },

  // 获取任务紧急程度分数
  getTaskUrgencyScore(dueTime) {
    if (!dueTime) return 0

    const now = new Date()
    const due = new Date()
    const [hours, minutes] = dueTime.split(':')
    due.setHours(parseInt(hours), parseInt(minutes), 0, 0)

    const diffHours = (due - now) / (1000 * 60 * 60)

    if (diffHours <= 0) return 100 // 已过期，最高优先级
    if (diffHours <= 2) return 90  // 2小时内
    if (diffHours <= 6) return 80  // 6小时内
    if (diffHours <= 12) return 70 // 12小时内
    if (diffHours <= 24) return 60 // 今天内
    return 50 // 明天及以后
  },

  // 获取优先级分数
  getPriorityScore(priority) {
    const scores = {
      'high': 10,
      'medium': 5,
      'low': 1
    }
    return scores[priority] || 1
  },

  // 考试倒计时点击事件
  onCountdownTap() {
    const { nearestExam } = this.data
    if (nearestExam) {
      wx.navigateTo({
        url: `/pages/exam-detail/index?id=${nearestExam._id}`
      })
    }
  },

  // 添加考试
  onAddExam() {
    if (!this.data.isLoggedIn) {
      this.showLoginPrompt('添加考试需要先登录')
      return
    }
    
    wx.navigateTo({
      url: '/pages/add-exam/index'
    })
  },

  // 添加任务
  onAddTask() {
    if (!this.data.isLoggedIn) {
      this.showLoginPrompt('添加任务需要先登录')
      return
    }

    wx.navigateTo({
      url: '/pages/task-form/index?mode=add'
    })
  },

  // 任务点击事件 - 跳转到任务详情页面
  onTaskTap(e) {
    const { taskId } = e.currentTarget.dataset
    const task = this.data.todayTasks.find(t => t.id === taskId)

    if (!task) {
      console.error('未找到任务:', taskId)
      return
    }

    console.log('点击任务:', task.title)

    // 跳转到任务编辑页面
    wx.navigateTo({
      url: `/pages/task-form/index?mode=edit&id=${taskId}`
    })
  },

  // 计算距离考试的天数
  calculateDaysLeft(examDate) {
    const today = new Date()
    const exam = new Date(examDate)
    
    // 重置时间为0点，只比较日期
    today.setHours(0, 0, 0, 0)
    exam.setHours(0, 0, 0, 0)
    
    const diffTime = exam - today
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return {
      daysLeft: Math.max(0, diffDays),
      isToday: diffDays === 0
    }
  },

  // 格式化复习时长
  formatStudyTime(minutes) {
    if (minutes < 60) {
      return `${minutes}分钟`
    } else {
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`
    }
  },

  // 正确提取考试科目名称
  getExamSubjectName(exam) {
    if (!exam) return ''
    
    // 1. 优先使用 exam.subject 字段
    if (exam.subject) {
      if (typeof exam.subject === 'string') {
        // 尝试解析可能的JSON字符串
        try {
          const parsed = JSON.parse(exam.subject)
          if (Array.isArray(parsed)) {
            return parsed.length > 0 ? parsed[0] : ''
          }
        } catch (e) {
          // 不是JSON，直接返回字符串
          return exam.subject
        }
        return exam.subject
      } else if (Array.isArray(exam.subject)) {
        return exam.subject.length > 0 ? exam.subject[0] : ''
      }
    }
    
    // 2. 使用 exam.subjects 字段（可能的备用字段）
    if (exam.subjects && Array.isArray(exam.subjects)) {
      return exam.subjects.length > 0 ? exam.subjects[0] : ''
    }
    
    // 3. 使用 exam.subjectList 字段（可能的备用字段）
    if (exam.subjectList && Array.isArray(exam.subjectList)) {
      return exam.subjectList.length > 0 ? exam.subjectList[0] : ''
    }
    
    return ''
  },



  // 设置问候语
  setGreeting() {
    const hour = new Date().getHours()
    const app = getApp()
    const userInfo = app.globalData.userInfo
    let greetingTime, greetingMessage

    // 获取用户昵称
    const userName = userInfo?.nickName || '同学'

    if (hour < 6) {
      greetingTime = '深夜好'
      greetingMessage = `${userName}，夜深了，注意休息哦`
    } else if (hour < 12) {
      greetingTime = '早上好'
      greetingMessage = `${userName}，新的一天，加油备考！`
    } else if (hour < 18) {
      greetingTime = '下午好'
      greetingMessage = `${userName}，继续保持复习状态`
    } else {
      greetingTime = '晚上好'
      greetingMessage = `${userName}，今天复习得怎么样？`
    }

    this.setData({
      greetingTime,
      greetingMessage,
      userInfo: userInfo
    })
  },

  // 设置日期信息
  setDateInfo() {
    const now = new Date()
    const currentDate = now.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })

    this.setData({
      currentDate,
      lunarDate: '农历十一月初八' // 这里可以接入农历API
    })
  },

  // 初始化快捷操作
  initQuickActions() {
    const quickActions = [
      {
        id: 'add_task',
        label: '制定复习计划',
        icon: '📝',
        bgColor: '#E6F7FF',
        action: 'addTask'
      },
      {
        id: 'add_exam',
        label: '添加考试',
        icon: '📅',
        bgColor: '#F6FFED',
        action: 'addExam'
      },
      {
        id: 'start_pomodoro',
        label: '开始专注',
        icon: '🍅',
        bgColor: '#FFF2E8',
        action: 'startPomodoro'
      },
      {
        id: 'view_stats',
        label: '查看统计',
        icon: '📊',
        bgColor: '#F9F0FF',
        action: 'viewStats'
      }
    ]

    this.setData({ quickActions })
  },

  // 初始化悬浮按钮菜单
  initFabMenu() {
    const fabMenuItems = [
      {
        id: 'add_task',
        label: '制定复习计划',
        icon: '📝',
        color: '#52C41A',
        action: 'addTask'
      },
      {
        id: 'add_exam',
        label: '添加考试',
        icon: '📅',
        color: '#1890FF',
        action: 'addExam'
      },
      {
        id: 'start_pomodoro',
        label: '开始专注',
        icon: '🍅',
        color: '#FA8C16',
        action: 'startPomodoro'
      }
    ]

    const fabMainButtonStyle = {
      size: 112,
      color: '#1890ff',
      icon: '+'
    }

    // 计算右下角的初始位置
    const systemInfo = wx.getSystemInfoSync()
    console.log('屏幕信息:', systemInfo)

    // 根据你的期望位置 (295-299, 537) 来计算比例
    // 假设你的屏幕宽度约为375px，高度约为667px
    const targetXRatio = 297 / 375  // 约0.792
    const targetYRatio = 537 / 667  // 约0.805

    const fabInitialPosition = {
      x: Math.round(systemInfo.windowWidth * targetXRatio),   // 按比例计算X位置
      y: Math.round(systemInfo.windowHeight * targetYRatio)   // 按比例计算Y位置
    }

    console.log('计算的初始位置:', fabInitialPosition)

    this.setData({
      fabMenuItems,
      fabMainButtonStyle,
      fabInitialPosition
    })
  },

  // 刷新数据
  async refreshData() {
    console.log('🔄 开始刷新首页数据')

    // 首页对所有用户开放，包括未登录用户
    // 未登录用户可以看到示例数据或空状态
    return await this.loadData()
  },

  // 加载当前考试的任务
  async loadCurrentExamTasks() {
    const { exams, currentExamIndex } = this.data

    console.log('loadCurrentExamTasks - 开始加载任务')
    console.log('当前考试索引:', currentExamIndex)
    console.log('考试列表:', exams)

    if (!exams || exams.length === 0 || currentExamIndex >= exams.length) {
      console.log('loadCurrentExamTasks - 没有考试数据或索引超出范围')
      return
    }

    const currentExam = exams[currentExamIndex]
    console.log('当前考试:', currentExam)

    const tasks = await this.loadTasksForExam(currentExam.id)
    console.log('加载到的任务:', tasks)

    // 获取科目选项
    const subjectOptions = [...new Set(tasks.map(task => task.subject).filter(Boolean))]

    // 更新当前考试的任务数据
    const updatedExams = exams.map((exam, index) => {
      if (index === currentExamIndex) {
        const updatedExam = {
          ...exam,
          tasks: tasks,
          subjectOptions: subjectOptions,
          filteredTasks: tasks, // 初始时显示所有任务
          completedTasks: tasks.filter(task => task.completed).length,
          totalTasks: tasks.length
        }
        console.log('更新后的考试数据:', updatedExam)
        return updatedExam
      }
      return exam
    })

    console.log('更新后的考试列表:', updatedExams)

    this.setData({
      exams: updatedExams,
      currentExam: updatedExams[currentExamIndex]
    })

    console.log('setData完成，页面数据:', this.data)
  },



  // 加载最近考试
  async loadNearestExam() {
    try {
      const result = await SmartApi.getUpcomingExams(30, 1) // 获取30天内最近的1个考试

      if (result.success && result.data.length > 0) {
        const exam = result.data[0]
        const nearestExam = {
          id: exam._id,
          name: exam.title, // 已经是标准字段名
          date: exam.examDate, // 已经是标准字段名
          subject: exam.subject,
          location: exam.location,
          status: '备考中',
          preparationProgress: 65, // 这里可以根据相关任务完成情况计算
          daysLeft: Math.ceil((new Date(exam.examDate) - new Date()) / (1000 * 60 * 60 * 24))
        }

        // 计算倒计时
        const countdown = this.calculateCountdown(nearestExam.date)

        this.setData({
          nearestExam,
          countdown
        })
      } else {
        // 没有即将到来的考试
        this.setData({
          nearestExam: null,
          countdown: []
        })
      }
    } catch (error) {
      console.error('加载最近考试失败:', error)
      // 设置空状态
      this.setData({
        nearestExam: null,
        countdown: []
      })
    }
  },

  // 计算倒计时
  calculateCountdown(targetDate) {
    const now = new Date()
    const target = new Date(targetDate)
    const diff = target - now

    if (diff <= 0) {
      return []
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    return [
      { value: days, unit: '天' },
      { value: hours, unit: '时' },
      { value: minutes, unit: '分' }
    ]
  },

  // 加载今日任务
  async loadTodayTasks() {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)

      // 获取当前用户信息用于调试
      const app = getApp()
      const userInfo = app.globalData.userInfo || {}
      const userId = userInfo._id || ''

      console.log('🔍 loadTodayTasks - 用户身份信息:', {
        hasUserInfo: !!userInfo,
        userId: userId,
        isLoggedIn: this.data.isLoggedIn
      })

      // 云函数会自动验证用户身份，这里只做基本的登录状态检查
      if (!this.data.isLoggedIn) {
        console.log('⚠️ 用户未登录，无法加载任务')
        this.setData({
          todayTasks: [],
          completedTasks: 0,
          totalTasks: 0
        })
        return
      }

      console.log('👤 当前用户ID:', userId)

      const result = await SmartApi.getTasks({
        dateRange: {
          start: today.toISOString(),
          end: tomorrow.toISOString()
        }
        // 注意：不需要传递userId，云函数会自动使用当前登录用户的ID
      }, 10) // 限制显示10个任务

      if (result.success) {
        const todayTasks = result.data.map(task => {
          // 处理检查点数据
          const subtasks = task.subtasks || []
          const completedSubtasks = subtasks.filter(subtask => subtask.completed).length

          return {
            id: task._id,
            title: task.title,
            subject: task.subject,
            priority: task.priority,
            priorityText: this.getPriorityText(task.priority),
            estimatedTime: task.estimatedTime ? `${task.estimatedTime}分钟` : '未设置',
            completed: task.completed,
            statusText: task.completed ? '已完成' : '进行中',
            // 检查点相关字段
            subtasks: subtasks,
            completedSubtasks: completedSubtasks
          }
        })

        // 验证和修复任务数据的检查点字段
        const validatedTasks = this.validateTaskCheckpointData(todayTasks)

        const completedTasks = validatedTasks.filter(task => task.completed).length
        const totalTasks = validatedTasks.length

        this.setData({
          todayTasks: validatedTasks,
          completedTasks,
          totalTasks
        })
      }
    } catch (error) {
      console.error('加载今日任务失败:', error)
      // 设置空状态
      this.setData({
        todayTasks: [],
        completedTasks: 0,
        totalTasks: 0
      })
    }
  },

  // 获取优先级文本
  getPriorityText(priority) {
    const priorityMap = {
      'high': '高优先级',
      'medium': '中优先级',
      'low': '低优先级'
    }
    return priorityMap[priority] || '普通'
  },

  // 加载今日统计
  async loadTodayStats() {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)

      const dateRange = {
        start: today.toISOString(),
        end: tomorrow.toISOString()
      }

      // 并行获取各种统计数据
      const [taskStatsResult, studyStatsResult, pomodoroStatsResult] = await Promise.all([
        SmartApi.getTaskStats(dateRange),
        SmartApi.getStudyStats(dateRange),
        SmartApi.getPomodoroStats(dateRange)
      ])

      let studyTime = '0h'
      let completedTasks = '0个'
      let pomodoroCount = '0次'
      let efficiency = '0%'

      if (studyStatsResult.success) {
        const totalMinutes = studyStatsResult.data.totalDuration || 0
        const hours = Math.floor(totalMinutes / 60)
        const minutes = Math.floor(totalMinutes % 60)
        studyTime = hours > 0 ? `${hours}.${Math.floor(minutes/6)}h` : `${minutes}m`
      }

      if (taskStatsResult.success) {
        completedTasks = `${taskStatsResult.data.completed || 0}个`
        if (taskStatsResult.data.total > 0) {
          efficiency = `${Math.round((taskStatsResult.data.completed / taskStatsResult.data.total) * 100)}%`
        }
      }

      if (pomodoroStatsResult.success) {
        pomodoroCount = `${pomodoroStatsResult.data.completedSessions || 0}次`
      }

      const todayStats = [
        {
          label: '复习时长',
          value: studyTime,
          icon: '⏰',
          color: '#1890FF'
        },
        {
          label: '完成复习',
          value: completedTasks,
          icon: '✅',
          color: '#52C41A'
        },
        {
          label: '专注次数',
          value: pomodoroCount,
          icon: '🍅',
          color: '#FA8C16'
        },
        {
          label: '复习效率',
          value: efficiency,
          icon: '📈',
          color: '#722ED1'
        }
      ]

      this.setData({ todayStats })
    } catch (error) {
      console.error('加载今日统计失败:', error)
      // 使用默认数据
      const todayStats = [
        {
          label: '复习时长',
          value: '0h',
          icon: '⏰',
          color: '#1890FF'
        },
        {
          label: '完成复习',
          value: '0个',
          icon: '✅',
          color: '#52C41A'
        },
        {
          label: '专注次数',
          value: '0次',
          icon: '🍅',
          color: '#FA8C16'
        },
        {
          label: '复习效率',
          value: '0%',
          icon: '📈',
          color: '#722ED1'
        }
      ]

      this.setData({ todayStats })
    }
  },

  // 加载最近活动
  loadRecentActivities() {
    const recentActivities = [
      {
        id: 'activity_001',
        title: '完成数学练习题',
        time: '2小时前',
        icon: '📝',
        status: 'success',
        statusText: '已完成'
      },
      {
        id: 'activity_002',
        title: '25分钟专注复习',
        time: '3小时前',
        icon: '🍅',
        status: 'success',
        statusText: '已完成'
      },
      {
        id: 'activity_003',
        title: '英语单词测试',
        time: '昨天',
        icon: '📖',
        status: 'warning',
        statusText: '需复习'
      }
    ]

    this.setData({ recentActivities })
  },

  // 切换任务状态
  async toggleTask(e) {
    if (!this.data.isLoggedIn) {
      this.showLoginPrompt('修改任务状态需要先登录')
      return
    }

    const taskId = e.currentTarget.dataset.id
    const task = this.data.todayTasks.find(t => t.id === taskId)

    if (!task) return

    const newCompleted = !task.completed

    // 显示加载提示
    wx.showLoading({
      title: '更新中...',
      mask: true
    })

    try {
      // 调用API更新任务状态
      const result = await SmartApi.completeTask(taskId, newCompleted)

      if (result.success) {
        // 更新本地数据
        const tasks = this.data.todayTasks.map(task => {
          if (task.id === taskId) {
            return {
              ...task,
              completed: newCompleted,
              completedTime: newCompleted ? this.formatTime(new Date()) : '',
              statusText: newCompleted ? '已完成' : '进行中'
            }
          }
          return task
        })

        // 重新排序任务
        const sortedTasks = this.sortTodayTasks(tasks)
        const completedCount = sortedTasks.filter(task => task.completed).length

        this.setData({
          todayTasks: sortedTasks,
          completedCount: completedCount
        })

        // 隐藏加载提示
        wx.hideLoading()

        // 显示反馈
        wx.showToast({
          title: newCompleted ? '复习完成！' : '重新开始复习',
          icon: 'success'
        })

        // 如果任务完成，刷新统计数据
        if (newCompleted) {
          this.loadTodayStats()
        }
      } else {
        // 隐藏加载提示
        wx.hideLoading()
        wx.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('切换任务状态失败:', error)
      // 隐藏加载提示
      wx.hideLoading()
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      })
    }
  },



  // 处理操作
  handleAction(action) {
    switch (action) {
      case 'addTask':
        this.addTask()
        break
      case 'addExam':
        this.addExam()
        break
      case 'startPomodoro':
        this.startPomodoro()
        break
      case 'viewStats':
        this.viewDataCenter()
        break
      default:
        console.log('未知操作:', action)
    }
  },

  // 页面跳转方法
  viewExamDetail() {
    wx.navigateTo({
      url: '/pages/exam-detail/index?id=' + this.data.nearestExam.id
    })
  },

  startStudy() {
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  viewAllTasks() {
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  viewTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/task-form/index?mode=edit&id=${taskId}`
    })
  },

  addTask() {
    if (!this.data.isLoggedIn) {
      this.showLoginPrompt('添加任务需要先登录')
      return
    }

    wx.navigateTo({
      url: '/pages/task-form/index?mode=add'
    })
  },

  addExam() {
    if (!this.data.isLoggedIn) {
      this.showLoginPrompt('添加考试需要先登录')
      return
    }
    
    wx.navigateTo({
      url: '/pages/add-exam/index'
    })
  },

  startPomodoro() {
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  viewDataCenter() {
    wx.switchTab({
      url: '/pages/data-center/index'
    })
  },

  // 加载备考搭子
  async loadStudyGroups() {
    try {
      const result = await SmartApi.getMyStudyGroups()

      if (result.success && result.data) {
        const studyGroups = result.data.map(group => ({
          id: group._id,
          examName: group.examName,
          memberCount: group.currentMembers,
          maxMembers: group.maxMembers,
          members: group.members,
          updateTime: group.updateTime
        }))

        this.setData({
          studyGroups: studyGroups,
          hasStudyGroup: studyGroups.length > 0
        })
      } else {
        this.setData({
          studyGroups: [],
          hasStudyGroup: false
        })
      }
    } catch (error) {
      console.error('加载备考搭子失败:', error)
      this.setData({
        studyGroups: [],
        hasStudyGroup: false
      })
    }
  },

  // 创建搭子小组
  onCreateStudyGroup() {
    if (!this.data.isLoggedIn) {
      this.showLoginPrompt('创建复习小组需要先登录')
      return
    }
    
    wx.navigateTo({
      url: '/pages/create-study-group/index'
    })
  },

  // 加入搭子小组
  onJoinStudyGroup() {
    if (!this.data.isLoggedIn) {
      this.showLoginPrompt('加入复习小组需要先登录')
      return
    }
    
    wx.navigateTo({
      url: '/pages/join-study-group/index'
    })
  },

  // 查看搭子详情
  onViewStudyGroup(e) {
    const { groupId } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/study-group-detail/index?groupId=${groupId}`
    })
  },





  // 加载指定考试的任务
  async loadTasksForExam(examId) {
    try {
      // 获取当前用户信息用于调试
      const app = getApp()
      const userInfo = app.globalData.userInfo || {}
      const userId = userInfo._id || ''

      console.log('🔍 loadTasksForExam - 用户身份信息:', {
        hasUserInfo: !!userInfo,
        userId: userId,
        examId: examId,
        isLoggedIn: this.data.isLoggedIn
      })

      // 云函数会自动验证用户身份，这里只做基本的登录状态检查
      if (!this.data.isLoggedIn) {
        console.log('⚠️ 用户未登录，无法加载任务')
        return { success: true, data: [] }
      }

      console.log('👤 当前用户ID:', userId)

      // 获取该考试的今日全部任务，包括已完成的任务
      const result = await SmartApi.getTasks({
        examId: examId,
        includeCompleted: true  // 明确指定包含已完成任务
        // 注意：不需要传递userId，云函数会自动使用当前登录用户的ID
      }, 100) // 设置较大的limit确保获取全部任务

      if (result.success && result.data) {
        const tasks = result.data.map(task => ({
          id: task._id,
          title: task.title,
          subject: task.subject || '',
          estimatedTime: task.estimatedTime ? `${task.estimatedTime}分钟` : '',
          priority: task.priority || 'medium',
          priorityText: this.getPriorityText(task.priority),
          dueTime: task.dueTime || '',
          completed: task.completed || false,
          // 新增显示字段
          subjectColor: this.getSubjectColor(task.subject),
          urgencyLevel: this.getUrgencyLevel(task.dueTime),
          dueTimeText: this.formatDueTime(task.dueTime),
          completedTime: task.completedTime ? this.formatTime(task.completedTime) : ''
        }))

        console.log('处理后的任务数据:', tasks)
        return tasks
      } else {
        // 没有任务数据，返回测试数据
        return [
          {
            id: 'test_task_1',
            title: '高等数学-极限专题',
            estimatedTime: '60分钟',
            priority: 'high',
            priorityText: '重要',
            completed: false
          },
          {
            id: 'test_task_2',
            title: '线性代数-矩阵运算',
            estimatedTime: '45分钟',
            priority: 'medium',
            priorityText: '一般',
            completed: true
          },
          {
            id: 'test_task_3',
            title: '概率论-随机变量',
            estimatedTime: '50分钟',
            priority: 'high',
            priorityText: '重要',
            completed: false
          }
        ]
      }
    } catch (error) {
      console.error('加载任务失败:', error)

      // 加载失败时返回测试数据
      return [
        {
          id: 'test_task_1',
          title: '高等数学-极限专题',
          estimatedTime: '60分钟',
          priority: 'high',
          priorityText: '重要',
          completed: false
        },
        {
          id: 'test_task_2',
          title: '线性代数-矩阵运算',
          estimatedTime: '45分钟',
          priority: 'medium',
          priorityText: '一般',
          completed: true
        }
      ]
    }
  },

  // 获取科目颜色
  getSubjectColor(subject) {
    const colors = {
      '数学': '#FF6B6B',
      '英语': '#4ECDC4',
      '政治': '#45B7D1',
      '专业课': '#96CEB4',
      '语文': '#FECA57',
      '物理': '#6C5CE7',
      '化学': '#A29BFE',
      '生物': '#FD79A8',
      '历史': '#FDCB6E',
      '地理': '#6C5CE7'
    }
    return colors[subject] || '#1890FF'
  },

  // 获取紧急程度等级
  getUrgencyLevel(dueTime) {
    if (!dueTime) return 'later'

    const now = new Date()
    const due = new Date()
    const [hours, minutes] = dueTime.split(':')
    due.setHours(parseInt(hours), parseInt(minutes), 0, 0)

    const diffHours = (due - now) / (1000 * 60 * 60)

    if (diffHours <= 0) return 'overdue'
    if (diffHours <= 24) return 'today'
    if (diffHours <= 48) return 'tomorrow'
    return 'later'
  },

  // 格式化截止时间显示
  formatDueTime(dueTime) {
    if (!dueTime) return '无截止时间'

    const urgency = this.getUrgencyLevel(dueTime)
    const timeStr = dueTime

    switch (urgency) {
      case 'overdue':
        return `已过期 ${timeStr}`
      case 'today':
        return `今天 ${timeStr} 截止`
      case 'tomorrow':
        return `明天 ${timeStr} 截止`
      default:
        return `${timeStr} 截止`
    }
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return ''

    const time = new Date(timeStr)
    const hours = time.getHours().toString().padStart(2, '0')
    const minutes = time.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  },

  // 获取优先级文本
  getPriorityText(priority) {
    switch (priority) {
      case 'high': return '重要'
      case 'medium': return '一般'
      case 'low': return '较低'
      default: return '一般'
    }
  },



  // 开始复习（新首页版本）
  onStartStudy(e) {
    const { taskId } = e.currentTarget.dataset
    const task = this.data.todayTasks.find(t => t.id === taskId)

    if (!task) {
      wx.showToast({
        title: '任务不存在',
        icon: 'none'
      })
      return
    }

    // 移除触觉反馈
    // wx.vibrateShort({
    //   type: 'light'
    // })

    // 由于pomodoro是tabbar页面，需要使用switchTab
    // 先存储任务信息到全局数据或缓存
    wx.setStorageSync('currentTaskId', taskId)
    wx.setStorageSync('currentExamId', task.examId)
    wx.setStorageSync('currentTaskTitle', task.title)
    wx.setStorageSync('currentExamName', task.examName)

    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  // 添加任务
  onAddTask(e) {
    if (!this.data.isLoggedIn) {
      this.showLoginPrompt('添加任务需要先登录')
      return
    }

    const { examId } = e.currentTarget.dataset

    wx.navigateTo({
      url: `/pages/task-form/index?mode=add&examId=${examId}`
    })
  },

  // 创建考试
  onCreateExam() {
    if (!this.data.isLoggedIn) {
      this.showLoginPrompt('创建考试需要先登录')
      return
    }
    
    wx.navigateTo({
      url: '/pages/add-exam/index'
    })
  },

  // 管理考试
  onManageExam(e) {
    const { examId } = e.currentTarget.dataset

    wx.navigateTo({
      url: `/pages/exam-detail/index?examId=${examId}`
    })
  },

  // 创建搭子小组
  async onCreateStudyGroup(e) {
    if (!this.data.isLoggedIn) {
      this.showLoginPrompt('创建复习小组需要先登录')
      return
    }
    
    const { examId } = e.currentTarget.dataset
    const { currentExam } = this.data

    try {
      wx.showLoading({
        title: '创建中...',
        mask: true
      })

      const result = await SmartApi.createStudyGroup(examId, currentExam.name)

      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: '创建成功',
          icon: 'success'
        })

        // 重新加载考试数据以更新搭子信息
        await this.loadExams()
      } else {
        wx.showToast({
          title: result.error || '创建失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('创建搭子小组失败:', error)
      wx.showToast({
        title: '创建失败',
        icon: 'none'
      })
    }
  },

  // 查看搭子小组
  onViewStudyGroup(e) {
    const { groupId } = e.currentTarget.dataset

    // 触觉反馈
    wx.vibrateShort({
      type: 'light'
    })

    wx.navigateTo({
      url: `/pages/study-group-detail/index?groupId=${groupId}`
    })
  },



  // 新悬浮按钮点击事件
  onFabButtonClick(e) {
    const { action, button } = e.detail
    console.log('悬浮按钮点击:', action, button)

    switch (action) {
      case 'addTask':
        this.onAddTask()
        break
      case 'addExam':
        this.onAddExam()
        break
      case 'startPomodoro':
        this.onStartPomodoro()
        break
      default:
        console.warn('未知的悬浮按钮操作:', action)
    }
  },

  // 悬浮按钮位置变化事件
  onFabPositionChange(e) {
    const { x, y } = e.detail
    console.log('悬浮按钮位置变化:', x, y)
  },

  // 悬浮按钮展开状态变化事件
  onFabExpandChange(e) {
    const { expanded } = e.detail
    console.log('悬浮按钮展开状态:', expanded)
  },

  // 保留原有的点击事件作为备用
  onFabClick() {
    // 显示操作选择
    wx.showActionSheet({
      itemList: ['添加任务', '添加考试', '开始专注'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0: // 添加任务
            this.onAddTask()
            break
          case 1: // 添加考试
            this.onAddExam()
            break
          case 2: // 开始专注
            this.onStartPomodoro()
            break
        }
      }
    })
  },



  // 开始专注（番茄钟）- 跳转到专注页面
  onStartPomodoro() {
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  // 智能生成任务
  onQuickCreateTasks() {
    if (!this.data.isLoggedIn) {
      this.showLoginPrompt('智能生成任务需要先登录')
      return
    }
    
    console.log('智能生成任务')

    if (this.data.examList.length === 0) {
      wx.showToast({
        title: '请先添加考试',
        icon: 'none'
      })
      return
    }

    // 显示考试选择弹窗
    const examNames = this.data.examList.map(exam => exam.name) // exam.name 已经映射为 exam.title

    wx.showActionSheet({
      itemList: examNames,
      success: (res) => {
        const selectedExam = this.data.examList[res.tapIndex]
        this.generateTasksForExam(selectedExam)
      },
      fail: (res) => {
        console.log('用户取消选择考试')
      }
    })
  },

  // 为指定考试生成任务
  async generateTasksForExam(exam) {
    try {
      wx.showLoading({
        title: '正在生成任务...'
      })

      // 调用AI生成任务的API
      const result = await SmartApi.generateTasksForExam(exam.id)

      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: `已生成${result.data.length}个任务`,
          icon: 'success'
        })

        // 重新加载今日数据
        this.loadTodayData()
      } else {
        wx.showToast({
          title: result.message || '生成任务失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('生成任务失败:', error)
      wx.showToast({
        title: '生成任务失败',
        icon: 'none'
      })
    }
  },

  // 考试板块swiper切换事件
  onExamSwiperChange(e) {
    const { current } = e.detail
    this.setData({
      currentExamIndex: current
    })
    console.log('切换到考试:', current)
  },

  // 考试信息区域点击事件
  onExamInfoTap(e) {
    const { examId } = e.currentTarget.dataset
    console.log('点击考试信息，examId:', examId)

    wx.navigateTo({
      url: `/pages/exam-detail/index?id=${examId}`
    })
  },

  // 点击科目
  onSubjectTap(e) {
    const { examId, subject } = e.currentTarget.dataset
    console.log('点击科目:', subject, '考试ID:', examId)

    // 跳转到任务中心，并筛选该科目的任务
    wx.navigateTo({
      url: `/pages/task-center/index?examId=${examId}&subject=${encodeURIComponent(subject)}`
    })
  },

  // 计算 swiper 动态高度
  calculateSwiperHeight(examList) {
    if (!examList || examList.length === 0) {
      console.log('📏 高度计算: 无考试数据，使用默认高度 400rpx')
      return 400 // 默认高度
    }

    // 计算最大高度的考试卡片
    let maxHeight = 0

    examList.forEach((exam, index) => {
      let cardHeight = 0

      // 基础高度（考试信息 + 内边距）
      cardHeight += 250 // 考试标题、倒计时、进度条等基础信息（增加50rpx）

      // 科目信息高度
      if (exam.hasSubjects && exam.subjectStats && exam.subjectStats.length > 0) {
        const subjectHeight = 60 + exam.subjectStats.length * 50 + 30 // 标题60 + 每科目50 + 内边距30
        cardHeight += subjectHeight
        console.log(`📏 考试${index + 1} 科目高度: ${subjectHeight}rpx (${exam.subjectStats.length}个科目)`)
      }

      // 搭子信息高度
      if (exam.hasPartner) {
        cardHeight += 140 // 搭子信息区域（增加20rpx）
      } else {
        cardHeight += 100 // 无搭子提示区域（增加20rpx）
      }

      // 分隔线和间距
      cardHeight += 60 // 增加间距

      console.log(`📏 考试${index + 1} "${exam.name}" 计算高度: ${cardHeight}rpx`)
      maxHeight = Math.max(maxHeight, cardHeight)
    })

    // 确保最小高度，并添加更多缓冲空间
    const finalHeight = Math.max(maxHeight + 80, 500) // 增加缓冲空间和最小高度
    console.log(`📏 最终 swiper 高度: ${finalHeight}rpx (最大卡片: ${maxHeight}rpx)`)
    return finalHeight
  },

  // 搭子功能区域点击事件
  async onPartnerTap(e) {
    const { exam } = e.currentTarget.dataset
    console.log('点击搭子功能，exam:', exam)

    if (exam.hasPartner && exam.studyGroup) {
      // 有搭子：跳转搭子小组详情页面
      const groupId = exam.studyGroup.id
      if (groupId) {
        wx.navigateTo({
          url: `/pages/study-group-detail/index?groupId=${groupId}`
        })
      } else {
        wx.showToast({
          title: '搭子小组信息错误',
          icon: 'none'
        })
      }
    } else {
      // 无搭子时不处理，由按钮的单独事件处理
      console.log('无搭子状态，由按钮事件处理')
    }
  },

  // 创建搭子小组按钮点击事件
  onCreatePartner(e) {
    const { examId } = e.currentTarget.dataset
    console.log('点击创建搭子小组，examId:', examId)

    // WuxUI button组件的事件处理
    if (e.detail && e.detail.stopPropagation) {
      e.detail.stopPropagation()
    } else if (e.stopPropagation) {
      e.stopPropagation()
    }

    // 跳转创建搭子页面
    wx.navigateTo({
      url: `/pages/create-study-group/index?examId=${examId}`
    })
  },

  // 搭子成员点击事件
  onPartnerMemberTap(e) {
    const { examId } = e.currentTarget.dataset
    const memberId = e.currentTarget.dataset.memberId || e.currentTarget.dataset.member?.id

    console.log('点击搭子成员, examId:', examId, 'memberId:', memberId)

    if (!memberId) {
      wx.showToast({
        title: '成员信息错误',
        icon: 'none'
      })
      return
    }

    // 跳转到成员详情页面，只传递必要的ID参数
    wx.navigateTo({
      url: `/pages/member-detail/index?userId=${memberId}&examId=${examId}`
    })
  },

  // 查看日历
  onViewCalendar() {
    wx.navigateTo({
      url: '/pages/calendar/index'
    })
  },

  // 查看复习统计
  onViewStats() {
    wx.navigateTo({
      url: '/pages/stats/index'
    })
  },

  // 显示登录提示
  showLoginPrompt(message = '此功能需要先登录') {
    wx.navigateTo({
      url: '/pages/login/index'
    })
  },

  // 阻止事件冒泡的空方法
  stopPropagation(e) {
    // 这个方法的存在就是为了阻止事件冒泡
    // 通过catchtap绑定，事件不会继续向上传播
    console.log('阻止事件冒泡')
  },

  // ==================== 检查点操作方法 ====================

  // 打开检查点弹窗
  async openCheckpointModal(e) {
    const taskId = e.currentTarget.dataset.id
    console.log('打开检查点弹窗，任务ID:', taskId)

    // 查找对应的任务
    const task = this.data.todayTasks.find(t => t.id === taskId)
    if (!task) {
      console.error('未找到任务:', taskId)
      wx.showToast({
        title: '任务不存在',
        icon: 'error'
      })
      return
    }

    // 如果是首次打开且未加载过检查点，则懒加载
    if (!this.data.loadedCheckpoints.includes(taskId) && (!task.subtasks || task.subtasks.length === 0)) {
      console.log('首次打开，尝试懒加载检查点数据')
      await this.lazyLoadCheckpoints(taskId)
      // 重新获取任务数据
      const updatedTask = this.data.todayTasks.find(t => t.id === taskId)
      if (updatedTask) {
        task.subtasks = updatedTask.subtasks
        task.completedSubtasks = updatedTask.completedSubtasks
      }
    }

    // 设置弹窗数据，确保数据完整性
    const subtasks = task.subtasks || []
    const completedCount = subtasks.filter(item => item.completed).length

    const checkpointModalData = {
      taskId: taskId,
      taskTitle: task.title,
      subtasks: [...subtasks], // 深拷贝避免引用问题
      completedCount: completedCount
    }

    this.setData({
      showCheckpointModal: true,
      checkpointModalData: checkpointModalData,
      showAddCheckpointInput: false,
      newCheckpointTitle: ''
    })

    console.log('检查点弹窗已打开，数据:', checkpointModalData)
  },

  // 关闭检查点弹窗
  closeCheckpointModal() {
    console.log('关闭检查点弹窗')

    this.setData({
      showCheckpointModal: false,
      showAddCheckpointInput: false,
      newCheckpointTitle: ''
    })
  },

  // 更新检查点统计（弹窗中）
  updateCheckpointModalStats() {
    const { checkpointModalData } = this.data
    const completedCount = checkpointModalData.subtasks.filter(item => item.completed).length

    this.setData({
      'checkpointModalData.completedCount': completedCount
    })

    // 添加统计更新的视觉反馈
    this.triggerStatsAnimation()

    console.log('检查点统计已更新:', completedCount, '/', checkpointModalData.subtasks.length)
  },

  // 触发统计动画效果
  triggerStatsAnimation() {
    // 简单的动画效果，可以根据需要扩展
    setTimeout(() => {
      console.log('统计动画触发')
    }, 100)
  },

  // 切换检查点状态（弹窗中）
  toggleCheckpointInModal(e) {
    const index = parseInt(e.currentTarget.dataset.index)
    const checkpointModalData = { ...this.data.checkpointModalData }

    if (checkpointModalData.subtasks[index]) {
      checkpointModalData.subtasks[index].completed = !checkpointModalData.subtasks[index].completed

      this.setData({ checkpointModalData })

      // 更新统计
      this.updateCheckpointModalStats()

      // 同步到主任务列表
      this.syncCheckpointToTaskList()

      console.log('检查点状态已切换:', checkpointModalData.subtasks[index])
    }
  },

  // 删除检查点（弹窗中）
  removeCheckpointInModal(e) {
    const index = parseInt(e.currentTarget.dataset.index)
    const checkpointModalData = { ...this.data.checkpointModalData }

    if (checkpointModalData.subtasks[index]) {
      const removedItem = checkpointModalData.subtasks[index]
      const updatedSubtasks = [...checkpointModalData.subtasks]
      updatedSubtasks.splice(index, 1)

      checkpointModalData.subtasks = updatedSubtasks

      this.setData({ checkpointModalData })

      // 更新统计
      this.updateCheckpointModalStats()

      // 同步到主任务列表
      this.syncCheckpointToTaskList()

      wx.showToast({
        title: '检查点已删除',
        icon: 'success',
        duration: 1000
      })

      console.log('检查点已删除:', removedItem)
    }
  },

  // 切换添加检查点输入框显示状态
  toggleAddCheckpointInput() {
    console.log('切换添加检查点输入框显示状态，当前状态:', this.data.showAddCheckpointInput)
    this.setData({
      showAddCheckpointInput: true,
      newCheckpointTitle: ''
    }, () => {
      console.log('setData完成，新状态:', this.data.showAddCheckpointInput)
    })
  },

  // 取消添加检查点
  cancelAddCheckpoint() {
    this.setData({
      showAddCheckpointInput: false,
      newCheckpointTitle: ''
    })
  },

  // 更新新检查点标题
  updateNewCheckpointTitle(e) {
    const newTitle = e.detail.value || e.detail
    this.setData({
      newCheckpointTitle: newTitle
    })
  },

  // 添加检查点（弹窗中）
  addCheckpointInModal() {
    const { newCheckpointTitle, checkpointModalData } = this.data
    const title = newCheckpointTitle.trim()

    if (!title) {
      wx.showToast({
        title: '请输入检查点内容',
        icon: 'none'
      })
      return
    }

    // 检查是否重复
    const isDuplicate = checkpointModalData.subtasks.some(item => item.title === title)
    if (isDuplicate) {
      wx.showToast({
        title: '检查点已存在',
        icon: 'none'
      })
      return
    }

    // 添加新检查点
    const newCheckpoint = {
      id: `checkpoint_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: title,
      completed: false
    }

    const updatedModalData = { ...checkpointModalData }
    updatedModalData.subtasks.push(newCheckpoint)

    this.setData({
      checkpointModalData: updatedModalData,
      newCheckpointTitle: '',
      showAddCheckpointInput: false
    })

    // 更新统计
    this.updateCheckpointModalStats()

    // 同步到主任务列表
    this.syncCheckpointToTaskList()

    wx.showToast({
      title: '检查点已添加',
      icon: 'success'
    })

    console.log('新检查点已添加:', newCheckpoint)
  },

  // 批量完成所有检查点
  completeAllCheckpoints() {
    const checkpointModalData = { ...this.data.checkpointModalData }
    let changedCount = 0

    checkpointModalData.subtasks.forEach(subtask => {
      if (!subtask.completed) {
        subtask.completed = true
        changedCount++
      }
    })

    if (changedCount === 0) {
      wx.showToast({
        title: '所有检查点已完成',
        icon: 'none',
        duration: 1500
      })
      return
    }

    this.setData({ checkpointModalData })
    this.updateCheckpointModalStats()
    this.syncCheckpointToTaskList()

    wx.showToast({
      title: `已完成 ${changedCount} 个检查点`,
      icon: 'success',
      duration: 1500
    })

    console.log('批量完成检查点:', changedCount)
  },

  // 批量重置所有检查点
  resetAllCheckpoints() {
    const checkpointModalData = { ...this.data.checkpointModalData }
    let changedCount = 0

    checkpointModalData.subtasks.forEach(subtask => {
      if (subtask.completed) {
        subtask.completed = false
        changedCount++
      }
    })

    if (changedCount === 0) {
      wx.showToast({
        title: '所有检查点已重置',
        icon: 'none',
        duration: 1500
      })
      return
    }

    this.setData({ checkpointModalData })
    this.updateCheckpointModalStats()
    this.syncCheckpointToTaskList()

    wx.showToast({
      title: `已重置 ${changedCount} 个检查点`,
      icon: 'success',
      duration: 1500
    })

    console.log('批量重置检查点:', changedCount)
  },

  // 更新检查点内容（弹窗中）
  updateCheckpointInModal(e) {
    const index = parseInt(e.currentTarget.dataset.index)
    const newTitle = e.detail.value || e.detail
    const checkpointModalData = { ...this.data.checkpointModalData }

    if (checkpointModalData.subtasks[index]) {
      checkpointModalData.subtasks[index].title = newTitle
      this.setData({ checkpointModalData })

      // 同步到任务列表和服务器
      this.syncCheckpointToTaskList()

      console.log('检查点内容已更新:', newTitle)
    }
  },

  // 同步检查点数据到任务列表
  syncCheckpointToTaskList() {
    const { checkpointModalData } = this.data
    const { taskId, subtasks, completedCount } = checkpointModalData

    console.log('开始同步检查点数据到任务列表:', taskId)

    // 更新todayTasks数组中的检查点数据
    const updatedTasks = this.data.todayTasks.map(task => {
      if (task.id === taskId) {
        task.subtasks = [...subtasks]
        task.completedSubtasks = completedCount
        console.log('任务检查点数据已更新:', task.id, completedCount + '/' + subtasks.length)
      }
      return task
    })

    // 一次性更新所有相关数据
    this.setData({
      todayTasks: updatedTasks
    })

    // 更新缓存
    this.updateCheckpointCache(taskId, subtasks)

    // 防抖更新到服务器
    this.debouncedSyncToServer(taskId, subtasks)

    console.log('检查点数据同步完成')
  },

  // 更新检查点缓存
  updateCheckpointCache(taskId, subtasks) {
    const checkpointCache = { ...this.data.checkpointCache }
    checkpointCache[taskId] = [...subtasks]

    this.setData({ checkpointCache })

    // 标记为已加载
    if (!this.data.loadedCheckpoints.includes(taskId)) {
      this.setData({
        loadedCheckpoints: [...this.data.loadedCheckpoints, taskId]
      })
    }

    console.log('检查点缓存已更新:', taskId)
  },

  // 懒加载检查点数据
  async lazyLoadCheckpoints(taskId) {
    console.log('懒加载检查点数据:', taskId)

    // 检查缓存
    if (this.data.checkpointCache[taskId]) {
      console.log('使用缓存的检查点数据:', taskId)

      const cachedSubtasks = this.data.checkpointCache[taskId]
      const updatedTasks = this.data.todayTasks.map(task => {
        if (task.id === taskId) {
          task.subtasks = [...cachedSubtasks]
          task.completedSubtasks = cachedSubtasks.filter(s => s.completed).length
        }
        return task
      })

      this.setData({ todayTasks: updatedTasks })
      return
    }

    try {
      // 标记为已加载，避免重复尝试
      const loadedCheckpoints = [...this.data.loadedCheckpoints]
      if (!loadedCheckpoints.includes(taskId)) {
        loadedCheckpoints.push(taskId)
      }

      this.setData({ loadedCheckpoints })

      // 从服务器获取任务详情（包含检查点数据）
      console.log('从服务器加载检查点数据:', taskId)
      const result = await SmartApi.getTask(taskId)

      if (result && result.success && result.data) {
        const taskData = result.data
        const subtasks = taskData.subtasks || []

        console.log('服务器返回的检查点数据:', subtasks)

        // 更新任务数据
        const updatedTasks = this.data.todayTasks.map(task => {
          if (task.id === taskId) {
            task.subtasks = [...subtasks]
            task.completedSubtasks = subtasks.filter(s => s.completed).length
          }
          return task
        })

        this.setData({ todayTasks: updatedTasks })

        // 更新缓存
        this.updateCheckpointCache(taskId, subtasks)

        console.log('检查点数据加载成功:', taskId, subtasks.length, '个检查点')
      } else {
        console.log('服务器未返回检查点数据，使用现有数据')
      }

      console.log('检查点懒加载完成:', taskId)
    } catch (error) {
      console.error('懒加载检查点失败:', error)
      // 使用现有数据，不影响用户体验
    }
  },

  // ==================== 检查点服务器同步方法 ====================

  // 同步检查点数据到服务器
  async syncCheckpointToServer(taskId, subtasks, retryCount = 0) {
    const maxRetries = 2

    try {
      console.log('开始同步检查点数据到服务器:', taskId, retryCount > 0 ? `(重试 ${retryCount}/${maxRetries})` : '')
      console.log('要同步的检查点数据:', subtasks)

      // 过滤空的检查点
      const filteredSubtasks = subtasks.filter(item =>
        item.title && item.title.trim()
      )

      // 准备更新数据（参考task-form的实现）
      const updates = {
        subtasks: filteredSubtasks,
        completedSubtasks: filteredSubtasks.filter(item => item.completed).length,
        totalSubtasks: filteredSubtasks.length
      }

      console.log('格式化后的更新数据:', JSON.stringify(updates, null, 2))

      const result = await SmartApi.updateTask(taskId, updates)
      console.log('服务器返回结果:', JSON.stringify(result, null, 2))

      if (result && result.success) {
        console.log('检查点数据同步成功:', taskId)
        wx.showToast({
          title: '数据已保存',
          icon: 'success',
          duration: 1000
        })
        return { success: true }
      } else {
        console.error('检查点数据同步失败:', result ? result.error : '未知错误')

        // 如果是网络错误且还有重试次数，则重试
        if (retryCount < maxRetries && this.isRetryableError(result ? result.error : '网络错误')) {
          console.log('准备重试同步...')
          await this.delay(1000 * (retryCount + 1)) // 递增延迟
          return await this.syncCheckpointToServer(taskId, subtasks, retryCount + 1)
        }

        return { success: false, error: result ? result.error : '同步失败' }
      }
    } catch (error) {
      console.error('检查点数据同步异常:', error)

      // 如果是网络错误且还有重试次数，则重试
      if (retryCount < maxRetries && this.isRetryableError(error.message)) {
        console.log('准备重试同步...')
        await this.delay(1000 * (retryCount + 1)) // 递增延迟
        return await this.syncCheckpointToServer(taskId, subtasks, retryCount + 1)
      }

      return { success: false, error: error.message }
    }
  },

  // 判断是否为可重试的错误
  isRetryableError(errorMessage) {
    const retryableErrors = ['网络错误', 'timeout', 'Network Error', '请求超时', 'connection']
    return retryableErrors.some(error =>
      errorMessage && errorMessage.toLowerCase().includes(error.toLowerCase())
    )
  },

  // 延迟工具方法
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  // 防抖更新检查点到服务器
  debouncedSyncToServer(taskId, subtasks) {
    // 清除之前的定时器
    if (this.syncTimeouts && this.syncTimeouts[taskId]) {
      clearTimeout(this.syncTimeouts[taskId])
    }

    // 初始化定时器对象
    if (!this.syncTimeouts) {
      this.syncTimeouts = {}
    }

    // 设置新的定时器
    this.syncTimeouts[taskId] = setTimeout(async () => {
      try {
        console.log('开始防抖同步检查点到服务器:', taskId)
        const result = await this.syncCheckpointToServer(taskId, subtasks)
        if (result.success) {
          console.log('检查点数据同步成功')
        } else {
          console.warn('检查点数据同步失败:', result.error)
          wx.showToast({
            title: '数据同步失败',
            icon: 'none',
            duration: 2000
          })
        }
        delete this.syncTimeouts[taskId]
      } catch (error) {
        console.error('防抖同步检查点失败:', error)
        delete this.syncTimeouts[taskId]
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none',
          duration: 2000
        })
      }
    }, 1000) // 1秒防抖
  }

})
