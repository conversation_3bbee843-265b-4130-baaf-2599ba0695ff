/* pages/login/index.wxss */
page {
  background-color: #f7f8fa;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 0 40rpx 40rpx 40rpx;
  box-sizing: border-box;
  min-height: 100vh;
}

/* 导航栏样式 */
.van-nav-bar {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  --van-nav-bar-background-color: #ffffff;
  --van-nav-bar-title-text-color: #333333;
  --van-nav-bar-icon-color: #333333;
  --van-nav-bar-text-color: #333333;
  --van-nav-bar-height: 88rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
  margin-top: 260rpx; /* 再增加40rpx(约10%的下移) */
}

.logo {
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  border: 1px solid #ddd;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  margin-top: 20rpx;
  color: #333;
}

.subtitle {
  font-size: 28rpx;
  color: #999;
  margin-top: 10rpx;
}

.login-container {
  width: 100%;
}

.form-container, .wechat-login-container {
  padding: 30rpx 10rpx;
}

.button-group {
  margin-top: 40rpx;
}

.links {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
  font-size: 26rpx;
}

.link {
  color: #1989fa;
  cursor: pointer;
}

.wechat-login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.wechat-prompt {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
  font-weight: 500;
}

.wechat-tips {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
}

.wechat-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 60rpx;
}

.auth-button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}



.guest-login {
  text-align: center;
  margin-top: 50rpx;
}

/* Register Modal Styles */
.register-modal {
  padding: 40rpx;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
  position: relative;
}

.modal-title::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #1989fa;
  border-radius: 2rpx;
}

.button-group-modal {
  margin-top: 50rpx;
  margin-bottom: 20rpx;
}

/* 表单项样式优化 */
.form-section {
  margin: 20rpx 0;
}

.register-modal .van-field {
  margin-bottom: 20rpx;
  border-radius: 8rpx;
}

.register-modal .van-field:last-child {
  margin-bottom: 0;
}

/* 弹窗覆盖层样式 */
.van-popup {
  z-index: 1000;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .register-modal {
    padding: 30rpx 20rpx;
  }
  
  .modal-title {
    font-size: 32rpx;
  }
}
