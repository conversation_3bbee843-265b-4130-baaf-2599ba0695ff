/* pages/task-form/index.wxss */

/* ==================== 全局z-index设置 ==================== */
page {
  --van-popup-z-index: 10001;
  --van-action-sheet-z-index: 10001;
  --van-overlay-z-index: 10000;
  --van-dialog-z-index: 10001;
  --van-toast-z-index: 10001;
}

/* ==================== 基础容器样式 ==================== */
.task-form-container {
  background-color: #f7f8fa;
  min-height: 100vh;
  padding-bottom: calc(160rpx + env(safe-area-inset-bottom));
}



/* 表单内容区域 */
.form-content {
  padding: 32rpx 16rpx 16rpx 16rpx; /* 顶部增加一些间距 */
}

/* 加载状态样式 */
.loading-content {
  background: #ffffff;
  padding: 60rpx 32rpx;
  border-radius: 12rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 表单区域样式 */
.form-sections {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 模式信息样式 */
.mode-info {
  background: #ffffff;
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.mode-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #1890ff;
}

.task-id {
  font-size: 24rpx;
  color: #999;
}

/* 任务信息样式 */
.task-info {
  background: #ffffff;
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.task-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.task-exam,
.task-subject,
.task-checkpoints {
  font-size: 28rpx;
  color: #666;
}

/* 占位内容样式 */
.placeholder-content {
  background: #ffffff;
  padding: 60rpx 32rpx;
  border-radius: 12rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
}

.placeholder-content text {
  font-size: 28rpx;
  color: #666;
}

/* ==================== Vant组件自定义样式 ==================== */
.form-section {
  --van-cell-group-background-color: #fff;
  --van-cell-group-title-color: #333;
  --van-cell-group-title-font-size: 32rpx;
  --van-cell-group-title-font-weight: 600;
  --van-cell-group-title-padding: 24rpx 32rpx 16rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.form-field {
  --van-field-label-color: #333;
  --van-field-label-font-size: 28rpx;
  --van-field-label-font-weight: 500;
  --van-field-input-text-color: #333;
  --van-field-input-font-size: 28rpx;
  --van-field-placeholder-text-color: #999;
  --van-field-border-color: #f0f0f0;
  --van-field-error-message-color: #ff4d4f;
}

.form-cell {
  --van-cell-title-color: #333;
  --van-cell-title-font-size: 28rpx;
  --van-cell-title-font-weight: 500;
  --van-cell-value-color: #666;
  --van-cell-value-font-size: 26rpx;
  --van-cell-border-color: #f0f0f0;
  --van-cell-padding: 24rpx 32rpx;
}

/* 提醒设置样式 */
.reminder-switch {
  --van-switch-on-background-color: #1989fa;
  --van-switch-off-background-color: #e5e5e5;
}

/* 操作按钮样式 - 参考检查点操作区域设计 */
.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 32rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  z-index: 999;
  box-sizing: border-box;
}

.form-action-button {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 88rpx;
  padding: 0 10rpx;
}

.cancel-action {
  background: #f8f9fa;
  border-radius: 8rpx 0 0 8rpx;
}

.submit-action {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 0 8rpx 8rpx 0;
  flex: 2; /* 提交按钮占更多空间 */
}

/* 按钮样式重置 - 确保按钮完全填充容器 */
.cancel-btn {
  width: 100% !important;
  height: 88rpx !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  color: #646566 !important;
  font-size: 30rpx !important;
  font-weight: 500 !important;
  box-shadow: none !important;
}

.submit-btn {
  width: 100% !important;
  height: 88rpx !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  color: #ffffff !important;
  font-size: 30rpx !important;
  font-weight: 600 !important;
  box-shadow: none !important;
}

/* ==================== 弹窗样式（已合并到后面的弹窗样式优化部分） ==================== */



/* ==================== 检查点编辑样式优化（与编辑页面一致） ==================== */
.checkpoint-section {
  padding: 0;
}

/* 统计信息卡片 */
.checkpoint-stats-card {
  margin: 16rpx 32rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  border: 1rpx solid #f0f0f0;
}

.stats-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-main {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.stats-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #1890ff;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

.stats-detail {
  flex: 1;
  margin-left: 20rpx;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.completed-count {
  font-size: 22rpx;
  color: #52c41a;
  font-weight: 500;
  min-width: 80rpx;
}

.progress-bar {
  flex: 1;
  height: 6rpx;
  background-color: #f0f0f0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

/* 检查点列表卡片 */
.checkpoint-list-card {
  margin: 16rpx 32rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}

.checkpoint-item {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  border-bottom: 1rpx solid #f8f9fa;
  gap: 12rpx;
  transition: background-color 0.2s ease;
}

.checkpoint-item:last-child {
  border-bottom: none;
}

.checkpoint-item:active {
  background-color: #f8f9fa;
}

.checkpoint-item.dragging {
  background-color: #e6f7ff;
  border: 2rpx solid #1890ff;
  transform: scale(1.02);
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.2);
  z-index: 10;
}

.drag-handle {
  flex-shrink: 0;
  padding: 8rpx;
  cursor: grab;
  transition: all 0.2s ease;
  border-radius: 4rpx;
}

.drag-handle:active {
  cursor: grabbing;
  background-color: #f0f8ff;
}

.checkpoint-item.dragging .drag-handle {
  background-color: #e6f7ff;
}

.checkpoint-checkbox {
  flex-shrink: 0;
  --van-checkbox-size: 36rpx;
  --van-checkbox-checked-icon-color: #52c41a;
}

.checkpoint-content {
  flex: 1;
  min-width: 0;
}

.checkpoint-field {
  --van-field-padding: 12rpx 0;
  --van-field-input-text-color: #333;
  --van-field-placeholder-text-color: #999;
  --van-field-input-font-size: 28rpx;
}

/* 检查点文字包装器 */
.checkpoint-text-wrapper {
  position: relative;
}

/* 已完成检查点的样式 */
.checkpoint-text-wrapper.completed-wrapper {
  position: relative;
}

/* 已完成状态下的文字颜色 */
.completed-wrapper .checkpoint-field {
  --van-field-input-text-color: #999 !important;
  opacity: 0.7;
}

/* 删除线效果 */
.completed-wrapper::after {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  right: 0;
  height: 2rpx;
  background-color: #999;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 1;
}

.checkpoint-actions {
  flex-shrink: 0;
}

.delete-action {
  padding: 8rpx;
  color: #ff4d4f;
  transition: all 0.2s ease;
  border-radius: 50%;
}

.delete-action:active {
  color: #ffffff;
  background-color: #ff4d4f;
}

/* 添加检查点卡片 */
.add-checkpoint-card {
  margin: 16rpx 32rpx;
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}

/* 默认触发状态 */
.add-checkpoint-trigger {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-checkpoint-trigger:active {
  background-color: #f8f9fa;
}

.trigger-text {
  font-size: 28rpx;
  color: #1890ff;
  font-weight: 500;
}

/* 展开状态 */
.add-checkpoint-expanded {
  padding: 20rpx 20rpx 24rpx 20rpx;
  background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
  border: 1rpx solid #e6f7ff;
}

.add-input-section {
  margin-bottom: 16rpx;
}

.add-checkpoint-field {
  --van-field-border-color: #d9d9d9;
  --van-field-background-color: #ffffff;
  --van-field-padding: 14rpx 16rpx;
  --van-field-input-font-size: 28rpx;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 1rpx 4rpx rgba(0,0,0,0.04);
}

/* 检查点操作区域 - 参考GoodsAction组件设计 */
.checkpoint-actions {
  display: flex;
  margin-top: 16rpx;
  background: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  overflow: hidden;
}

.checkpoint-action-button {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80rpx;
  padding: 12rpx;
}

.save-action {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.cancel-action {
  background: #f8f9fa;
  border-left: 1rpx solid #f0f0f0;
}

/* 按钮样式重置 - 确保按钮完全填充容器 */
.save-checkpoint-btn {
  width: 100% !important;
  height: 56rpx !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  color: #ffffff !important;
  font-size: 26rpx !important;
  font-weight: 600 !important;
  box-shadow: none !important;
}

.cancel-checkpoint-btn {
  width: 100% !important;
  height: 56rpx !important;
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  color: #666 !important;
  font-size: 26rpx !important;
  font-weight: 500 !important;
  box-shadow: none !important;
}

/* ==================== 日期时间选择器样式 ==================== */
.date-picker-popup,
.time-picker-popup {
  --van-popup-background-color: #fff;
  --van-popup-border-radius: 24rpx 24rpx 0 0;
  --van-popup-z-index: 10001;
  --van-overlay-z-index: 10000;
  z-index: 10001 !important;
}

.datetime-picker {
  --van-picker-toolbar-height: 88rpx;
  --van-picker-option-font-size: 32rpx;
  --van-picker-option-text-color: #333;
  --van-picker-confirm-action-color: #1989fa;
  --van-picker-cancel-action-color: #666;
}

.datetime-picker {
  --van-picker-toolbar-height: 88rpx;
  --van-picker-option-font-size: 32rpx;
  --van-picker-option-text-color: #333;
  --van-picker-confirm-action-color: #1989fa;
  --van-picker-cancel-action-color: #666;
}

/* ==================== 弹窗样式优化 ==================== */
.exam-action-sheet,
.subject-action-sheet,
.priority-action-sheet,
.reminder-action-sheet,
.duration-action-sheet {
  --van-action-sheet-header-font-size: 32rpx;
  --van-action-sheet-header-color: #333;
  --van-action-sheet-header-font-weight: 600;
  --van-action-sheet-z-index: 10001;
  --van-overlay-z-index: 10000;
  z-index: 10001 !important;
  background: #fff;
  border-radius: 16rpx 16rpx 0 0;
}

.exam-options,
.subject-options,
.priority-options,
.reminder-options,
.duration-options {
  max-height: 60vh;
  overflow-y: auto;
  position: relative;
  z-index: 10002;
  padding: 0 32rpx 32rpx;
}

.exam-option-cell,
.subject-option-cell,
.priority-option-cell,
.reminder-option-cell,
.duration-option-cell {
  --van-cell-title-font-size: 28rpx;
  --van-cell-title-color: #333;
  --van-cell-label-font-size: 24rpx;
  --van-cell-label-color: #666;
  --van-cell-border-color: #f0f0f0;
  --van-cell-padding: 24rpx 32rpx;
}

.clear-option {
  --van-cell-title-color: #ff4d4f;
}

.priority-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.priority-icon {
  font-size: 24rpx;
}

.priority-text {
  font-size: 28rpx;
  color: #333;
}

.reminder-action-sheet {
  --van-action-sheet-header-font-size: 32rpx;
  --van-action-sheet-header-color: #333;
  --van-action-sheet-item-font-size: 28rpx;
  --van-action-sheet-item-height: 88rpx;
}

.empty-exams {
  padding: 40rpx 32rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 375px) {
  .task-form-container {
    padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
  }

  .form-content {
    padding: 12rpx;
  }

  .form-section {
    --van-cell-group-title-padding: 20rpx 24rpx 12rpx;
    margin-bottom: 16rpx;
  }

  .form-cell {
    --van-cell-padding: 20rpx 24rpx;
  }

  .form-field {
    --van-field-padding: 20rpx 24rpx;
  }

  .form-actions {
    padding: 16rpx 24rpx;
    gap: 16rpx;
  }

  .checkpoint-stats-card,
  .checkpoint-list-card,
  .add-checkpoint-card {
    margin: 12rpx 24rpx;
  }
}

/* ==================== 暗黑模式适配 ==================== */
@media (prefers-color-scheme: dark) {
  .task-form-container {
    background: #1a1a1a;
  }

  .loading-content {
    background: #2d2d2d;
    color: #ffffff;
  }

  .form-section {
    --van-cell-group-background-color: #2d2d2d;
    --van-cell-group-title-color: #ffffff;
  }

  .form-field {
    --van-field-label-color: #ffffff;
    --van-field-input-text-color: #ffffff;
    --van-field-placeholder-text-color: #888888;
    --van-field-border-color: #404040;
  }

  .form-cell {
    --van-cell-title-color: #ffffff;
    --van-cell-value-color: #cccccc;
    --van-cell-border-color: #404040;
  }

  .checkpoint-stats-card,
  .checkpoint-list-card,
  .add-checkpoint-card {
    background: #2d2d2d;
    border-color: #404040;
  }

  .stats-number {
    color: #40a9ff;
  }

  .stats-label,
  .completed-count {
    color: #cccccc;
  }

  .checkpoint-field {
    --van-field-input-text-color: #ffffff;
    --van-field-placeholder-text-color: #888888;
  }

  .add-checkpoint-field {
    --van-field-input-text-color: #ffffff;
    --van-field-placeholder-text-color: #888888;
    --van-field-background-color: #404040;
    --van-field-border-color: #555555;
  }

  .trigger-text {
    color: #40a9ff;
  }

  .form-actions {
    background: #2d2d2d;
    border-top-color: #404040;
  }
}

/* Vant组件全局样式设置 */
van-popup,
van-action-sheet,
van-dialog,
van-overlay {
  z-index: 10001 !important;
}

van-overlay {
  z-index: 10000 !important;
}

/* 确保所有弹窗组件都有正确的层级 */
.van-popup,
.van-action-sheet,
.van-dialog {
  z-index: 10001 !important;
}

.van-overlay {
  z-index: 10000 !important;
}

/* 额外的弹窗层级保证 */
van-popup[show="true"],
van-action-sheet[show="true"] {
  z-index: 10001 !important;
}

van-popup[show="true"] + van-overlay,
van-action-sheet[show="true"] + van-overlay {
  z-index: 10000 !important;
}
