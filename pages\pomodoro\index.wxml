<!--pages/pomodoro/index.wxml - Vant重构版本-->
<view class="container {{focusMode ? 'focus-mode' : ''}}">

  <!-- 普通模式布局 -->
  <view class="normal-layout" wx:if="{{!focusMode}}">

    <!-- 顶部区域已移除 -->
    
    <!-- 任务信息区域 -->
    <van-cell-group custom-class="task-info-section">
      <van-cell 
        title="{{selectedTask ? selectedTask.title : '点击选择复习任务'}}" 
        label="{{selectedTask ? ('🍅 ' + (selectedTask.completedPomodoros || 0) + '/' + (selectedTask.totalPomodoros || 1)) : '或直接开始快速专注'}}"
        is-link 
        bind:click="showTaskSelector"
        custom-class="task-info-cell">
        <van-icon 
          slot="icon" 
          name="{{selectedTask ? 'completed' : 'plus'}}" 
          size="20" 
          color="{{selectedTask ? '#07c160' : '#969799'}}" />
      </van-cell>
    </van-cell-group>

    <!-- 个人签名区域 -->
    <view class="signature-section" wx:if="{{userSignature}}">
      <view class="signature-container">
        <van-icon name="quote" size="16" color="#1890ff" custom-class="signature-icon" />
        <text class="signature-text">{{userSignature}}</text>
        <van-icon name="quote" size="16" color="#1890ff" custom-class="signature-icon signature-icon-right" />
      </view>
      <view class="signature-author">— 致努力的自己</view>
    </view>

    <!-- 主计时器区域 -->
    <view class="main-timer-area">
      <!-- 计时器圆环 -->
      <view class="timer-circle {{isRunning ? 'breathing' : ''}}"
            style="background: conic-gradient({{timerColor}} 0deg {{progressDegree}}deg, rgba(0,0,0,0.1) {{progressDegree}}deg 360deg)"
            bindlongpress="enterFocusMode">
        <view class="timer-inner">
          <text class="time-display">{{displayTime}}</text>
          <text class="session-type">{{sessionTypeText}}</text>
          <view class="sound-info-wrapper">
            <van-tag 
              type="primary" 
              size="mini" 
              custom-class="sound-tag">{{currentSoundLabel}}</van-tag>
          </view>
        </view>
      </view>

      <!-- 主要控制按钮 -->
      <view class="primary-controls">
        <van-button 
          type="{{isRunning ? 'warning' : 'primary'}}" 
          size="large" 
          round 
          bind:click="toggleTimer"
          custom-class="main-control-btn">
          <van-icon 
            name="{{isRunning ? 'pause' : 'play'}}" 
            size="24" />
        </van-button>
        
        <van-button 
          wx:if="{{isRunning || isPaused}}" 
          type="danger" 
          size="large" 
          round 
          bind:click="stopTimer"
          custom-class="stop-btn">
          <van-icon name="stop" size="24" />
        </van-button>
      </view>
    </view>

    <!-- 底部快捷操作 -->
    <van-grid column-num="4" custom-class="bottom-actions">
      <van-grid-item 
        text="设置" 
        bind:click="showSettings"
        custom-class="action-item">
        <van-icon slot="icon" name="setting-o" size="24" />
      </van-grid-item>
      
      <van-grid-item 
        text="专注" 
        bind:click="enterFocusMode"
        custom-class="action-item">
        <van-icon slot="icon" name="eye-o" size="24" />
      </van-grid-item>
      
      <van-grid-item 
        text="声音" 
        bind:click="showSoundSettings"
        custom-class="action-item">
        <van-icon slot="icon" name="volume-o" size="24" />
      </van-grid-item>
      
      <van-grid-item 
        text="统计" 
        bind:click="viewStatistics"
        custom-class="action-item">
        <van-icon slot="icon" name="bar-chart-o" size="24" />
      </van-grid-item>
    </van-grid>

    <!-- 今日统计 -->
    <van-panel 
      title="今日专注" 
      custom-class="stats-panel">
      <van-grid column-num="3" custom-class="stats-grid">
        <van-grid-item 
          text="完成番茄钟" 
          custom-class="stats-item">
          <view slot="icon" class="stats-value">{{completedSessions}}</view>
        </van-grid-item>
        
        <van-grid-item 
          text="专注时长" 
          custom-class="stats-item">
          <view slot="icon" class="stats-value">{{todayFocusTime}}</view>
        </van-grid-item>
        
        <van-grid-item 
          text="完成任务" 
          custom-class="stats-item">
          <view slot="icon" class="stats-value">{{todayCompletedTasks}}</view>
        </van-grid-item>
      </van-grid>
    </van-panel>
  </view>

  <!-- 专注模式布局 -->
  <view class="focus-layout" wx:if="{{focusMode}}">
    <!-- 专注模式头部 -->
    <van-nav-bar 
      title="🌙 深度专注" 
      left-arrow 
      bind:click-left="exitFocusMode"
      custom-class="focus-nav-bar"
      title-class="focus-nav-title" />

    <!-- 专注模式主体 -->
    <view class="focus-main">
      <!-- 专注模式计时器 -->
      <view class="focus-timer-display">
        <view class="focus-timer-circle {{isRunning ? 'breathing' : ''}}"
              style="background: conic-gradient({{timerColor}} 0deg {{progressDegree}}deg, rgba(255,255,255,0.1) {{progressDegree}}deg 360deg)">
          <view class="focus-timer-inner">
            <text class="focus-time-text">{{displayTime}}</text>
            <text class="focus-session-indicator">{{sessionIndicator}}</text>
          </view>
        </view>
      </view>

      <!-- 专注模式任务信息 -->
      <van-card 
        wx:if="{{selectedTask}}" 
        title="{{selectedTask.title}}" 
        desc="专注复习中..." 
        custom-class="focus-task-card" />

      <!-- 专注模式控制 -->
      <view class="focus-controls">
        <van-button 
          type="{{isRunning ? 'warning' : 'primary'}}" 
          size="large" 
          round 
          bind:click="toggleTimer"
          custom-class="focus-control-btn">
          <van-icon 
            name="{{isRunning ? 'pause' : 'play'}}" 
            size="28" />
        </van-button>
        
        <van-button 
          type="danger" 
          size="large" 
          round 
          bind:click="stopTimer"
          custom-class="focus-stop-btn">
          <van-icon name="stop" size="28" />
        </van-button>
      </view>
    </view>
  </view>
</view>

<!-- 声音设置弹窗 -->
<van-popup 
  show="{{showSoundModal}}" 
  position="bottom" 
  custom-style="height: 70%; z-index: 9999;" 
  bind:close="hideSoundModal"
  custom-class="sound-popup"
  z-index="9999">
  <view class="popup-content">
    <van-nav-bar 
      title="声音设置" 
      left-arrow 
      bind:click-left="hideSoundModal"
      custom-class="popup-nav-bar" />
    
    <view class="popup-body">
      <!-- 背景音选择 -->
      <van-divider content-position="left">背景音</van-divider>
      <van-grid column-num="2" custom-class="sound-grid">
        <van-grid-item 
          wx:for="{{backgroundSounds}}"
          wx:for-item="sound"
          wx:key="id"
          text="{{sound.name}}"
          bind:click="selectBackgroundSound"
          data-sound="{{sound.id}}"
          custom-class="sound-item {{selectedBgSound === sound.id ? 'active' : ''}}">
          <view slot="icon" class="sound-icon">{{sound.icon}}</view>
        </van-grid-item>
      </van-grid>

      <!-- 音量控制 -->
      <van-divider content-position="left">音量</van-divider>
      <van-cell-group>
        <van-cell title="背景音音量" value="{{bgVolume}}%">
          <van-slider 
            slot="right-icon" 
            value="{{bgVolume}}" 
            min="0" 
            max="100" 
            bind:change="adjustBgVolume"
            custom-class="volume-slider" />
        </van-cell>
      </van-cell-group>
    </view>
  </view>
</van-popup>

<!-- 设置弹窗 -->
<van-popup 
  show="{{showSettingsModal}}" 
  position="bottom" 
  custom-style="height: 80%; z-index: 9999;" 
  bind:close="hideSettings"
  custom-class="settings-popup"
  z-index="9999">
  <view class="popup-content">
    <van-nav-bar 
      title="设置" 
      left-arrow 
      bind:click-left="hideSettings"
      custom-class="popup-nav-bar" />
    
    <view class="popup-body">
      <!-- 时间设置 -->
      <van-divider content-position="left">时间设置</van-divider>
      <van-cell-group>
        <van-cell title="工作时长" value="{{workDuration}}分钟">
          <van-stepper 
            slot="right-icon" 
            value="{{workDuration}}" 
            min="1" 
            max="60" 
            bind:change="changeWorkDuration"
            custom-class="time-stepper" />
        </van-cell>
        
        <van-cell title="短休息" value="{{shortBreakDuration}}分钟">
          <van-stepper 
            slot="right-icon" 
            value="{{shortBreakDuration}}" 
            min="1" 
            max="30" 
            bind:change="changeShortBreakDuration"
            custom-class="time-stepper" />
        </van-cell>
        
        <van-cell title="长休息" value="{{longBreakDuration}}分钟">
          <van-stepper 
            slot="right-icon" 
            value="{{longBreakDuration}}" 
            min="1" 
            max="60" 
            bind:change="changeLongBreakDuration"
            custom-class="time-stepper" />
        </van-cell>
      </van-cell-group>

      <!-- 通知设置 -->
      <van-divider content-position="left">通知设置</van-divider>
      <van-cell-group>
        <van-cell title="震动提醒">
          <van-switch 
            slot="right-icon" 
            checked="{{enableVibration}}" 
            bind:change="toggleVibration" />
        </van-cell>
        
        <van-cell title="通知提醒">
          <van-switch 
            slot="right-icon" 
            checked="{{enableNotification}}" 
            bind:change="toggleNotification" />
        </van-cell>
        
        <van-cell title="自动开始休息">
          <van-switch 
            slot="right-icon" 
            checked="{{autoStartBreak}}" 
            bind:change="toggleAutoStartBreak" />
        </van-cell>
        
        <van-cell title="自动开始工作">
          <van-switch 
            slot="right-icon" 
            checked="{{autoStartWork}}" 
            bind:change="toggleAutoStartWork" />
        </van-cell>
      </van-cell-group>
    </view>
  </view>
</van-popup>

<!-- 任务选择弹窗 -->
<van-action-sheet 
  show="{{showTaskSelector}}" 
  title="选择复习任务" 
  actions="{{taskOptions}}" 
  bind:close="hideTaskSelector"
  bind:select="selectTask"
  custom-class="task-action-sheet"
  z-index="9999" />
