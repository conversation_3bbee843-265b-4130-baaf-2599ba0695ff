<!--pages/pomodoro/index.wxml - Vant重构版本-->
<view class="container {{focusMode ? 'focus-mode' : ''}}">



  <!-- 普通模式布局 -->
  <view class="normal-layout" wx:if="{{!focusMode}}">
    
    <!-- 任务选择和设置区域 -->
    <view class="task-settings-container">
      <view class="task-content" bind:tap="showTaskSelector">
        <van-icon
          name="{{selectedTask ? 'completed' : 'plus'}}"
          size="20"
          color="{{selectedTask ? '#07c160' : '#969799'}}"
          custom-class="task-icon" />
        <view class="task-info">
          <view class="task-title">{{selectedTask ? selectedTask.title : '点击选择复习任务'}}</view>
          <view class="task-label">{{selectedTask ? ('🍅 ' + (selectedTask.completedPomodoros || 0) + '/' + (selectedTask.totalPomodoros || 1)) : '或直接开始快速专注'}}</view>
        </view>
        <van-icon name="arrow" size="16" color="#c8c9cc" custom-class="arrow-icon" />
      </view>

      <view class="settings-controls">
        <view class="quick-mode-switch">
          <van-switch
            checked="{{studyMode === 'quick'}}"
            bind:change="toggleQuickMode"
            size="20"
            custom-class="mode-switch" />
          <text class="switch-label">快速</text>
        </view>
        <view class="settings-icon-wrapper" catchtap="showSettings">
          <van-icon
            name="setting-o"
            size="20"
            color="#969799" />
        </view>
      </view>
    </view>

    <!-- 个人签名区域 -->
    <view class="signature-section" wx:if="{{userSignature}}">
      <view class="signature-container">
        <van-icon name="quote" size="16" color="#1890ff" custom-class="signature-icon" />
        <text class="signature-text">{{userSignature}}</text>
        <van-icon name="quote" size="16" color="#1890ff" custom-class="signature-icon signature-icon-right" />
      </view>
    </view>

    <!-- 主计时器区域 -->
    <view class="main-timer-area">
      <!-- 计时器圆环 -->
      <view class="timer-circle {{isRunning ? 'breathing' : ''}} {{timeNodeHighlight ? 'time-node-highlight' : ''}}"
            style="background: conic-gradient(var({{timerColorVar}}) 0deg {{progressDegree}}deg, rgba(0,0,0,0.1) {{progressDegree}}deg 360deg)"
            bindlongpress="enterFocusMode"
            bindtouchstart="onTimerTouchStart"
            bindtouchmove="onTimerTouchMove"
            bindtouchend="onTimerTouchEnd">
        <view class="timer-inner">
          <text class="time-display">{{displayTime}}</text>
          <text class="session-type">{{sessionTypeText}}</text>
          <view class="sound-info-wrapper">
            <van-tag
              type="primary"
              size="mini"
              custom-class="sound-tag">{{currentSoundLabel}}</van-tag>
          </view>
          <!-- 时间节点提示 -->
          <view class="time-nodes" wx:if="{{!isRunning}}">
            <view class="time-node"
                  wx:for="{{timeNodes}}"
                  wx:key="value"
                  style="transform: rotate({{item.angle}}deg)"
                  data-time="{{item.value}}"
                  bindtap="setTimerDuration">
              <view class="node-dot {{item.value === currentTime/60 ? 'active' : ''}}"></view>
            </view>
          </view>
        </view>
        <!-- 手势提示 -->
        <view class="gesture-hint" wx:if="{{showGestureHint}}">
          <text class="hint-text">{{gestureHintText}}</text>
        </view>
      </view>

      <!-- 主要控制按钮 -->
      <view class="primary-controls">
        <van-button 
          type="{{isRunning ? 'warning' : 'primary'}}" 
          size="large" 
          round 
          bind:click="toggleTimer"
          custom-class="main-control-btn">
          <van-icon 
            name="{{isRunning ? 'pause' : 'play'}}" 
            size="24" />
        </van-button>
        
        <van-button
          wx:if="{{isRunning || isPaused}}"
          type="danger"
          size="large"
          round
          bind:click="stopTimer"
          custom-class="stop-btn">
          <van-icon name="stop" size="24" />
        </van-button>

        <van-button
          wx:if="{{isRunning}}"
          type="info"
          size="large"
          round
          bind:click="enterFocusMode"
          custom-class="focus-mode-btn">
          <van-icon name="eye-o" size="20" />
        </van-button>
      </view>
    </view>



    <!-- 统计信息面板 -->
    <view class="stats-panel-container">
      <!-- 时间范围切换 -->
      <view class="stats-header">
        <text class="stats-title">📊 学习统计</text>
        <van-tabs
          active="{{currentStatsTab}}"
          bind:change="onStatsTabChange"
          custom-class="stats-tabs"
          tab-class="stats-tab"
          tab-active-class="stats-tab-active">
          <van-tab title="今日" name="today"></van-tab>
          <van-tab title="本周" name="week"></van-tab>
          <van-tab title="本月" name="month"></van-tab>
        </van-tabs>
      </view>

      <!-- 统计卡片 -->
      <view class="stats-content">
        <view class="stats-grid">
          <view class="stat-card" wx:for="{{pomodoroStats}}" wx:key="label">
            <view class="stat-content">
              <view class="stat-value">{{item.value}}</view>
              <view class="stat-label">{{item.label}}</view>
            </view>
          </view>
        </view>



        <!-- 数据对比卡片 -->
        <view class="comparison-cards">
          <view class="comparison-header">
            <text class="comparison-title">📊 数据对比</text>
            <van-button
              size="mini"
              type="default"
              bind:click="switchComparisonPeriod"
              custom-class="switch-period-btn">
              {{comparisonPeriod === 'daily' ? '今日vs昨日' : '本周vs上周'}}
            </van-button>
          </view>

          <view class="comparison-content">
            <view class="comparison-item" wx:for="{{comparisonData}}" wx:key="label">
              <view class="comparison-label">{{item.label}}</view>
              <view class="comparison-values">
                <view class="current-value">
                  <text class="value-number">{{item.current}}</text>
                  <text class="value-period">{{comparisonPeriod === 'daily' ? '今日' : '本周'}}</text>
                </view>
                <view class="comparison-arrow {{item.trend}}">
                  <text class="arrow-icon">{{item.trend === 'up' ? '↗' : item.trend === 'down' ? '↘' : '→'}}</text>
                  <text class="trend-text">{{item.changeText}}</text>
                </view>
                <view class="previous-value">
                  <text class="value-number">{{item.previous}}</text>
                  <text class="value-period">{{comparisonPeriod === 'daily' ? '昨日' : '上周'}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 趋势图表 -->
        <view class="trend-chart-container" wx:if="{{showTrendChart}}">
          <view class="chart-header">
            <text class="chart-title">📈 专注趋势</text>
            <view class="chart-controls">
              <van-button
                size="mini"
                type="{{chartTimeRange === 'week' ? 'primary' : 'default'}}"
                bind:click="setChartTimeRange"
                data-range="week"
                custom-class="time-range-btn">
                7天
              </van-button>
              <van-button
                size="mini"
                type="{{chartTimeRange === 'month' ? 'primary' : 'default'}}"
                bind:click="setChartTimeRange"
                data-range="month"
                custom-class="time-range-btn">
                30天
              </van-button>
              <van-button
                size="mini"
                type="default"
                bind:click="exportChartImage"
                custom-class="export-btn">
                📷
              </van-button>
              <van-button
                size="mini"
                type="default"
                bind:click="hideTrendChart"
                custom-class="chart-close-btn">
                收起
              </van-button>
            </view>
          </view>

          <view class="chart-area">
            <view class="chart-y-axis">
              <text class="y-label">60</text>
              <text class="y-label">45</text>
              <text class="y-label">30</text>
              <text class="y-label">15</text>
              <text class="y-label">0</text>
            </view>

            <view class="chart-content">
              <view class="chart-bars">
                <view class="bar-group" wx:for="{{trendData}}" wx:key="date">
                  <view class="bar focus-bar"
                        style="height: {{item.focusHeight}}%; background-color: {{item.color}}"></view>
                </view>
              </view>

              <view class="chart-x-axis">
                <text class="x-label" wx:for="{{trendData}}" wx:key="date">{{item.label}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 专注模式布局 -->
  <view class="focus-layout" wx:if="{{focusMode}}" bindtap="onFocusModeTap">
    <!-- 专注模式头部 -->
    <van-nav-bar
      title="🌙 深度专注"
      left-arrow
      bind:click-left="exitFocusMode"
      custom-class="focus-nav-bar"
      title-class="focus-nav-title">
      <view slot="right" class="focus-nav-right">
        <text class="exit-hint">双击屏幕退出</text>
      </view>
    </van-nav-bar>

    <!-- 专注模式主体 -->
    <view class="focus-main">
      <!-- 激励文案 -->
      <view class="focus-motivation" wx:if="{{focusMotivation}}">
        <text class="motivation-text">{{focusMotivation}}</text>
      </view>

      <!-- 专注模式计时器 -->
      <view class="focus-timer-display">
        <view class="focus-timer-circle {{isRunning ? 'breathing' : ''}} {{milestoneAnimation ? 'milestone-glow' : ''}}"
              style="background: conic-gradient(var({{timerColorVar}}) 0deg {{progressDegree}}deg, rgba(255,255,255,0.1) {{progressDegree}}deg 360deg)">
          <view class="focus-timer-inner">
            <text class="focus-time-text">{{displayTime}}</text>
            <text class="focus-session-indicator">{{sessionIndicator}}</text>
            <!-- 里程碑提示 -->
            <view class="milestone-indicator" wx:if="{{showMilestone}}">
              <text class="milestone-text">{{milestoneText}}</text>
            </view>
          </view>
          <!-- 进度环 -->
          <view class="progress-rings">
            <view class="progress-ring" wx:for="{{progressRings}}" wx:key="index"
                  style="animation-delay: {{item.delay}}s; opacity: {{item.opacity}}"></view>
          </view>
        </view>
      </view>

      <!-- 专注模式任务信息 -->
      <van-card
        wx:if="{{selectedTask}}"
        title="{{selectedTask.title}}"
        desc="{{focusTaskDesc}}"
        custom-class="focus-task-card">
        <view slot="footer" class="task-progress">
          <van-progress
            percentage="{{taskProgress}}"
            color="{{timerColor}}"
            track-color="rgba(255,255,255,0.2)"
            custom-class="focus-progress" />
          <text class="progress-text">{{taskProgressText}}</text>
        </view>
      </van-card>

      <!-- 专注统计 -->
      <view class="focus-stats">
        <view class="stat-item">
          <text class="stat-value">{{todayFocusTime}}</text>
          <text class="stat-label">今日专注</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{completedSessions}}</text>
          <text class="stat-label">完成番茄</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{currentSessionTime}}</text>
          <text class="stat-label">本次时长</text>
        </view>
      </view>

      <!-- 专注模式控制 -->
      <view class="focus-controls">
        <van-button
          type="{{isRunning ? 'warning' : 'primary'}}"
          size="large"
          round
          bind:click="toggleTimer"
          custom-class="focus-control-btn">
          <van-icon
            name="{{isRunning ? 'pause' : 'play'}}"
            size="28" />
          <text class="btn-text">{{isRunning ? '暂停' : '开始'}}</text>
        </van-button>

        <van-button
          type="danger"
          size="large"
          round
          bind:click="stopTimer"
          custom-class="focus-stop-btn">
          <van-icon name="stop" size="28" />
          <text class="btn-text">结束</text>
        </van-button>
      </view>

      <!-- 环境音效控制 -->
      <view class="focus-audio-control">
        <van-slider
          value="{{bgVolume}}"
          min="0"
          max="100"
          bind:change="onVolumeChange"
          active-color="rgba(255,255,255,0.8)"
          inactive-color="rgba(255,255,255,0.3)"
          custom-class="focus-volume-slider" />
        <view class="audio-info">
          <van-icon name="volume-o" size="20" color="rgba(255,255,255,0.8)" />
          <text class="audio-label">{{currentSoundLabel}}</text>
          <van-button
            size="mini"
            type="default"
            bind:click="showSoundSelector"
            custom-class="sound-switch-btn">
            切换
          </van-button>
        </view>
      </view>
    </view>

    <!-- 里程碑庆祝动画 -->
    <view class="milestone-celebration" wx:if="{{showCelebration}}">
      <view class="celebration-content">
        <text class="celebration-title">🎉 {{celebrationTitle}}</text>
        <text class="celebration-message">{{celebrationMessage}}</text>
        <view class="celebration-effects">
          <view class="effect-particle" wx:for="{{celebrationParticles}}" wx:key="index"
                style="left: {{item.x}}%; top: {{item.y}}%; animation-delay: {{item.delay}}s"></view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 声音设置弹窗 -->
<van-popup 
  show="{{showSoundModal}}" 
  position="bottom" 
  custom-style="height: 70%; z-index: 9999;" 
  bind:close="hideSoundModal"
  custom-class="sound-popup"
  z-index="9999">
  <view class="popup-content">
    <van-nav-bar 
      title="声音设置" 
      left-arrow 
      bind:click-left="hideSoundModal"
      custom-class="popup-nav-bar" />
    
    <view class="popup-body">
      <!-- 背景音选择 -->
      <van-divider content-position="left">背景音</van-divider>
      <van-grid column-num="2" custom-class="sound-grid">
        <van-grid-item 
          wx:for="{{backgroundSounds}}"
          wx:for-item="sound"
          wx:key="id"
          text="{{sound.name}}"
          bind:click="selectBackgroundSound"
          data-sound="{{sound.id}}"
          custom-class="sound-item {{selectedBgSound === sound.id ? 'active' : ''}}">
          <view slot="icon" class="sound-icon">{{sound.icon}}</view>
        </van-grid-item>
      </van-grid>

      <!-- 音量控制 -->
      <van-divider content-position="left">音量</van-divider>
      <van-cell-group>
        <van-cell title="背景音音量" value="{{bgVolume}}%">
          <van-slider 
            slot="right-icon" 
            value="{{bgVolume}}" 
            min="0" 
            max="100" 
            bind:change="adjustBgVolume"
            custom-class="volume-slider" />
        </van-cell>
      </van-cell-group>
    </view>
  </view>
</van-popup>

<!-- 设置弹窗 -->
<van-popup
  show="{{showSettingsModal}}"
  position="bottom"
  custom-style="height: 85%; z-index: 9999;"
  bind:close="hideSettings"
  custom-class="settings-popup"
  z-index="9999">
  <view class="popup-content">
    <van-nav-bar
      title="设置"
      left-arrow
      bind:click-left="hideSettings"
      custom-class="popup-nav-bar">
      <view slot="right" class="nav-actions">
        <van-button
          size="mini"
          type="primary"
          bind:click="showPresetConfigs"
          custom-class="preset-btn">
          预设
        </van-button>
      </view>
    </van-nav-bar>

    <!-- 智能推荐 -->
    <view class="recommendation-banner" wx:if="{{showRecommendation}}">
      <van-notice-bar
        text="{{recommendationText}}"
        mode="closeable"
        bind:close="hideRecommendation"
        custom-class="recommendation-notice" />
    </view>

    <view class="popup-body">
      <van-collapse
        value="{{activeSettingsTab}}"
        bind:change="onSettingsTabChange"
        custom-class="settings-collapse">

        <!-- 时间设置 -->
        <van-collapse-item
          title="⏰ 时间设置"
          name="time"
          custom-class="settings-section">
          <view class="section-content">
            <van-cell-group custom-class="settings-group">
              <van-cell title="工作时长" value="{{workDuration}}分钟" label="专注工作的时间长度">
                <van-stepper
                  slot="right-icon"
                  value="{{workDuration}}"
                  min="5"
                  max="120"
                  step="5"
                  bind:change="changeWorkDuration"
                  custom-class="time-stepper" />
              </van-cell>

              <van-cell title="短休息" value="{{shortBreakDuration}}分钟" label="工作间隙的短暂休息">
                <van-stepper
                  slot="right-icon"
                  value="{{shortBreakDuration}}"
                  min="1"
                  max="30"
                  bind:change="changeShortBreakDuration"
                  custom-class="time-stepper" />
              </van-cell>

              <van-cell title="长休息" value="{{longBreakDuration}}分钟" label="完成多个番茄钟后的长休息">
                <van-stepper
                  slot="right-icon"
                  value="{{longBreakDuration}}"
                  min="5"
                  max="60"
                  step="5"
                  bind:change="changeLongBreakDuration"
                  custom-class="time-stepper" />
              </van-cell>

              <van-cell title="长休息间隔" value="{{longBreakInterval}}个番茄钟" label="多少个番茄钟后进行长休息">
                <van-stepper
                  slot="right-icon"
                  value="{{longBreakInterval}}"
                  min="2"
                  max="8"
                  bind:change="changeLongBreakInterval"
                  custom-class="time-stepper" />
              </van-cell>
            </van-cell-group>
          </view>
        </van-collapse-item>

        <!-- 声音设置 -->
        <van-collapse-item
          title="🔊 声音设置"
          name="sound"
          custom-class="settings-section">
          <view class="section-content">
            <van-cell-group custom-class="settings-group">
              <van-cell
                title="背景音效"
                value="{{currentSoundLabel}}"
                label="专注时播放的背景音"
                is-link
                bind:click="showSoundCategorySelector">
              </van-cell>

              <van-cell title="音量" label="背景音效的音量大小">
                <view slot="right-icon" class="volume-control">
                  <van-slider
                    value="{{bgVolume}}"
                    min="0"
                    max="100"
                    bind:change="onVolumeChange"
                    custom-class="volume-slider" />
                  <text class="volume-text">{{bgVolume}}%</text>
                </view>
              </van-cell>
            </van-cell-group>
          </view>
        </van-collapse-item>

        <!-- 通知设置 -->
        <van-collapse-item
          title="🔔 通知设置"
          name="notification"
          custom-class="settings-section">
          <view class="section-content">
            <van-cell-group custom-class="settings-group">
              <van-cell title="震动提醒" label="操作时的震动反馈">
                <van-switch
                  slot="right-icon"
                  checked="{{enableVibration}}"
                  bind:change="toggleVibration" />
              </van-cell>

              <van-cell title="通知提醒" label="时间到达时的系统通知">
                <van-switch
                  slot="right-icon"
                  checked="{{enableNotification}}"
                  bind:change="toggleNotification" />
              </van-cell>

              <van-cell title="自动开始休息" label="工作结束后自动开始休息">
                <van-switch
                  slot="right-icon"
                  checked="{{autoStartBreak}}"
                  bind:change="toggleAutoStartBreak" />
              </van-cell>

              <van-cell title="自动开始工作" label="休息结束后自动开始工作">
                <van-switch
                  slot="right-icon"
                  checked="{{autoStartWork}}"
                  bind:change="toggleAutoStartWork" />
              </van-cell>
            </van-cell-group>
          </view>
        </van-collapse-item>

        <!-- 高级设置 -->
        <van-collapse-item
          title="⚙️ 高级设置"
          name="advanced"
          custom-class="settings-section">
          <view class="section-content">
            <van-cell-group custom-class="settings-group">
              <van-cell
                title="重置设置"
                label="恢复到默认配置"
                is-link
                bind:click="resetSettings">
                <van-icon slot="right-icon" name="replay" color="#ff6b6b" />
              </van-cell>

              <van-cell
                title="导出设置"
                label="备份当前配置"
                is-link
                bind:click="exportSettings">
                <van-icon slot="right-icon" name="share" color="#52c41a" />
              </van-cell>

              <van-cell
                title="导入设置"
                label="从备份恢复配置"
                is-link
                bind:click="importSettings">
                <van-icon slot="right-icon" name="upgrade" color="#1890ff" />
              </van-cell>
            </van-cell-group>
          </view>
        </van-collapse-item>
      </van-collapse>
    </view>
  </view>
</van-popup>

<!-- 预设配置弹窗 -->
<van-popup
  show="{{showPresetModal}}"
  position="bottom"
  custom-style="height: 60%; z-index: 10000;"
  bind:close="hidePresetModal"
  custom-class="preset-popup"
  z-index="10000">
  <view class="popup-content">
    <van-nav-bar
      title="选择预设配置"
      left-arrow
      bind:click-left="hidePresetModal"
      custom-class="popup-nav-bar" />

    <view class="popup-body">
      <view class="preset-list">
        <van-cell-group custom-class="preset-group">
          <van-cell
            wx:for="{{presetConfigs}}"
            wx:key="id"
            title="{{item.icon}} {{item.name}}"
            label="{{item.description}}"
            is-link
            clickable
            custom-class="preset-item"
            bind:click="applyPresetConfig"
            data-preset="{{item.id}}">
            <view slot="right-icon" class="preset-details">
              <text class="preset-time">{{item.config.workDuration}}-{{item.config.shortBreakDuration}}-{{item.config.longBreakDuration}}</text>
            </view>
          </van-cell>
        </van-cell-group>

        <view class="preset-tip">
          <van-notice-bar
            text="选择预设配置将覆盖当前设置，请确认后操作"
            mode="alert"
            custom-class="preset-warning" />
        </view>
      </view>
    </view>
  </view>
</van-popup>

<!-- 音效分类选择弹窗 -->
<van-popup
  show="{{showSoundCategoryModal}}"
  position="bottom"
  custom-style="height: 70%; z-index: 10000;"
  bind:close="hideSoundCategoryModal"
  custom-class="sound-category-popup"
  z-index="10000">
  <view class="popup-content">
    <van-nav-bar
      title="选择背景音效"
      left-arrow
      bind:click-left="hideSoundCategoryModal"
      custom-class="popup-nav-bar" />

    <view class="popup-body">
      <van-tabs
        active="{{activeSoundCategory}}"
        bind:change="onSoundCategoryChange"
        custom-class="sound-tabs">
        <van-tab
          wx:for="{{soundCategories}}"
          wx:key="id"
          title="{{item.icon}} {{item.name}}"
          name="{{item.id}}">
          <view class="sound-list">
            <van-cell-group custom-class="sound-group">
              <van-cell
                wx:for="{{filteredSounds}}"
                wx:for-item="sound"
                wx:key="id"
                title="{{sound.icon}} {{sound.name}}"
                label="{{sound.description}}"
                clickable
                custom-class="sound-item {{selectedBgSound === sound.id ? 'selected' : ''}}"
                bind:click="selectBackgroundSound"
                data-sound="{{sound.id}}">
                <van-icon
                  wx:if="{{selectedBgSound === sound.id}}"
                  slot="right-icon"
                  name="success"
                  color="#1890ff" />
              </van-cell>
            </van-cell-group>
          </view>
        </van-tab>
      </van-tabs>
    </view>
  </view>
</van-popup>

<!-- 任务选择弹窗 -->
<van-popup
  show="{{showTaskSelector}}"
  position="bottom"
  custom-style="height: 60%; z-index: 9999;"
  bind:close="hideTaskSelector"
  custom-class="task-selector-popup"
  z-index="9999">
  <view class="task-selector-content">
    <van-nav-bar
      title="选择复习任务"
      left-arrow
      bind:click-left="hideTaskSelector"
      custom-class="task-selector-nav" />

    <view class="task-list-container">
      <!-- 快速专注选项 -->
      <van-cell-group custom-class="quick-focus-group">
        <van-cell
          title="⚡ 快速专注"
          label="25分钟专注复习"
          clickable
          custom-class="task-option {{studyMode === 'quick' ? 'selected' : ''}}"
          bind:click="selectQuickMode">
          <van-icon
            wx:if="{{studyMode === 'quick'}}"
            slot="right-icon"
            name="success"
            color="#1890ff"
            size="20px" />
        </van-cell>
      </van-cell-group>

      <!-- 任务列表 -->
      <van-cell-group custom-class="task-list-group" wx:if="{{availableTasks.length > 0}}">
        <van-divider content-position="left">复习任务</van-divider>
        <van-cell
          wx:for="{{availableTasks}}"
          wx:key="id"
          title="{{item.title}}"
          label="{{item.subject || '通用任务'}}"
          value="🍅 {{item.completedPomodoros || 0}}/{{item.totalPomodoros || 1}}"
          clickable
          custom-class="task-option {{selectedTask && selectedTask.id === item.id ? 'selected' : ''}}"
          bind:click="selectTaskItem"
          data-task="{{item}}">
          <van-icon
            wx:if="{{selectedTask && selectedTask.id === item.id}}"
            slot="right-icon"
            name="success"
            color="#1890ff"
            size="20px" />
        </van-cell>
      </van-cell-group>

      <!-- 空状态 -->
      <view class="empty-tasks" wx:if="{{availableTasks.length === 0}}">
        <van-empty description="暂无复习任务" />
        <van-button
          type="primary"
          size="small"
          bind:click="goToTaskCenter">
          去添加任务
        </van-button>
      </view>
    </view>
  </view>
</van-popup>
