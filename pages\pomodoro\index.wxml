<!--pages/pomodoro/index.wxml - Vant重构版本-->
<view class="container {{focusMode ? 'focus-mode' : ''}}">

  <!-- 普通模式布局 -->
  <view class="normal-layout" wx:if="{{!focusMode}}">

    <!-- 顶部区域已移除 -->
    
    <!-- 任务信息区域 -->
    <van-cell-group custom-class="task-info-section">
      <van-cell
        title="{{selectedTask ? selectedTask.title : '点击选择复习任务'}}"
        label="{{selectedTask ? ('🍅 ' + (selectedTask.completedPomodoros || 0) + '/' + (selectedTask.totalPomodoros || 1)) : '或直接开始快速专注'}}"
        is-link
        bind:click="showTaskSelector"
        custom-class="task-info-cell">
        <van-icon
          slot="icon"
          name="{{selectedTask ? 'completed' : 'plus'}}"
          size="20"
          color="{{selectedTask ? '#07c160' : '#969799'}}" />
        <view slot="right-icon" class="quick-mode-switch">
          <van-switch
            checked="{{studyMode === 'quick'}}"
            bind:change="toggleQuickMode"
            size="20"
            custom-class="mode-switch" />
          <text class="switch-label">快速</text>
        </view>
      </van-cell>
    </van-cell-group>

    <!-- 个人签名区域 -->
    <view class="signature-section" wx:if="{{userSignature}}">
      <view class="signature-container">
        <van-icon name="quote" size="16" color="#1890ff" custom-class="signature-icon" />
        <text class="signature-text">{{userSignature}}</text>
        <van-icon name="quote" size="16" color="#1890ff" custom-class="signature-icon signature-icon-right" />
      </view>
      <view class="signature-author">— 致努力的自己</view>
    </view>

    <!-- 主计时器区域 -->
    <view class="main-timer-area">
      <!-- 计时器圆环 -->
      <view class="timer-circle {{isRunning ? 'breathing' : ''}} {{timeNodeHighlight ? 'time-node-highlight' : ''}}"
            style="background: conic-gradient(var({{timerColorVar}}) 0deg {{progressDegree}}deg, rgba(0,0,0,0.1) {{progressDegree}}deg 360deg)"
            bindlongpress="enterFocusMode"
            bindtouchstart="onTimerTouchStart"
            bindtouchmove="onTimerTouchMove"
            bindtouchend="onTimerTouchEnd">
        <view class="timer-inner">
          <text class="time-display">{{displayTime}}</text>
          <text class="session-type">{{sessionTypeText}}</text>
          <view class="sound-info-wrapper">
            <van-tag
              type="primary"
              size="mini"
              custom-class="sound-tag">{{currentSoundLabel}}</van-tag>
          </view>
          <!-- 时间节点提示 -->
          <view class="time-nodes" wx:if="{{!isRunning}}">
            <view class="time-node"
                  wx:for="{{timeNodes}}"
                  wx:key="value"
                  style="transform: rotate({{item.angle}}deg)"
                  data-time="{{item.value}}"
                  bindtap="setTimerDuration">
              <view class="node-dot {{item.value === currentTime/60 ? 'active' : ''}}"></view>
            </view>
          </view>
        </view>
        <!-- 手势提示 -->
        <view class="gesture-hint" wx:if="{{showGestureHint}}">
          <text class="hint-text">{{gestureHintText}}</text>
        </view>
      </view>

      <!-- 主要控制按钮 -->
      <view class="primary-controls">
        <van-button 
          type="{{isRunning ? 'warning' : 'primary'}}" 
          size="large" 
          round 
          bind:click="toggleTimer"
          custom-class="main-control-btn">
          <van-icon 
            name="{{isRunning ? 'pause' : 'play'}}" 
            size="24" />
        </van-button>
        
        <van-button 
          wx:if="{{isRunning || isPaused}}" 
          type="danger" 
          size="large" 
          round 
          bind:click="stopTimer"
          custom-class="stop-btn">
          <van-icon name="stop" size="24" />
        </van-button>
      </view>
    </view>

    <!-- 底部快捷操作 -->
    <van-grid column-num="4" custom-class="bottom-actions">
      <van-grid-item 
        text="设置" 
        bind:click="showSettings"
        custom-class="action-item">
        <van-icon slot="icon" name="setting-o" size="24" />
      </van-grid-item>
      
      <van-grid-item 
        text="专注" 
        bind:click="enterFocusMode"
        custom-class="action-item">
        <van-icon slot="icon" name="eye-o" size="24" />
      </van-grid-item>
      
      <van-grid-item 
        text="声音" 
        bind:click="showSoundSettings"
        custom-class="action-item">
        <van-icon slot="icon" name="volume-o" size="24" />
      </van-grid-item>
      
      <van-grid-item 
        text="统计" 
        bind:click="viewStatistics"
        custom-class="action-item">
        <van-icon slot="icon" name="bar-chart-o" size="24" />
      </van-grid-item>
    </van-grid>

    <!-- 统计信息面板 -->
    <van-card custom-class="stats-panel-card">
      <!-- 时间范围切换 -->
      <view slot="header" class="stats-header">
        <text class="stats-title">📊 学习统计</text>
        <van-tabs
          active="{{currentStatsTab}}"
          bind:change="onStatsTabChange"
          custom-class="stats-tabs"
          tab-class="stats-tab"
          tab-active-class="stats-tab-active">
          <van-tab title="今日" name="today"></van-tab>
          <van-tab title="本周" name="week"></van-tab>
          <van-tab title="本月" name="month"></van-tab>
        </van-tabs>
      </view>

      <!-- 统计卡片 -->
      <view class="stats-content">
        <van-grid
          column-num="2"
          border="{{false}}"
          gutter="16"
          custom-class="stats-grid">
          <van-grid-item
            wx:for="{{statCards}}"
            wx:key="id"
            custom-class="stat-card {{item.clickable ? 'clickable' : ''}}"
            bind:tap="onStatCardTap"
            data-type="{{item.id}}">
            <view class="stat-content">
              <view class="stat-icon" style="background-color: {{item.bgColor}}">
                <text>{{item.icon}}</text>
              </view>
              <text class="stat-value">{{item.value}}</text>
              <text class="stat-label">{{item.label}}</text>
              <view class="stat-trend" wx:if="{{item.trend}}">
                <van-tag
                  type="{{item.trend.type}}"
                  size="mini"
                  custom-class="trend-tag">
                  {{item.trend.icon}} {{item.trend.text}}
                </van-tag>
              </view>
            </view>
          </van-grid-item>
        </van-grid>

        <!-- 趋势图表 -->
        <view class="trend-chart-container" wx:if="{{showTrendChart}}">
          <view class="chart-header">
            <text class="chart-title">📈 专注趋势</text>
            <van-button
              size="mini"
              type="default"
              bind:click="hideTrendChart"
              custom-class="chart-close-btn">
              收起
            </van-button>
          </view>

          <view class="chart-area">
            <view class="chart-y-axis">
              <text class="y-label">60</text>
              <text class="y-label">45</text>
              <text class="y-label">30</text>
              <text class="y-label">15</text>
              <text class="y-label">0</text>
            </view>

            <view class="chart-content">
              <view class="chart-bars">
                <view class="bar-group" wx:for="{{trendData}}" wx:key="date">
                  <view class="bar focus-bar"
                        style="height: {{item.focusHeight}}%; background-color: {{item.color}}"></view>
                </view>
              </view>

              <view class="chart-x-axis">
                <text class="x-label" wx:for="{{trendData}}" wx:key="date">{{item.label}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </van-card>
  </view>

  <!-- 专注模式布局 -->
  <view class="focus-layout" wx:if="{{focusMode}}" bindtap="onFocusModeTap">
    <!-- 专注模式头部 -->
    <van-nav-bar
      title="🌙 深度专注"
      left-arrow
      bind:click-left="exitFocusMode"
      custom-class="focus-nav-bar"
      title-class="focus-nav-title">
      <view slot="right" class="focus-nav-right">
        <text class="exit-hint">双击屏幕退出</text>
      </view>
    </van-nav-bar>

    <!-- 专注模式主体 -->
    <view class="focus-main">
      <!-- 激励文案 -->
      <view class="focus-motivation" wx:if="{{focusMotivation}}">
        <text class="motivation-text">{{focusMotivation}}</text>
      </view>

      <!-- 专注模式计时器 -->
      <view class="focus-timer-display">
        <view class="focus-timer-circle {{isRunning ? 'breathing' : ''}} {{milestoneAnimation ? 'milestone-glow' : ''}}"
              style="background: conic-gradient(var({{timerColorVar}}) 0deg {{progressDegree}}deg, rgba(255,255,255,0.1) {{progressDegree}}deg 360deg)">
          <view class="focus-timer-inner">
            <text class="focus-time-text">{{displayTime}}</text>
            <text class="focus-session-indicator">{{sessionIndicator}}</text>
            <!-- 里程碑提示 -->
            <view class="milestone-indicator" wx:if="{{showMilestone}}">
              <text class="milestone-text">{{milestoneText}}</text>
            </view>
          </view>
          <!-- 进度环 -->
          <view class="progress-rings">
            <view class="progress-ring" wx:for="{{progressRings}}" wx:key="index"
                  style="animation-delay: {{item.delay}}s; opacity: {{item.opacity}}"></view>
          </view>
        </view>
      </view>

      <!-- 专注模式任务信息 -->
      <van-card
        wx:if="{{selectedTask}}"
        title="{{selectedTask.title}}"
        desc="{{focusTaskDesc}}"
        custom-class="focus-task-card">
        <view slot="footer" class="task-progress">
          <van-progress
            percentage="{{taskProgress}}"
            color="{{timerColor}}"
            track-color="rgba(255,255,255,0.2)"
            custom-class="focus-progress" />
          <text class="progress-text">{{taskProgressText}}</text>
        </view>
      </van-card>

      <!-- 专注统计 -->
      <view class="focus-stats">
        <view class="stat-item">
          <text class="stat-value">{{todayFocusTime}}</text>
          <text class="stat-label">今日专注</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{completedSessions}}</text>
          <text class="stat-label">完成番茄</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{currentSessionTime}}</text>
          <text class="stat-label">本次时长</text>
        </view>
      </view>

      <!-- 专注模式控制 -->
      <view class="focus-controls">
        <van-button
          type="{{isRunning ? 'warning' : 'primary'}}"
          size="large"
          round
          bind:click="toggleTimer"
          custom-class="focus-control-btn">
          <van-icon
            name="{{isRunning ? 'pause' : 'play'}}"
            size="28" />
          <text class="btn-text">{{isRunning ? '暂停' : '开始'}}</text>
        </van-button>

        <van-button
          type="danger"
          size="large"
          round
          bind:click="stopTimer"
          custom-class="focus-stop-btn">
          <van-icon name="stop" size="28" />
          <text class="btn-text">结束</text>
        </van-button>
      </view>

      <!-- 环境音效控制 -->
      <view class="focus-audio-control">
        <van-slider
          value="{{bgVolume}}"
          min="0"
          max="100"
          bind:change="onVolumeChange"
          active-color="rgba(255,255,255,0.8)"
          inactive-color="rgba(255,255,255,0.3)"
          custom-class="focus-volume-slider" />
        <view class="audio-info">
          <van-icon name="volume-o" size="20" color="rgba(255,255,255,0.8)" />
          <text class="audio-label">{{currentSoundLabel}}</text>
          <van-button
            size="mini"
            type="default"
            bind:click="showSoundSelector"
            custom-class="sound-switch-btn">
            切换
          </van-button>
        </view>
      </view>
    </view>

    <!-- 里程碑庆祝动画 -->
    <view class="milestone-celebration" wx:if="{{showCelebration}}">
      <view class="celebration-content">
        <text class="celebration-title">🎉 {{celebrationTitle}}</text>
        <text class="celebration-message">{{celebrationMessage}}</text>
        <view class="celebration-effects">
          <view class="effect-particle" wx:for="{{celebrationParticles}}" wx:key="index"
                style="left: {{item.x}}%; top: {{item.y}}%; animation-delay: {{item.delay}}s"></view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 声音设置弹窗 -->
<van-popup 
  show="{{showSoundModal}}" 
  position="bottom" 
  custom-style="height: 70%; z-index: 9999;" 
  bind:close="hideSoundModal"
  custom-class="sound-popup"
  z-index="9999">
  <view class="popup-content">
    <van-nav-bar 
      title="声音设置" 
      left-arrow 
      bind:click-left="hideSoundModal"
      custom-class="popup-nav-bar" />
    
    <view class="popup-body">
      <!-- 背景音选择 -->
      <van-divider content-position="left">背景音</van-divider>
      <van-grid column-num="2" custom-class="sound-grid">
        <van-grid-item 
          wx:for="{{backgroundSounds}}"
          wx:for-item="sound"
          wx:key="id"
          text="{{sound.name}}"
          bind:click="selectBackgroundSound"
          data-sound="{{sound.id}}"
          custom-class="sound-item {{selectedBgSound === sound.id ? 'active' : ''}}">
          <view slot="icon" class="sound-icon">{{sound.icon}}</view>
        </van-grid-item>
      </van-grid>

      <!-- 音量控制 -->
      <van-divider content-position="left">音量</van-divider>
      <van-cell-group>
        <van-cell title="背景音音量" value="{{bgVolume}}%">
          <van-slider 
            slot="right-icon" 
            value="{{bgVolume}}" 
            min="0" 
            max="100" 
            bind:change="adjustBgVolume"
            custom-class="volume-slider" />
        </van-cell>
      </van-cell-group>
    </view>
  </view>
</van-popup>

<!-- 设置弹窗 -->
<van-popup 
  show="{{showSettingsModal}}" 
  position="bottom" 
  custom-style="height: 80%; z-index: 9999;" 
  bind:close="hideSettings"
  custom-class="settings-popup"
  z-index="9999">
  <view class="popup-content">
    <van-nav-bar 
      title="设置" 
      left-arrow 
      bind:click-left="hideSettings"
      custom-class="popup-nav-bar" />
    
    <view class="popup-body">
      <!-- 时间设置 -->
      <van-divider content-position="left">时间设置</van-divider>
      <van-cell-group>
        <van-cell title="工作时长" value="{{workDuration}}分钟">
          <van-stepper 
            slot="right-icon" 
            value="{{workDuration}}" 
            min="1" 
            max="60" 
            bind:change="changeWorkDuration"
            custom-class="time-stepper" />
        </van-cell>
        
        <van-cell title="短休息" value="{{shortBreakDuration}}分钟">
          <van-stepper 
            slot="right-icon" 
            value="{{shortBreakDuration}}" 
            min="1" 
            max="30" 
            bind:change="changeShortBreakDuration"
            custom-class="time-stepper" />
        </van-cell>
        
        <van-cell title="长休息" value="{{longBreakDuration}}分钟">
          <van-stepper 
            slot="right-icon" 
            value="{{longBreakDuration}}" 
            min="1" 
            max="60" 
            bind:change="changeLongBreakDuration"
            custom-class="time-stepper" />
        </van-cell>
      </van-cell-group>

      <!-- 通知设置 -->
      <van-divider content-position="left">通知设置</van-divider>
      <van-cell-group>
        <van-cell title="震动提醒">
          <van-switch 
            slot="right-icon" 
            checked="{{enableVibration}}" 
            bind:change="toggleVibration" />
        </van-cell>
        
        <van-cell title="通知提醒">
          <van-switch 
            slot="right-icon" 
            checked="{{enableNotification}}" 
            bind:change="toggleNotification" />
        </van-cell>
        
        <van-cell title="自动开始休息">
          <van-switch 
            slot="right-icon" 
            checked="{{autoStartBreak}}" 
            bind:change="toggleAutoStartBreak" />
        </van-cell>
        
        <van-cell title="自动开始工作">
          <van-switch 
            slot="right-icon" 
            checked="{{autoStartWork}}" 
            bind:change="toggleAutoStartWork" />
        </van-cell>
      </van-cell-group>
    </view>
  </view>
</van-popup>

<!-- 任务选择弹窗 -->
<van-popup
  show="{{showTaskSelector}}"
  position="bottom"
  custom-style="height: 60%; z-index: 9999;"
  bind:close="hideTaskSelector"
  custom-class="task-selector-popup"
  z-index="9999">
  <view class="task-selector-content">
    <van-nav-bar
      title="选择复习任务"
      left-arrow
      bind:click-left="hideTaskSelector"
      custom-class="task-selector-nav" />

    <view class="task-list-container">
      <!-- 快速专注选项 -->
      <van-cell-group custom-class="quick-focus-group">
        <van-cell
          title="⚡ 快速专注"
          label="25分钟专注复习"
          clickable
          custom-class="task-option {{studyMode === 'quick' ? 'selected' : ''}}"
          bind:click="selectQuickMode">
          <van-icon
            wx:if="{{studyMode === 'quick'}}"
            slot="right-icon"
            name="success"
            color="#1890ff"
            size="20px" />
        </van-cell>
      </van-cell-group>

      <!-- 任务列表 -->
      <van-cell-group custom-class="task-list-group" wx:if="{{availableTasks.length > 0}}">
        <van-divider content-position="left">复习任务</van-divider>
        <van-cell
          wx:for="{{availableTasks}}"
          wx:key="id"
          title="{{item.title}}"
          label="{{item.subject || '通用任务'}}"
          value="🍅 {{item.completedPomodoros || 0}}/{{item.totalPomodoros || 1}}"
          clickable
          custom-class="task-option {{selectedTask && selectedTask.id === item.id ? 'selected' : ''}}"
          bind:click="selectTaskItem"
          data-task="{{item}}">
          <van-icon
            wx:if="{{selectedTask && selectedTask.id === item.id}}"
            slot="right-icon"
            name="success"
            color="#1890ff"
            size="20px" />
        </van-cell>
      </van-cell-group>

      <!-- 空状态 -->
      <view class="empty-tasks" wx:if="{{availableTasks.length === 0}}">
        <van-empty description="暂无复习任务" />
        <van-button
          type="primary"
          size="small"
          bind:click="goToTaskCenter">
          去添加任务
        </van-button>
      </view>
    </view>
  </view>
</van-popup>
