<!--pages/task-center/index.wxml-->
<view class="container">
  <!-- 顶部统计概览 -->
  <view class="header-section">
    <view class="stats-section">
      <view class="stats-grid">
        <view class="stat-card" wx:for="{{taskStats}}" wx:key="label">
          <view class="stat-content">
            <view class="stat-value">{{item.value}}</view>
            <view class="stat-label">{{item.label}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 筛选器 -->
    <view class="filter-section">
      <!-- 基础筛选标签 -->
      <van-tabs
        active="{{activeTab}}"
        bind:change="onTabChange"
        color="#1890FF"
        line-width="30">
        <van-tab
          wx:for="{{basicFilterOptions}}"
          wx:key="value"
          title="{{item.label}}"
          info="{{item.count > 0 ? item.count : ''}}">
        </van-tab>
      </van-tabs>

      <!-- 考试筛选和快捷操作区域 -->
      <view class="exam-controls-section" wx:if="{{availableExams.length > 0}}">
        <van-dropdown-menu active-color="#1890FF">
          <!-- 考试下拉选择器 -->
          <van-dropdown-item
            value="{{examDropdownValue}}"
            options="{{examDropdownOptions}}"
            title="{{examDropdownValue ? getExamShortName(examDropdownValue) : '选择考试'}}"
            bind:change="onExamDropdownChange">
          </van-dropdown-item>
        </van-dropdown-menu>

        <!-- 添加任务快捷按钮 -->
        <van-button
          type="primary"
          size="small"
          icon="plus"
          bind:click="addTask"
          custom-class="add-task-quick-btn">
          添加复习任务
        </van-button>
      </view>


    </view>
  </view>

  <!-- 任务列表 -->
  <view class="tasks-section" wx:if="{{filteredTasks.length > 0}}">
    <view class="task-group" wx:for="{{groupedTasks}}" wx:key="date" wx:for-item="group">
      <van-divider content-position="left" custom-class="group-divider">
        <text class="group-date">{{group.date}}</text>
        <text class="group-count">{{group.tasks.length}}个任务</text>
      </van-divider>

      <van-cell-group border="{{false}}" custom-class="task-cell-group">
        <van-cell 
          wx:for="{{group.tasks}}" 
          wx:key="id"
          custom-class="task-cell"
          bind:click="viewTaskDetail"
          data-id="{{item.id}}"
          bindlongpress="showTaskActions"
          data-task="{{item}}">
          
          <view slot="icon" class="task-status-wrapper">
            <view class="task-status-indicator status-{{item.status}}"></view>
            <van-checkbox 
              value="{{item.completed}}" 
              bind:change="toggleTask" 
              data-id="{{item.id}}"
              catchtap="true"
              custom-class="task-checkbox" />
          </view>

          <view slot="title" class="task-title-wrapper">
            <!-- 标题和优先级行 -->
            <view class="task-title-line">
              <text class="task-title {{item.completed ? 'completed' : ''}}">{{item.title}}</text>
              <van-tag
                type="{{item.priority === 'high' ? 'danger' : item.priority === 'medium' ? 'warning' : 'default'}}"
                size="mini"
                custom-class="task-priority-tag">{{item.priorityText}}</van-tag>
            </view>

            <!-- 考试名称行 -->
            <text wx:if="{{item.examName}}" class="task-exam-name">{{item.examName.length > 10 ? item.examName.substring(0, 10) + '...' : item.examName}}</text>

            <!-- 科目和截止时间行 -->
            <view class="task-meta-line">
              <van-tag
                wx:if="{{item.subject}}"
                type="primary"
                size="mini"
                custom-class="task-subject-tag">{{item.subject}}</van-tag>
              <text class="task-due">{{item.dueDate}}</text>
            </view>

            <!-- 检查点进度预览行 -->
            <view class="checkpoint-preview-line" wx:if="{{item.subtasks && item.subtasks.length > 0}}"
                  catchtap="openCheckpointModal"
                  data-id="{{item.id}}">
              <view class="checkpoint-progress-wrapper">
                <view class="checkpoint-progress-bar">
                  <view class="progress-fill" style="width: {{(item.completedSubtasks / item.subtasks.length) * 100}}%"></view>
                </view>
                <text class="checkpoint-stats">{{item.completedSubtasks}}/{{item.subtasks.length}} 检查点 [{{item.showCheckpoints ? '展开' : '收起'}}]</text>
              </view>
              <van-icon name="{{item.showCheckpoints ? 'arrow-down' : 'arrow'}}" size="12" class="expand-icon" />
            </view>

            <!-- 添加检查点引导 -->
            <view class="add-checkpoint-guide" wx:if="{{!item.subtasks || item.subtasks.length === 0}}"
                  catchtap="openCheckpointModal"
                  data-id="{{item.id}}">
              <van-icon name="plus" size="14" color="#1890ff" />
              <text class="guide-text">添加检查点来跟踪学习进度</text>
              <van-icon name="arrow" size="12" color="#999999" class="guide-arrow" />
            </view>
          </view>

          <view slot="right-icon" class="task-actions">
            <van-tag 
              type="{{item.status === 'completed' ? 'success' : 'primary'}}" 
              size="mini">{{item.statusText}}</van-tag>
            <van-icon 
              name="ellipsis" 
              size="20" 
              bind:click="showTaskActions" 
              data-task="{{item}}" 
              catchtap="true" />
          </view>
        </van-cell>

        <!-- 检查点快速展开区域 -->
        <view class="checkpoint-expand-area" wx:if="{{item.showCheckpoints}}">
          <text style="color: red; font-size: 24rpx; display: block; margin-bottom: 10rpx;">✅ 检查点展开区域显示成功！</text>
          <text style="color: blue; font-size: 20rpx; display: block; margin-bottom: 10rpx;">showCheckpoints: {{item.showCheckpoints}}</text>
          <text style="color: green; font-size: 20rpx; display: block; margin-bottom: 10rpx;">任务ID: {{item.id}}</text>
          <text style="color: purple; font-size: 20rpx; display: block; margin-bottom: 10rpx;">检查点数量: {{item.subtasks ? item.subtasks.length : 0}}</text>

          <view class="checkpoint-list" wx:if="{{item.subtasks && item.subtasks.length > 0}}">
            <view class="checkpoint-item"
                  wx:for="{{item.subtasks}}"
                  wx:key="id"
                  wx:for-item="checkpoint">
              <van-checkbox
                value="{{checkpoint.completed}}"
                bind:change="toggleSubtask"
                data-task-id="{{item.id}}"
                data-subtask-id="{{checkpoint.id}}"
                custom-class="checkpoint-checkbox" />
              <text class="checkpoint-title {{checkpoint.completed ? 'completed' : ''}}">
                {{checkpoint.title}}
              </text>
            </view>
          </view>
          <text wx:else style="color: orange; font-size: 20rpx;">❌ 没有检查点数据</text>
        </view>

        <!-- 始终显示的调试信息 -->
        <view wx:if="{{item.subtasks && item.subtasks.length > 0}}" style="background: #ffffcc; padding: 8rpx; margin: 4rpx 0; font-size: 18rpx;">
          🔍 调试: showCheckpoints={{item.showCheckpoints}}, 检查点数={{item.subtasks.length}}
        </view>

        <!-- 任务详情展开区域 -->
        <van-collapse 
          wx:if="{{item.showDetails}}" 
          value="{{item.showDetails ? ['details'] : []}}"
          custom-class="task-details-collapse">
          <van-collapse-item name="details" custom-class="task-details">
            <!-- 时间信息 -->
            <view class="task-time-info">
              <view class="time-item" wx:if="{{item.dueDate}}">
                <van-icon name="clock-o" size="16" />
                <text class="time-text">{{item.dueDate}}</text>
              </view>
              <view class="time-item" wx:if="{{item.estimatedTime}}">
                <van-icon name="stopwatch-o" size="16" />
                <text class="time-text">{{item.estimatedTime}}</text>
              </view>
            </view>

            <!-- 进度条 -->
            <view class="task-progress-wrapper" wx:if="{{item.progress !== undefined}}">
              <view class="progress-header">
                <text class="progress-label">完成进度</text>
                <text class="progress-percentage">{{item.progress}}%</text>
              </view>
              <van-progress 
                percentage="{{item.progress}}" 
                stroke-width="6" 
                color="#1989fa"
                custom-class="task-progress" />
            </view>

            <!-- 检查点 -->
            <view class="subtasks-wrapper" wx:if="{{item.subtasks && item.subtasks.length > 0}}">
              <view class="subtasks-header" bindtap="toggleSubtasks" data-id="{{item.id}}">
                <text class="subtasks-title">检查点 ({{item.completedSubtasks}}/{{item.subtasks.length}})</text>
                <view class="subtasks-actions">
                  <van-button
                    wx:if="{{item.showSubtasks && item.subtasks.length > 1}}"
                    type="primary"
                    size="mini"
                    bind:click="toggleBatchMode"
                    data-id="{{item.id}}"
                    catchtap="true"
                    custom-class="batch-btn">
                    {{item.batchMode ? '取消' : '批量'}}
                  </van-button>
                  <van-icon
                    name="{{item.showSubtasks ? 'arrow-down' : 'arrow'}}"
                    size="16"
                    custom-class="subtasks-toggle" />
                </view>
              </view>

              <van-cell-group wx:if="{{item.showSubtasks}}" border="{{false}}" custom-class="subtasks-list">
                <van-cell
                  wx:for="{{item.subtasks}}"
                  wx:key="id"
                  wx:for-item="subtask"
                  custom-class="subtask-cell {{item.batchMode ? 'batch-mode' : ''}}"
                  border="{{false}}">
                  <view slot="icon" class="subtask-icon-wrapper">
                    <van-checkbox
                      wx:if="{{item.batchMode}}"
                      value="{{subtask.selected}}"
                      bind:change="toggleSubtaskSelection"
                      data-task-id="{{item.id}}"
                      data-subtask-id="{{subtask.id}}"
                      custom-class="batch-checkbox" />
                    <van-checkbox
                      wx:else
                      value="{{subtask.completed}}"
                      bind:change="toggleSubtask"
                      data-task-id="{{item.id}}"
                      data-subtask-id="{{subtask.id}}" />
                  </view>
                  <view slot="title">
                    <text class="subtask-title {{subtask.completed ? 'completed' : ''}}">{{subtask.title}}</text>
                  </view>
                  <view slot="right-icon" wx:if="{{item.batchMode && subtask.selected}}">
                    <van-icon name="success" size="16" color="#52c41a" />
                  </view>
                </van-cell>
              </van-cell-group>

              <!-- 批量操作按钮 -->
              <view class="batch-actions" wx:if="{{item.batchMode && item.showSubtasks}}">
                <van-button
                  type="default"
                  size="small"
                  bind:click="selectAllSubtasks"
                  data-id="{{item.id}}"
                  custom-class="batch-action-btn">
                  全选
                </van-button>
                <van-button
                  type="primary"
                  size="small"
                  bind:click="batchCompleteSubtasks"
                  data-id="{{item.id}}"
                  custom-class="batch-action-btn">
                  批量完成
                </van-button>
                <van-button
                  type="warning"
                  size="small"
                  bind:click="batchResetSubtasks"
                  data-id="{{item.id}}"
                  custom-class="batch-action-btn">
                  批量重置
                </van-button>
              </view>
            </view>

            <!-- 快捷操作 -->
            <view class="task-quick-actions">
              <van-button 
                type="primary" 
                size="small" 
                bind:click="startTask" 
                data-id="{{item.id}}"
                custom-class="quick-action-btn">开始任务</van-button>
              <van-button 
                type="default" 
                size="small" 
                bind:click="editTask" 
                data-id="{{item.id}}"
                custom-class="quick-action-btn">编辑</van-button>
              <van-button 
                type="warning" 
                size="small" 
                bind:click="startPomodoro" 
                data-id="{{item.id}}"
                custom-class="quick-action-btn">专注</van-button>
            </view>
          </van-collapse-item>
        </van-collapse>
      </van-cell-group>
    </view>
  </view>

  <!-- 空状态 -->
  <van-empty
    wx:else
    image="https://img.yzcdn.cn/vant/custom-empty-image.png"
    description="{{getEmptyMessage()}}"
    custom-class="empty-state">
    <van-button
      type="primary"
      size="small"
      bind:click="addTask"
      custom-class="empty-add-btn">
      <van-icon name="plus" size="16" />
      添加复习任务
    </van-button>
  </van-empty>

  <!-- 添加任务按钮 -->
  <view class="add-task-section" wx:if="{{filteredTasks.length > 0}}">
    <van-button 
      type="primary" 
      size="large" 
      bind:click="addTask"
      custom-class="add-task-btn">
      <van-icon name="plus" size="20" />
      添加复习任务
    </van-button>
  </view>
</view>

<!-- 任务操作菜单 -->
<van-action-sheet 
  show="{{showActionSheet}}" 
  title="{{selectedTask.title}}" 
  actions="{{taskActions}}" 
  bind:close="hideActionSheet"
  bind:select="executeTaskAction"
  custom-class="task-action-sheet" />

<!-- 筛选菜单 -->
<van-popup 
  show="{{showFilterMenu}}" 
  position="bottom" 
  custom-style="height: 60%;" 
  bind:close="hideFilterMenu"
  custom-class="filter-popup">
  <view class="filter-menu-content">
    <view class="filter-menu-header">
      <text class="filter-menu-title">筛选和排序</text>
      <van-icon name="cross" size="20" bind:click="hideFilterMenu" />
    </view>

    <view class="filter-menu-body">
      <!-- 排序选项 -->
      <view class="filter-section">
        <text class="filter-section-title">排序方式</text>
        <van-cell-group border="{{false}}">
          <van-cell 
            wx:for="{{sortOptions}}" 
            wx:key="value"
            title="{{item.label}}" 
            clickable 
            bind:click="changeSort"
            data-sort="{{item.value}}"
            custom-class="filter-option-cell">
            <van-icon 
              wx:if="{{currentSort === item.value}}" 
              name="success" 
              size="16" 
              color="#1989fa" 
              slot="right-icon" />
          </van-cell>
        </van-cell-group>
      </view>

      <!-- 优先级筛选 -->
      <view class="filter-section">
        <text class="filter-section-title">优先级</text>
        <van-cell-group border="{{false}}">
          <van-cell
            wx:for="{{priorityOptions}}"
            wx:key="value"
            title="{{item.label}}"
            clickable
            bind:click="togglePriority"
            data-priority="{{item.value}}"
            custom-class="filter-option-cell">
            <van-icon
              wx:if="{{selectedPriorities.includes(item.value)}}"
              name="success"
              size="16"
              color="#1989fa"
              slot="right-icon" />
          </van-cell>
        </van-cell-group>
      </view>



      <!-- 快捷筛选组合 -->
      <view class="filter-section">
        <text class="filter-section-title">快捷筛选</text>
        <view class="quick-filter-options">
          <van-cell-group border="{{false}}">
            <van-cell
              wx:for="{{quickFilterOptions}}"
              wx:key="value"
              title="{{item.label}}"
              label="{{item.description}}"
              clickable
              bind:click="applyQuickFilter"
              data-filter="{{item.value}}"
              custom-class="filter-option-cell quick-filter-cell">
              <van-icon
                name="arrow"
                size="16"
                color="#969799"
                slot="right-icon" />
            </van-cell>
          </van-cell-group>
        </view>
      </view>
    </view>

    <view class="filter-menu-footer">
      <van-button 
        type="default" 
        size="large" 
        bind:click="resetFilters"
        custom-class="filter-reset-btn">重置</van-button>
      <van-button 
        type="primary" 
        size="large" 
        bind:click="applyFilters"
        custom-class="filter-apply-btn">应用</van-button>
    </view>
  </view>
</van-popup>

<!-- 检查点弹窗 -->
<van-popup
  show="{{showCheckpointModal}}"
  position="center"
  custom-style="width: 80%; max-height: 70%; border-radius: 16rpx;"
  bind:close="closeCheckpointModal"
  custom-class="checkpoint-modal"
  z-index="10001">

  <!-- 弹窗头部 -->
  <view class="checkpoint-modal-header">
    <text class="modal-title">{{checkpointModalData.taskTitle}}</text>
    <van-icon name="cross" size="20" bind:click="closeCheckpointModal" />
  </view>

  <!-- 弹窗内容区域 -->
  <view class="checkpoint-modal-content">
    <!-- 统计信息卡片 -->
    <view class="checkpoint-stats-card" wx:if="{{checkpointModalData.subtasks.length > 0}}">
      <view class="stats-content">
        <view class="stats-main">
          <text class="stats-number">{{checkpointModalData.subtasks.length}}</text>
          <text class="stats-label">个检查点</text>
        </view>
        <view class="stats-detail">
          <view class="progress-info">
            <text class="completed-count">已完成 {{checkpointModalData.completedCount}}</text>
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{checkpointModalData.subtasks.length > 0 ? (checkpointModalData.completedCount / checkpointModalData.subtasks.length * 100) : 0}}%"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 检查点列表卡片 -->
    <view class="checkpoint-list-card" wx:if="{{checkpointModalData.subtasks.length > 0}}">
      <view
        class="checkpoint-item"
        wx:for="{{checkpointModalData.subtasks}}"
        wx:key="index"
        data-index="{{index}}">

        <!-- 复选框 -->
        <van-checkbox
          value="{{item.completed}}"
          bind:change="toggleCheckpointInModal"
          data-index="{{index}}"
          custom-class="checkpoint-checkbox" />

        <!-- 内容区域 -->
        <view class="checkpoint-content">
          <view class="checkpoint-text-wrapper {{item.completed ? 'completed-wrapper' : ''}}">
            <van-field
              value="{{item.title}}"
              placeholder="检查点内容"
              bind:change="updateCheckpointInModal"
              data-index="{{index}}"
              maxlength="30"
              border="{{false}}"
              custom-class="checkpoint-field" />
          </view>
        </view>

        <!-- 操作区域 -->
        <view class="checkpoint-actions">
          <van-icon
            name="delete"
            size="16"
            bind:click="removeCheckpointInModal"
            data-index="{{index}}"
            custom-class="delete-action" />
        </view>
      </view>
    </view>

    <!-- 添加检查点卡片 -->
    <view class="add-checkpoint-card">
      <!-- 默认状态：点击展开 -->
      <view class="add-checkpoint-trigger" wx:if="{{!showAddCheckpointInput}}" bind:tap="toggleAddCheckpointInput">
        <van-icon name="plus" size="16" color="#1890ff" />
        <text class="trigger-text">添加检查项</text>
      </view>

      <!-- 展开状态：输入和操作 -->
      <view class="add-checkpoint-expanded" wx:if="{{showAddCheckpointInput}}">
        <view class="add-input-section">
          <van-cell-group>
            <van-field
              value="{{newCheckpointTitle}}"
              placeholder="请输入检查项内容"
              bind:change="updateNewCheckpointTitle"
              maxlength="30"
              focus="{{showAddCheckpointInput}}"
              border="{{false}}"
              custom-class="add-checkpoint-field" />
          </van-cell-group>
        </view>

        <!-- 按钮操作区域 -->
        <view class="checkpoint-actions">
          <view class="checkpoint-action-button save-action">
            <van-button
              type="primary"
              size="small"
              bind:click="addCheckpointInModal"
              disabled="{{!newCheckpointTitle}}"
              custom-class="save-checkpoint-btn">
              保存
            </van-button>
          </view>
          <view class="checkpoint-action-button cancel-action">
            <van-button
              type="default"
              size="small"
              bind:click="cancelAddCheckpoint"
              custom-class="cancel-checkpoint-btn">
              取消
            </van-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态提示 -->
    <view class="empty-checkpoints" wx:if="{{checkpointModalData.subtasks.length === 0}}">
      <van-icon name="todo-list-o" size="48" color="#cccccc" />
      <text class="empty-text">暂无检查点</text>
      <text class="empty-desc">添加检查点来跟踪学习进度</text>
    </view>

    <!-- 弹窗底部操作区域 -->
    <view class="checkpoint-modal-footer" wx:if="{{checkpointModalData.subtasks.length > 0}}">
      <view class="checkpoint-actions">
        <view class="checkpoint-action-button reset-action">
          <van-button
            type="default"
            size="small"
            bind:click="resetAllCheckpoints"
            custom-class="reset-checkpoint-btn">
            全部重置
          </van-button>
        </view>
        <view class="checkpoint-action-button complete-action">
          <van-button
            type="primary"
            size="small"
            bind:click="completeAllCheckpoints"
            custom-class="complete-checkpoint-btn">
            全部完成
          </van-button>
        </view>
      </view>
    </view>
  </view>
</van-popup>