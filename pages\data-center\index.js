// pages/data-center/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    // 考试准备度
    preparationScore: 78,
    preparationLevel: '良好',
    preparationDescription: '继续保持，距离目标还有一步',
    scoreFactors: [],
    showScoreInfo: false,
    scoreFactorExplanations: [],

    // 复习统计
    currentTimeRange: 'week',
    timeRangeOptions: [],
    studyStats: [],

    // 效率分析
    efficiencyData: [],
    efficiencyInsights: [],

    // 科目分析
    subjectsData: [],

    // 备考习惯
    studyHabits: [],

    // 成就
    recentAchievements: []
  },

  onLoad() {
    this.initPage()
  },

  async onShow() {
    await this.loadData()
  },

  // 初始化页面
  async initPage() {
    this.initTimeRangeOptions()
    this.initScoreFactorExplanations()
    await this.loadData()
  },

  // 加载数据
  async loadData() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      await Promise.all([
        this.loadStudyStats(),
        this.loadEfficiencyData(),
        this.loadSubjectsData(),
        this.loadStudyHabits(),
        this.loadRecentAchievements()
      ])

      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      console.error('加载数据失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  },

  // 初始化时间范围选项
  initTimeRangeOptions() {
    const timeRangeOptions = [
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' },
      { value: 'quarter', label: '本季度' },
      { value: 'year', label: '本年' }
    ]

    this.setData({ timeRangeOptions })
  },

  // 初始化评分因子说明
  initScoreFactorExplanations() {
    const scoreFactorExplanations = [
      {
        name: '复习时长',
        description: '每日复习时间的稳定性和充足性'
      },
      {
        name: '计划完成',
        description: '复习计划的完成率和质量'
      },
      {
        name: '知识掌握',
        description: '通过测试和练习反映的知识掌握程度'
      },
      {
        name: '复习效率',
        description: '单位时间内的复习效果和专注度'
      }
    ]

    this.setData({ scoreFactorExplanations })
  },

  // 加载数据
  loadData() {
    this.loadScoreFactors()
    this.loadStudyStats()
    this.loadEfficiencyData()
    this.loadSubjectsData()
    this.loadStudyHabits()
    this.loadRecentAchievements()
  },

  // 加载评分因子
  loadScoreFactors() {
    const scoreFactors = [
      {
        name: '复习时长',
        score: 85,
        color: '#52C41A'
      },
      {
        name: '计划完成',
        score: 72,
        color: '#1890FF'
      },
      {
        name: '知识掌握',
        score: 68,
        color: '#FA8C16'
      },
      {
        name: '复习效率',
        score: 88,
        color: '#722ED1'
      }
    ]

    this.setData({ scoreFactors })
  },

  // 加载复习统计
  async loadStudyStats() {
    try {
      const timeRange = this.data.currentTimeRange
      const result = await SmartApi.getUserStats(timeRange)

      if (result.success && result.data) {
        const data = result.data

        const studyStats = [
          {
            icon: '⏰',
            label: '复习时长',
            value: data.studyTime || '0h',
            bgColor: '#E6F7FF',
            trend: {
              icon: '📈',
              text: '+12%' // 可以根据历史数据计算趋势
            }
          },
          {
            icon: '📝',
            label: '完成复习',
            value: `${data.completedTasks || 0}个`,
            bgColor: '#F6FFED',
            trend: {
              icon: '📈',
              text: '+8%'
            }
          },
          {
            icon: '🍅',
            label: '专注次数',
            value: `${data.pomodoroCount || 0}次`,
            bgColor: '#FFF7E6',
            trend: {
              icon: '📈',
              text: '+15%'
            }
          },
          {
            icon: '📊',
            label: '平均效率',
            value: `${data.averageEfficiency || 0}%`,
            bgColor: '#F9F0FF',
            trend: {
              icon: '📈',
              text: '+5%'
            }
          }
        ]

        this.setData({ studyStats })
      } else {
        this.setDefaultStudyStats()
      }
    } catch (error) {
      console.error('加载复习统计失败:', error)
      this.setDefaultStudyStats()
    }
  },

  // 设置默认复习统计
  setDefaultStudyStats() {
    const studyStats = [
      {
        icon: '⏰',
        label: '复习时长',
        value: '0h',
        bgColor: '#E6F7FF',
        trend: {
          icon: '📈',
          text: '+0%'
        }
      },
      {
        icon: '📝',
        label: '完成复习',
        value: '0个',
        bgColor: '#F6FFED',
        trend: {
          icon: '📈',
          text: '+0%'
        }
      },
      {
        icon: '🍅',
        label: '专注次数',
        value: '0次',
        bgColor: '#FFF7E6',
        trend: {
          icon: '📈',
          text: '+0%'
        }
      },
      {
        icon: '📊',
        label: '平均效率',
        value: '0%',
        bgColor: '#F9F0FF',
        trend: {
          icon: '📈',
          text: '+0%'
        }
      }
    ]

    this.setData({ studyStats })
  },

  // 加载效率数据
  async loadEfficiencyData() {
    try {
      const result = await SmartApi.getEfficiencyData()
      
      if (result.success && result.data) {
        const processedData = this.processEfficiencyData(result.data)
        const insights = this.generateEfficiencyInsights(result.data)
        
        this.setData({ 
          efficiencyData: processedData,
          efficiencyInsights: insights 
        })
      } else {
        this.setDefaultEfficiencyData()
      }
    } catch (error) {
      console.error('加载效率数据失败:', error)
      this.setDefaultEfficiencyData()
    }
  },

  // 处理效率数据
  processEfficiencyData(dailyData) {
    const days = ['一', '二', '三', '四', '五', '六', '日']
    const now = new Date()
    const result = []

    for (let i = 6; i >= 0; i--) {
      const date = new Date(now)
      date.setDate(now.getDate() - i)
      
      const dayData = dailyData.find(item => {
        const itemDate = new Date(item.date)
        return itemDate.toDateString() === date.toDateString()
      })

      result.push({
        date: date.toISOString().split('T')[0],
        day: days[date.getDay()],
        efficiency: dayData ? dayData.efficiency : 0
      })
    }

    return result
  },

  // 生成效率洞察
  generateEfficiencyInsights(dailyData) {
    const insights = []
    const avgEfficiency = dailyData.reduce((sum, item) => sum + item.efficiency, 0) / dailyData.length

    if (avgEfficiency > 80) {
      insights.push({
        id: 'high_efficiency',
        icon: '🔥',
        text: '效率表现优秀，继续保持'
      })
    } else if (avgEfficiency > 60) {
      insights.push({
        id: 'medium_efficiency',
        icon: '📈',
        text: '效率良好，还有提升空间'
      })
    } else {
      insights.push({
        id: 'low_efficiency',
        icon: '⚠️',
        text: '效率有待提高，建议调整复习方法'
      })
    }

    return insights
  },

  // 设置默认效率数据
  setDefaultEfficiencyData() {
    const days = ['一', '二', '三', '四', '五', '六', '日']
    const efficiencyData = []
    const now = new Date()

    for (let i = 6; i >= 0; i--) {
      const date = new Date(now)
      date.setDate(now.getDate() - i)
      
      efficiencyData.push({
        date: date.toISOString().split('T')[0],
        day: days[date.getDay()],
        efficiency: Math.floor(Math.random() * 40 + 40) // 40-80%
      })
    }

    const efficiencyInsights = [
      {
        id: 'default',
        icon: '📊',
        text: '暂无数据，开始复习后将显示分析结果'
      }
    ]

    this.setData({ 
      efficiencyData,
      efficiencyInsights 
    })
  },

  // 加载科目数据
  async loadSubjectsData() {
    try {
      const [examsResult, tasksResult] = await Promise.all([
        SmartApi.getUserExams(),
        SmartApi.getUserTasks()
      ])

      if (examsResult.success && tasksResult.success) {
        const processedData = this.processSubjectsData(examsResult.data, tasksResult.data)
        this.setData({ subjectsData: processedData })
      } else {
        this.setDefaultSubjectsData()
      }
    } catch (error) {
      console.error('加载科目数据失败:', error)
      this.setDefaultSubjectsData()
    }
  },

  // 处理科目数据
  processSubjectsData(examsResult, tasksResult) {
    const subjectsMap = new Map()
    const tasks = tasksResult || []
    const exams = examsResult || []

    // 统计每个科目的数据
    exams.forEach(exam => {
      const subjects = Array.isArray(exam.subject) ? exam.subject : [exam.subject]
      subjects.forEach(subject => {
        if (!subjectsMap.has(subject)) {
          subjectsMap.set(subject, {
            name: subject,
            exam: exam.name,
            score: 0,
            progress: 0,
            progressColor: '#1890FF',
            studyTime: '0h',
            completedTasks: 0,
            avgEfficiency: 0
          })
        }
      })
    })

    // 统计任务数据
    tasks.forEach(task => {
      const subject = task.subject
      if (subjectsMap.has(subject)) {
        const subjectData = subjectsMap.get(subject)
        if (task.status === 'completed') {
          subjectData.completedTasks++
        }
        // 这里可以添加更多的统计逻辑
      }
    })

    // 计算进度和分数
    subjectsMap.forEach((value, key) => {
      const relatedTasks = tasks.filter(task => task.subject === key)
      const completedTasks = relatedTasks.filter(task => task.status === 'completed').length
      const totalTasks = relatedTasks.length
      
      value.progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
      value.score = Math.min(value.progress + Math.floor(Math.random() * 20), 100)
      value.progressColor = value.progress >= 80 ? '#52C41A' : value.progress >= 60 ? '#FA8C16' : '#FF4D4F'
      value.studyTime = `${Math.floor(Math.random() * 10 + 5)}h`
      value.avgEfficiency = Math.floor(Math.random() * 30 + 60)
    })

    return Array.from(subjectsMap.values())
  },

  // 获取默认科目数据
  getDefaultSubjectsData() {
    return [
      {
        name: '数学',
        exam: '期末考试',
        score: 85,
        progress: 75,
        progressColor: '#FA8C16',
        studyTime: '12h',
        completedTasks: 8,
        avgEfficiency: 82
      },
      {
        name: '英语',
        exam: '期末考试',
        score: 78,
        progress: 65,
        progressColor: '#FA8C16',
        studyTime: '10h',
        completedTasks: 6,
        avgEfficiency: 78
      }
    ]
  },

  // 设置默认科目数据
  setDefaultSubjectsData() {
    this.setData({ subjectsData: this.getDefaultSubjectsData() })
  },

  // 加载复习习惯
  async loadStudyHabits() {
    try {
      const [statsResult, pomodoroResult] = await Promise.all([
        SmartApi.getUserStats('month'),
        SmartApi.getPomodoroStats('month')
      ])

      if (statsResult.success && pomodoroResult.success) {
        const processedData = this.processStudyHabits(statsResult.data, pomodoroResult.data)
        this.setData({ studyHabits: processedData })
      } else {
        this.setDefaultStudyHabits()
      }
    } catch (error) {
      console.error('加载复习习惯失败:', error)
      this.setDefaultStudyHabits()
    }
  },

  // 处理复习习惯数据
  processStudyHabits(statsResult, pomodoroResult) {
    const habits = []

    // 复习时长习惯
    habits.push({
      icon: '⏰',
      title: '每日复习',
      value: statsResult.avgDailyStudyTime || '0h',
      description: '平均每天复习时间'
    })

    // 专注习惯
    habits.push({
      icon: '🍅',
      title: '专注时长',
      value: `${pomodoroResult.avgSessionTime || 0}分钟`,
      description: '平均专注时长'
    })

    // 完成习惯
    habits.push({
      icon: '✅',
      title: '完成率',
      value: `${statsResult.completionRate || 0}%`,
      description: '任务完成率'
    })

    // 效率习惯
    habits.push({
      icon: '📊',
      title: '效率指数',
      value: `${statsResult.efficiencyIndex || 0}`,
      description: '综合效率评分'
    })

    return habits
  },

  // 设置默认复习习惯
  setDefaultStudyHabits() {
    const studyHabits = [
      {
        icon: '⏰',
        title: '每日复习',
        value: '2.5h',
        description: '平均每天复习时间'
      },
      {
        icon: '🍅',
        title: '专注时长',
        value: '25分钟',
        description: '平均专注时长'
      },
      {
        icon: '✅',
        title: '完成率',
        value: '85%',
        description: '任务完成率'
      },
      {
        icon: '📊',
        title: '效率指数',
        value: '78',
        description: '综合效率评分'
      }
    ]

    this.setData({ studyHabits })
  },

  // 加载最近成就
  loadRecentAchievements() {
    const recentAchievements = [
      {
        id: 'achievement_1',
        icon: '🏆',
        name: '连续复习7天',
        description: '保持良好的复习习惯',
        time: '2天前',
        isNew: true
      },
      {
        id: 'achievement_2',
        icon: '⭐',
        name: '完成10个任务',
        description: '效率达人',
        time: '5天前',
        isNew: false
      },
      {
        id: 'achievement_3',
        icon: '🎯',
        name: '专注时长100小时',
        description: '专注大师',
        time: '1周前',
        isNew: false
      }
    ]

    this.setData({ recentAchievements })
  },

  // 切换时间范围 - 适配Vant tabs组件
  switchTimeRange(e) {
    const timeRange = e.detail.name
    this.setData({ currentTimeRange: timeRange })
    this.loadStudyStats()
  },

  // 显示评分说明
  showScoreInfo() {
    this.setData({ showScoreInfo: true })
  },

  // 隐藏评分说明
  hideScoreInfo() {
    this.setData({ showScoreInfo: false })
  },

  // 阻止冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 查看效率详情
  viewEfficiencyDetail() {
    wx.navigateTo({
      url: '/pages/statistics-detail/index?type=efficiency'
    })
  },

  // 查看科目详情
  viewSubjectsDetail() {
    wx.navigateTo({
      url: '/pages/statistics-detail/index?type=subjects'
    })
  },

  // 查看所有成就
  viewAllAchievements() {
    wx.navigateTo({
      url: '/pages/achievement-system/index'
    })
  },

  // 导出数据
  exportData() {
    wx.navigateTo({
      url: '/pages/data-export/index'
    })
  }
})