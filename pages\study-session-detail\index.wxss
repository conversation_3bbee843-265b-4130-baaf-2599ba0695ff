/* pages/study-session-detail/index.wxss */

/* 容器 */
.container {
  background-color: #F8F9FA;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.header {
  background-color: #FFFFFF;
  padding: 40rpx 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  margin-bottom: 24rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  display: block;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: #7F8C8D;
}

/* 筛选栏 */
.filter-bar {
  background-color: #FFFFFF;
  padding: 24rpx 32rpx;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.filter-tabs {
  display: flex;
  gap: 8rpx;
}

.filter-tab {
  padding: 12rpx 24rpx;
  border: 1rpx solid #E9ECEF;
  border-radius: 20rpx;
  background-color: #F8F9FA;
  color: #7F8C8D;
  font-size: 24rpx;
}

.filter-tab.active {
  background-color: #3498DB;
  color: #FFFFFF;
  border-color: #3498DB;
}

.sort-options {
  min-width: 120rpx;
}

.sort-picker {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
  border: 1rpx solid #E9ECEF;
}

.sort-text {
  font-size: 24rpx;
  color: #2C3E50;
}

.sort-arrow {
  font-size: 20rpx;
  color: #7F8C8D;
}

/* 统计概览 */
.stats-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  padding: 0 32rpx;
  margin-bottom: 24rpx;
}

.stat-card {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #7F8C8D;
}

/* 会话容器 */
.sessions-container {
  padding: 0 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2C3E50;
}

.session-count {
  font-size: 22rpx;
  color: #7F8C8D;
}

/* 会话列表 */
.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.session-item {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.session-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

/* 会话头部 */
.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.session-type {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.type-icon {
  font-size: 24rpx;
}

.type-text {
  font-size: 24rpx;
  color: #2C3E50;
  font-weight: 500;
}

.session-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.session-status.completed {
  background-color: #E8F5E8;
  color: #27AE60;
}

.session-status.interrupted {
  background-color: #FFEBEE;
  color: #E74C3C;
}

.session-status.paused {
  background-color: #FFF3E0;
  color: #FF9800;
}

/* 会话内容 */
.session-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.session-info {
  flex: 1;
}

.session-title {
  font-size: 26rpx;
  color: #2C3E50;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.session-desc {
  font-size: 22rpx;
  color: #7F8C8D;
  display: block;
  margin-bottom: 4rpx;
}

.session-metrics {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  align-items: flex-end;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.metric-icon {
  font-size: 16rpx;
}

.metric-text {
  font-size: 20rpx;
  color: #7F8C8D;
}

/* 会话底部 */
.session-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #F0F0F0;
}

.session-time {
  font-size: 22rpx;
  color: #7F8C8D;
}

.session-date {
  font-size: 22rpx;
  color: #95A5A6;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 28rpx;
  color: #2C3E50;
  font-weight: 500;
  display: block;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #7F8C8D;
  display: block;
  margin-bottom: 40rpx;
}

.start-study-btn {
  background-color: #3498DB;
  color: #FFFFFF;
  border: none;
  border-radius: 24rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
}

/* 会话详情弹窗 */
.session-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.session-modal-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2C3E50;
}

.modal-close {
  font-size: 36rpx;
  color: #7F8C8D;
  padding: 8rpx;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* 详情区块 */
.detail-section {
  margin-bottom: 32rpx;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2C3E50;
  margin-bottom: 16rpx;
  display: block;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.detail-label {
  font-size: 22rpx;
  color: #7F8C8D;
}

.detail-value {
  font-size: 24rpx;
  color: #2C3E50;
  font-weight: 500;
}

/* 效率图表 */
.efficiency-chart {
  margin-bottom: 16rpx;
}

.efficiency-bar {
  width: 100%;
  height: 12rpx;
  background-color: #F0F0F0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.efficiency-fill {
  height: 100%;
  background: linear-gradient(90deg, #27AE60, #2ECC71);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.efficiency-text {
  font-size: 24rpx;
  color: #2C3E50;
  font-weight: 500;
}

.efficiency-factors {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.factor-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
}

.factor-item.good {
  background-color: #E8F5E8;
}

.factor-item.warning {
  background-color: #FFEBEE;
}

.factor-item.info {
  background-color: #E3F2FD;
}

.factor-icon {
  font-size: 16rpx;
}

.factor-text {
  font-size: 22rpx;
  color: #2C3E50;
}

/* 关联信息 */
.related-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.related-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 16rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.related-label {
  font-size: 22rpx;
  color: #7F8C8D;
}

.related-value {
  font-size: 24rpx;
  color: #2C3E50;
  font-weight: 500;
}

/* 建议列表 */
.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.suggestion-item {
  font-size: 24rpx;
  color: #2C3E50;
  line-height: 1.6;
  padding: 8rpx 0;
}

/* 动画 */
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
