/* components/floating-action-button/index.wxss */

.fab-container {
  position: relative;
  z-index: 1000;
}

/* 拖动区域 */
.fab-movable-area {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  pointer-events: none;
}

.fab-movable-view {
  width: 112rpx;
  height: 112rpx;
  pointer-events: auto;
}

/* 主按钮 */
.fab-main {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12rpx 32rpx rgba(24, 144, 255, 0.4);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  z-index: 1001;
}

.fab-main.expanded {
  background-color: #ff4757 !important;
  box-shadow: 0 12rpx 32rpx rgba(255, 71, 87, 0.4);
}

.fab-main-icon {
  font-size: 48rpx;
  color: white;
  font-weight: bold;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fab-main-icon.rotated {
  transform: rotate(45deg);
}

/* 子按钮容器 */
.fab-sub-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  pointer-events: none;
}

/* 子按钮 */
.fab-sub-button {
  position: fixed;
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  transform: scale(0);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  pointer-events: auto;
  cursor: pointer;
}

.fab-sub-button.show {
  transform: scale(1);
  opacity: 1;
}

.fab-sub-button:hover {
  transform: scale(1.1);
}

.fab-sub-button:active {
  transform: scale(0.95);
}

/* 子按钮图标 */
.fab-sub-icon {
  font-size: 32rpx;
  color: white;
  margin-bottom: 4rpx;
}

/* 子按钮标签 */
.fab-sub-label {
  position: absolute;
  left: 50%;
  top: 100%;
  transform: translateX(-50%);
  margin-top: 8rpx;
  font-size: 20rpx;
  color: #333;
  background: rgba(255, 255, 255, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  white-space: nowrap;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.fab-sub-button.show .fab-sub-label {
  opacity: 1;
  transition-delay: 0.2s;
}

/* 错开动画时间 */
.fab-sub-button:nth-child(1) { 
  transition-delay: 0.05s; 
}

.fab-sub-button:nth-child(2) { 
  transition-delay: 0.1s; 
}

.fab-sub-button:nth-child(3) { 
  transition-delay: 0.15s; 
}

.fab-sub-button:nth-child(4) { 
  transition-delay: 0.2s; 
}

.fab-sub-button:nth-child(5) { 
  transition-delay: 0.25s; 
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .fab-main {
    width: 96rpx;
    height: 96rpx;
  }
  
  .fab-main-icon {
    font-size: 40rpx;
  }
  
  .fab-sub-button {
    width: 72rpx;
    height: 72rpx;
  }
  
  .fab-sub-icon {
    font-size: 28rpx;
  }
}
