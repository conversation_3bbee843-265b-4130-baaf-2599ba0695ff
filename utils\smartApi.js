// 云开发API - 统一使用微信云开发
const CloudApi = require('./cloudApi')
const ExamModel = require('../models/ExamModel')

class SmartApi {


  // 复习计划管理API - 强制使用云开发
  static async getTasks(filter = {}, limit = 20, skip = 0) {
    console.log('使用云开发API获取复习计划')
    return await CloudApi.getTasks(filter, limit, skip)
  }

  static async getTaskById(taskId) {
    console.log('使用云开发API获取复习计划详情')
    return await CloudApi.getTaskById(taskId)
  }

  static async addTask(taskData) {
    console.log('使用云开发API添加复习计划')
    return await CloudApi.addTask(taskData)
  }

  static async updateTask(taskId, updates) {
    console.log('使用云开发API更新复习计划')
    return await CloudApi.updateTask(taskId, updates)
  }

  static async deleteTask(taskId) {
    console.log('使用云开发API删除复习计划')
    return await CloudApi.deleteTask(taskId)
  }

  static async completeTask(taskId, completed = true) {
    console.log('使用云开发API完成复习')
    return await CloudApi.completeTask(taskId, completed)
  }

  static async getTaskStats(dateRange = null) {
    console.log('使用云开发API获取复习统计')
    return await CloudApi.getTaskStats(dateRange)
  }

  // 批量获取多个考试的任务
  static async getBatchTasks(examIds, includeCompleted = true, todayOnly = false) {
    console.log('使用云开发API批量获取任务', todayOnly ? '(仅今日任务)' : '')
    return await CloudApi.getBatchTasks(examIds, includeCompleted, todayOnly)
  }

  // 批量更新任务子任务状态
  static async updateTaskSubtasks(taskId, updates) {
    console.log('使用云开发API批量更新子任务状态')
    return await CloudApi.updateTaskSubtasks(taskId, updates)
  }

  // 考试管理API - 使用ExamModel进行数据验证和格式化
  static async getExams(filter = {}, limit = 20, skip = 0) {
    try {
      console.log('使用云开发API获取考试')
      const result = await CloudApi.getExams(filter, limit, skip)

      if (result.success && Array.isArray(result.data)) {
        // 使用ExamModel格式化考试列表
        result.data = ExamModel.formatList(result.data)
      }

      return result
    } catch (error) {
      console.error('获取考试列表失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getExamsWithAnalytics(includeProgress = true, includeTasks = false) {
    try {
      console.log('使用云开发API获取考试（含分析数据）')
      const result = await CloudApi.getExamsWithAnalytics(includeProgress, includeTasks)

      if (result.success && Array.isArray(result.data)) {
        // 使用ExamModel格式化考试列表
        result.data = ExamModel.formatList(result.data)
      }

      return result
    } catch (error) {
      console.error('获取考试分析数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getExamById(examId) {
    try {
      console.log('使用云开发API获取考试详情')
      const result = await CloudApi.getExamById(examId)

      if (result.success && result.data) {
        // 使用ExamModel格式化考试数据
        const examModel = new ExamModel(result.data)
        result.data = examModel.data
      }

      return result
    } catch (error) {
      console.error('获取考试详情失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async addExam(examData) {
    try {
      console.log('使用云开发API添加考试')

      // 使用ExamModel验证和格式化数据
      const examModel = new ExamModel(examData)
      const validatedData = examModel.toApiFormat()

      const result = await CloudApi.addExam(validatedData)

      if (result.success && result.data) {
        // 格式化返回的考试数据
        const returnedExamModel = new ExamModel(result.data)
        result.data = returnedExamModel.data
      }

      return result
    } catch (error) {
      console.error('添加考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async updateExam(examId, updates) {
    try {
      console.log('使用云开发API更新考试')

      // 如果提供了完整的考试数据，使用ExamModel验证
      if (updates && Object.keys(updates).length > 1) {
        // 先获取现有数据
        const existingResult = await CloudApi.getExamById(examId)
        if (existingResult.success && existingResult.data) {
          // 合并现有数据和更新数据
          const mergedData = { ...existingResult.data, ...updates }
          const examModel = new ExamModel(mergedData)
          updates = examModel.toApiFormat()

          // 移除不应该更新的系统字段
          delete updates._id
          delete updates.userId
          delete updates.createTime
        }
      }

      const result = await CloudApi.updateExam(examId, updates)

      if (result.success && result.data) {
        // 格式化返回的考试数据
        const updatedExamModel = new ExamModel(result.data)
        result.data = updatedExamModel.data
      }

      return result
    } catch (error) {
      console.error('更新考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async deleteExam(examId) {
    try {
      console.log('使用云开发API删除考试')
      return await CloudApi.deleteExam(examId)
    } catch (error) {
      console.error('删除考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getUpcomingExams(days = 7, limit = 10) {
    try {
      console.log('使用云开发API获取即将到来的考试')
      const result = await CloudApi.getUpcomingExams(days, limit)

      if (result.success && Array.isArray(result.data)) {
        // 使用ExamModel格式化考试列表
        result.data = ExamModel.formatList(result.data)
      }

      return result
    } catch (error) {
      console.error('获取即将到来的考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getExamStats(dateRange = null) {
    try {
      console.log('使用云开发API获取考试统计')
      return await CloudApi.getExamStats(dateRange)
    } catch (error) {
      console.error('获取考试统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 新增：考试数据验证方法
  static validateExamData(examData) {
    try {
      return ExamModel.validateData(examData)
    } catch (error) {
      console.error('验证考试数据失败:', error)
      return { isValid: false, errors: [error.message] }
    }
  }

  // 新增：获取默认考试数据
  static getDefaultExamData() {
    return ExamModel.getDefaultData()
  }

  // 新增：格式化考试数据为显示格式
  static formatExamForDisplay(examData) {
    try {
      const examModel = new ExamModel(examData)
      return examModel.toDisplayFormat()
    } catch (error) {
      console.error('格式化考试显示数据失败:', error)
      return examData
    }
  }

  // 新增：批量格式化考试列表为显示格式
  static formatExamListForDisplay(examList) {
    try {
      return ExamModel.toDisplayList(examList)
    } catch (error) {
      console.error('批量格式化考试显示数据失败:', error)
      return examList
    }
  }

  // 新增：处理旧格式考试数据
  static migrateLegacyExamData(oldExamData) {
    try {
      const examModel = ExamModel.fromLegacyData(oldExamData)
      return { success: true, data: examModel.data }
    } catch (error) {
      console.error('迁移旧格式考试数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 复习管理API - 强制使用云开发
  static async getStudyStats(dateRange = null, groupBy = 'day') {
    console.log('使用云开发API获取复习统计')
    return await CloudApi.getStudyStats(dateRange, groupBy)
  }

  // 统计数据管理API
  static async getUserStats(timeRange = 'week') {
    console.log('使用云开发API获取用户统计')
    return await CloudApi.getUserStats(timeRange)
  }

  // 获取真实的用户统计数据 - 直接从源数据表查询
  static async getUserRealStats(timeRange = 'month') {
    console.log('使用云开发API获取真实用户统计数据')
    try {
      // 并行获取各种统计数据
      const [
        examsResult,
        tasksResult,
        studySessionsResult,
        pomodoroResult,
        achievementsResult
      ] = await Promise.all([
        CloudApi.getExams({}, 1000, 0), // 获取所有考试
        CloudApi.getTasks({}, 1000, 0), // 获取所有任务
        CloudApi.getStudyStats(this.getDateRange(timeRange), 'day'), // 获取复习统计
        CloudApi.getPomodoroStats(this.getDateRange(timeRange)), // 获取番茄钟统计
        CloudApi.getUserAchievements() // 获取成就数据
      ])

      // 计算真实统计数据
      const realStats = this.calculateRealUserStats({
        exams: examsResult.success ? examsResult.data : [],
        tasks: tasksResult.success ? tasksResult.data : [],
        studyStats: studySessionsResult.success ? studySessionsResult.data : {},
        pomodoroStats: pomodoroResult.success ? pomodoroResult.data : {},
        achievements: achievementsResult.success ? achievementsResult.data : []
      }, timeRange)

      return { success: true, data: realStats }
    } catch (error) {
      console.error('获取真实用户统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 计算日期范围
  static getDateRange(timeRange) {
    const endDate = new Date()
    const startDate = new Date()

    switch (timeRange) {
      case 'day':
        startDate.setDate(endDate.getDate() - 1)
        break
      case 'week':
        startDate.setDate(endDate.getDate() - 7)
        break
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1)
        break
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1)
        break
    }

    return {
      start: startDate.toISOString(),
      end: endDate.toISOString()
    }
  }

  // 计算真实用户统计数据
  static calculateRealUserStats(data, timeRange) {
    const { exams, tasks, studyStats, pomodoroStats, achievements } = data

    // 1. 计算备考天数 - 从最早的考试或任务创建日期开始
    let studyDays = 0
    const now = new Date()

    // 找到最早的考试创建日期
    const earliestExamDate = exams.length > 0
      ? Math.min(...exams.map(exam => new Date(exam.createTime || exam.createdAt || now).getTime()))
      : now.getTime()

    // 找到最早的任务创建日期
    const earliestTaskDate = tasks.length > 0
      ? Math.min(...tasks.map(task => new Date(task.createTime || task.createdAt || now).getTime()))
      : now.getTime()

    // 取最早的日期
    const startDate = new Date(Math.min(earliestExamDate, earliestTaskDate))
    if (startDate < now) {
      studyDays = Math.ceil((now - startDate) / (1000 * 60 * 60 * 24))
    }

    // 2. 计算完成复习数量 - 直接统计完成的任务
    const completedTasks = tasks.filter(task => task.completed === true).length

    // 3. 计算专注时长 - 从复习会话和番茄钟统计
    let totalStudyMinutes = 0

    // 从复习统计中获取总时长
    if (studyStats.totalDuration) {
      totalStudyMinutes += Math.round(studyStats.totalDuration / 60) // 转换为分钟
    }

    // 从番茄钟统计中获取专注时长
    if (pomodoroStats.totalFocusTime) {
      totalStudyMinutes += pomodoroStats.totalFocusTime // 番茄钟时长通常已经是分钟
    }

    // 格式化时长显示
    const studyTime = this.formatStudyTime(totalStudyMinutes)

    // 4. 计算获得成就 - 基于真实数据或虚拟成就
    let achievementCount = 0
    if (achievements && achievements.length > 0) {
      achievementCount = achievements.filter(achievement => achievement.unlocked).length
    } else {
      // 如果没有成就系统，基于用户行为计算虚拟成就
      achievementCount = this.calculateVirtualAchievements({
        studyDays,
        completedTasks,
        totalStudyMinutes,
        examsCount: exams.length,
        pomodoroCount: pomodoroStats.completedSessions || 0
      })
    }

    return {
      studyDays,
      completedTasks,
      studyTime,
      achievementCount,
      // 额外的详细数据
      totalExams: exams.length,
      totalTasks: tasks.length,
      totalStudyMinutes,
      pomodoroSessions: pomodoroStats.completedSessions || 0,
      // 趋势数据（暂时设为静态，后续可以实现真实趋势计算）
      trends: {
        studyDays: '+0',
        completedTasks: this.calculateTaskTrend(tasks, timeRange),
        studyTime: '+0%',
        achievements: '+0'
      }
    }
  }

  // 格式化复习时长
  static formatStudyTime(minutes) {
    if (minutes < 60) {
      return `${minutes}m`
    } else {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      return remainingMinutes > 0 ? `${hours}h${remainingMinutes}m` : `${hours}h`
    }
  }

  // 计算虚拟成就
  static calculateVirtualAchievements(stats) {
    let count = 0

    // 基于复习天数的成就
    if (stats.studyDays >= 7) count++ // 坚持一周
    if (stats.studyDays >= 30) count++ // 坚持一月
    if (stats.studyDays >= 100) count++ // 百日坚持

    // 基于完成任务的成就
    if (stats.completedTasks >= 10) count++ // 任务新手
    if (stats.completedTasks >= 50) count++ // 任务达人
    if (stats.completedTasks >= 100) count++ // 任务专家

    // 基于复习时长的成就
    if (stats.totalStudyMinutes >= 300) count++ // 5小时专注
    if (stats.totalStudyMinutes >= 1200) count++ // 20小时专注
    if (stats.totalStudyMinutes >= 3600) count++ // 60小时专注

    // 基于番茄钟的成就
    if (stats.pomodoroCount >= 10) count++ // 番茄钟新手
    if (stats.pomodoroCount >= 50) count++ // 专注达人

    return count
  }

  // 计算任务完成趋势
  static calculateTaskTrend(tasks, timeRange) {
    // 简化的趋势计算，实际应该比较不同时间段的数据
    const recentTasks = tasks.filter(task => {
      const taskDate = new Date(task.createTime || task.createdAt)
      const daysAgo = (new Date() - taskDate) / (1000 * 60 * 60 * 24)
      return daysAgo <= (timeRange === 'week' ? 7 : 30)
    })

    const recentCompleted = recentTasks.filter(task => task.completed).length
    return recentCompleted > 0 ? `+${recentCompleted}` : '+0'
  }

  static async updateDailyStats(statsData) {
    console.log('使用云开发API更新每日统计')
    return await CloudApi.updateDailyStats(statsData)
  }

  static async getDailyStats(date = null) {
    console.log('使用云开发API获取每日统计')
    return await CloudApi.getDailyStats(date)
  }

  // 成就系统API
  static async getUserAchievements(category = null) {
    console.log('使用云开发API获取用户成就')
    return await CloudApi.getUserAchievements(category)
  }

  static async checkAchievements(triggerType, value) {
    console.log('使用云开发API检查成就')
    return await CloudApi.checkAchievements(triggerType, value)
  }

  static async getAchievementStats() {
    console.log('使用云开发API获取成就统计')
    return await CloudApi.getAchievementStats()
  }

  // 备考搭子API
  static async createStudyGroup(examId, examName, groupName = null) {
    console.log('使用云开发API创建搭子小组')
    return await CloudApi.createStudyGroup(examId, examName, groupName)
  }

  static async verifyInviteCode(inviteCode) {
    console.log('使用云开发API验证邀请码')
    return await CloudApi.verifyInviteCode(inviteCode)
  }

  static async joinStudyGroup(inviteCode) {
    console.log('使用云开发API加入搭子小组')
    return await CloudApi.joinStudyGroup(inviteCode)
  }

  static async leaveStudyGroup(groupId) {
    console.log('使用云开发API退出搭子小组')
    return await CloudApi.leaveStudyGroup(groupId)
  }

  static async getMyStudyGroups() {
    console.log('使用云开发API获取我的搭子小组')
    return await CloudApi.getMyStudyGroups()
  }

  static async getStudyGroupDetail(groupId) {
    console.log('使用云开发API获取搭子小组详情')
    return await CloudApi.getStudyGroupDetail(groupId)
  }

  static async getStudyGroupByExam(examId) {
    console.log('使用云开发API获取考试的搭子小组')
    return await CloudApi.getStudyGroupByExam(examId)
  }

  // 批量获取多个考试的搭子组信息
  static async getBatchStudyGroupsByExam(examIds) {
    console.log('使用云开发API批量获取搭子组')
    return await CloudApi.getBatchStudyGroupsByExam(examIds)
  }

  static async shareGroupPlan(groupId, planData) {
    console.log('使用云开发API分享小组计划')
    return await CloudApi.shareGroupPlan(groupId, planData)
  }

  static async copyGroupPlan(shareId) {
    console.log('使用云开发API复制小组计划')
    return await CloudApi.copyGroupPlan(shareId)
  }

  static async likeGroupPlan(shareId) {
    console.log('使用云开发API点赞小组计划')
    return await CloudApi.likeGroupPlan(shareId)
  }

  static async fixGroupData() {
    console.log('使用云开发API修复小组数据')
    return await CloudApi.fixGroupData()
  }



  static async getPomodoroStats(dateRange = null) {
    console.log('使用云开发API获取番茄钟统计')
    return await CloudApi.getPomodoroStats(dateRange)
  }

  static async addPomodoroSession(pomodoroData) {
    console.log('使用云开发API添加番茄钟会话')
    return await CloudApi.addPomodoroSession(pomodoroData)
  }

  // 获取考试的科目统计信息 - 基于考试定义的科目列表
  static async getExamSubjectStats(examId) {
    console.log('获取考试科目统计信息，examId:', examId)
    try {
      // 并行获取考试信息和任务信息
      const [examResult, tasksResult] = await Promise.all([
        CloudApi.getExamById(examId),
        CloudApi.getTasks({ examId }, 1000, 0)
      ])

      if (!examResult.success || !examResult.data) {
        console.log('获取考试信息失败')
        return { success: true, data: [] }
      }

      const exam = examResult.data
      const tasks = tasksResult.success ? tasksResult.data : []

      // 从考试数据中获取科目列表
      const examSubjects = this.getExamSubjects(exam)
      console.log('考试定义的科目:', examSubjects)

      // 基于考试科目生成统计数据（包含无任务的科目）
      const subjectStats = this.calculateSubjectStatsFromExam(examSubjects, tasks)

      return { success: true, data: subjectStats }
    } catch (error) {
      console.error('获取考试科目统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 从考试数据中提取科目列表
  static getExamSubjects(exam) {
    let subjects = []

    // 检查不同可能的科目字段
    if (exam.subject) {
      if (Array.isArray(exam.subject)) {
        subjects = exam.subject.filter(s => s && s.trim())
      } else if (typeof exam.subject === 'string') {
        try {
          // 尝试解析JSON字符串
          const parsed = JSON.parse(exam.subject)
          if (Array.isArray(parsed)) {
            subjects = parsed.filter(s => s && s.trim())
          } else {
            subjects = [exam.subject.trim()].filter(s => s)
          }
        } catch {
          // 不是JSON，作为普通字符串处理
          subjects = [exam.subject.trim()].filter(s => s)
        }
      }
    }

    // 检查其他可能的科目字段（兼容性）
    if (subjects.length === 0 && exam.subjects && Array.isArray(exam.subjects)) {
      subjects = exam.subjects.filter(s => s && s.trim())
    }

    // 如果没有科目，使用默认科目
    if (subjects.length === 0) {
      subjects = ['通用']
    }

    return subjects
  }

  // 基于考试科目生成统计数据（包含无任务的科目）
  static calculateSubjectStatsFromExam(examSubjects, tasks) {
    const subjectStats = []

    // 为每个考试科目创建统计数据
    examSubjects.forEach(subjectName => {
      // 筛选该科目的任务
      const subjectTasks = tasks.filter(task => task.subject === subjectName)
      const totalTasks = subjectTasks.length
      const completedTasks = subjectTasks.filter(task => task.completed).length
      const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0

      // 根据进度设置颜色
      let progressColor = '#ff4d4f' // 红色 - 默认
      if (progress >= 80) {
        progressColor = '#52c41a' // 绿色
      } else if (progress >= 60) {
        progressColor = '#fa8c16' // 橙色
      } else if (progress >= 40) {
        progressColor = '#1890ff' // 蓝色
      }

      subjectStats.push({
        name: subjectName,
        totalTasks: totalTasks,
        completedTasks: completedTasks,
        progress: progress,
        progressColor: progressColor,
        color: SmartApi.getSubjectColor(subjectName),
        icon: SmartApi.getSubjectIcon(subjectName)
      })
    })

    // 按进度排序，进度高的在前，但无任务的科目排在最后
    return subjectStats.sort((a, b) => {
      // 如果都有任务或都没有任务，按进度排序
      if ((a.totalTasks > 0 && b.totalTasks > 0) || (a.totalTasks === 0 && b.totalTasks === 0)) {
        return b.progress - a.progress
      }
      // 有任务的排在前面
      return b.totalTasks - a.totalTasks
    })
  }

  // 数据中心专用API - 为数据中心页面提供所需的方法
  static async getEfficiencyData(dateRange = null) {
    console.log('使用云开发API获取效率数据')
    try {
      // 获取复习统计数据来计算效率
      const studyStatsResult = await CloudApi.getStudyStats(dateRange, 'day')
      const taskStatsResult = await CloudApi.getTaskStats(dateRange)
      const pomodoroStatsResult = await CloudApi.getPomodoroStats(dateRange)

      if (studyStatsResult.success && taskStatsResult.success && pomodoroStatsResult.success) {
        // 处理效率数据
        const efficiencyData = this.calculateEfficiencyData(
          studyStatsResult.data,
          taskStatsResult.data,
          pomodoroStatsResult.data
        )
        return { success: true, data: efficiencyData }
      } else {
        return { success: false, error: '获取效率数据失败' }
      }
    } catch (error) {
      console.error('获取效率数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getUserExams(filter = {}) {
    console.log('使用云开发API获取用户考试数据')
    try {
      // 使用现有的 getExams 方法，但不限制数量以获取所有考试
      return await this.getExams(filter, 1000, 0)
    } catch (error) {
      console.error('获取用户考试数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getUserTasks(filter = {}) {
    console.log('使用云开发API获取用户任务数据')
    try {
      // 使用现有的 getTasks 方法，但不限制数量以获取所有任务
      return await this.getTasks(filter, 1000, 0)
    } catch (error) {
      console.error('获取用户任务数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 计算效率数据的辅助方法
  static calculateEfficiencyData(studyStats, taskStats, pomodoroStats) {
    const days = ['一', '二', '三', '四', '五', '六', '日']
    const now = new Date()
    const result = []

    // 确保数据存在且格式正确
    const studyByDate = studyStats?.byDate || {}
    const tasksByDate = taskStats?.byDate || {}
    const pomodoroByDate = pomodoroStats?.byDate || {}

    for (let i = 6; i >= 0; i--) {
      const date = new Date(now)
      date.setDate(now.getDate() - i)

      const dayName = days[date.getDay() === 0 ? 6 : date.getDay() - 1]
      const dateStr = date.toISOString().split('T')[0]

      // 从统计数据中查找对应日期的数据
      const dayStudyStats = studyByDate[dateStr] || {}
      const dayTaskStats = tasksByDate[dateStr] || {}
      const dayPomodoroStats = pomodoroByDate[dateStr] || {}

      // 计算效率分数 (0-100)
      let efficiency = 0

      // 基于完成任务数量 (40%) - 如果没有任务数据，给基础分
      const completedTasks = dayTaskStats.completed || 0
      const totalTasks = dayTaskStats.total || 0
      if (totalTasks > 0) {
        efficiency += (completedTasks / totalTasks) * 40
      } else if (completedTasks > 0) {
        // 如果有完成任务但没有总数统计，给予部分分数
        efficiency += 20
      }

      // 基于复习时长 (30%) - duration 单位可能是秒
      const studyDuration = dayStudyStats.duration || 0
      const studyMinutes = studyDuration > 1000 ? studyDuration / 60 : studyDuration // 处理秒和分钟的单位差异
      const targetMinutes = 120 // 目标2小时
      efficiency += Math.min(studyMinutes / targetMinutes, 1) * 30

      // 基于番茄钟专注次数 (30%)
      const pomodoroCount = dayPomodoroStats.count || dayPomodoroStats.completed || 0
      const targetPomodoros = 4 // 目标4个番茄钟
      efficiency += Math.min(pomodoroCount / targetPomodoros, 1) * 30

      result.push({
        day: dayName,
        date: dateStr,
        efficiency: Math.round(efficiency),
        studyTime: Math.round(studyMinutes),
        completedTasks: completedTasks,
        pomodoroCount: pomodoroCount
      })
    }

    return result
  }

  // 获取科目颜色
  static getSubjectColor(subject) {
    const colors = {
      '数学': '#1890ff',
      '英语': '#52c41a',
      '语文': '#fa8c16',
      '物理': '#722ed1',
      '化学': '#eb2f96',
      '生物': '#13c2c2',
      '政治': '#f5222d',
      '历史': '#fa541c',
      '地理': '#a0d911',
      '计算机': '#1890ff',
      '专业课': '#722ed1',
      '公共课': '#52c41a',
      '通用': '#666666'
    }
    return colors[subject] || '#666666'
  }

  // 获取科目图标
  static getSubjectIcon(subject) {
    const icons = {
      '数学': '📐',
      '英语': '🔤',
      '语文': '📝',
      '物理': '⚛️',
      '化学': '🧪',
      '生物': '🧬',
      '政治': '🏛️',
      '历史': '📜',
      '地理': '🌍',
      '计算机': '💻',
      '专业课': '📚',
      '公共课': '📖',
      '通用': '📖'
    }
    return icons[subject] || '📖'
  }

  // 初始化数据 - 强制使用云开发
  static async initData() {
    console.log('使用云开发初始化数据')
    try {
      const result = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: { action: 'init' }
      })
      return result.result || { success: false, error: '云函数调用失败' }
    } catch (error) {
      console.error('云开发初始化失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 用户管理API
  static async cleanDuplicateUsers() {
    console.log('清理重复用户数据')
    try {
      const result = await wx.cloud.callFunction({
        name: 'userManager',
        data: { action: 'cleanDuplicateUsers' }
      })
      return result.result
    } catch (error) {
      console.error('清理重复用户失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getUserData() {
    console.log('获取用户数据统计')
    try {
      const result = await wx.cloud.callFunction({
        name: 'userManager',
        data: { action: 'getUserData' }
      })
      return result.result
    } catch (error) {
      console.error('获取用户数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async deleteUserData() {
    console.log('删除用户所有数据')
    try {
      const result = await wx.cloud.callFunction({
        name: 'userManager',
        data: { action: 'deleteUserData' }
      })
      return result.result
    } catch (error) {
      console.error('删除用户数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 头像上传API
  static async uploadAvatar(filePath) {
    console.log('SmartApi: 开始上传用户头像, filePath:', filePath)

    try {
      // 检查参数
      if (!filePath) {
        throw new Error('文件路径不能为空')
      }

      // 先获取上传路径
      console.log('SmartApi: 获取上传路径')
      const pathResult = await new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'uploadAvatar',
          data: {
            action: 'getUploadUrl',
            data: {
              fileName: filePath.split('/').pop() || 'avatar.jpg',
              fileType: 'image'
            }
          },
          success: resolve,
          fail: reject
        })
      })

      console.log('SmartApi: 获取上传路径结果:', JSON.stringify(pathResult, (key, value) => {
        // 安全地序列化，跳过Symbol值
        if (typeof value === 'symbol') {
          return value.toString()
        }
        return value
      }))

      if (!pathResult.result || !pathResult.result.success) {
        const errorResult = pathResult.result || { success: false, error: '获取上传路径失败' }
        console.log('SmartApi: 获取上传路径失败:', JSON.stringify(errorResult))
        return errorResult
      }

      const cloudPath = pathResult.result.data.cloudPath
      console.log('SmartApi: 云存储路径:', cloudPath)

      // 上传文件到云存储
      console.log('SmartApi: 开始上传文件到云存储')
      const uploadResult = await new Promise((resolve, reject) => {
        wx.cloud.uploadFile({
          cloudPath: cloudPath,
          filePath: filePath,
          success: (res) => {
            console.log('SmartApi: 云存储上传成功回调:', res)
            resolve(res)
          },
          fail: (error) => {
            console.error('SmartApi: 云存储上传失败回调:', error)
            reject(error)
          }
        })
      })

      console.log('SmartApi: 云存储上传结果:', JSON.stringify(uploadResult, (key, value) => {
        if (typeof value === 'symbol') {
          return value.toString()
        }
        return value
      }))

      if (uploadResult.fileID) {
        // 调用云函数更新用户头像
        console.log('SmartApi: 更新用户头像记录')

        // 准备云函数参数，确保没有Symbol值
        const functionData = {
          action: 'uploadAvatar',
          data: {
            fileID: String(uploadResult.fileID), // 确保是字符串
            fileName: String(filePath.split('/').pop() || 'avatar.jpg'),
            fileSize: Number(uploadResult.fileSize || 0)
          }
        }

        console.log('SmartApi: 云函数参数:', JSON.stringify(functionData))

        const updateResult = await new Promise((resolve, reject) => {
          wx.cloud.callFunction({
            name: 'uploadAvatar',
            data: functionData,
            success: (res) => {
              console.log('SmartApi: 云函数调用成功回调:', res)
              resolve(res)
            },
            fail: (error) => {
              console.error('SmartApi: 云函数调用失败回调:', error)
              reject(error)
            }
          })
        })

        console.log('SmartApi: 更新用户头像结果:', JSON.stringify(updateResult, (key, value) => {
          if (typeof value === 'symbol') {
            return value.toString()
          }
          return value
        }))

        if (updateResult.result) {
          // 安全地处理返回结果
          const result = updateResult.result
          console.log('SmartApi: 最终返回结果:', JSON.stringify(result))

          // 确保返回的数据不包含Symbol
          return {
            success: Boolean(result.success),
            data: result.data ? {
              avatarUrl: String(result.data.avatarUrl || ''),
              fileName: String(result.data.fileName || ''),
              fileSize: Number(result.data.fileSize || 0),
              uploadTime: String(result.data.uploadTime || '')
            } : null,
            error: result.error ? String(result.error) : null
          }
        } else {
          return { success: false, error: '更新用户头像记录失败' }
        }
      } else {
        return { success: false, error: '文件上传到云存储失败，未获得fileID' }
      }
    } catch (error) {
      console.error('SmartApi: 上传头像异常:', error)

      // 安全地获取错误信息
      let errorMessage = '上传头像失败'
      if (error && typeof error === 'object') {
        if (error.message && typeof error.message === 'string') {
          errorMessage = error.message
        } else if (error.errMsg && typeof error.errMsg === 'string') {
          errorMessage = error.errMsg
        }
      } else if (typeof error === 'string') {
        errorMessage = error
      }

      return { success: false, error: errorMessage }
    }
  }

  static async deleteAvatar(fileID) {
    console.log('删除用户头像')
    try {
      const result = await wx.cloud.callFunction({
        name: 'uploadAvatar',
        data: {
          action: 'deleteAvatar',
          data: { fileID }
        }
      })
      return result.result
    } catch (error) {
      console.error('删除头像失败:', error)
      return { success: false, error: error.message }
    }
  }
}

module.exports = SmartApi
