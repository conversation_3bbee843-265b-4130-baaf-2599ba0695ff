# 考试中心页面组件设计规范

## 📋 文档概述

**文档版本**: v1.0  
**创建日期**: 2025-01-10  
**设计师**: AI交互设计专家  
**文档类型**: 组件设计规范

## 🧩 组件架构

### 组件层次结构
```
ExamCenter (页面容器)
├── StatsSection (统计区域)
│   └── StatCard (统计卡片) × 4
├── FilterSection (筛选区域)
│   └── FilterTab (筛选标签) × N
├── ExamListSection (考试列表区域)
│   └── ExamCard (考试卡片) × N
│       ├── ExamHeader (卡片头部)
│       ├── ExamMeta (元信息)
│       ├── CountdownDisplay (倒计时)
│       ├── ProgressSection (进度区域)
│       ├── SubjectTags (科目标签)
│       └── ActionButtons (操作按钮)
└── EmptyState (空状态)
    ├── EmptyIcon (空状态图标)
    ├── EmptyContent (空状态内容)
    └── QuickActions (快捷操作)
```

## 📊 StatCard 统计卡片组件

### 组件属性
```typescript
interface StatCardProps {
  label: string;          // 统计标签
  value: string | number; // 统计数值
  color?: string;         // 主题色
  clickable?: boolean;    // 是否可点击
  loading?: boolean;      // 加载状态
  onClick?: () => void;   // 点击回调
}
```

### 视觉规范
```css
.stat-card {
  /* 布局 */
  flex: 1;
  min-height: 120rpx;
  padding: 24rpx 16rpx;
  
  /* 外观 */
  background: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
  
  /* 交互 */
  transition: all 0.2s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(0,0,0,0.1);
}

.stat-card:active {
  transform: scale(0.98);
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1890FF;
  line-height: 1.2;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #646566;
  line-height: 1.4;
}
```

### 状态变化
```css
/* 加载状态 */
.stat-card--loading .stat-value {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  color: transparent;
}

/* 点击状态 */
.stat-card--clickable:active {
  background: #F5F5F5;
}

/* 禁用状态 */
.stat-card--disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
```

## 🏷️ FilterTab 筛选标签组件

### 组件属性
```typescript
interface FilterTabProps {
  label: string;          // 标签文本
  value: string;          // 标签值
  count?: number;         // 数量徽章
  active?: boolean;       // 激活状态
  disabled?: boolean;     // 禁用状态
  onClick?: (value: string) => void; // 点击回调
}
```

### 视觉规范
```css
.filter-tab {
  /* 布局 */
  position: relative;
  padding: 24rpx 32rpx;
  min-width: 120rpx;
  
  /* 文字 */
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  
  /* 交互 */
  transition: all 0.3s ease;
  cursor: pointer;
}

/* 默认状态 */
.filter-tab--default {
  color: #8C8C8C;
}

/* 激活状态 */
.filter-tab--active {
  color: #1890FF;
  font-weight: 600;
}

.filter-tab--active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 4rpx;
  background: #1890FF;
  border-radius: 2rpx;
  animation: slideIn 0.3s ease;
}

/* 徽章 */
.filter-tab__badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 8rpx;
  background: #FF4444;
  color: #FFFFFF;
  font-size: 18rpx;
  font-weight: 600;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
```

### 动画效果
```css
@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 30rpx;
    opacity: 1;
  }
}

.filter-tab__badge {
  animation: bounceIn 0.3s ease;
}

@keyframes bounceIn {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
```

## 📋 ExamCard 考试卡片组件

### 组件属性
```typescript
interface ExamCardProps {
  exam: {
    id: string;
    name: string;
    subjectDisplay: string;
    date: string;
    status: string;
    statusText: string;
    countdownText?: string;
    countdownColor?: string;
    progress?: number;
    progressColor?: string;
    progressText?: string;
    isUrgent?: boolean;
    daysLeft?: number;
  };
  onCardClick?: (id: string) => void;
  onStatusToggle?: (id: string) => void;
  onActionClick?: (id: string, action: string) => void;
}
```

### 卡片结构
```css
.exam-card {
  /* 布局 */
  background: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  
  /* 阴影 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  
  /* 交互 */
  transition: all 0.3s ease;
  cursor: pointer;
}

.exam-card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.exam-card:active {
  transform: scale(0.98);
}

/* 紧急状态样式 */
.exam-card--urgent {
  background: linear-gradient(135deg, #FFF5F5 0%, #FFFFFF 100%);
  border: 2rpx solid #FFE7E7;
  box-shadow: 0 6rpx 16rpx rgba(255, 68, 68, 0.15);
}

/* 已完成状态样式 */
.exam-card--completed {
  opacity: 0.8;
  background: linear-gradient(135deg, #F6FFED 0%, #FFFFFF 100%);
  border: 1rpx solid #B7EB8F;
}

/* 已过期状态样式 */
.exam-card--past {
  opacity: 0.6;
  background: #FAFAFA;
  border: 1rpx solid #E8E8E8;
}
```

### 卡片头部
```css
.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx 24rpx 16rpx;
}

.exam-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
  flex: 1;
  margin-right: 16rpx;
}

.status-toggle {
  min-width: 60rpx;
  height: 32rpx;
  padding: 0 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  border-radius: 16rpx;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-toggle--complete {
  background: #52C41A;
  color: #FFFFFF;
}

.status-toggle--restore {
  background: transparent;
  color: #8C8C8C;
  border: 1rpx solid #D9D9D9;
}
```

### 元信息区域
```css
.exam-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24rpx 16rpx;
}

.exam-info {
  font-size: 24rpx;
  color: #8C8C8C;
  line-height: 1.4;
}

.exam-status-tag {
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  border-radius: 12rpx;
  line-height: 1.2;
}

.exam-status-tag--preparing {
  background: #E6F7FF;
  color: #1890FF;
}

.exam-status-tag--upcoming {
  background: #FFF7E6;
  color: #FA8C16;
}

.exam-status-tag--completed {
  background: #F6FFED;
  color: #52C41A;
}
```

### 倒计时显示
```css
.countdown-display {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 0 24rpx 12rpx;
}

.countdown-text {
  font-size: 24rpx;
  font-weight: 600;
  line-height: 1.2;
}

.urgency-indicator {
  font-size: 20rpx;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}
```

### 进度区域
```css
.progress-section {
  padding: 12rpx 24rpx 16rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.progress-label {
  font-size: 24rpx;
  color: #646566;
  line-height: 1.2;
}

.progress-text {
  font-size: 22rpx;
  font-weight: 600;
  line-height: 1.2;
}

.progress-bar {
  height: 6rpx;
  background: #F0F0F0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 3rpx;
  transition: width 0.5s ease;
}
```

### 操作按钮区域
```css
.action-buttons {
  display: flex;
  gap: 8rpx;
  padding: 16rpx 24rpx 24rpx;
  border-top: 1rpx solid #F0F0F0;
}

.action-button {
  flex: 1;
  height: 64rpx;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 8rpx;
  border: 1rpx solid #D9D9D9;
  background: #FFFFFF;
  color: #595959;
  transition: all 0.2s ease;
  cursor: pointer;
}

.action-button:hover {
  border-color: #1890FF;
  color: #1890FF;
}

.action-button:active {
  transform: scale(0.95);
}

.action-button--primary {
  background: #1890FF;
  border-color: #1890FF;
  color: #FFFFFF;
}

.action-button--primary:hover {
  background: #40A9FF;
  border-color: #40A9FF;
}
```

#### 按钮配置说明
```
优化后按钮: [复习] [任务] [更多]
移除按钮: [编辑] (通过左滑操作实现)

按钮功能:
- 复习: 跳转到复习页面 (主要操作)
- 任务: 查看考试相关任务
- 更多: 显示更多操作选项
```
```

## 🚫 EmptyState 空状态组件

### 组件属性
```typescript
interface EmptyStateProps {
  type: 'global' | 'filter';
  filterType?: string;
  onActionClick?: () => void;
  onTemplateClick?: (template: string) => void;
}
```

### 视觉规范
```css
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 48rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 96rpx;
  color: #D9D9D9;
  margin-bottom: 24rpx;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8rpx);
  }
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.empty-description {
  font-size: 24rpx;
  color: #8C8C8C;
  margin-bottom: 32rpx;
  line-height: 1.5;
  max-width: 400rpx;
}

.empty-action {
  min-width: 200rpx;
  height: 88rpx;
  font-size: 28rpx;
  font-weight: 600;
  background: #1890FF;
  color: #FFFFFF;
  border: none;
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 32rpx;
}

.empty-action:hover {
  background: #40A9FF;
  transform: translateY(-2rpx);
}

.empty-action:active {
  transform: scale(0.95);
}
```

### 快捷模板
```css
.quick-templates {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
  justify-content: center;
}

.template-button {
  width: 120rpx;
  height: 80rpx;
  font-size: 22rpx;
  color: #595959;
  background: #FFFFFF;
  border: 1rpx solid #D9D9D9;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1.3;
}

.template-button:hover {
  border-color: #1890FF;
  color: #1890FF;
  transform: translateY(-2rpx);
}

.template-button:active {
  transform: scale(0.95);
}
```

## 🎨 通用样式规范

### 动画缓动函数
```css
:root {
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
  --ease-out-back: cubic-bezier(0.34, 1.56, 0.64, 1);
}
```

### 响应式断点
```css
/* 小屏幕 */
@media (max-width: 375px) {
  .exam-card {
    margin-bottom: 12rpx;
  }
  
  .action-buttons {
    flex-wrap: wrap;
  }
  
  .action-button {
    flex: 0 0 calc(50% - 4rpx);
  }
}

/* 大屏幕 */
@media (min-width: 414px) {
  .stats-grid {
    gap: 20rpx;
  }
  
  .exam-card {
    margin-bottom: 20rpx;
  }
}
```

### 深色模式适配
```css
@media (prefers-color-scheme: dark) {
  .exam-card {
    background: #2A2A2A;
    border-color: #404040;
  }
  
  .exam-title {
    color: #E8E8E8;
  }
  
  .exam-info {
    color: #A0A0A0;
  }
  
  .empty-state {
    background: #1A1A1A;
  }
  
  .empty-title {
    color: #E8E8E8;
  }
}
```

---

**文档状态**: ✅ 已完成  
**组件版本**: v1.0  
**兼容性**: 支持微信小程序、H5、App  
**维护周期**: 每季度更新一次
