// pages/share-plan/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    groupId: '',
    examId: '',
    groupInfo: null,
    examInfo: null,
    relatedTasks: [],
    loading: false
  },

  async onLoad(options) {
    const { groupId, examId } = options
    
    if (!groupId || !examId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      wx.navigateBack()
      return
    }

    this.setData({ groupId, examId })
    await this.loadData()
  },

  // 加载数据
  async loadData() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      // 获取小组信息
      const groupResult = await SmartApi.getStudyGroupDetail(this.data.groupId)
      if (groupResult.success && groupResult.data) {
        this.setData({ groupInfo: groupResult.data.group })
      }

      // 获取考试信息
      try {
        const examResult = await SmartApi.getExamById(this.data.examId)
        if (examResult.success && examResult.data) {
          this.setData({ examInfo: examResult.data })
        }
      } catch (error) {
        console.error('获取考试信息失败:', error)
        // 如果获取考试信息失败，使用默认信息
        this.setData({
          examInfo: {
            title: '考试计划',
            description: '复习计划'
          }
        })
      }

      // 获取相关任务
      const tasksResult = await SmartApi.getTasks({ examId: this.data.examId }, 100, 0)
      if (tasksResult.success && tasksResult.data) {
        this.setData({ relatedTasks: tasksResult.data })
      }

      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      console.error('加载数据失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  },

  // 分享计划
  async onSharePlan() {
    const { groupId, examInfo, relatedTasks } = this.data

    if (relatedTasks.length === 0) {
      wx.showToast({
        title: '暂无复习计划可分享',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    try {
      // 构建计划数据
      const planData = {
        title: `${examInfo.title}复习计划`,
        description: examInfo.description || '',
        tasks: relatedTasks.map(task => ({
          title: task.title,
          description: task.description,
          subject: task.subject,
          priority: task.priority,
          estimatedDuration: task.estimatedDuration
        })),
        totalTasks: relatedTasks.length,
        estimatedDays: this.calculateEstimatedDays(relatedTasks)
      }

      const result = await SmartApi.shareGroupPlan(groupId, planData)

      if (result.success) {
        wx.showToast({
          title: '分享成功！',
          icon: 'success'
        })

        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: result.error || '分享失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('分享计划失败:', error)
      wx.showToast({
        title: '分享失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 计算预估天数
  calculateEstimatedDays(tasks) {
    const totalHours = tasks.reduce((sum, task) => {
      return sum + (task.estimatedDuration || 2) // 默认2小时
    }, 0)

    // 假设每天复习4小时
    return Math.ceil(totalHours / 4)
  },


})
