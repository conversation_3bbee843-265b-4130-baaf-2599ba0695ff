// pages/profile/index.js
const LoginApi = require('../../utils/loginApi')
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    userInfo: {},
    userStats: [
      { label: '备考天数', value: '0' },
      { label: '完成复习', value: '0' },
      { label: '专注时长', value: '0h' },
      { label: '获得成就', value: '0' }
    ],
    recentAchievements: [],
    previewAchievements: [
      { id: 'first_task', icon: '🌱', name: '初学者' },
      { id: 'focus_master', icon: '🎯', name: '专注大师' },
      { id: 'task_killer', icon: '⚡', name: '复习达人' },
      { id: 'streak_week', icon: '🔥', name: '坚持不懈' }
    ],
    menuSections: [],
    dataCards: [],
    quickActions: [],
    version: '1.0.0',
    isLoggedIn: false
  },

  onLoad() {
    this.checkLoginStatus()
    this.initPage()
  },

  async onShow() {
    console.log('=== Profile页面 onShow 开始执行 ===')
    this.checkLoginStatus()
    // 重新初始化页面数据
    this.initMenuSections()
    await this.loadData()
    console.log('当前页面数据:', {
      userStats: this.data.userStats,
      recentAchievements: this.data.recentAchievements,
      quickActions: this.data.quickActions,
      menuSections: this.data.menuSections,
      dataCards: this.data.dataCards
    })
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 4
      })
    }
    console.log('=== Profile页面 onShow 执行完毕 ===')
  },

  // 检查登录状态
  checkLoginStatus() {
    const app = getApp()
    const loginStatus = app.checkLoginStatus()

    console.log('个人中心页面：检查登录状态结果:', loginStatus)
    console.log('个人中心页面：用户信息详情:', JSON.stringify(loginStatus.userInfo, null, 2))

    this.setData({
      isLoggedIn: loginStatus.isLoggedIn,
      userInfo: loginStatus.userInfo || {}
    })

    // 重新初始化快捷操作（因为登录状态可能改变）
    // this.initQuickActions() // 快捷入口已移除

    return loginStatus.isLoggedIn
  },

  // 初始化页面
  initPage() {
    this.initMenuSections()
    // this.initQuickActions() // 快捷入口已移除
    this.loadUserInfo()
  },

  // 初始化菜单
  initMenuSections() {
    console.log('=== initMenuSections 被调用 ===')
    const menuSections = [
      {
        title: '',
        items: [
          {
            id: 'data_center',
            icon: '📈',
            text: '数据中心',
            action: 'dataCenter'
          },
          {
            id: 'help',
            icon: '❓',
            text: '帮助中心',
            action: 'help'
          },
          {
            id: 'feedback',
            icon: '💬',
            text: '意见反馈',
            action: 'feedback'
          },
          {
            id: 'about',
            icon: 'ℹ️',
            text: '关于我们',
            action: 'about'
          },
          {
            id: 'settings',
            icon: '⚙️',
            text: '设置',
            action: 'settings'
          }
        ]
      }
    ]

    console.log('生成的菜单数据:', menuSections)
    this.setData({ menuSections })
    console.log('menuSections setData 完成')
  },

  // 初始化快捷操作（已移除）
  // initQuickActions() {
  //   // 快捷入口功能已移除
  // },

  // 加载用户信息
  loadUserInfo() {
    console.log('个人中心页面：开始加载用户信息')
    
    const app = getApp()
    let userInfo = app.globalData.userInfo || wx.getStorageSync('kaoba_user_info') || {}

    console.log('个人中心页面：从全局数据获取的用户信息:', app.globalData.userInfo)
    console.log('个人中心页面：从本地存储获取的用户信息:', wx.getStorageSync('kaoba_user_info'))
    console.log('个人中心页面：最终使用的用户信息:', JSON.stringify(userInfo, null, 2))

    // 确保所有字段都有默认值，避免null值
    userInfo = {
      nickName: userInfo.nickName || '考试达人',
      avatarUrl: userInfo.avatarUrl || '',
      signature: userInfo.signature || '努力备考，金榜题名！',
      ...userInfo
    }

    console.log('个人中心页面：处理后的用户信息:', JSON.stringify(userInfo, null, 2))

    this.setData({ userInfo })
  },

  // 加载数据
  async loadData() {
    console.log('=== loadData 开始执行 ===')
    await this.loadUserStats()
    await this.loadRecentAchievements()
    // this.loadDataCards() // 数据卡片已移除
    console.log('=== loadData 执行完毕 ===')
  },

  // 加载用户统计
  async loadUserStats() {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      console.log('用户未登录，显示默认统计数据')
      this.setDefaultUserStats()
      return
    }

    try {
      console.log('开始加载真实用户统计数据...')

      // 使用新的真实统计API
      const result = await SmartApi.getUserRealStats('month')

      if (result.success && result.data) {
        const data = result.data
        console.log('获取到的真实统计数据:', data)

        const userStats = [
          {
            label: '备考天数',
            value: `${data.studyDays || 0}`,
            trend: data.trends?.studyDays || '+0'
          },
          {
            label: '完成复习',
            value: `${data.completedTasks || 0}`,
            trend: data.trends?.completedTasks || '+0'
          },
          {
            label: '专注时长',
            value: data.studyTime || '0h',
            trend: data.trends?.studyTime || '+0%'
          },
          {
            label: '获得成就',
            value: `${data.achievementCount || 0}`,
            trend: data.trends?.achievements || '+0'
          }
        ]

        this.setData({ userStats })
        console.log('用户统计数据设置完成:', userStats)
      } else {
        console.log('获取真实统计数据失败，使用默认数据')
        // 使用默认数据
        this.setDefaultUserStats()
      }
    } catch (error) {
      console.error('加载用户统计失败:', error)
      this.setDefaultUserStats()
    }
  },

  // 设置默认用户统计
  setDefaultUserStats() {
    console.log('=== 使用默认用户统计数据 ===')

    // 根据登录状态显示不同的默认数据
    const userStats = this.data.isLoggedIn ? [
      // 已登录但数据加载失败时的默认值
      { label: '备考天数', value: '0', trend: '' },
      { label: '完成复习', value: '0', trend: '' },
      { label: '专注时长', value: '0h', trend: '' },
      { label: '获得成就', value: '0', trend: '' }
    ] : [
      // 未登录时的提示性数据
      { label: '备考天数', value: '--', trend: '' },
      { label: '完成复习', value: '--', trend: '' },
      { label: '专注时长', value: '--', trend: '' },
      { label: '获得成就', value: '--', trend: '' }
    ]

    this.setData({ userStats })
  },

  // 加载最近成就
  async loadRecentAchievements() {
    console.log('=== loadRecentAchievements 被调用 ===')

    // 检查登录状态
    if (!this.data.isLoggedIn) {
      console.log('用户未登录，清空成就数据')
      this.setData({ recentAchievements: [] })
      return
    }

    try {
      // 尝试从成就系统获取真实数据
      const result = await SmartApi.getUserAchievements()

      if (result.success && result.data && result.data.length > 0) {
        // 使用真实成就数据，增强显示信息
        const recentAchievements = result.data.slice(0, 4).map(achievement => ({
          id: achievement._id || achievement.id,
          icon: achievement.icon || '🏆',
          name: achievement.name,
          unlocked: achievement.unlocked || false,
          isNew: achievement.isNew || false,
          progress: achievement.progress || 0,
          description: achievement.description || ''
        }))
        this.setData({ recentAchievements })
        console.log('加载真实成就数据:', recentAchievements)
      } else {
        // 使用默认示例成就
        const recentAchievements = [
          { id: 'focus_master', icon: '🎯', name: '专注大师', unlocked: false, progress: 0 },
          { id: 'task_killer', icon: '⚡', name: '复习达人', unlocked: false, progress: 0 },
          { id: 'early_bird', icon: '🌅', name: '早起鸟儿', unlocked: false, progress: 0 },
          { id: 'night_owl', icon: '🦉', name: '夜猫子', unlocked: false, progress: 0 }
        ]
        this.setData({ recentAchievements })
        console.log('使用默认成就数据')
      }
    } catch (error) {
      console.error('加载成就数据失败:', error)
      // 使用默认示例成就
      const recentAchievements = [
        { id: 'focus_master', icon: '🎯', name: '专注大师', unlocked: false, progress: 0 },
        { id: 'task_killer', icon: '⚡', name: '复习达人', unlocked: false, progress: 0 },
        { id: 'early_bird', icon: '🌅', name: '早起鸟儿', unlocked: false, progress: 0 },
        { id: 'night_owl', icon: '🦉', name: '夜猫子', unlocked: false, progress: 0 }
      ]
      this.setData({ recentAchievements })
    }

    console.log('recentAchievements setData 完成')
  },

  // 编辑个人资料
  editProfile(e) {
    console.log('点击编辑个人资料')
    wx.navigateTo({
      url: '/pages/personal-info/index'
    })
  },

  // 查看所有成就
  viewAllAchievements() {
    if (!this.data.isLoggedIn) {
      this.goToLogin()
      return
    }
    wx.navigateTo({
      url: '/pages/achievement-system/index'
    })
  },

  // 跳转到登录页面
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/index'
    })
  },

  // 显示成就详情
  showAchievementDetail(e) {
    const achievement = e.currentTarget.dataset.achievement
    if (!achievement) return

    const content = achievement.unlocked
      ? `🎉 恭喜解锁成就：${achievement.name}\n\n${achievement.description || '继续努力，解锁更多成就！'}`
      : `🔒 成就进度：${achievement.progress || 0}%\n\n${achievement.description || '继续努力即可解锁此成就！'}`

    wx.showModal({
      title: achievement.name,
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 处理菜单点击
  handleMenuTap(e) {
    const action = e.currentTarget.dataset.action
    this.handleAction(action)
  },

  // 处理快捷操作
  handleQuickAction(e) {
    const action = e.currentTarget.dataset.action
    this.handleAction(action)
  },

  // 处理操作
  handleAction(action) {
    console.log('处理操作:', action)

    switch (action) {
      case 'pomodoro':
        wx.switchTab({
          url: '/pages/pomodoro/index'
        })
        break
      case 'task':
        wx.switchTab({
          url: '/pages/task-center/index'
        })
        break
      case 'exam':
        wx.switchTab({
          url: '/pages/exam-center/index'
        })
        break
      case 'dataCenter':
        wx.navigateTo({
          url: '/pages/data-center/index'
        })
        break
      case 'dataExport':
        wx.navigateTo({
          url: '/pages/data-export/index'
        })
        break
      case 'settings':
        wx.navigateTo({
          url: '/pages/settings/index'
        })
        break
      case 'help':
        wx.navigateTo({
          url: '/pages/help-feedback/index'
        })
        break
      case 'feedback':
        wx.navigateTo({
          url: '/pages/help-feedback/index?tab=feedback'
        })
        break
      case 'about':
        wx.showModal({
          title: '关于我们',
          content: '备考助手是您的专业考试伴侣，助您高效备考，成功上岸！',
          showCancel: false,
          confirmText: '知道了'
        })
        break
      case 'share':
        this.shareApp()
        break
      case 'backup':
        this.backupData()
        break
      case 'rate':
        this.rateApp()
        break
      case 'update':
        this.checkUpdate()
        break
      case 'login':
        this.login()
        break
      case 'logout':
        this.logout()
        break
      case 'admin':
        wx.navigateTo({
          url: '/pages/admin/index'
        })
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  // 查看数据详情
  viewDataDetail(e) {
    const type = e.currentTarget.dataset.type
    wx.switchTab({
      url: '/pages/data-center/index'
    })
  },

  // 分享应用
  shareApp() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })

    wx.showToast({
      title: '请点击右上角分享',
      icon: 'none'
    })
  },

  // 备份数据
  backupData() {
    wx.showLoading({
      title: '备份中...'
    })

    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '备份成功',
        icon: 'success'
      })
    }, 2000)
  },

  // 评价应用
  rateApp() {
    wx.showModal({
      title: '应用评价',
      content: '如果您觉得这个应用对您有帮助，请给我们一个好评吧！',
      confirmText: '去评价',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '感谢您的支持',
            icon: 'success'
          })
        }
      }
    })
  },

  // 检查更新
  checkUpdate() {
    wx.showLoading({
      title: '检查中...'
    })

    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '已是最新版本',
        icon: 'success'
      })
    }, 1500)
  },

  // 登录
  async login() {
    try {
      wx.showLoading({
        title: '登录中...'
      })

      const result = await LoginApi.login()
      wx.hideLoading()

      if (result.success) {
        this.setData({
          isLoggedIn: true,
          userInfo: result.data.user
        })

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 刷新页面数据
        this.loadData()
      } else {
        wx.showToast({
          title: result.error || '登录失败',
          icon: 'error'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('登录失败:', error)
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      })
    }
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '退出登录后，数据将无法同步，确定要退出吗？',
      success: (res) => {
        if (res.confirm) {
          const app = getApp()
          const result = app.logout()

          if (result.success) {
            this.setData({
              isLoggedIn: false,
              userInfo: {}
            })

            wx.showToast({
              title: '已退出登录',
              icon: 'success'
            })

            // 跳转到登录页
            setTimeout(() => {
              wx.redirectTo({
                url: '/pages/login/index'
              })
            }, 1500)
          }
        }
      }
    })
  },

  // 跳转到登录页
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/index'
    })
  },

  // 头像变更回调
  async onAvatarChange(e) {
    const { avatarUrl, success } = e.detail

    if (success) {
      try {
        // 显示保存状态
        wx.showLoading({
          title: '保存头像...',
          mask: true
        })

        // 调用云函数保存头像到数据库
        const result = await LoginApi.updateUserProfile({
          avatarUrl: avatarUrl
        })

        if (result.success) {
          // 更新本地用户信息
          const userInfo = { ...this.data.userInfo }
          userInfo.avatarUrl = avatarUrl
          this.setData({ userInfo })

          // 更新全局用户信息
          const app = getApp()
          if (app.globalData.userInfo) {
            app.globalData.userInfo.avatarUrl = avatarUrl
            wx.setStorageSync('kaoba_user_info', app.globalData.userInfo)
          }

          wx.hideLoading()
          wx.showToast({
            title: '头像保存成功',
            icon: 'success'
          })

          console.log('头像更新并保存成功:', avatarUrl)
        } else {
          throw new Error(result.error || '保存失败')
        }
      } catch (error) {
        console.error('保存头像失败:', error)
        wx.hideLoading()
        wx.showToast({
          title: '头像保存失败',
          icon: 'none'
        })
      }
    }
  },


})