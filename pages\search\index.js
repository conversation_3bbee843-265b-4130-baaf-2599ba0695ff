// pages/search/index.js
Page({
  data: {
    searchQuery: '',
    autoFocus: true,
    showFilters: false,
    hasSearched: false,

    searchFilters: [
      { id: 'all', label: '全部', active: true },
      { id: 'task', label: '任务', active: false },
      { id: 'exam', label: '考试', active: false },
      { id: 'note', label: '笔记', active: false }
    ],

    recentSearches: [],

    hotSearches: [
      '考研数学', '英语四级', '高考复习', '公务员考试', '计算机二级'
    ],

    searchSuggestions: [
      '数学', '英语', '政治', '专业课', '复习计划'
    ],

    searchResults: {
      total: 0,
      items: []
    },

    searchTimer: null
  },

  onLoad(options) {
    this.initSearch()

    // 如果有传入的搜索关键词，直接搜索
    if (options.query) {
      this.setData({
        searchQuery: options.query,
        autoFocus: false
      })
      this.performSearch()
    }
  },

  onShow() {
    this.loadRecentSearches()
  },

  onUnload() {
    this.clearSearchTimer()
  },

  // 初始化搜索
  initSearch() {
    this.loadRecentSearches()
  },

  // 加载最近搜索
  loadRecentSearches() {
    try {
      const recentSearches = wx.getStorageSync('recentSearches') || []
      this.setData({ recentSearches })
    } catch (error) {
      console.error('加载最近搜索失败:', error)
    }
  },

  // 搜索输入处理
  onSearchInput(e) {
    const value = e.detail.value
    this.setData({ searchQuery: value })

    // 清除之前的定时器
    this.clearSearchTimer()

    // 如果有输入内容，延迟搜索
    if (value.trim()) {
      this.data.searchTimer = setTimeout(() => {
        this.performSearch()
      }, 500)
    } else {
      // 清空搜索结果
      this.setData({
        hasSearched: false,
        searchResults: { total: 0, items: [] }
      })
    }
  },

  // 执行搜索
  performSearch() {
    const query = this.data.searchQuery.trim()

    if (!query) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      })
      return
    }

    this.setData({ hasSearched: true })

    // 保存搜索记录
    this.saveSearchHistory(query)

    // 执行搜索
    this.executeSearch(query)
  },

  // 执行搜索逻辑
  executeSearch(query) {
    wx.showLoading({ title: '搜索中...' })

    // 模拟搜索延迟
    setTimeout(() => {
      const results = this.searchInData(query)

      this.setData({
        searchResults: {
          total: results.length,
          items: results
        }
      })

      wx.hideLoading()
    }, 800)
  },

  // 在数据中搜索
  searchInData(query) {
    const results = []

    try {
      // 搜索任务
      const tasks = this.searchTasks(query)
      results.push(...tasks)

      // 搜索考试
      const exams = this.searchExams(query)
      results.push(...exams)

      // 搜索笔记
      const notes = this.searchNotes(query)
      results.push(...notes)

    } catch (error) {
      console.error('搜索数据失败:', error)
    }

    return results
  },

  // 搜索任务
  searchTasks(query) {
    // 模拟任务数据搜索
    const mockTasks = [
      {
        id: 'task_001',
        type: '任务',
        title: '数学高数第一章复习',
        description: '复习极限的概念、性质和计算方法',
        icon: '📝',
        date: '2025-01-28',
        status: '进行中',
        statusColor: '#FA8C16',
        statusBg: '#FFF7E6'
      },
      {
        id: 'task_002',
        type: '任务',
        title: '英语单词背诵',
        description: '背诵考研英语核心词汇500个',
        icon: '📚',
        date: '2025-01-25',
        status: '已完成',
        statusColor: '#52C41A',
        statusBg: '#F6FFED'
      }
    ]

    return mockTasks.filter(task =>
      task.title.includes(query) ||
      task.description.includes(query)
    )
  },

  // 搜索考试
  searchExams(query) {
    // 模拟考试数据搜索
    const mockExams = [
      {
        id: 'exam_001',
        type: '考试',
        title: '2025年考研',
        description: '全国硕士研究生统一招生考试',
        icon: '🎓',
        date: '2025-12-23',
        status: '备考中',
        statusColor: '#1890FF',
        statusBg: '#E6F7FF'
      }
    ]

    return mockExams.filter(exam =>
      exam.title.includes(query) ||
      exam.description.includes(query)
    )
  },

  // 搜索笔记
  searchNotes(query) {
    // 模拟笔记数据搜索
    const mockNotes = [
      {
        id: 'note_001',
        type: '笔记',
        title: '数学极限知识点总结',
        description: '极限的定义、性质、计算方法等重点内容',
        icon: '📖',
        date: '2025-01-20',
        status: '',
        statusColor: '',
        statusBg: ''
      }
    ]

    return mockNotes.filter(note =>
      note.title.includes(query) ||
      note.description.includes(query)
    )
  },

  // 保存搜索历史
  saveSearchHistory(query) {
    try {
      let recentSearches = wx.getStorageSync('recentSearches') || []

      // 移除重复项
      recentSearches = recentSearches.filter(item => item.query !== query)

      // 添加到开头
      recentSearches.unshift({
        query,
        timestamp: new Date().toISOString()
      })

      // 限制数量
      if (recentSearches.length > 10) {
        recentSearches = recentSearches.slice(0, 10)
      }

      // 保存到本地存储
      wx.setStorageSync('recentSearches', recentSearches)

      // 更新页面数据
      this.setData({ recentSearches })

    } catch (error) {
      console.error('保存搜索历史失败:', error)
    }
  },

  // 清空搜索
  clearSearch() {
    this.setData({
      searchQuery: '',
      hasSearched: false,
      searchResults: { total: 0, items: [] }
    })
  },

  // 取消搜索
  cancelSearch() {
    wx.navigateBack()
  },

  // 切换筛选器
  toggleFilter(e) {
    const filterId = e.currentTarget.dataset.filter
    const filters = this.data.searchFilters.map(filter => ({
      ...filter,
      active: filter.id === filterId
    }))

    this.setData({ searchFilters: filters })

    // 重新搜索
    if (this.data.hasSearched) {
      this.performSearch()
    }
  },

  // 切换筛选器显示
  toggleFilters() {
    this.setData({ showFilters: !this.data.showFilters })
  },

  // 选择最近搜索
  selectRecentSearch(e) {
    const query = e.currentTarget.dataset.query
    this.setData({ searchQuery: query })
    this.performSearch()
  },

  // 选择热门搜索
  selectHotSearch(e) {
    const tag = e.currentTarget.dataset.tag
    this.setData({ searchQuery: tag })
    this.performSearch()
  },

  // 清空最近搜索
  clearRecentSearches() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('recentSearches')
            this.setData({ recentSearches: [] })

            wx.showToast({
              title: '已清空',
              icon: 'success'
            })
          } catch (error) {
            console.error('清空搜索历史失败:', error)
          }
        }
      }
    })
  },

  // 打开搜索结果
  openSearchResult(e) {
    const item = e.currentTarget.dataset.item

    switch (item.type) {
      case '任务':
        wx.navigateTo({
          url: `/pages/task-form/index?mode=edit&id=${item.id}`
        })
        break
      case '考试':
        wx.navigateTo({
          url: `/pages/exam-detail/index?id=${item.id}`
        })
        break
      case '笔记':
        wx.showToast({
          title: '笔记功能开发中',
          icon: 'none'
        })
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  // 清除搜索定时器
  clearSearchTimer() {
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer)
      this.data.searchTimer = null
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.hasSearched) {
      this.performSearch()
    }
    wx.stopPullDownRefresh()
  }
})