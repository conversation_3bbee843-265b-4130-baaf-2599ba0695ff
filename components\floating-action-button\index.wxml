<!-- components/floating-action-button/index.wxml -->
<view class="fab-container">
  <!-- 拖动区域 -->
  <movable-area class="fab-movable-area" wx:if="{{draggable}}">
    <movable-view
      class="fab-movable-view"
      direction="all"
      x="{{movablePosition.x}}"
      y="{{movablePosition.y}}"
      bindchange="onPositionChange"
      bindtouchstart="onTouchStart"
      bindtouchend="onTouchEnd"
      damping="20"
      friction="2"
      disabled="{{isExpanded}}">

      <!-- 主按钮 -->
      <view
        class="fab-main {{isExpanded ? 'expanded' : ''}}"
        style="width: {{mainButtonStyle.size}}rpx; height: {{mainButtonStyle.size}}rpx; background-color: {{isExpanded ? '#ff4757' : mainButtonStyle.color}};">
        <view class="fab-main-icon {{isExpanded ? 'rotated' : ''}}">
          {{mainButtonStyle.icon}}
        </view>
      </view>
    </movable-view>
  </movable-area>

  <!-- 非拖动模式的主按钮 -->
  <view
    wx:else
    class="fab-main {{isExpanded ? 'expanded' : ''}}"
    style="position: fixed; left: {{currentPosition.x}}px; top: {{currentPosition.y}}px; width: {{mainButtonStyle.size}}rpx; height: {{mainButtonStyle.size}}rpx; background-color: {{isExpanded ? '#ff4757' : mainButtonStyle.color}};"
    bindtouchstart="onTouchStart"
    bindtouchend="onTouchEnd">
    <view class="fab-main-icon {{isExpanded ? 'rotated' : ''}}">
      {{mainButtonStyle.icon}}
    </view>
  </view>
  
  <!-- 子按钮容器 -->
  <view class="fab-sub-container" wx:if="{{isExpanded && buttons.length > 0}}">
    <view
      wx:for="{{buttons}}"
      wx:key="id"
      wx:if="{{index < 5 && subButtonPositions[index]}}"
      class="fab-sub-button {{isExpanded ? 'show' : ''}}"
      style="left: {{subButtonPositions[index].x}}px; top: {{subButtonPositions[index].y}}px; background-color: {{item.color || '#52c41a'}}; transition-delay: {{(index + 1) * 0.05}}s;"
      data-index="{{index}}"
      bindtap="onSubButtonClick">
      
      <!-- 子按钮图标 -->
      <view class="fab-sub-icon">
        {{item.icon}}
      </view>
      
      <!-- 子按钮标签 -->
      <view class="fab-sub-label" wx:if="{{item.label}}">
        {{item.label}}
      </view>
    </view>
  </view>
</view>
