// pages/task-complete/index.js
Page({
  data: {
    completedTask: {
      id: '',
      title: '',
      completionTime: ''
    },
    
    completionMessage: '你的坚持和努力值得赞赏！',
    
    taskSummaryStats: [
      { label: '复习时长', value: '2.5h', color: '#1890FF' },
      { label: '完成度', value: '100%', color: '#52C41A' },
      { label: '子任务', value: '4/4', color: '#FA8C16' },
      { label: '效率', value: '92%', color: '#722ED1' }
    ],
    
    newAchievements: [],
    
    difficultyRating: 0,
    difficultyStars: [
      { value: 1, filled: false },
      { value: 2, filled: false },
      { value: 3, filled: false },
      { value: 4, filled: false },
      { value: 5, filled: false }
    ],
    
    selectedSatisfaction: '',
    satisfactionOptions: [
      { value: 'very_bad', emoji: '😞' },
      { value: 'bad', emoji: '😐' },
      { value: 'good', emoji: '😊' },
      { value: 'very_good', emoji: '😄' },
      { value: 'excellent', emoji: '🤩' }
    ],
    
    reflectionNotes: '',
    
    suggestions: [
      {
        id: 'next_task',
        title: '开始下一个任务',
        description: '英语单词背诵',
        icon: '📝',
        bgColor: '#F6FFED',
        borderColor: '#B7EB8F',
        action: 'startNextTask'
      },
      {
        id: 'review_notes',
        title: '复习今日笔记',
        description: '巩固今天复习的内容',
        icon: '📖',
        bgColor: '#E6F7FF',
        borderColor: '#91D5FF',
        action: 'reviewNotes'
      },
      {
        id: 'practice_test',
        title: '做练习题',
        description: '检验复习效果',
        icon: '✏️',
        bgColor: '#FFF2E8',
        borderColor: '#FFD591',
        action: 'practiceTest'
      }
    ],
    
    todayProgress: {
      completedTasks: 3,
      studyTime: '4.2h',
      goalProgress: 85
    }
  },

  onLoad(options) {
    this.loadTaskCompletion(options)
    this.initReflection()
  },

  onUnload() {
    this.saveReflectionData()
  },

  // 加载任务完成数据
  loadTaskCompletion(options) {
    const taskId = options.taskId || 'task_001'
    
    // 模拟加载任务数据
    const completedTask = {
      id: taskId,
      title: '数学高数第一章复习',
      completionTime: new Date().toLocaleString()
    }
    
    this.setData({ completedTask })
    
    // 检查新成就
    this.checkNewAchievements()
    
    // 更新今日进度
    this.updateTodayProgress()
  },

  // 初始化反思
  initReflection() {
    // 重置评分状态
    this.setData({
      difficultyRating: 0,
      selectedSatisfaction: '',
      reflectionNotes: ''
    })
  },

  // 检查新成就
  checkNewAchievements() {
    // 模拟检查成就逻辑
    const achievements = []
    
    // 随机生成成就（实际应该基于真实数据）
    if (Math.random() > 0.7) {
      achievements.push({
        id: 'task_master',
        name: '任务达人',
        icon: '🎯'
      })
    }
    
    if (Math.random() > 0.8) {
      achievements.push({
        id: 'efficiency_expert',
        name: '效率专家',
        icon: '⚡'
      })
    }
    
    this.setData({ newAchievements: achievements })
    
    // 如果有新成就，显示庆祝动画
    if (achievements.length > 0) {
      wx.showToast({
        title: '🏆 解锁新成就！',
        icon: 'none',
        duration: 2000
      })
    }
  },

  // 更新今日进度
  updateTodayProgress() {
    try {
      const today = new Date().toISOString().split('T')[0]
      const todayStats = wx.getStorageSync('todayStats') || {
        date: today,
        completedTasks: 0,
        totalMinutes: 0,
        goalProgress: 0
      }
      
      // 更新完成任务数
      todayStats.completedTasks += 1
      
      // 计算目标进度
      const dailyGoal = 5
      todayStats.goalProgress = Math.min(100, (todayStats.completedTasks / dailyGoal) * 100)
      
      // 保存统计
      wx.setStorageSync('todayStats', todayStats)
      
      // 更新页面显示
      this.setData({
        'todayProgress.completedTasks': todayStats.completedTasks,
        'todayProgress.studyTime': `${(todayStats.totalMinutes / 60).toFixed(1)}h`,
        'todayProgress.goalProgress': Math.round(todayStats.goalProgress)
      })
      
    } catch (error) {
      console.error('更新今日进度失败:', error)
    }
  },

  // 设置难度评分
  setDifficultyRating(e) {
    const rating = e.currentTarget.dataset.rating
    const stars = this.data.difficultyStars.map((star, index) => ({
      ...star,
      filled: index < rating
    }))
    
    this.setData({
      difficultyRating: rating,
      difficultyStars: stars
    })
  },

  // 设置满意度评分
  setSatisfactionRating(e) {
    const satisfaction = e.currentTarget.dataset.satisfaction
    this.setData({ selectedSatisfaction: satisfaction })
  },

  // 更新反思笔记
  updateReflectionNotes(e) {
    this.setData({ reflectionNotes: e.detail.value })
  },

  // 执行建议操作
  executeSuggestion(e) {
    const action = e.currentTarget.dataset.suggestion
    
    switch (action) {
      case 'startNextTask':
        this.startNextTask()
        break
      case 'reviewNotes':
        this.reviewNotes()
        break
      case 'practiceTest':
        this.practiceTest()
        break
    }
  },

  // 开始下一个任务
  startNextTask() {
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  // 复习笔记
  reviewNotes() {
    wx.showToast({
      title: '笔记功能开发中',
      icon: 'none'
    })
  },

  // 练习测试
  practiceTest() {
    wx.showToast({
      title: '练习功能开发中',
      icon: 'none'
    })
  },

  // 继续复习
  continueLearning() {
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  // 休息一下
  takeBreak() {
    wx.showModal({
      title: '休息提醒',
      content: '建议休息10-15分钟，活动一下身体，保护视力。',
      confirmText: '好的',
      showCancel: false
    })
  },

  // 查看进度
  viewProgress() {
    wx.switchTab({
      url: '/pages/data-center/index'
    })
  },

  // 分享成果
  shareAchievement() {
    const { completedTask, todayProgress } = this.data
    
    return {
      title: `我刚完成了"${completedTask.title}"！今日已完成${todayProgress.completedTasks}个任务`,
      path: '/pages/task-complete/index',
      imageUrl: '/images/share-task-complete.png'
    }
  },

  // 完成并返回
  finishAndReturn() {
    this.saveReflectionData()
    
    wx.showToast({
      title: '复习记录已保存',
      icon: 'success'
    })
    
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }, 1500)
  },

  // 保存反思数据
  saveReflectionData() {
    const reflectionData = {
      taskId: this.data.completedTask.id,
      difficultyRating: this.data.difficultyRating,
      satisfactionRating: this.data.selectedSatisfaction,
      notes: this.data.reflectionNotes,
      timestamp: new Date().toISOString()
    }
    
    try {
      // 获取现有反思记录
      const reflections = wx.getStorageSync('taskReflections') || []
      
      // 添加新记录
      reflections.unshift(reflectionData)
      
      // 保存到本地存储
      wx.setStorageSync('taskReflections', reflections)
      
    } catch (error) {
      console.error('保存反思数据失败:', error)
    }
  },

  // 分享功能
  onShareAppMessage() {
    return this.shareAchievement()
  }
})
