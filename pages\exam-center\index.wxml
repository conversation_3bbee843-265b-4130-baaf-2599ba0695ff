<!--pages/exam-center/index.wxml-->
<view class="container">
  <!-- 顶部统计 - 新的三项统计 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-card" wx:for="{{examStats}}" wx:key="label"
            bind:tap="onStatCardTap" data-type="{{item.label}}">
        <view class="stat-content">
          <view class="stat-value" style="color: {{item.color}}">{{item.value}}</view>
          <view class="stat-label">{{item.label}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 筛选标签 - 使用van-tabs with 角标 -->
  <view class="filter-section">
    <van-tabs active="{{ activeTab }}" bind:change="onTabChange" color="#1890FF" line-width="30">
      <van-tab wx:for="{{filterTabs}}" wx:key="value" title="{{item.label}}" info="{{item.count > 0 ? item.count : ''}}">
      </van-tab>
    </van-tabs>
  </view>

  <!-- 考试列表 - 重构版本 -->
  <view class="exams-section" wx:if="{{filteredExams.length > 0}}">
    <view wx:for="{{filteredExams}}" wx:key="id" class="exam-item-wrapper">
      <!-- 滑动容器 -->
      <view class="swipe-container">
        <!-- 考试卡片主体 -->
        <view class="exam-card {{item.isUrgent ? 'exam-card--urgent' : ''}}"
              style="transform: translateX({{swipeData[item.id] && swipeData[item.id].translateX || 0}}rpx)"
              bind:touchstart="onTouchStart"
              bind:touchmove="onTouchMove"
              bind:touchend="onTouchEnd"
              bind:tap="viewExamDetail"
              data-id="{{item.id}}">

          <!-- 卡片头部 -->
          <view class="exam-header">
            <view class="exam-title-section">
              <text wx:if="{{item.isUrgent}}" class="urgent-icon">🔥</text>
              <text class="exam-title">{{item.name}}</text>
            </view>
            <view class="header-actions">
              <view wx:if="{{item.canToggleStatus}}" class="status-toggle-section">
                <van-button size="mini"
                           type="{{item.status === 'completed' ? 'default' : 'primary'}}"
                           bind:click="quickToggleStatus"
                           data-id="{{item.id}}">
                  {{item.statusButtonText}}
                </van-button>
              </view>
              <!-- 三个点菜单按钮 -->
              <view class="menu-button" catchtap="showExamActions" data-exam="{{item}}">
                <van-icon name="ellipsis" size="20px" color="#8C8C8C" />
              </view>
            </view>
          </view>

          <!-- 考试标签信息 -->
          <view class="exam-tags">
            <!-- 考试类型标签 -->
            <view class="exam-type-tag" style="background-color: {{item.examTypeColor}}">
              <text class="type-icon">{{item.examTypeIcon}}</text>
              <text class="type-text">{{item.examTypeName}}</text>
            </view>
            
            <!-- 重要性标签 -->
            <view class="importance-tag" style="background-color: {{item.importanceColor}}">
              <text class="importance-icon">{{item.importanceIcon}}</text>
              <text class="importance-text">{{item.importanceText}}</text>
            </view>

            <!-- 状态标签 -->
            <view wx:if="{{item.statusTag}}" class="status-tag" style="background-color: {{item.statusTagColor}}">
              <text class="status-tag-text">{{item.statusTag}}</text>
            </view>
            <!-- 考试时间（纯文本） -->
            <text class="exam-datetime-text">{{item.date}} {{item.time}}</text>
          </view>

          <!-- 突出提示行 -->
          <view class="exam-alert-section">
            <text class="exam-alert-text" style="color: {{item.countdownColor}}">
              {{item.countdownText}} · {{item.alertMessage}}
            </text>
          </view>

          <!-- 整体进度 -->
          <view class="progress-section">
            <view class="progress-header">
              <text class="progress-label">整体进度 {{item.preparationProgress}}%</text>
              <text class="efficiency-badge" wx:if="{{item.preparationProgress >= 80}}">⚡高效</text>
              <text class="efficiency-badge" wx:elif="{{item.preparationProgress < 40}}">💪需努力</text>
            </view>
            <van-progress
              percentage="{{item.preparationProgress}}"
              color="{{item.progressColor}}"
              stroke-width="8"
              show-pivot="{{false}}" />
          </view>

          <!-- 科目进度区域 -->
          <view class="subjects-section" wx:if="{{item.subjects && item.subjects.length > 0}}">
            <!-- 科目概览 -->
            <view class="subjects-overview" 
                  catchtap="toggleSubjects" 
                  data-id="{{item.id}}">
              <view class="subjects-summary">
                <text class="subjects-title">📚 科目进度</text>
                <view class="subjects-compact">
                  <!-- 显示前2个科目 -->
                  <text wx:for="{{item.displaySubjects}}"
                        wx:key="name"
                        wx:for-item="subject"
                        class="subject-compact-item"
                        style="color: {{subject.color}}">
                    {{subject.name}} · {{subject.progress}}%
                  </text>
                  <!-- 如果有更多科目，显示+更多 -->
                  <text wx:if="{{item.hasMoreSubjects}}"
                        class="subject-more-item">
                    +{{item.moreSubjectsCount}}更多
                  </text>
                </view>
              </view>
              <view class="subjects-toggle">
                <text class="toggle-text">{{item.showSubjects ? '收起' : '详情'}}</text>
                <van-icon name="{{item.showSubjects ? 'arrow-up' : 'arrow-down'}}" 
                          size="16px" 
                          color="#8C8C8C" />
              </view>
            </view>

            <!-- 科目详情（可展开） -->
            <view class="subjects-detail {{item.showSubjects ? 'subjects-detail--show' : ''}}"
                  wx:if="{{item.showSubjects}}">
              <view wx:for="{{item.subjects}}" 
                    wx:key="name" 
                    wx:for-item="subject"
                    class="subject-item">
                <view class="subject-info">
                  <text class="subject-icon">{{subject.icon}}</text>
                  <text class="subject-name">{{subject.name}}</text>
                  <text class="subject-progress-text">{{subject.progress}}%</text>
                </view>
                <view class="subject-progress-bar">
                  <van-progress
                    percentage="{{subject.progress}}"
                    color="{{subject.color}}"
                    stroke-width="4"
                    show-pivot="{{false}}" />
                </view>
                <view class="subject-tasks">
                  <text class="tasks-text">{{subject.completedTasks}}/{{subject.totalTasks}} 任务</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 心理激励文案 -->
          <view wx:if="{{item.showMotivation}}" class="motivation-section">
            <text class="motivation-text" style="color: {{item.motivationColor}}">
              {{item.motivationText}}
            </text>
          </view>

          <!-- 备考搭子小组信息 -->
          <view wx:if="{{item.groupInfo}}" class="group-section">
            <!-- 小组概览 -->
            <view class="group-overview" 
                  catchtap="toggleGroupDetail" 
                  data-id="{{item.id}}">
              <view class="group-summary">
                <text class="group-title">👥 备考搭子小组</text>
                                 <view class="group-members-avatars">
                   <view wx:for="{{item.groupInfo.members}}" 
                         wx:key="userId" 
                         wx:for-item="member"
                         wx:if="{{index < 3}}"
                         class="member-avatar"
                         style="z-index: {{3-index}}; margin-left: {{index > 0 ? '-8rpx' : '0'}}">
                     <image src="{{member.userInfo.avatarUrl || '/images/default-avatar.png'}}" 
                            class="avatar-image" 
                            mode="aspectFill" />
                   </view>
                   <!-- 空位显示加号，可以邀请 -->
                   <view wx:if="{{item.groupInfo.memberCount < 3}}" 
                         wx:for="{{3 - item.groupInfo.memberCount}}"
                         wx:key="*this"
                         class="empty-slot"
                         style="z-index: {{3-item.groupInfo.memberCount-index}}; margin-left: {{(item.groupInfo.memberCount + index) > 0 ? '-8rpx' : '0'}}"
                         catchtap="inviteToGroup"
                         data-id="{{item.id}}">
                     <text class="add-icon">+</text>
                   </view>
                 </view>
              </view>
              <view class="group-toggle">
                <text class="toggle-text">{{item.showGroupDetail ? '收起' : '详情'}}</text>
                <van-icon name="{{item.showGroupDetail ? 'arrow-up' : 'arrow-down'}}" 
                          size="16px" 
                          color="#8C8C8C" />
              </view>
            </view>

            <view class="group-stats-line">
              <text class="group-stats">{{item.groupInfo.progressText}}</text>
              <view wx:if="{{item.groupInfo.rankChangeIcon}}" class="rank-change">
                <text class="rank-change-icon" style="color: {{item.groupInfo.rankChangeColor}}">
                  {{item.groupInfo.rankChangeIcon}}
                </text>
              </view>
            </view>

            <!-- 小组成员详情（可展开） -->
            <view class="group-detail {{item.showGroupDetail ? 'group-detail--show' : ''}}"
                  wx:if="{{item.showGroupDetail}}">
              <view wx:for="{{item.groupInfo.members}}" 
                    wx:key="userId" 
                    wx:for-item="member"
                    class="member-item">
                <view class="member-info">
                  <image src="{{member.userInfo.avatarUrl || '/images/default-avatar.png'}}" 
                         class="member-avatar-large" 
                         mode="aspectFill" />
                  <view class="member-details">
                    <text class="member-name">{{member.userInfo.nickName || '匿名用户'}}</text>
                    <text class="member-progress-text">{{member.progress || 0}}%</text>
                  </view>
                  <view wx:if="{{member.isOnline}}" class="online-indicator">
                    <text class="online-dot">●</text>
                    <text class="online-text">在线</text>
                  </view>
                </view>
                <view class="member-progress-bar">
                  <van-progress
                    percentage="{{member.progress || 0}}"
                    color="{{member.progressColor || '#1890FF'}}"
                    stroke-width="4"
                    show-pivot="{{false}}" />
                </view>
              </view>
              
                             <!-- 小组操作按钮 -->
               <view class="group-actions">
                 <van-button size="small" 
                            type="default" 
                            bind:click="viewStudyGroup"
                            data-id="{{item.id}}"
                            custom-class="group-action-btn">
                   查看详情
                 </van-button>
                 <view wx:if="{{item.groupInfo.memberCount < 3}}" class="invite-btn-wrapper">
                   <van-button size="small" 
                              type="primary" 
                              bind:click="inviteToGroup"
                              data-id="{{item.id}}"
                              custom-class="group-action-btn">
                     邀请成员 ({{3 - item.groupInfo.memberCount}}个空位)
                   </van-button>
                 </view>
                 <view wx:else class="group-full-tip">
                   <text class="full-text">🎉 小组已满员</text>
                 </view>
               </view>
            </view>
          </view>

          <!-- 创建搭子小组引导 -->
          <view wx:if="{{!item.groupInfo}}" class="group-create-section" 
                catchtap="createStudyGroup" 
                data-id="{{item.id}}"
                data-exam-name="{{item.name}}">
            <view class="create-info">
              <text class="create-icon">👥</text>
              <view class="create-content">
                <text class="create-title">组建备考搭子小组</text>
                <text class="create-desc">邀请同学一起备考，互相监督进度</text>
              </view>
            </view>
            <view class="create-action">
              <view class="create-button">
                <text class="create-button-text">立即创建</text>
                <van-icon name="arrow" size="14px" color="#1890FF" />
              </view>
            </view>
          </view>


        </view>

        <!-- 左滑操作按钮 -->
        <view class="swipe-actions" wx:if="{{swipeData[item.id] && swipeData[item.id].showActions}}">
          <view class="swipe-action swipe-action--edit"
                bind:tap="onSwipeEdit"
                data-id="{{item.id}}">
            <text class="swipe-action-icon">📝</text>
            <text class="swipe-action-text">编辑</text>
          </view>
          <view class="swipe-action swipe-action--delete"
                bind:tap="onSwipeDelete"
                data-id="{{item.id}}">
            <text class="swipe-action-icon">🗑️</text>
            <text class="swipe-action-text">删除</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 - 自定义空状态 -->
  <view class="empty-section" wx:if="{{filteredExams.length === 0}}">
    <view class="empty-container">
      <view class="empty-icon">
        <text class="empty-emoji">📚</text>
      </view>
      <view class="empty-content">
        <view class="empty-title">{{getEmptyTitle()}}</view>
        <view class="empty-message">{{getEmptyMessage()}}</view>
      </view>
      <view class="empty-actions">
        <van-button 
          type="primary" 
          size="large"
          bind:click="onEmptyAction"
          custom-class="empty-action-button">
          {{getEmptyActionText()}}
        </van-button>
        <view wx:if="{{currentFilter === 'all'}}" class="empty-quick-actions">
          <text class="quick-action-title">🚀 快速开始</text>
          <view class="quick-action-grid">
            <view class="quick-action-item" bind:tap="addExam">
              <text class="quick-action-icon">📝</text>
              <text class="quick-action-text">添加考试</text>
            </view>
            <view class="quick-action-item" bind:tap="showLoginPrompt">
              <text class="quick-action-icon">👥</text>
              <text class="quick-action-text">加入小组</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 添加考试按钮 - 始终显示在页面底部 -->
  <view class="add-exam-section">
    <van-button 
      type="primary" 
      size="large" 
      block
      icon="plus"
      bind:click="addExam"
      class="add-exam-button">
      添加新考试
    </van-button>
  </view>
</view>

<!-- 考试操作菜单 - 使用van-action-sheet -->
<van-action-sheet
  show="{{ showActionSheet }}"
  actions="{{ examActions }}"
  cancel-text="取消"
  title="{{ selectedExam ? selectedExam.name : '' }}"
  bind:select="onActionSelect"
  bind:cancel="hideActionSheet"
  bind:close="hideActionSheet"
/>
