// 导航工具函数
class NavigationUtils {
  /**
   * 安全的返回上一页
   * 如果有上一页则返回，否则跳转到首页
   * @param {string} fallbackUrl - 后备页面URL，默认为首页
   */
  static safeNavigateBack(fallbackUrl = '/pages/home/<USER>') {
    const pages = getCurrentPages()
    if (pages.length > 1) {
      // 有上一页，安全返回
      wx.navigateBack()
    } else {
      // 没有上一页，跳转到指定页面
      if (fallbackUrl.startsWith('/pages/home/') ||
          fallbackUrl.startsWith('/pages/task-center/') ||
          fallbackUrl.startsWith('/pages/data-center/') ||
          fallbackUrl.startsWith('/pages/profile/')) {
        // 是tabbar页面，使用switchTab
        wx.switchTab({
          url: fallbackUrl
        })
      } else {
        // 不是tabbar页面，使用navigateTo
        wx.navigateTo({
          url: fallbackUrl
        })
      }
    }
  }

  /**
   * 安全的导航到指定页面
   * 自动判断是否是tabbar页面
   * @param {string} url - 目标页面URL
   */
  static safeNavigateTo(url) {
    const tabbarPages = [
      '/pages/home/<USER>',
      '/pages/task-center/index',
      '/pages/data-center/index',
      '/pages/profile/index'
    ]

    if (tabbarPages.includes(url)) {
      wx.switchTab({ url })
    } else {
      wx.navigateTo({ url })
    }
  }

  /**
   * 获取当前页面栈信息
   */
  static getCurrentPageInfo() {
    const pages = getCurrentPages()
    return {
      length: pages.length,
      current: pages[pages.length - 1]?.route || '',
      canGoBack: pages.length > 1
    }
  }
}

module.exports = NavigationUtils