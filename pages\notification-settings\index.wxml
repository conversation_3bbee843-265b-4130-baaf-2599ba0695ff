<!--pages/notification-settings/index.wxml-->
<view class="notification-settings-container">
  <!-- 页面标题 -->
  <van-nav-bar
    title="通知设置"
    left-text="返回"
    left-arrow
    bind:click-left="onBack"
  />

  <!-- 订阅状态卡片 -->
  <view class="subscription-status-card">
    <view class="status-header">
      <text class="status-title">消息通知状态</text>
      <van-tag type="{{subscriptionStatus.isActive ? 'success' : 'warning'}}" size="medium">
        {{subscriptionStatus.isActive ? '已开启' : '未开启'}}
      </van-tag>
    </view>
    
    <view class="status-info">
      <text class="status-desc">
        {{subscriptionStatus.isActive ? '您已开启消息通知，将及时收到学习提醒' : '开启消息通知，获得更好的学习体验'}}
      </text>
      
      <view wx:if="{{subscriptionStatus.isActive}}" class="subscription-details">
        <text class="detail-item">订阅时间：{{subscriptionStatus.subscriptionTime}}</text>
        <text class="detail-item">已订阅模板：{{subscriptionStatus.templateCount}}个</text>
      </view>
    </view>

    <van-button 
      wx:if="{{!subscriptionStatus.isActive}}"
      type="primary" 
      size="large"
      bind:click="requestSubscription"
      loading="{{requesting}}"
      custom-class="subscribe-button"
    >
      开启消息通知
    </van-button>
  </view>

  <!-- 通知类型设置 -->
  <van-cell-group title="通知类型" custom-class="settings-group">
    <van-cell
      wx:for="{{notificationTypes}}"
      wx:key="type"
      title="{{item.name}}"
      label="{{item.description}}"
      >
      <van-switch
        slot="right-icon"
        checked="{{item.enabled}}"
        bind:change="toggleNotificationType"
        data-type="{{item.type}}"
        disabled="{{!subscriptionStatus.isActive}}"
        />
    </van-cell>
  </van-cell-group>

  <!-- 小组动态通知说明 -->
  <view class="notice-box" wx:if="{{subscriptionStatus.isActive}}">
    <view class="notice-title">📢 小组动态通知说明</view>
    <view class="notice-content">
      <text>• 小组动态采用每日汇总模式，减少打扰</text>
      <text>• 每天晚上8点自动发送前一天的动态汇总</text>
      <text>• 汇总内容包括：任务完成、计划分享、新成员加入等</text>
      <text>• 只有加入搭子小组的用户才会收到通知</text>
    </view>
  </view>

  <!-- 提醒时间设置 -->
  <van-cell-group title="提醒时间" custom-class="settings-group">
    <van-cell 
      title="任务提醒时间"
      value="{{taskReminderTime}}"
      is-link
      bind:click="showTaskReminderPicker"
      disabled="{{!subscriptionStatus.isActive}}"
      />
    <van-cell 
      title="考试提醒提前"
      value="{{examReminderAdvance}}"
      is-link
      bind:click="showExamReminderPicker"
      disabled="{{!subscriptionStatus.isActive}}"
      />
  </van-cell-group>

  <!-- 免打扰设置 -->
  <van-cell-group title="免打扰时间" custom-class="settings-group">
    <van-cell 
      title="开启免打扰"
      >
      <van-switch 
        slot="right-icon"
        checked="{{quietMode.enabled}}" 
        bind:change="toggleQuietMode"
        disabled="{{!subscriptionStatus.isActive}}"
        />
    </van-cell>
    
    <van-cell 
      wx:if="{{quietMode.enabled}}"
      title="免打扰时段"
      value="{{quietMode.startTime}} - {{quietMode.endTime}}"
      is-link
      bind:click="showQuietTimePicker"
      />
  </van-cell-group>

  <!-- 测试通知 -->
  <van-cell-group title="测试功能" custom-class="settings-group">
    <van-cell 
      title="发送测试通知"
      label="测试消息通知是否正常工作"
      is-link
      bind:click="sendTestNotification"
      disabled="{{!subscriptionStatus.isActive}}"
      />
  </van-cell-group>

  <!-- 帮助说明 -->
  <view class="help-section">
    <van-collapse value="{{activeCollapse}}" bind:change="onCollapseChange">
      <van-collapse-item title="通知说明" name="help">
        <view class="help-content">
          <text class="help-item">• 消息通知需要您的授权才能正常工作</text>
          <text class="help-item">• 任务提醒会在截止时间前发送</text>
          <text class="help-item">• 考试提醒会按您设置的提前时间发送</text>
          <text class="help-item">• 免打扰时间内不会发送通知</text>
          <text class="help-item">• 您可以随时在此页面管理通知设置</text>
        </view>
      </van-collapse-item>
    </van-collapse>
  </view>

  <!-- 重新订阅按钮 -->
  <view class="resubscribe-section" wx:if="{{subscriptionStatus.isActive}}">
    <van-button 
      type="default" 
      size="large"
      bind:click="resubscribe"
      loading="{{requesting}}"
      custom-class="resubscribe-button"
    >
      重新订阅消息
    </van-button>
    <text class="resubscribe-tip">如果通知异常，可以尝试重新订阅</text>
  </view>
</view>

<!-- 时间选择器 -->
<van-popup show="{{showTimePicker}}" position="bottom" bind:close="hideTimePicker">
  <van-datetime-picker
    type="time"
    value="{{currentTime}}"
    bind:confirm="onTimeConfirm"
    bind:cancel="hideTimePicker"
  />
</van-popup>

<!-- 考试提醒选择器 -->
<van-popup show="{{showExamPicker}}" position="bottom" bind:close="hideExamPicker">
  <van-picker
    columns="{{examReminderOptions}}"
    bind:confirm="onExamReminderConfirm"
    bind:cancel="hideExamPicker"
  />
</van-popup>

<!-- Toast提示 -->
<van-toast id="van-toast" />

<!-- 加载提示 -->
<van-loading wx:if="{{loading}}" type="spinner" size="24px" custom-class="loading-overlay">加载中...</van-loading>
