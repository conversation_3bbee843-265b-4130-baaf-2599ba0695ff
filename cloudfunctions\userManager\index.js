// 用户管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action, data } = event

  try {
    switch (action) {
      case 'cleanDuplicateUsers':
        return await cleanDuplicateUsers()
      case 'getUserData':
        return await getUserData(wxContext.OPENID)
      case 'deleteUserData':
        return await deleteUserData(wxContext.OPENID)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('用户管理云函数错误:', error)
    return { success: false, error: error.message }
  }
}

// 清理重复用户数据
async function cleanDuplicateUsers() {
  try {
    console.log('开始清理重复用户数据...')
    
    // 获取所有用户
    const allUsers = await db.collection('users').get()
    console.log(`找到 ${allUsers.data.length} 个用户记录`)
    
    // 按openid分组
    const userGroups = {}
    allUsers.data.forEach(user => {
      const openid = user.openid || user._openid
      if (!userGroups[openid]) {
        userGroups[openid] = []
      }
      userGroups[openid].push(user)
    })
    
    let duplicateCount = 0
    let deletedCount = 0
    
    // 处理每个openid组
    for (const openid in userGroups) {
      const users = userGroups[openid]
      
      if (users.length > 1) {
        duplicateCount++
        console.log(`发现重复用户 ${openid}，共 ${users.length} 条记录`)
        
        // 保留最新的记录（根据createTime或updateTime）
        users.sort((a, b) => {
          const timeA = new Date(a.updateTime || a.createTime || 0)
          const timeB = new Date(b.updateTime || b.createTime || 0)
          return timeB - timeA
        })
        
        const keepUser = users[0]
        const deleteUsers = users.slice(1)
        
        console.log(`保留用户记录: ${keepUser._id}`)
        
        // 删除重复记录
        for (const user of deleteUsers) {
          await db.collection('users').doc(user._id).remove()
          deletedCount++
          console.log(`删除重复记录: ${user._id}`)
        }
      }
    }
    
    console.log(`清理完成: 发现 ${duplicateCount} 个重复用户，删除 ${deletedCount} 条记录`)
    
    return {
      success: true,
      data: {
        totalUsers: allUsers.data.length,
        duplicateUsers: duplicateCount,
        deletedRecords: deletedCount,
        remainingUsers: allUsers.data.length - deletedCount
      }
    }
  } catch (error) {
    console.error('清理重复用户失败:', error)
    return { success: false, error: error.message }
  }
}

// 获取用户数据统计
async function getUserData(openid) {
  try {
    // 获取用户基本信息
    const userResult = await db.collection('users')
      .where({ openid: openid })
      .get()

    if (userResult.data.length === 0) {
      return { success: false, error: '用户不存在' }
    }

    const user = userResult.data[0]
    const userId = user._id

    // 获取用户的任务数量
    const tasksResult = await db.collection('tasks')
      .where({ userId: userId })
      .count()

    // 获取用户的考试数量
    const examsResult = await db.collection('exams')
      .where({ userId: userId })
      .count()

    // 获取用户的番茄钟会话数量
    const pomodoroResult = await db.collection('pomodoro_sessions')
      .where({ userId: userId })
      .count()
    
    return {
      success: true,
      data: {
        user: user,
        stats: {
          tasks: tasksResult.total,
          exams: examsResult.total,
          pomodoroSessions: pomodoroResult.total
        }
      }
    }
  } catch (error) {
    console.error('获取用户数据失败:', error)
    return { success: false, error: error.message }
  }
}

// 删除用户所有数据
async function deleteUserData(openid) {
  try {
    console.log(`开始删除用户 ${openid} 的所有数据...`)

    // 先获取用户ID
    const userResult = await db.collection('users')
      .where({ openid: openid })
      .get()

    if (userResult.data.length === 0) {
      return { success: false, error: '用户不存在' }
    }

    const userId = userResult.data[0]._id

    // 删除用户的任务
    const tasksResult = await db.collection('tasks')
      .where({ userId: userId })
      .remove()

    // 删除用户的考试
    const examsResult = await db.collection('exams')
      .where({ userId: userId })
      .remove()

    // 删除用户的番茄钟会话
    const pomodoroResult = await db.collection('pomodoro_sessions')
      .where({ userId: userId })
      .remove()

    // 删除用户的复习会话
    const studyResult = await db.collection('study_sessions')
      .where({ userId: userId })
      .remove()
    
    // 删除用户记录
    const userResult = await db.collection('users')
      .where({ openid: openid })
      .remove()
    
    console.log(`用户 ${openid} 数据删除完成`)
    
    return {
      success: true,
      data: {
        deletedTasks: tasksResult.stats.removed,
        deletedExams: examsResult.stats.removed,
        deletedPomodoroSessions: pomodoroResult.stats.removed,
        deletedStudySessions: studyResult.stats.removed,
        deletedUsers: userResult.stats.removed
      }
    }
  } catch (error) {
    console.error('删除用户数据失败:', error)
    return { success: false, error: error.message }
  }
}
