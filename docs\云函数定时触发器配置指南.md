# 云函数定时触发器配置指南

## 概述
本项目需要为notificationManager云函数配置两个定时触发器，实现自动批量发送通知功能。

## 配置步骤

### 方法一：微信开发者工具配置

#### 1. 打开微信开发者工具
- 确保项目已打开
- 确保云开发环境已初始化

#### 2. 配置任务/考试提醒触发器
1. 右键点击 `cloudfunctions/notificationManager` 文件夹
2. 选择"云函数配置" → "触发器管理"
3. 点击"添加触发器"
4. 配置参数：
   - **触发器名称**: `dailyReminders`
   - **触发器类型**: `定时触发器`
   - **Cron表达式**: `0 0 9 * * * *`
   - **触发器状态**: `启用`
   - **备注**: `每天上午9点执行任务和考试提醒`

#### 3. 配置小组动态汇总触发器
1. 继续在触发器管理页面点击"添加触发器"
2. 配置参数：
   - **触发器名称**: `dailyGroupDigest`
   - **触发器类型**: `定时触发器`
   - **Cron表达式**: `0 0 20 * * * *`
   - **触发器状态**: `启用`
   - **备注**: `每天晚上8点执行小组动态汇总`

### 方法二：云开发控制台配置

#### 1. 登录云开发控制台
- 访问 https://console.cloud.tencent.com/tcb
- 选择对应的环境ID

#### 2. 进入云函数管理
- 左侧菜单：云函数 → 函数服务
- 找到 `notificationManager` 函数

#### 3. 配置触发器
1. 点击函数名称进入详情页
2. 选择"触发器管理"标签
3. 点击"创建触发器"
4. 按照上述参数配置两个触发器

## Cron表达式说明

### 格式
```
秒 分 时 日 月 星期 年
* * * * * * *
```

### 本项目使用的表达式

#### 每天上午9点 - 任务/考试提醒
```
0 0 9 * * * *
```
- `0` - 秒：第0秒
- `0` - 分：第0分
- `9` - 时：上午9点
- `*` - 日：每天
- `*` - 月：每月
- `*` - 星期：每周
- `*` - 年：每年

#### 每天晚上8点 - 小组动态汇总
```
0 0 20 * * * *
```
- `0` - 秒：第0秒
- `0` - 分：第0分
- `20` - 时：晚上8点（20:00）
- `*` - 日：每天
- `*` - 月：每月
- `*` - 星期：每周
- `*` - 年：每年

## 触发器工作原理

### 定时触发流程
1. **触发器激活**：到达设定时间时，微信云开发自动调用云函数
2. **参数识别**：云函数通过 `context.source === 'timer'` 识别为定时调用
3. **任务分发**：根据 `context.name` 执行对应的任务
4. **执行逻辑**：
   - `dailyReminders` → 执行 `sendBatchReminders()`
   - `dailyGroupDigest` → 执行 `sendDailyGroupDigest()`

### 代码实现
```javascript
// 处理定时触发器调用
if (context.source === 'timer') {
  console.log('定时触发器调用，触发器名称:', context.name)
  
  switch (context.name) {
    case 'dailyReminders':
      // 每天9点执行的任务/考试提醒
      return await sendBatchReminders()
    case 'dailyGroupDigest':
      // 每天20点执行的小组动态汇总
      return await sendDailyGroupDigest()
    default:
      return { success: false, error: '未知的定时触发器' }
  }
}
```

## 验证配置

### 1. 检查触发器状态
- 在云开发控制台或微信开发者工具中确认触发器状态为"启用"
- 确认Cron表达式配置正确

### 2. 查看执行日志
- 在云函数日志中查看定时执行记录
- 确认触发器按时执行且无错误

### 3. 手动测试
```javascript
// 在云开发控制台测试面板中手动调用
{
  "action": "sendBatchReminders"
}

{
  "action": "sendDailyGroupDigest"
}
```

## 注意事项

### 1. 时区设置
- 云函数使用UTC时间，需要考虑时区差异
- 中国时区(UTC+8)：配置时间需要减去8小时
- 例如：想要北京时间9点执行，应配置为UTC时间1点

### 2. 触发器限制
- 每个云函数最多可配置5个触发器
- 定时触发器最小间隔为1分钟
- 触发器名称在同一函数内必须唯一

### 3. 执行监控
- 定期检查云函数执行日志
- 监控通知发送成功率
- 关注异常情况和错误日志

### 4. 成本控制
- 定时触发器会产生云函数调用费用
- 建议设置合理的执行频率
- 监控资源使用情况

## 故障排除

### 触发器不执行
1. 检查触发器状态是否为"启用"
2. 验证Cron表达式格式是否正确
3. 查看云函数是否有语法错误
4. 检查云开发环境是否正常

### 执行失败
1. 查看云函数执行日志
2. 检查数据库连接和权限
3. 验证微信API调用是否正常
4. 确认订阅消息模板ID是否正确

### 通知未发送
1. 检查用户订阅状态
2. 验证模板ID配置
3. 查看notification_logs表中的错误记录
4. 确认微信订阅消息配额是否充足
