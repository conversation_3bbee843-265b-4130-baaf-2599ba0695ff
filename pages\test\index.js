// pages/test/index.js
const TestRunner = require('../../utils/testRunner')

Page({
  data: {
    testResults: null,
    isRunning: false,
    showDetails: false,
    currentTest: '',
    progress: 0
  },

  onLoad() {
    console.log('测试页面加载')
  },

  // 运行所有测试
  async runAllTests() {
    if (this.data.isRunning) return

    this.setData({
      isRunning: true,
      testResults: null,
      currentTest: '准备运行测试...',
      progress: 0
    })

    try {
      const testRunner = new TestRunner()
      
      // 模拟进度更新
      this.updateProgress('初始化测试环境...', 10)
      await this.delay(500)
      
      this.updateProgress('运行 ExamModel 测试...', 30)
      await this.delay(500)
      
      this.updateProgress('运行 SmartApi 测试...', 60)
      await this.delay(500)
      
      this.updateProgress('运行集成测试...', 80)
      await this.delay(500)
      
      // 运行实际测试
      const results = await testRunner.runAllTests()
      
      this.updateProgress('测试完成', 100)
      
      this.setData({
        testResults: results,
        isRunning: false,
        currentTest: '测试完成'
      })

      // 显示结果提示
      if (results.failed === 0) {
        wx.showToast({
          title: `✅ 所有测试通过 (${results.passed}/${results.total})`,
          icon: 'success',
          duration: 2000
        })
      } else {
        wx.showToast({
          title: `❌ ${results.failed} 个测试失败`,
          icon: 'none',
          duration: 2000
        })
      }

    } catch (error) {
      console.error('运行测试失败:', error)
      
      this.setData({
        isRunning: false,
        currentTest: '测试失败'
      })

      wx.showToast({
        title: '测试运行失败',
        icon: 'none'
      })
    }
  },

  // 更新进度
  updateProgress(message, progress) {
    this.setData({
      currentTest: message,
      progress: progress
    })
  },

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  // 切换详细结果显示
  toggleDetails() {
    this.setData({
      showDetails: !this.data.showDetails
    })
  },

  // 查看单个测试详情
  viewTestDetail(e) {
    const index = e.currentTarget.dataset.index
    const test = this.data.testResults.results[index]
    
    let content = `状态: ${test.status}\n结果: ${test.message}`
    if (test.error) {
      content += `\n错误: ${test.error.message}`
    }

    wx.showModal({
      title: test.name,
      content: content,
      showCancel: false,
      confirmText: '确定'
    })
  },

  // 导出测试报告
  exportReport() {
    if (!this.data.testResults) {
      wx.showToast({
        title: '请先运行测试',
        icon: 'none'
      })
      return
    }

    try {
      const testRunner = new TestRunner()
      testRunner.testResults = this.data.testResults.results
      testRunner.totalTests = this.data.testResults.total
      testRunner.passedTests = this.data.testResults.passed
      testRunner.failedTests = this.data.testResults.failed
      
      const report = testRunner.exportReport()
      
      // 保存到本地存储
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const reportKey = `test_report_${timestamp}`
      wx.setStorageSync(reportKey, report)
      
      wx.showModal({
        title: '报告已导出',
        content: `测试报告已保存到本地存储\n键名: ${reportKey}`,
        showCancel: false,
        confirmText: '确定'
      })

    } catch (error) {
      console.error('导出报告失败:', error)
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      })
    }
  },

  // 清除测试结果
  clearResults() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除测试结果吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            testResults: null,
            showDetails: false,
            currentTest: '',
            progress: 0
          })
        }
      }
    })
  },

  // 运行快速测试
  async runQuickTest() {
    if (this.data.isRunning) return

    this.setData({
      isRunning: true,
      currentTest: '运行快速测试...'
    })

    try {
      // 快速验证核心功能
      const ExamModel = require('../../models/ExamModel')
      const SmartApi = require('../../utils/smartApi')

      // 测试ExamModel基本功能
      const examData = {
        title: '快速测试考试',
        examDate: '2024-01-15',
        examTime: '09:00'
      }
      
      const exam = new ExamModel(examData)
      const validation = SmartApi.validateExamData(exam.data)

      if (validation.isValid) {
        wx.showToast({
          title: '✅ 快速测试通过',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: '❌ 快速测试失败',
          icon: 'none'
        })
      }

    } catch (error) {
      console.error('快速测试失败:', error)
      wx.showToast({
        title: '快速测试异常',
        icon: 'none'
      })
    } finally {
      this.setData({
        isRunning: false,
        currentTest: ''
      })
    }
  },

  // 测试数据创建
  testDataCreation() {
    try {
      const SmartApi = require('../../utils/smartApi')
      const defaultData = SmartApi.getDefaultExamData()
      
      wx.showModal({
        title: '默认数据',
        content: JSON.stringify(defaultData, null, 2),
        showCancel: false,
        confirmText: '确定'
      })

    } catch (error) {
      wx.showToast({
        title: '获取默认数据失败',
        icon: 'none'
      })
    }
  },

  // 测试数据验证
  testDataValidation() {
    try {
      const SmartApi = require('../../utils/smartApi')
      
      // 测试有效数据
      const validData = {
        title: '测试考试',
        examDate: '2024-01-15',
        examTime: '09:00'
      }
      
      const validation = SmartApi.validateExamData(validData)
      
      wx.showModal({
        title: '验证结果',
        content: `有效性: ${validation.isValid}\n错误数: ${validation.errors.length}`,
        showCancel: false,
        confirmText: '确定'
      })

    } catch (error) {
      wx.showToast({
        title: '数据验证失败',
        icon: 'none'
      })
    }
  },

  // 分享测试结果
  onShareAppMessage() {
    if (this.data.testResults) {
      return {
        title: `考试模型测试结果: ${this.data.testResults.passed}/${this.data.testResults.total} 通过`,
        path: '/pages/test/index'
      }
    }
    
    return {
      title: '考试模型测试工具',
      path: '/pages/test/index'
    }
  }
})
