// pages/help/index.js
Page({
  data: {
    // 常见问题
    faqList: [
      {
        id: 1,
        question: '如何添加考试和任务？',
        answer: '在考试中心或任务中心页面，点击右上角的"+"按钮即可添加新的考试或任务。填写相关信息后保存即可。',
        expanded: false
      },
      {
        id: 2,
        question: '番茄钟如何使用？',
        answer: '进入专注页面，选择复习模式（快速专注或任务专注），设置背景音，点击开始按钮即可。可以长按计时器进入全屏专注模式。',
        expanded: false
      },
      {
        id: 3,
        question: '如何查看复习统计？',
        answer: '在"我的"页面点击"数据中心"，或在专注页面点击底部"统计"按钮，可以查看详细的复习数据分析。',
        expanded: false
      },
      {
        id: 4,
        question: '数据会丢失吗？',
        answer: '所有数据都保存在本地，不会上传到服务器。建议定期使用"数据导出"功能备份重要数据。',
        expanded: false
      },
      {
        id: 5,
        question: '如何设置提醒？',
        answer: '在各个功能页面可以设置相应的提醒。添加考试时也可以设置具体的提醒时间。',
        expanded: false
      }
    ],

    // 使用指南
    guideList: [
      {
        id: 1,
        icon: '📚',
        title: '考试管理指南',
        description: '如何高效管理考试信息',
        content: '1. 添加考试：点击考试中心的"+"按钮\n2. 设置重要程度：选择考试的重要级别\n3. 配置提醒：设置考试前的提醒时间\n4. 查看倒计时：在首页可以看到最近考试的倒计时\n5. 准备度跟踪：通过关联任务跟踪复习进度'
      },
      {
        id: 2,
        icon: '📝',
        title: '任务管理指南',
        description: '如何创建和管理复习任务',
        content: '1. 创建任务：在任务中心点击"+"添加新任务\n2. 设置优先级：根据重要性设置任务优先级\n3. 关联考试：将任务与相关考试关联\n4. 跟踪进度：及时更新任务完成状态\n5. 复习计划：制定合理的复习时间安排'
      },
      {
        id: 3,
        icon: '🍅',
        title: '番茄钟使用指南',
        description: '如何使用番茄钟提高专注力',
        content: '1. 选择模式：快速专注或任务专注\n2. 设置时长：根据需要调整专注时间\n3. 选择背景音：选择合适的背景音乐\n4. 开始专注：点击开始按钮进入专注状态\n5. 全屏模式：长按计时器进入全屏专注\n6. 查看统计：完成后查看专注数据'
      },
      {
        id: 4,
        icon: '📊',
        title: '数据统计指南',
        description: '如何查看和分析复习数据',
        content: '1. 数据中心：在"我的"页面进入数据中心\n2. 复习统计：查看专注时长、完成任务等\n3. 趋势分析：观察复习效率变化趋势\n4. 数据导出：备份重要的复习数据\n5. 目标设定：根据数据调整复习目标'
      }
    ],

    // 弹窗状态
    showGuideModal: false,
    currentGuide: {}
  },

  onLoad(options) {
    // 页面加载时的初始化
  },

  // 切换FAQ展开状态
  toggleFaq(e) {
    const id = e.currentTarget.dataset.id
    const faqList = this.data.faqList.map(item => ({
      ...item,
      expanded: item.id === id ? !item.expanded : false
    }))
    this.setData({ faqList })
  },

  // 打开使用指南
  openGuide(e) {
    const guide = e.currentTarget.dataset.guide
    this.setData({
      currentGuide: guide,
      showGuideModal: true
    })
  },

  // 关闭使用指南弹窗
  closeGuideModal() {
    this.setData({
      showGuideModal: false,
      currentGuide: {}
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 复制联系方式
  copyContact(e) {
    const text = e.currentTarget.dataset.text
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        })
      }
    })
  },

  // 加入用户群
  joinGroup() {
    wx.showModal({
      title: '加入用户群',
      content: '请添加微信号：augmentcode，备注"备考助手"，我们会邀请您加入用户交流群。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 打开GitHub
  openGithub() {
    wx.showModal({
      title: '开源项目',
      content: '感谢您对开源项目的关注！项目地址：github.com/augmentcode/beikao-helper',
      showCancel: false,
      confirmText: '知道了'
    })
  }
})
