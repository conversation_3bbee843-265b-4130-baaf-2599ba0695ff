/* pages/exam-detail/index.wxss - Vant重构版本 */

/* ==================== 基础容器样式 ==================== */
.container {
  background-color: #f7f8fa;
  min-height: 100vh;
  padding: 16rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
}

/* ==================== 考试头部卡片 ==================== */
.exam-header-card {
  --van-card-background-color: #fff;
  --van-card-title-color: #333;
  --van-card-desc-color: #666;
  --van-card-padding: 24rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.exam-status-section {
  display: flex;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.status-tag {
  --van-tag-primary-color: #1989fa;
  --van-tag-text-color: #fff;
}

.importance-tag {
  --van-tag-danger-color: #ff4d4f;
  --van-tag-warning-color: #faad14;
  --van-tag-default-color: #f0f0f0;
}

.exam-actions {
  display: flex;
  gap: 12rpx;
  align-items: center;
  margin-top: 16rpx;
}

.action-btn {
  --van-button-default-background-color: #f7f8fa;
  --van-button-default-border-color: #ebedf0;
  --van-button-default-color: #646566;
}

/* ==================== 面板通用样式 ==================== */
.countdown-panel,
.subjects-panel,
.preparation-panel,
.tasks-panel,
.actions-panel {
  --van-panel-background-color: #fff;
  --van-panel-header-font-size: 32rpx;
  --van-panel-header-color: #333;
  --van-panel-header-font-weight: 600;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* ==================== 倒计时面板 ==================== */
.countdown-header {
  display: flex;
  align-items: center;
}

.exam-date {
  font-size: 24rpx;
  color: #666;
  background: rgba(25, 137, 250, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  margin-left: auto;
}

.countdown-content {
  padding: 24rpx;
}

.countdown-display {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  color: #fff;
}

.countdown-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80rpx;
}

.countdown-number {
  font-size: 48rpx;
  font-weight: 600;
  line-height: 1;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', monospace;
}

.countdown-label {
  font-size: 24rpx;
  margin-top: 8rpx;
  opacity: 0.8;
}

.countdown-separator {
  font-size: 40rpx;
  font-weight: 300;
  margin: 0 20rpx;
  opacity: 0.6;
}

.countdown-progress-section {
  margin-top: 24rpx;
}

.time-progress {
  --van-progress-color: #1989fa;
  --van-progress-background-color: #f0f0f0;
  margin-bottom: 16rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  display: block;
}

/* ==================== 科目面板 ==================== */
.subjects-header {
  display: flex;
  align-items: center;
}

.subject-item {
  --van-collapse-item-title-font-size: 28rpx;
  --van-collapse-item-title-color: #333;
  --van-collapse-item-content-padding: 24rpx;
}

.subject-score {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
}

.target-score {
  color: #52c41a;
  font-weight: 500;
}

.total-score {
  color: #999;
}

.subject-content {
  padding-top: 16rpx;
}

.subject-progress {
  margin-bottom: 24rpx;
}

.subject-progress-bar {
  --van-progress-color: #52c41a;
  --van-progress-background-color: #f0f0f0;
  margin-bottom: 12rpx;
}

.subject-stats {
  --van-grid-item-content-padding: 20rpx;
  --van-grid-item-text-color: #666;
  --van-grid-item-text-font-size: 24rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

/* ==================== 准备进度面板 ==================== */
.preparation-header {
  display: flex;
  align-items: center;
}

.preparation-content {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-circle-section {
  margin-bottom: 32rpx;
}

.overall-progress-circle {
  --van-circle-color: #52c41a;
  --van-circle-layer-color: #f0f0f0;
}

.circle-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.progress-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.progress-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.preparation-stats {
  --van-grid-item-content-padding: 24rpx;
  width: 100%;
}

.prep-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.prep-stat-icon {
  font-size: 32rpx;
  margin-bottom: 12rpx;
}

.prep-stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.prep-stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.prep-stat-label {
  font-size: 22rpx;
  color: #666;
  margin-top: 4rpx;
}

/* ==================== 任务面板 ==================== */
.tasks-header {
  display: flex;
  align-items: center;
}

.tasks-list {
  --van-cell-group-background-color: transparent;
}

.task-cell {
  --van-cell-background-color: #fff;
  --van-cell-title-color: #333;
  --van-cell-label-color: #666;
  --van-cell-border-color: #f0f0f0;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
  overflow: hidden;
}

.task-cell.completed {
  --van-cell-title-color: #999;
  opacity: 0.7;
}

.task-status {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
}

.task-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.progress-text {
  font-size: 22rpx;
  color: #666;
}

.empty-tasks {
  margin: 40rpx 0;
}

/* ==================== 快捷操作面板 ==================== */
.action-grid {
  --van-grid-item-content-padding: 32rpx;
  --van-grid-item-text-color: #333;
  --van-grid-item-text-font-size: 24rpx;
}

.quick-action-item {
  transition: all 0.3s ease;
  border-radius: 12rpx;
  margin: 4rpx;
}

.quick-action-item:active {
  background: rgba(25, 137, 250, 0.05);
  transform: scale(0.95);
}

/* ==================== 操作菜单 ==================== */
.more-actions-sheet {
  --van-action-sheet-header-font-size: 32rpx;
  --van-action-sheet-item-font-size: 28rpx;
  --van-action-sheet-item-text-color: #333;
  --van-action-sheet-item-background-color: #fff;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 375px) {
  .countdown-number {
    font-size: 40rpx;
  }
  
  .countdown-item {
    min-width: 60rpx;
  }
  
  .countdown-separator {
    margin: 0 12rpx;
    font-size: 32rpx;
  }
  
  .prep-stat-icon {
    font-size: 28rpx;
  }
  
  .prep-stat-value {
    font-size: 24rpx;
  }
}

/* ==================== 暗黑模式适配 ==================== */
@media (prefers-color-scheme: dark) {
  .container {
    background: #1a1a1a;
  }
  
  .exam-header-card,
  .countdown-panel,
  .subjects-panel,
  .preparation-panel,
  .tasks-panel,
  .actions-panel {
    --van-panel-background-color: #2d2d2d;
    --van-panel-header-color: #fff;
  }
  
  .exam-header-card {
    --van-card-background-color: #2d2d2d;
    --van-card-title-color: #fff;
    --van-card-desc-color: #ccc;
  }
  
  .task-cell {
    --van-cell-background-color: #2d2d2d;
    --van-cell-title-color: #fff;
    --van-cell-label-color: #ccc;
    --van-cell-border-color: #444;
  }
}


