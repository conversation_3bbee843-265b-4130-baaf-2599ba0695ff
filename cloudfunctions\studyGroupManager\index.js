// 备考搭子管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { action, data } = event
  const { OPENID } = cloud.getWXContext()

  console.log('studyGroupManager云函数调用:', { action, openid: OPENID })

  // 根据OpenID获取真正的用户ID
  const realUserId = await getRealUserIdByOpenId(OPENID)
  if (!realUserId) {
    console.error('无法获取用户ID，OpenID:', OPENID)
    return { success: false, error: '用户未登录或用户信息不存在' }
  }

  console.log('获取到真正的用户ID:', realUserId)

  try {
    switch (action) {
      case 'createGroup':
        return await createGroup(realUserId, data)
      case 'verifyInviteCode':
        return await verifyInviteCode(realUserId, data)
      case 'joinGroup':
        return await joinGroup(realUserId, data)
      case 'leaveGroup':
        return await leaveGroup(realUserId, data)
      case 'getMyGroups':
        return await getMyGroups(realUserId, data)
      case 'getGroupDetail':
        return await getGroupDetail(realUserId, data)
      case 'generateInviteCode':
        return await generateInviteCode(realUserId, data)
      case 'sharePlan':
        return await sharePlan(realUserId, data)
      case 'copyPlan':
        return await copyPlan(realUserId, data)
      case 'likePlan':
        return await likePlan(realUserId, data)
      case 'getGroupActivities':
        return await getGroupActivities(realUserId, data)
      case 'fixGroupData':
        return await fixGroupData(realUserId, data)
      case 'getBatchGroupsByExam':
        return await getBatchGroupsByExam(realUserId, data)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('studyGroupManager云函数执行失败:', error)
    return { success: false, error: error.message }
  }
}

// 创建搭子小组
async function createGroup(userId, params) {
  const { examId, examName, groupName } = params || {}
  
  if (!examId || !examName) {
    return { success: false, error: '缺少必要参数' }
  }

  try {
    // 检查用户是否已经在该考试的小组中
    const existingGroup = await db.collection('study_groups')
      .where({
        examId: examId,
        'members.userId': userId,
        status: 'active'
      })
      .get()

    if (existingGroup.data.length > 0) {
      return { success: false, error: '您已经在该考试的搭子小组中' }
    }

    // 生成邀请码
    const inviteCode = generateRandomCode()

    // 获取用户信息
    const userInfo = await getUserInfo(userId)

    const groupData = {
      examId: examId,
      examName: examName,
      groupName: groupName || examName + '搭子小组',
      members: [
        {
          userId: userId,
          userInfo: userInfo,
          joinTime: new Date().toISOString(),
          role: 'member',
          isActive: true
        }
      ],
      maxMembers: 3,
      currentMembers: 1,
      creatorId: userId,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      status: 'active',
      inviteCode: inviteCode,
      settings: {
        allowShare: true,
        allowInvite: true
      }
    }

    const result = await db.collection('study_groups').add({
      data: groupData
    })

    // 创建邀请记录（如果失败不影响主流程）
    try {
      await createInvitation(result._id, userId, inviteCode, examName)
    } catch (inviteError) {
      console.error('创建邀请记录失败:', inviteError)
      // 不影响主流程，继续执行
    }

    return {
      success: true,
      data: {
        groupId: result._id,
        inviteCode: inviteCode,
        ...groupData
      }
    }
  } catch (error) {
    console.error('创建搭子小组失败:', error)
    return { success: false, error: '创建失败' }
  }
}

// 验证邀请码
async function verifyInviteCode(userId, params) {
  const { inviteCode } = params || {}

  if (!inviteCode) {
    return { success: false, error: '邀请码不能为空' }
  }

  try {
    // 查找邀请记录
    const inviteResult = await db.collection('group_invitations')
      .where({
        inviteCode: inviteCode,
        status: 'pending'
      })
      .get()

    if (inviteResult.data.length === 0) {
      return { success: false, error: '邀请码无效或已过期' }
    }

    const invitation = inviteResult.data[0]

    // 检查邀请是否过期
    if (new Date(invitation.expireTime) < new Date()) {
      return { success: false, error: '邀请码已过期' }
    }

    // 查找小组
    const groupResult = await db.collection('study_groups')
      .where({
        _id: invitation.groupId,
        status: 'active'
      })
      .get()

    if (groupResult.data.length === 0) {
      return { success: false, error: '小组不存在或已解散' }
    }

    const group = groupResult.data[0]

    // 检查小组是否已满
    if (group.currentMembers >= group.maxMembers) {
      return { success: false, error: '小组人数已满' }
    }

    // 检查用户是否已在小组中
    const isAlreadyMember = group.members.some(member => member.userId === userId)
    if (isAlreadyMember) {
      return { success: false, error: '您已经在该小组中' }
    }

    // 返回小组信息和邀请者信息
    return {
      success: true,
      data: {
        groupId: group._id,
        groupName: group.groupName,
        examName: group.examName,
        currentMembers: group.currentMembers,
        maxMembers: group.maxMembers,
        inviterInfo: invitation.inviterInfo,
        inviteCode: inviteCode
      }
    }
  } catch (error) {
    console.error('验证邀请码失败:', error)
    return { success: false, error: '验证失败' }
  }
}

// 加入搭子小组
async function joinGroup(userId, params) {
  const { inviteCode } = params || {}
  
  if (!inviteCode) {
    return { success: false, error: '邀请码不能为空' }
  }

  try {
    // 查找邀请记录
    const inviteResult = await db.collection('group_invitations')
      .where({
        inviteCode: inviteCode,
        status: 'pending'
      })
      .get()

    if (inviteResult.data.length === 0) {
      return { success: false, error: '邀请码无效或已过期' }
    }

    const invitation = inviteResult.data[0]
    
    // 检查邀请是否过期
    if (new Date(invitation.expireTime) < new Date()) {
      return { success: false, error: '邀请码已过期' }
    }

    // 查找小组
    const groupResult = await db.collection('study_groups')
      .where({
        _id: invitation.groupId,
        status: 'active'
      })
      .get()

    if (groupResult.data.length === 0) {
      return { success: false, error: '小组不存在或已解散' }
    }

    const group = groupResult.data[0]

    // 检查小组是否已满
    if (group.currentMembers >= group.maxMembers) {
      return { success: false, error: '小组人数已满' }
    }

    // 检查用户是否已在小组中
    const isAlreadyMember = group.members.some(member => member.userId === userId)
    if (isAlreadyMember) {
      return { success: false, error: '您已经在该小组中' }
    }

    // 获取用户信息
    const userInfo = await getUserInfo(userId)

    // 添加用户到小组
    const newMember = {
      userId: userId,
      userInfo: userInfo,
      joinTime: new Date().toISOString(),
      role: 'member',
      isActive: true
    }

    const updatedMembers = [...group.members, newMember]

    await db.collection('study_groups').doc(group._id).update({
      data: {
        members: updatedMembers,
        currentMembers: updatedMembers.length,
        updateTime: new Date().toISOString()
      }
    })

    // 更新邀请状态
    await db.collection('group_invitations').doc(invitation._id).update({
      data: {
        status: 'accepted',
        usedBy: userId,
        usedTime: new Date().toISOString()
      }
    })

    // 创建加入动态
    await createActivity(group._id, userId, 'join_group', {})

    return { success: true, data: { groupId: group._id } }
  } catch (error) {
    console.error('加入搭子小组失败:', error)
    return { success: false, error: '加入失败' }
  }
}

// 退出搭子小组
async function leaveGroup(userId, params) {
  const { groupId } = params || {}
  
  if (!groupId) {
    return { success: false, error: '小组ID不能为空' }
  }

  try {
    // 查找小组
    const groupResult = await db.collection('study_groups').doc(groupId).get()
    
    if (!groupResult.data) {
      return { success: false, error: '小组不存在' }
    }

    const group = groupResult.data

    // 检查用户是否在小组中
    const memberIndex = group.members.findIndex(member => member.userId === userId)
    if (memberIndex === -1) {
      return { success: false, error: '您不在该小组中' }
    }

    // 移除用户
    const updatedMembers = group.members.filter(member => member.userId !== userId)

    // 如果小组只剩1人或没人，解散小组
    if (updatedMembers.length <= 1) {
      await db.collection('study_groups').doc(groupId).update({
        data: {
          status: 'disbanded',
          updateTime: new Date().toISOString()
        }
      })
    } else {
      await db.collection('study_groups').doc(groupId).update({
        data: {
          members: updatedMembers,
          currentMembers: updatedMembers.length,
          updateTime: new Date().toISOString()
        }
      })
    }

    return { success: true }
  } catch (error) {
    console.error('退出搭子小组失败:', error)
    return { success: false, error: '退出失败' }
  }
}

// 获取我的搭子小组
async function getMyGroups(userId, params) {
  try {
    const result = await db.collection('study_groups')
      .where({
        'members.userId': userId,
        status: 'active'
      })
      .orderBy('updateTime', 'desc')
      .get()

    return { success: true, data: result.data }
  } catch (error) {
    console.error('获取我的搭子小组失败:', error)
    return { success: false, error: '获取失败' }
  }
}

// 获取搭子小组详情
async function getGroupDetail(userId, params) {
  const { groupId } = params || {}

  if (!groupId) {
    return { success: false, error: '小组ID不能为空' }
  }

  try {
    // 获取小组信息
    const groupResult = await db.collection('study_groups').doc(groupId).get()

    if (!groupResult.data) {
      return { success: false, error: '小组不存在' }
    }

    const group = groupResult.data

    // 检查用户是否在小组中
    const isMember = group.members.some(member => member.userId === userId)
    if (!isMember) {
      return { success: false, error: '您不在该小组中' }
    }

    // 获取小组计划分享（添加错误处理）
    let shares = []
    try {
      const sharesResult = await db.collection('group_plan_shares')
        .where({
          groupId: groupId,
          status: 'active'
        })
        .orderBy('shareTime', 'desc')
        .limit(10)
        .get()
      shares = sharesResult.data || []
    } catch (error) {
      console.log('获取计划分享失败，可能集合不存在:', error.message)
      shares = []
    }

    // 获取小组动态（添加错误处理）
    let activities = []
    try {
      const activitiesResult = await db.collection('group_activities')
        .where({
          groupId: groupId
        })
        .orderBy('createTime', 'desc')
        .limit(20)
        .get()
      activities = activitiesResult.data || []
    } catch (error) {
      console.log('获取小组动态失败，可能集合不存在:', error.message)
      activities = []
    }

    return {
      success: true,
      data: {
        group: group,
        shares: shares,
        activities: activities
      }
    }
  } catch (error) {
    console.error('获取搭子小组详情失败:', error)
    return { success: false, error: '获取失败' }
  }
}

// 分享计划到小组
async function sharePlan(userId, params) {
  const { groupId, planData } = params || {}

  if (!groupId || !planData) {
    return { success: false, error: '缺少必要参数' }
  }

  try {
    // 检查用户是否在小组中
    const groupResult = await db.collection('study_groups').doc(groupId).get()

    if (!groupResult.data) {
      return { success: false, error: '小组不存在' }
    }

    const group = groupResult.data
    const isMember = group.members.some(member => member.userId === userId)
    if (!isMember) {
      return { success: false, error: '您不在该小组中' }
    }

    // 获取用户信息
    const userInfo = await getUserInfo(userId)

    // 创建分享记录
    const shareData = {
      groupId: groupId,
      sharerId: userId,
      sharerInfo: userInfo,
      examId: group.examId,
      planData: planData,
      shareTime: new Date().toISOString(),
      copyCount: 0,
      likes: [],
      status: 'active'
    }

    // 尝试添加分享记录
    let result
    try {
      result = await db.collection('group_plan_shares').add({
        data: shareData
      })
    } catch (addError) {
      console.error('添加分享记录失败:', addError)
      return { success: false, error: `添加分享记录失败: ${addError.message}` }
    }

    // 创建分享动态（如果失败不影响主流程）
    try {
      await createActivity(groupId, userId, 'plan_share', {
        planTitle: planData.title
      })
    } catch (activityError) {
      console.error('创建分享动态失败:', activityError)
      // 不影响主流程，继续执行
    }

    return { success: true, data: { shareId: result._id, ...shareData } }
  } catch (error) {
    console.error('分享计划失败:', error)
    return { success: false, error: `分享失败: ${error.message}` }
  }
}

// 复制小组计划
async function copyPlan(userId, params) {
  const { shareId } = params || {}

  if (!shareId) {
    return { success: false, error: '分享ID不能为空' }
  }

  try {
    // 获取分享记录
    const shareResult = await db.collection('group_plan_shares').doc(shareId).get()

    if (!shareResult.data) {
      return { success: false, error: '分享记录不存在' }
    }

    const share = shareResult.data

    // 检查用户是否在小组中
    const groupResult = await db.collection('study_groups').doc(share.groupId).get()
    const group = groupResult.data
    const isMember = group.members.some(member => member.userId === userId)
    if (!isMember) {
      return { success: false, error: '您不在该小组中' }
    }

    // 增加复制次数
    await db.collection('group_plan_shares').doc(shareId).update({
      data: {
        copyCount: db.command.inc(1)
      }
    })

    // 创建复制动态
    await createActivity(share.groupId, userId, 'copy_plan', {
      planTitle: share.planData.title,
      targetUserId: share.sharerId
    })

    return { success: true, data: share.planData }
  } catch (error) {
    console.error('复制计划失败:', error)
    return { success: false, error: '复制失败' }
  }
}

// 点赞小组计划
async function likePlan(userId, params) {
  const { shareId } = params || {}

  if (!shareId) {
    return { success: false, error: '分享ID不能为空' }
  }

  try {
    // 获取分享记录
    const shareResult = await db.collection('group_plan_shares').doc(shareId).get()

    if (!shareResult.data) {
      return { success: false, error: '分享记录不存在' }
    }

    const share = shareResult.data

    // 检查是否已经点赞
    const hasLiked = share.likes.some(like => like.userId === userId)

    if (hasLiked) {
      // 取消点赞
      const updatedLikes = share.likes.filter(like => like.userId !== userId)
      await db.collection('group_plan_shares').doc(shareId).update({
        data: {
          likes: updatedLikes
        }
      })
      return { success: true, data: { liked: false, likeCount: updatedLikes.length } }
    } else {
      // 添加点赞
      const newLike = {
        userId: userId,
        likeTime: new Date().toISOString()
      }
      await db.collection('group_plan_shares').doc(shareId).update({
        data: {
          likes: db.command.push(newLike)
        }
      })

      // 创建点赞动态
      await createActivity(share.groupId, userId, 'like_plan', {
        planTitle: share.planData.title,
        targetUserId: share.sharerId
      })

      return { success: true, data: { liked: true, likeCount: share.likes.length + 1 } }
    }
  } catch (error) {
    console.error('点赞计划失败:', error)
    return { success: false, error: '点赞失败' }
  }
}

// 获取小组动态
async function getGroupActivities(userId, params) {
  const { groupId, limit = 20 } = params || {}

  if (!groupId) {
    return { success: false, error: '小组ID不能为空' }
  }

  try {
    // 检查用户是否在小组中
    const groupResult = await db.collection('study_groups').doc(groupId).get()
    const group = groupResult.data
    const isMember = group.members.some(member => member.userId === userId)
    if (!isMember) {
      return { success: false, error: '您不在该小组中' }
    }

    // 获取动态
    const result = await db.collection('group_activities')
      .where({
        groupId: groupId
      })
      .orderBy('createTime', 'desc')
      .limit(limit)
      .get()

    return { success: true, data: result.data }
  } catch (error) {
    console.error('获取小组动态失败:', error)
    return { success: false, error: '获取失败' }
  }
}

// 辅助函数
function generateRandomCode() {
  return Math.random().toString(36).substr(2, 8).toUpperCase()
}

// 根据OpenID获取真正的用户ID
async function getRealUserIdByOpenId(openid) {
  try {
    const userResult = await db.collection('users')
      .where({
        openid: openid
      })
      .get()
      
    if (userResult.data && userResult.data.length > 0) {
      return userResult.data[0]._id
    }
  } catch (error) {
    console.error('根据OpenID获取用户ID失败:', error)
  }
  
  return null
}

async function getUserInfo(userId) {
  try {
    const userResult = await db.collection('users').doc(userId).get()
    if (userResult.data) {
      return {
        nickName: userResult.data.nickName || '用户',
        avatarUrl: userResult.data.avatarUrl || ''
      }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
  
  return {
    nickName: '用户',
    avatarUrl: ''
  }
}

// 获取成员任务统计的辅助函数
async function getMemberTaskStats(userId, examId) {
  try {
    // 获取该用户在该考试下的所有任务
    const tasksResult = await db.collection('tasks')
      .where({
        userId: userId,
        examId: examId
      })
      .get()

    const tasks = tasksResult.data || []
    const totalTasks = tasks.length
    const completedTasks = tasks.filter(task => task.completed === true).length

    console.log(`用户 ${userId} 在考试 ${examId} 下的任务统计:`, {
      totalTasks,
      completedTasks,
      progress: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
    })

    return {
      totalTasks: totalTasks,
      completedTasks: completedTasks
    }
  } catch (error) {
    console.error('获取成员任务统计失败:', error)
    return {
      totalTasks: 0,
      completedTasks: 0
    }
  }
}

async function createInvitation(groupId, inviterId, inviteCode, examName) {
  try {
    const inviterInfo = await getUserInfo(inviterId)

    const invitationData = {
      groupId: groupId,
      inviterId: inviterId,
      inviterInfo: inviterInfo,
      inviteCode: inviteCode,
      examName: examName,
      createTime: new Date().toISOString(),
      expireTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天后过期
      status: 'pending'
    }

    await db.collection('group_invitations').add({
      data: invitationData
    })
  } catch (error) {
    console.error('创建邀请记录失败:', error)
    throw error // 重新抛出错误，让调用方处理
  }
}

async function createActivity(groupId, userId, activityType, activityData) {
  try {
    const userInfo = await getUserInfo(userId)

    const activity = {
      groupId: groupId,
      userId: userId,
      userInfo: userInfo,
      activityType: activityType,
      activityData: activityData,
      createTime: new Date().toISOString(),
      isRead: false,
      notified: false,  // 添加notified字段，用于标记是否已发送通知
      type: activityType // 添加type字段，便于查询特定类型的活动
    }

    await db.collection('group_activities').add({
      data: activity
    })
  } catch (error) {
    console.error('创建活动记录失败:', error)
    // 不抛出异常，避免影响主流程
  }
}

// 修复小组数据
async function fixGroupData(userId, params) {
  try {
    console.log('开始修复小组数据...')

    // 获取所有活跃的小组
    const groupsResult = await db.collection('study_groups')
      .where({
        status: 'active'
      })
      .get()

    const results = []

    for (const group of groupsResult.data) {
      const actualMemberCount = group.members ? group.members.length : 0
      const currentMemberCount = group.currentMembers

      if (actualMemberCount !== currentMemberCount) {
        console.log(`修复小组 ${group._id}: 实际成员${actualMemberCount}人, 记录${currentMemberCount}人`)

        await db.collection('study_groups').doc(group._id).update({
          data: {
            currentMembers: actualMemberCount,
            updateTime: new Date().toISOString()
          }
        })

        results.push({
          groupId: group._id,
          groupName: group.groupName,
          before: currentMemberCount,
          after: actualMemberCount,
          fixed: true
        })
      } else {
        results.push({
          groupId: group._id,
          groupName: group.groupName,
          memberCount: actualMemberCount,
          fixed: false
        })
      }
    }

    return {
      success: true,
      data: {
        totalGroups: groupsResult.data.length,
        fixedGroups: results.filter(r => r.fixed).length,
        results: results
      }
    }
  } catch (error) {
    console.error('修复小组数据失败:', error)
    return { success: false, error: '修复失败' }
  }
}

// 批量获取多个考试的搭子组信息
async function getBatchGroupsByExam(userId, data) {
  try {
    const { examIds } = data

    if (!Array.isArray(examIds) || examIds.length === 0) {
      return { success: false, error: '考试ID列表不能为空' }
    }

    console.log('批量获取搭子组，考试数量:', examIds.length)

    // 批量查询所有考试的搭子组 - 修复集合名称和查询条件
    const result = await db.collection('study_groups')
      .where({
        examId: db.command.in(examIds),
        'members.userId': userId,
        status: 'active'
      })
      .get()

    console.log('批量查询到搭子组数量:', result.data.length)

    // 按考试ID分组搭子组信息
    const groupsByExam = {}
    examIds.forEach(examId => {
      groupsByExam[examId] = null
    })

    // 处理搭子组数据并关联用户信息
    for (const group of result.data) {
      if (group.examId && groupsByExam.hasOwnProperty(group.examId)) {
        // 获取所有成员的真实用户信息
        const enrichedMembers = []
        if (group.members && group.members.length > 0) {
          for (const member of group.members) {
            // 获取用户真实信息
            const realUserInfo = await getUserInfo(member.userId)

            // 获取该用户在该考试下的任务统计
            const memberTaskStats = await getMemberTaskStats(member.userId, group.examId)

            // 合并用户信息，优先使用真实用户信息
            const enrichedMember = {
              ...member,
              userInfo: realUserInfo,
              // 使用实时计算的任务统计
              completedTasks: memberTaskStats.completedTasks,
              totalTasks: memberTaskStats.totalTasks,
              progress: memberTaskStats.totalTasks > 0 ? Math.round((memberTaskStats.completedTasks / memberTaskStats.totalTasks) * 100) : 0
            }
            enrichedMembers.push(enrichedMember)
          }
        }
        
        // 计算最快搭子进度
        let bestProgress = 0
        if (enrichedMembers.length > 0) {
          bestProgress = Math.max(...enrichedMembers.map(member => member.progress || 0))
        }

        groupsByExam[group.examId] = {
          id: group._id,
          name: group.groupName || group.name,
          members: enrichedMembers,
          bestProgress: bestProgress,
          examId: group.examId,
          currentMembers: group.currentMembers || 0,
          maxMembers: group.maxMembers || 3,
          inviteCode: group.inviteCode
        }
      }
    }

    return { success: true, data: groupsByExam }
  } catch (error) {
    console.error('批量获取搭子组失败:', error)
    return { success: false, error: error.message }
  }
}


