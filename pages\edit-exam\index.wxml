<!--pages/edit-exam/index.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <text class="page-title">📝 编辑考试</text>
    <text class="page-subtitle">修改考试信息，调整复习计划</text>
  </view>

  <!-- 基本信息 -->
  <van-cell-group title="基本信息">
    <van-field
      name="title"
      label="考试名称"
      placeholder="请输入考试名称，如：期末考试、四级考试"
      value="{{examForm.title}}"
      bind:change="onTitleChange"
      maxlength="{{50}}"
      clearable
      required
      error-message="{{titleErrorMessage}}"
    />

    <!-- 科目管理区域 -->
    <van-cell title="考试科目" />

    <!-- 科目列表 -->
    <view class="subject-list" wx:if="{{examForm.subject.length > 0}}">
      <view class="subject-item" wx:for="{{examForm.subject}}" wx:key="*this">
        <van-tag type="primary" closeable bind:close="removeSubject" data-index="{{index}}">
          {{item}}
        </van-tag>
      </view>
    </view>

    <!-- 添加科目 -->
    <van-field
      name="newSubject"
      placeholder="输入科目名称，如：数学、英语"
      value="{{newSubject}}"
      bind:change="onNewSubjectChange"
      maxlength="{{30}}"
      clearable
      use-right-icon-slot
    >
      <van-icon
        slot="right-icon"
        name="plus"
        size="20px"
        color="{{canAddSubject ? '#1989fa' : '#c8c9cc'}}"
        bind:click="addSubject"
        class="add-subject-icon {{canAddSubject ? 'active' : 'disabled'}}"
      />
    </van-field>
  </van-cell-group>

  <van-divider />

  <!-- 时间地点 -->
  <van-cell-group title="时间地点">
    <van-cell
      title="考试日期"
      value="{{examForm.examDate || '请选择考试日期'}}"
      is-link
      bind:click="showDatePicker"
    />

    <van-cell
      title="考试时间"
      value="{{examForm.examTime || '请选择考试时间'}}"
      is-link
      bind:click="showTimePicker"
    />

    <van-field
      name="location"
      label="考试地点"
      placeholder="请输入考试地点，如：教学楼A101"
      value="{{examForm.location}}"
      bind:change="onLocationChange"
      maxlength="{{100}}"
      clearable
    />
  </van-cell-group>

  <van-divider />

  <!-- 考试设置 -->
  <van-cell-group title="考试设置">
    <van-cell title="重要程度" />
    <view class="importance-tabs">
      <van-tabs active="{{selectedImportanceIndex}}" bind:change="onImportanceChange">
        <van-tab wx:for="{{importanceOptions}}" wx:key="*this" title="{{item}}">
          {{item}}
        </van-tab>
      </van-tabs>
    </view>

    <van-cell
      title="考试类型"
      value="{{typeLabel || '请选择考试类型'}}"
      is-link
      bind:click="showTypePicker"
    />

    <van-cell
      title="考试状态"
      value="{{statusLabel || '请选择考试状态'}}"
      is-link
      bind:click="showStatusPicker"
    />
  </van-cell-group>

  <van-divider />

  <!-- 其他信息 -->
  <van-cell-group title="其他信息">
    <van-field
      name="description"
      label="考试描述"
      type="textarea"
      placeholder="请输入考试相关描述或备注信息"
      value="{{examForm.description}}"
      bind:change="onDescriptionChange"
      maxlength="{{500}}"
      show-word-limit
      autosize
      clearable
    />

    <van-cell title="考试提醒" />
    <view class="reminder-options">
      <view class="reminder-item" wx:for="{{reminderOptions}}" wx:key="value">
        <van-switch
          checked="{{item.checked}}"
          bind:change="onReminderChange"
          data-value="{{item.value}}"
        />
        <text class="reminder-text">{{item.label}}</text>
      </view>
    </view>
  </van-cell-group>

  <van-divider />

  <!-- 危险操作区域 -->
  <view class="danger-zone">
    <van-cell-group title="危险操作">
      <van-cell 
        title="删除考试" 
        label="删除后无法恢复，请谨慎操作"
        is-link
        link-type="redirection"
        bind:click="confirmDeleteExam"
        custom-class="danger-cell">
        <van-icon slot="icon" name="delete-o" size="20" color="#ff4444" />
      </van-cell>
    </van-cell-group>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <van-button
      type="default"
      size="large"
      bind:click="cancelEdit">
      取消
    </van-button>

    <van-button
      type="primary"
      size="large"
      bind:click="saveExam"
      disabled="{{!canSave}}">
      保存修改
    </van-button>
  </view>
</view>

<!-- 日期选择器弹窗 -->
<van-popup show="{{showDatePopup}}" position="bottom" bind:close="closeDatePicker">
  <van-datetime-picker
    type="date"
    value="{{currentDate}}"
    min-date="{{minDate}}"
    bind:confirm="onDateConfirm"
    bind:cancel="closeDatePicker"
  />
</van-popup>

<!-- 时间选择器弹窗 -->
<van-popup show="{{showTimePopup}}" position="bottom" bind:close="closeTimePicker">
  <van-datetime-picker
    type="time"
    value="{{currentTime}}"
    bind:confirm="onTimeConfirm"
    bind:cancel="closeTimePicker"
  />
</van-popup>

<!-- 类型选择器弹窗 -->
<van-popup show="{{showTypePopup}}" position="bottom" bind:close="closeTypePicker">
  <van-picker
    columns="{{typeColumns}}"
    bind:confirm="onTypeConfirm"
    bind:cancel="closeTypePicker"
    show-toolbar
    title="选择考试类型"
  />
</van-popup>

<!-- 状态选择器弹窗 -->
<van-popup show="{{showStatusPopup}}" position="bottom" bind:close="closeStatusPicker">
  <van-picker
    columns="{{statusColumns}}"
    bind:confirm="onStatusConfirm"
    bind:cancel="closeStatusPicker"
    show-toolbar
    title="选择考试状态"
  />
</van-popup>