/* pages/notification-center/index.wxss */

/* 页面容器 */
.container {
  background-color: #f7f8fa;
  padding: 16rpx;
  min-height: 100vh;
}

/* 卡片容器 */
.header-container,
.filter-container,
.notification-group {
  margin-bottom: 24rpx;
}

/* 通知头部 */
.header-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.header-actions {
  display: flex;
  gap: 12rpx;
}

.mark-all-read-btn {
  background-color: #1890ff !important;
  color: #ffffff !important;
  border: none !important;
  margin-right: 8rpx;
}

.settings-btn {
  background-color: #f2f3f5 !important;
  color: #969799 !important;
  border: none !important;
}

/* 统计网格 */
.notification-stats {
  display: flex;
  justify-content: space-around;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 16rpx;
}

.stat-item {
  text-align: center;
  padding: 8rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1890ff;
  margin-bottom: 4rpx;
  display: block;
}

.stat-label {
  font-size: 22rpx;
  color: #969799;
  display: block;
}

/* 筛选器标签计数 */
.filter-count {
  margin-left: 8rpx;
  font-size: 20rpx;
}

/* 筛选器 */
.filter-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.filter-tabs {
  display: flex;
  gap: 8rpx;
  overflow-x: auto;
}

.filter-tab {
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background-color: #1890FF;
  color: #FFFFFF;
}

/* 通知列表 */
.notifications-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.notification-group {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.group-date {
  font-size: 26rpx;
  font-weight: 500;
  color: #323233;
}

.group-count {
  font-size: 22rpx;
  color: #666666;
}

.group-notifications {
  margin-top: 16rpx;
}

/* 通知项 */
.notification-item {
  position: relative;
  padding: 16rpx 0;
}

.notification-item.unread {
  background-color: #f0f9ff;
}

.notification-item.read {
  opacity: 0.7;
}

.notification-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56rpx;
  height: 56rpx;
  margin-right: 16rpx;
}

.notification-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  flex-shrink: 0;
}

.unread-indicator {
  position: absolute;
  top: 0;
  right: 0;
  width: 12rpx;
  height: 12rpx;
  background-color: #ee0a24;
  border-radius: 50%;
  border: 2rpx solid #ffffff;
  box-sizing: border-box;
}

.notification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

.notification-title {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
  margin-right: 12rpx;
}

.notification-item.unread .notification-title {
  font-weight: 500;
}

.notification-time {
  font-size: 22rpx;
  color: #999999;
  flex-shrink: 0;
}

.notification-message {
  font-size: 26rpx;
  color: #646566;
  line-height: 1.4;
}

.notification-actions {
  display: flex;
  gap: 12rpx;
  margin-top: 12rpx;
}

.notification-action {
  font-size: 24rpx !important;
  height: 56rpx !important;
  line-height: 56rpx !important;
  padding: 0 16rpx !important;
  border-radius: 8rpx !important;
}

/* 滑动操作 */
.swipe-actions {
  display: flex;
  height: 100%;
  background-color: #f2f3f5;
}

.swipe-action-btn {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  border-radius: 0 !important;
  border: none !important;
}

.swipe-action-btn:first-child {
  background-color: #1890ff !important;
  color: #ffffff !important;
}

.swipe-action-btn:last-child {
  background-color: #ee0a24 !important;
  color: #ffffff !important;
}

/* 空状态 */
.empty-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 80rpx 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  display: block;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 32rpx;
  display: block;
}

.enable-notifications-btn {
  margin-top: 24rpx;
  background-color: #1890ff !important;
  color: #ffffff !important;
  border: none !important;
}

/* 操作菜单 */
.action-sheet-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.action-sheet {
  background-color: #FFFFFF;
  border-radius: 16rpx 16rpx 0 0;
  width: 100%;
  animation: slideUp 0.3s ease-out;
}

.action-sheet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.action-sheet-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.action-sheet-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.action-sheet-body {
  padding: 16rpx 0 32rpx;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background-color: transparent;
  border: none;
  padding: 16rpx 32rpx;
  width: 100%;
  text-align: left;
}

.action-item:active {
  background-color: #F8F9FA;
}

.action-icon {
  font-size: 24rpx;
  width: 32rpx;
}

.action-text {
  font-size: 28rpx;
  color: #333333;
}

/* 设置弹窗 */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.modal-content {
  padding: 32rpx;
  background-color: #ffffff;
  border-radius: 16rpx 16rpx 0 0;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #ebedf0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.modal-close {
  font-size: 36rpx;
  color: #969799;
  padding: 8rpx;
}

.modal-body {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.modal-footer {
  margin-top: 32rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #ebedf0;
}

/* 免打扰时间设置 */
.quiet-hours-modal {
  padding: 32rpx;
  background-color: #ffffff;
  border-radius: 16rpx 16rpx 0 0;
}

/* 设置项 */
.setting-section {
  margin-bottom: 32rpx;
}

.setting-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16rpx;
  display: block;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #ebedf0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-name {
  font-size: 28rpx;
  color: #323233;
  margin-bottom: 4rpx;
  display: block;
}

.setting-description {
  font-size: 24rpx;
  color: #969799;
  display: block;
}

.time-range {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #323233;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .notification-actions {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .notification-action {
    width: auto;
    min-width: 120rpx;
  }
}

/* 动画效果 */
.notification-item {
  transition: all 0.3s ease;
}

.notification-item:active {
  background-color: #f2f3f5;
}

/* 自定义van-tabs样式 */
.filter-tabs {
  background-color: transparent;
}

/* 自定义van-card样式 */
.header-container,
.filter-container,
.notification-group {
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 自定义van-empty样式 */
.van-empty {
  padding: 80rpx 32rpx;
}

/* 自定义van-action-sheet样式 */
.van-action-sheet {
  border-radius: 16rpx 16rpx 0 0;
}

/* 自定义van-popup样式 */
.van-popup {
  border-radius: 16rpx 16rpx 0 0;
}

/* 自定义van-cell样式 */
.van-cell {
  padding: 16rpx;
}

/* 自定义van-button样式 */
.van-button {
  border-radius: 8rpx;
}

/* 自定义van-switch样式 */
.van-switch {
  margin-left: 16rpx;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
