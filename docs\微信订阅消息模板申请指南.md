# 微信订阅消息模板申请指南

## 概述
本项目需要申请3个微信订阅消息模板，用于实现任务提醒、考试提醒和小组动态汇总通知功能。

## 申请步骤

### 1. 登录微信公众平台
- 访问 https://mp.weixin.qq.com/
- 使用小程序管理员账号登录

### 2. 进入订阅消息管理
- 左侧菜单：功能 → 订阅消息
- 点击"公共模板库"

### 3. 申请模板

## 需要申请的模板

### 模板1：任务提醒
**用途**：提醒用户任务即将到期
**关键词组合**：
- 任务名称：thing
- 截止时间：date  
- 提醒内容：thing
- 应用名称：thing

**模板格式示例**：
```
任务提醒
任务名称：{{thing1.DATA}}
截止时间：{{date2.DATA}}
提醒内容：{{thing3.DATA}}
应用名称：{{thing4.DATA}}
```

### 模板2：考试提醒  
**用途**：提醒用户考试时间
**关键词组合**：
- 考试名称：thing
- 考试时间：date
- 剩余天数：number
- 提醒内容：thing
- 应用名称：thing

**模板格式示例**：
```
考试提醒
考试名称：{{thing1.DATA}}
考试时间：{{date2.DATA}}
剩余天数：{{number3.DATA}}天
提醒内容：{{thing4.DATA}}
应用名称：{{thing5.DATA}}
```

### 模板3：小组动态汇总
**用途**：每日汇总小组活动动态
**关键词组合**：
- 小组名称：thing
- 活动汇总：thing
- 活动数量：number
- 日期：time
- 提醒文案：thing

**模板格式示例**：
```
小组动态汇总
小组名称：{{thing1.DATA}}
活动汇总：{{thing2.DATA}}
活动数量：{{number3.DATA}}个
日期：{{time4.DATA}}
提醒文案：{{thing5.DATA}}
```

## 配置模板ID

申请成功后，将获得的模板ID替换到以下文件中：

### 1. utils/notificationApi.js
```javascript
static TEMPLATE_IDS = {
  TASK_REMINDER: '你的任务提醒模板ID',
  EXAM_REMINDER: '你的考试提醒模板ID', 
  GROUP_DIGEST: '你的小组动态模板ID'
}
```

### 2. cloudfunctions/notificationManager/index.js
- 第254行：TASK_REMINDER_TEMPLATE_ID_PLACEHOLDER
- 第283行：EXAM_REMINDER_TEMPLATE_ID_PLACEHOLDER

### 3. cloudfunctions/taskManager/index.js  
- 第334行：GROUP_DIGEST_TEMPLATE_ID_PLACEHOLDER

## 注意事项

1. **模板审核**：提交后需要等待微信审核，通常1-3个工作日
2. **关键词限制**：每个模板最多5个关键词
3. **内容长度**：thing类型最多20个字符，number类型为纯数字
4. **使用场景**：申请时需要详细说明使用场景，确保符合微信规范
5. **测试验证**：模板通过后，建议先在开发环境测试

## 替换完成后的验证

1. 确保所有占位符都已替换为真实模板ID
2. 模板ID格式正确（通常以字母开头的长字符串）
3. 在微信开发者工具中测试订阅消息功能
4. 检查云函数日志确认发送成功

## 常见问题

**Q: 模板申请被拒绝怎么办？**
A: 检查使用场景描述是否清晰，关键词组合是否合理，重新申请时详细说明业务需求。

**Q: 可以修改已通过的模板吗？**
A: 不可以，需要重新申请新模板。

**Q: 模板ID在哪里查看？**
A: 在微信公众平台的订阅消息管理页面，已通过的模板会显示模板ID。
