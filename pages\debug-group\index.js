// pages/debug-group/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    groupId: '',
    groupInfo: null,
    rawData: ''
  },

  onLoad(options) {
    if (options.groupId) {
      this.setData({ groupId: options.groupId })
      this.loadGroupData()
    }
  },

  onGroupIdInput(e) {
    this.setData({ groupId: e.detail.value })
  },

  async loadGroupData() {
    if (!this.data.groupId) {
      wx.showToast({
        title: '请输入小组ID',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      const result = await SmartApi.getStudyGroupDetail(this.data.groupId)
      
      console.log('小组详情结果:', result)

      if (result.success && result.data) {
        const { group } = result.data
        const shouldShow = group.currentMembers < group.maxMembers

        this.setData({
          groupInfo: group,
          rawData: JSON.stringify(result.data, null, 2),
          shouldShowButton: `${group.currentMembers} < ${group.maxMembers} = ${shouldShow}`,
          buttonDisplayText: shouldShow ? '是' : '否'
        })

        console.log('小组信息:', group)
        console.log('当前成员数:', group.currentMembers)
        console.log('最大成员数:', group.maxMembers)
        console.log('按钮显示条件:', shouldShow)
      } else {
        wx.showToast({
          title: result.error || '加载失败',
          icon: 'none'
        })
        this.setData({
          rawData: JSON.stringify(result, null, 2)
        })
      }
    } catch (error) {
      console.error('加载小组详情失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      this.setData({
        rawData: `错误: ${error.message}`
      })
    } finally {
      wx.hideLoading()
    }
  },

  testInviteButton() {
    wx.showModal({
      title: '测试成功',
      content: '邀请按钮可以正常点击！',
      showCancel: false
    })
  }
})
