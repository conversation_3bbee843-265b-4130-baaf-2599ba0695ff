/* pages/search/index.wxss */

/* 搜索头部 */
.search-header {
  background-color: #FFFFFF;
  padding: 16rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #F8F9FA;
  border-radius: 24rpx;
  padding: 12rpx 20rpx;
  position: relative;
}

.search-icon {
  font-size: 24rpx;
  color: #999999;
  margin-right: 12rpx;
}

.search-input {
  flex: 1;
  font-size: 26rpx;
  color: #333333;
  background-color: transparent;
  border: none;
}

.clear-btn {
  background-color: #999999;
  color: #FFFFFF;
  border: none;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8rpx;
}

.cancel-btn {
  background-color: transparent;
  color: #1890FF;
  border: none;
  font-size: 26rpx;
  padding: 0;
}

.search-filters {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
  overflow-x: auto;
}

.filter-item {
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-item.active {
  background-color: #1890FF;
  color: #FFFFFF;
}

/* 搜索内容 */
.search-content {
  padding: 24rpx 32rpx;
}

/* 搜索建议 */
.suggestions-container {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.clear-recent-btn {
  background-color: transparent;
  color: #999999;
  border: none;
  font-size: 24rpx;
  padding: 0;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  background-color: transparent;
  border: none;
  padding: 12rpx 0;
  text-align: left;
}

.recent-icon {
  font-size: 20rpx;
  color: #999999;
}

.recent-text {
  font-size: 26rpx;
  color: #333333;
}

.hot-searches {
  margin-bottom: 32rpx;
}

.hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.hot-tag {
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.hot-tag:active {
  background-color: #E6E6E6;
}

.search-tips {
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
}

.tips-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

/* 搜索结果 */
.search-results {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-count {
  font-size: 26rpx;
  color: #666666;
}

.filter-toggle-btn {
  display: flex;
  align-items: center;
  gap: 4rpx;
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 20rpx;
  padding: 8rpx 12rpx;
  font-size: 24rpx;
}

.filter-icon {
  font-size: 20rpx;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.result-item:active {
  background-color: #F8F9FA;
  transform: scale(0.98);
}

.result-icon {
  width: 60rpx;
  height: 60rpx;
  background-color: #F5F5F5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  flex-shrink: 0;
}

.icon-emoji {
  font-size: 24rpx;
}

.result-content {
  flex: 1;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.result-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  flex: 1;
}

.result-type {
  background-color: #E6F7FF;
  color: #1890FF;
  border-radius: 4rpx;
  padding: 2rpx 6rpx;
  font-size: 20rpx;
}

.result-description {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
  line-height: 1.4;
  display: block;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.result-date {
  font-size: 22rpx;
  color: #999999;
}

.result-status {
  border-radius: 4rpx;
  padding: 2rpx 6rpx;
  font-size: 20rpx;
}

.result-arrow {
  font-size: 24rpx;
  color: #999999;
  flex-shrink: 0;
}

/* 无结果 */
.no-results {
  text-align: center;
  padding: 80rpx 40rpx;
}

.no-results-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  display: block;
  opacity: 0.3;
}

.no-results-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.no-results-subtitle {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 32rpx;
  display: block;
}

.search-suggestions-inline {
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
}

.suggestions-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: block;
}

.suggestions-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.suggestion-tag {
  background-color: #FFFFFF;
  color: #666666;
  border: 1rpx solid #E9ECEF;
  border-radius: 16rpx;
  padding: 6rpx 12rpx;
  font-size: 22rpx;
  transition: all 0.3s ease;
}

.suggestion-tag:active {
  background-color: #E6F7FF;
  border-color: #91D5FF;
  color: #1890FF;
}
