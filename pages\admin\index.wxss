/* pages/admin/index.wxss */
.admin-container {
  padding: 32rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 通用区块样式 */
.stats-section,
.cleanup-section,
.actions-section,
.info-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
}

.user-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.user-id,
.user-openid {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
  font-family: monospace;
}

/* 统计网格 */
.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  color: white;
  min-width: 160rpx;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

/* 清理统计 */
.cleanup-stats {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.cleanup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #e9ecef;
}

.cleanup-item:last-child {
  border-bottom: none;
}

.cleanup-label {
  font-size: 28rpx;
  color: #666;
}

.cleanup-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 操作按钮 */
.action-btn {
  width: 100%;
  margin-bottom: 24rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.primary-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.refresh-btn {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.danger-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
}

.action-btn[disabled] {
  opacity: 0.6;
  background: #ccc !important;
}

/* 说明文字 */
.info-text {
  line-height: 1.6;
}

.info-text text {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  padding-left: 16rpx;
  position: relative;
}

.info-text text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  background: #667eea;
  border-radius: 50%;
}
