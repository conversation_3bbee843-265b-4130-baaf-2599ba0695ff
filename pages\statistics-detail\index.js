// pages/statistics-detail/index.js
Page({
  data: {
    timeRange: 'month',
    rangeDescription: '',

    coreMetrics: {
      totalPomodoros: 0,
      totalTime: '0h',
      avgEfficiency: 0,
      completionRate: 0,
      pomodoroChange: 0,
      timeChange: 0,
      efficiencyChange: 0,
      completionChange: 0
    },

    trendData: [],
    subjectStats: [],
    timeDistribution: [],
    bestStudyTime: '09:00-11:00',
    suggestions: []
  },

  onLoad(options) {
    this.initData()
  },

  onShow() {
    this.loadStatistics()
  },

  // 初始化数据
  initData() {
    this.updateRangeDescription()
    this.loadStatistics()
  },

  // 更新时间范围描述
  updateRangeDescription() {
    const { timeRange } = this.data
    const now = new Date()
    let description = ''

    switch (timeRange) {
      case 'week':
        const weekStart = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000)
        description = `${this.formatDate(weekStart)} - ${this.formatDate(now)}`
        break
      case 'month':
        description = `${now.getFullYear()}年${now.getMonth() + 1}月`
        break
      case 'quarter':
        const quarter = Math.floor(now.getMonth() / 3) + 1
        description = `${now.getFullYear()}年第${quarter}季度`
        break
      case 'year':
        description = `${now.getFullYear()}年`
        break
    }

    this.setData({ rangeDescription: description })
  },

  // 加载统计数据
  loadStatistics() {
    try {
      // 从本地存储获取数据
      const sessions = wx.getStorageSync('studySessions') || []
      const tasks = wx.getStorageSync('tasks') || []
      const exams = wx.getStorageSync('exams') || []

      // 如果没有数据，生成示例数据
      if (sessions.length === 0) {
        this.generateSampleStatistics()
        return
      }

      // 计算各项统计
      this.calculateCoreMetrics(sessions)
      this.generateTrendData(sessions)
      this.calculateSubjectStats(sessions, tasks)
      this.calculateTimeDistribution(sessions)
      this.generateSuggestions()

    } catch (error) {
      console.error('加载统计数据失败:', error)
      this.generateSampleStatistics()
    }
  },

  // 生成示例统计数据
  generateSampleStatistics() {
    // 核心指标
    const coreMetrics = {
      totalPomodoros: 156,
      totalTime: '65h',
      avgEfficiency: 87,
      completionRate: 92,
      pomodoroChange: 15,
      timeChange: 23,
      efficiencyChange: 5,
      completionChange: 8
    }

    // 趋势数据（最近7天）
    const trendData = [
      { date: '06-22', label: '周一', studyTime: 90, targetTime: 120, studyHeight: 75, targetHeight: 100 },
      { date: '06-23', label: '周二', studyTime: 105, targetTime: 120, studyHeight: 87, targetHeight: 100 },
      { date: '06-24', label: '周三', studyTime: 75, targetTime: 120, studyHeight: 62, targetHeight: 100 },
      { date: '06-25', label: '周四', studyTime: 135, targetTime: 120, studyHeight: 112, targetHeight: 100 },
      { date: '06-26', label: '周五', studyTime: 120, targetTime: 120, studyHeight: 100, targetHeight: 100 },
      { date: '06-27', label: '周六', studyTime: 150, targetTime: 120, studyHeight: 125, targetHeight: 100 },
      { date: '06-28', label: '周日', studyTime: 95, targetTime: 120, studyHeight: 79, targetHeight: 100 }
    ]

    // 科目统计
    const subjectStats = [
      {
        subject: '数学',
        icon: '📐',
        totalTime: '25h',
        percentage: 38,
        pomodoros: 60,
        efficiency: 89,
        completedTasks: 12
      },
      {
        subject: '英语',
        icon: '🔤',
        totalTime: '18h',
        percentage: 28,
        pomodoros: 43,
        efficiency: 85,
        completedTasks: 8
      },
      {
        subject: '政治',
        icon: '🏛️',
        totalTime: '12h',
        percentage: 18,
        pomodoros: 29,
        efficiency: 82,
        completedTasks: 6
      },
      {
        subject: '专业课',
        icon: '💻',
        totalTime: '10h',
        percentage: 16,
        pomodoros: 24,
        efficiency: 91,
        completedTasks: 5
      }
    ]

    // 时间分布（24小时）
    const timeDistribution = [
      { hour: '6', height: 5 },
      { hour: '7', height: 15 },
      { hour: '8', height: 35 },
      { hour: '9', height: 85 },
      { hour: '10', height: 100 },
      { hour: '11', height: 75 },
      { hour: '12', height: 20 },
      { hour: '13', height: 10 },
      { hour: '14', height: 45 },
      { hour: '15', height: 70 },
      { hour: '16', height: 60 },
      { hour: '17', height: 40 },
      { hour: '18', height: 25 },
      { hour: '19', height: 55 },
      { hour: '20', height: 80 },
      { hour: '21', height: 65 },
      { hour: '22', height: 30 },
      { hour: '23', height: 10 }
    ]

    // 复习建议
    const suggestions = [
      {
        id: 1,
        type: 'success',
        icon: '🎉',
        title: '复习效率很高',
        description: '您的平均复习效率达到87%，继续保持这种专注状态！'
      },
      {
        id: 2,
        type: 'info',
        icon: '⏰',
        title: '最佳复习时间',
        description: '数据显示您在9-11点复习效率最高，建议安排重要任务在这个时间段。'
      },
      {
        id: 3,
        type: 'warning',
        icon: '📚',
        title: '科目平衡建议',
        description: '数学复习时间较多，建议适当增加其他科目的复习时间，保持均衡发展。'
      },
      {
        id: 4,
        type: 'tip',
        icon: '💡',
        title: '休息时间优化',
        description: '建议在长时间复习后安排15-20分钟的休息，有助于提高后续复习效率。'
      }
    ]

    this.setData({
      coreMetrics,
      trendData,
      subjectStats,
      timeDistribution,
      suggestions
    })
  },

  // 计算核心指标
  calculateCoreMetrics(sessions) {
    // 这里应该根据实际数据计算
    // 为了演示，使用示例数据
    this.generateSampleStatistics()
  },

  // 生成趋势数据
  generateTrendData(sessions) {
    // 根据时间范围生成趋势数据
    // 实际实现中应该根据sessions数据计算
  },

  // 计算科目统计
  calculateSubjectStats(sessions, tasks) {
    // 根据会话和任务数据计算科目统计
    // 实际实现中应该分析sessions中的科目数据
  },

  // 计算时间分布
  calculateTimeDistribution(sessions) {
    // 分析sessions中的时间分布
    // 计算每个小时的复习频率
  },

  // 生成复习建议
  generateSuggestions() {
    // 根据统计数据生成个性化建议
    // 已在generateSampleStatistics中实现
  },

  // 切换时间范围
  switchTimeRange(e) {
    const range = e.currentTarget.dataset.range
    this.setData({ timeRange: range })
    this.updateRangeDescription()
    this.loadStatistics()
  },

  // 导出数据
  exportData(e) {
    const format = e.currentTarget.dataset.format

    wx.showLoading({
      title: '准备导出...'
    })

    setTimeout(() => {
      wx.hideLoading()

      if (format === 'excel') {
        wx.showToast({
          title: 'Excel文件已生成',
          icon: 'success'
        })
      } else if (format === 'pdf') {
        wx.showToast({
          title: 'PDF报告已生成',
          icon: 'success'
        })
      }
    }, 2000)
  },

  // 分享报告
  shareReport() {
    wx.showActionSheet({
      itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
      success: (res) => {
        const actions = ['微信', '朋友圈', '链接']
        wx.showToast({
          title: `已分享到${actions[res.tapIndex]}`,
          icon: 'success'
        })
      }
    })
  },

  // 工具方法
  formatDate(date) {
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${month}-${day}`
  }
})