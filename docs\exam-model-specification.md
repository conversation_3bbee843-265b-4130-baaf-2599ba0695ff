# 考试模型标准化规范

## 概述

本文档定义了考试数据模型的标准规范，包括字段定义、类型约束、验证规则等，作为整个系统中考试相关功能开发的统一标准。

## 核心数据模型

### 考试实体 (Exam Entity)

```javascript
const ExamSchema = {
  // 基础标识字段
  _id: {
    type: String,
    required: true,
    description: '考试唯一标识符，由数据库自动生成'
  },
  
  // 基本信息字段
  title: {
    type: String,
    required: true,
    minLength: 1,
    maxLength: 50,
    trim: true,
    description: '考试名称，如：高等数学期末考试'
  },
  
  subject: {
    type: String,
    required: false,
    maxLength: 30,
    trim: true,
    description: '考试主要科目，如：数学、英语、物理'
  },
  
  // 时间地点字段
  examDate: {
    type: String,
    required: true,
    format: 'YYYY-MM-DD',
    validate: 'isValidDate',
    description: '考试日期，格式：2025-12-25'
  },
  
  examTime: {
    type: String,
    required: true,
    format: 'HH:MM',
    validate: 'isValidTime',
    description: '考试时间，格式：09:00'
  },
  
  location: {
    type: String,
    required: false,
    maxLength: 100,
    trim: true,
    description: '考试地点，如：教学楼A101'
  },
  
  // 描述信息字段
  description: {
    type: String,
    required: false,
    maxLength: 500,
    trim: true,
    description: '考试描述或备注信息'
  },
  
  // 分类字段
  type: {
    type: String,
    required: true,
    enum: ['final', 'midterm', 'quiz', 'certificate', 'entrance', 'other'],
    default: 'final',
    description: '考试类型'
  },
  
  importance: {
    type: String,
    required: true,
    enum: ['high', 'medium', 'low'],
    default: 'medium',
    description: '重要程度'
  },
  
  status: {
    type: String,
    required: true,
    enum: ['upcoming', 'ongoing', 'finished'],
    default: 'upcoming',
    description: '考试状态'
  },
  
  // 提醒设置字段
  reminderSettings: {
    type: Array,
    items: {
      type: String,
      enum: ['1day', '3days', '1week', '2weeks']
    },
    default: ['1day', '3days'],
    description: '考试提醒设置'
  },
  
  // 系统字段
  userId: {
    type: String,
    required: true,
    description: '创建用户ID'
  },
  
  createTime: {
    type: Date,
    required: true,
    default: 'Date.now',
    description: '创建时间'
  },
  
  updateTime: {
    type: Date,
    required: true,
    default: 'Date.now',
    description: '最后更新时间'
  }
}
```

## 枚举值定义

### 考试类型 (ExamType)
```javascript
const ExamType = {
  FINAL: 'final',           // 期末考试
  MIDTERM: 'midterm',       // 期中考试
  QUIZ: 'quiz',             // 小测验
  CERTIFICATE: 'certificate', // 资格考试
  ENTRANCE: 'entrance',     // 入学考试
  OTHER: 'other'            // 其他
}
```

### 重要程度 (ImportanceLevel)
```javascript
const ImportanceLevel = {
  HIGH: 'high',       // 非常重要
  MEDIUM: 'medium',   // 重要
  LOW: 'low'          // 一般
}
```

### 考试状态 (ExamStatus)
```javascript
const ExamStatus = {
  UPCOMING: 'upcoming',   // 即将到来
  ONGOING: 'ongoing',     // 进行中
  FINISHED: 'finished'    // 已结束
}
```

### 提醒设置 (ReminderSettings)
```javascript
const ReminderSettings = {
  ONE_DAY: '1day',       // 考试前1天
  THREE_DAYS: '3days',   // 考试前3天
  ONE_WEEK: '1week',     // 考试前1周
  TWO_WEEKS: '2weeks'    // 考试前2周
}
```

## 数据验证规则

### 必填字段验证
- `title`: 不能为空，去除首尾空格后长度 >= 1
- `examDate`: 必须是有效的日期格式 YYYY-MM-DD
- `examTime`: 必须是有效的时间格式 HH:MM
- `userId`: 不能为空

### 格式验证
```javascript
const ValidationRules = {
  // 日期格式验证
  isValidDate: (dateStr) => {
    const regex = /^\d{4}-\d{2}-\d{2}$/
    if (!regex.test(dateStr)) return false
    const date = new Date(dateStr)
    return date instanceof Date && !isNaN(date)
  },
  
  // 时间格式验证
  isValidTime: (timeStr) => {
    const regex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    return regex.test(timeStr)
  },
  
  // 枚举值验证
  isValidEnum: (value, enumObject) => {
    return Object.values(enumObject).includes(value)
  }
}
```

### 业务逻辑验证
- 考试日期不能早于当前日期（创建时）
- 考试时间必须是合理的时间范围（如：06:00-23:59）
- 提醒设置数组不能为空

## 数据转换规则

### 输入数据清理
```javascript
const DataCleaning = {
  // 字符串字段去除首尾空格
  trimString: (str) => typeof str === 'string' ? str.trim() : str,
  
  // 日期格式标准化
  normalizeDate: (dateStr) => {
    const date = new Date(dateStr)
    return date.toISOString().split('T')[0]
  },
  
  // 时间格式标准化
  normalizeTime: (timeStr) => {
    const [hours, minutes] = timeStr.split(':')
    return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`
  }
}
```

### 兼容性处理
```javascript
const CompatibilityMapping = {
  // 旧字段名映射到新字段名
  fieldMapping: {
    'name': 'title',           // 考试名称字段统一
    'date': 'examDate',        // 日期字段统一
    'time': 'examTime',        // 时间字段统一
    'notes': 'description',    // 描述字段统一
    'subjects': 'subject'      // 科目字段统一（数组转单个）
  },
  
  // 数组科目转单个科目
  convertSubjects: (subjects) => {
    if (Array.isArray(subjects)) {
      return subjects.length > 0 ? subjects[0] : ''
    }
    return subjects || ''
  }
}
```

## API接口规范

### 请求数据格式
```javascript
// 创建考试请求
const CreateExamRequest = {
  title: String,           // 必填
  subject: String,         // 可选
  examDate: String,        // 必填，YYYY-MM-DD
  examTime: String,        // 必填，HH:MM
  location: String,        // 可选
  description: String,     // 可选
  type: String,           // 可选，默认 'final'
  importance: String,     // 可选，默认 'medium'
  reminderSettings: Array // 可选，默认 ['1day', '3days']
}

// 更新考试请求
const UpdateExamRequest = {
  // 所有字段都是可选的，只更新提供的字段
  title: String,
  subject: String,
  examDate: String,
  examTime: String,
  location: String,
  description: String,
  type: String,
  importance: String,
  status: String,
  reminderSettings: Array
}
```

### 响应数据格式
```javascript
// 标准响应格式
const ApiResponse = {
  success: Boolean,
  data: Object | Array,
  message: String,
  error: String,
  timestamp: String
}

// 考试数据响应
const ExamResponse = {
  _id: String,
  title: String,
  subject: String,
  examDate: String,
  examTime: String,
  location: String,
  description: String,
  type: String,
  importance: String,
  status: String,
  reminderSettings: Array,
  userId: String,
  createTime: String,
  updateTime: String
}
```

## 错误处理规范

### 错误类型定义
```javascript
const ExamErrorTypes = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',     // 数据验证错误
  NOT_FOUND: 'NOT_FOUND',                   // 考试不存在
  PERMISSION_DENIED: 'PERMISSION_DENIED',   // 权限不足
  DUPLICATE_ERROR: 'DUPLICATE_ERROR',       // 重复数据
  SYSTEM_ERROR: 'SYSTEM_ERROR'              // 系统错误
}
```

### 错误消息模板
```javascript
const ErrorMessages = {
  TITLE_REQUIRED: '考试名称不能为空',
  TITLE_TOO_LONG: '考试名称不能超过50个字符',
  INVALID_DATE: '考试日期格式不正确，请使用YYYY-MM-DD格式',
  INVALID_TIME: '考试时间格式不正确，请使用HH:MM格式',
  DATE_IN_PAST: '考试日期不能早于当前日期',
  EXAM_NOT_FOUND: '考试不存在或已被删除',
  PERMISSION_DENIED: '您没有权限操作此考试'
}
```

## 使用示例

### 创建考试数据
```javascript
const examData = {
  title: '高等数学期末考试',
  subject: '数学',
  examDate: '2025-12-25',
  examTime: '09:00',
  location: '教学楼A101',
  description: '涵盖微积分、线性代数等内容',
  type: 'final',
  importance: 'high',
  reminderSettings: ['1day', '3days', '1week']
}
```

### 数据验证示例
```javascript
// 验证考试数据
const validateExamData = (data) => {
  const errors = []
  
  if (!data.title || data.title.trim().length === 0) {
    errors.push('考试名称不能为空')
  }
  
  if (!ValidationRules.isValidDate(data.examDate)) {
    errors.push('考试日期格式不正确')
  }
  
  if (!ValidationRules.isValidTime(data.examTime)) {
    errors.push('考试时间格式不正确')
  }
  
  return errors
}
```

## 数据库设计

### 索引设计
```javascript
// 考试集合索引
db.exams.createIndex({ "userId": 1, "examDate": 1 })  // 用户考试按日期查询
db.exams.createIndex({ "userId": 1, "status": 1 })    // 用户考试按状态查询
db.exams.createIndex({ "examDate": 1 })               // 全局考试日期查询
db.exams.createIndex({ "createTime": -1 })            // 创建时间倒序
```

### 查询优化
```javascript
// 常用查询模式
const CommonQueries = {
  // 获取用户即将到来的考试
  getUpcomingExams: {
    userId: userId,
    status: 'upcoming',
    examDate: { $gte: new Date().toISOString().split('T')[0] }
  },

  // 获取用户本月考试
  getMonthlyExams: {
    userId: userId,
    examDate: {
      $gte: startOfMonth,
      $lte: endOfMonth
    }
  }
}
```

## 性能优化建议

### 数据缓存策略
- 用户考试列表缓存时间：5分钟
- 考试详情缓存时间：10分钟
- 考试统计数据缓存时间：1小时

### 分页查询
```javascript
const PaginationConfig = {
  defaultPageSize: 20,
  maxPageSize: 100,
  sortFields: ['examDate', 'createTime', 'updateTime']
}
```

## 版本兼容性

### 数据迁移策略
```javascript
const MigrationRules = {
  // v1.0 -> v2.0 迁移规则
  'v1.0_to_v2.0': {
    // 字段重命名
    renameFields: {
      'name': 'title',
      'date': 'examDate',
      'time': 'examTime',
      'notes': 'description'
    },

    // 数据类型转换
    convertTypes: {
      'subjects': (subjects) => Array.isArray(subjects) ? subjects[0] || '' : subjects
    },

    // 默认值设置
    setDefaults: {
      'type': 'final',
      'importance': 'medium',
      'status': 'upcoming',
      'reminderSettings': ['1day', '3days']
    }
  }
}
```

---

**文档版本**: v1.0
**创建日期**: 2025-07-04
**最后更新**: 2025-07-04
**维护者**: 开发团队
