// pages/feedback/index.js
const CloudApi = require('../../utils/cloudApi')
const LoginApi = require('../../utils/loginApi')

Page({
  data: {
    // 反馈表单数据
    feedbackType: 'bug',
    feedbackContent: '',
    contactInfo: '',
    canSubmit: false,

    // 反馈历史
    feedbackHistory: []
  },

  onLoad(options) {
    // 检查登录状态
    this.checkLoginStatus()
    this.loadFeedbackHistory()
    this.checkSubmitStatus()
  },

  onShow() {
    this.checkSubmitStatus()
  },

  // 检查登录状态
  checkLoginStatus() {
    const loginStatus = LoginApi.checkLoginStatus()

    console.log('🔍 反馈页面-登录状态详细检查:', {
      isLoggedIn: loginStatus.isLoggedIn,
      hasUserInfo: !!loginStatus.userInfo,
      openid: loginStatus.openid,
      userInfo_id: loginStatus.userInfo?._id,
      userInfoKeys: loginStatus.userInfo ? Object.keys(loginStatus.userInfo) : null
    })

    if (!loginStatus.isLoggedIn) {
      wx.showModal({
        title: '需要登录',
        content: '提交反馈需要先登录，是否前往登录？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/index'
            })
          } else {
            // 用户取消登录，返回上一页
            wx.navigateBack()
          }
        }
      })
      return false
    }
    return true
  },

  // 选择反馈类型
  selectFeedbackType(e) {
    const type = e.detail
    this.setData({ feedbackType: type })
    this.checkSubmitStatus()
  },

  // 反馈内容输入
  onFeedbackInput(e) {
    this.setData({ feedbackContent: e.detail })
    this.checkSubmitStatus()
  },

  // 联系方式输入
  onContactInput(e) {
    this.setData({ contactInfo: e.detail })
  },

  // 检查是否可以提交
  checkSubmitStatus() {
    const canSubmit = this.data.feedbackContent.trim().length >= 8
    this.setData({ canSubmit })
  },

  // 提交反馈
  async submitFeedback() {
    // 检查登录状态
    if (!this.checkLoginStatus()) {
      return
    }

    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请填写至少8个字的问题描述',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '提交中...'
    })

    try {
      // 获取当前登录用户的真实ID
      const loginStatus = LoginApi.checkLoginStatus()

      console.log('🔍 反馈页面-登录状态检查:', {
        isLoggedIn: loginStatus.isLoggedIn,
        hasUserInfo: !!loginStatus.userInfo,
        userInfo: loginStatus.userInfo,
        openid: loginStatus.openid
      })

      if (!loginStatus.isLoggedIn || !loginStatus.userInfo) {
        throw new Error('用户未登录或用户信息缺失')
      }

      // 检查用户信息中的ID字段
      console.log('🔍 用户信息详细检查:', {
        '_id': loginStatus.userInfo._id,
        'id': loginStatus.userInfo.id,
        'userId': loginStatus.userInfo.userId,
        'openid字段': loginStatus.userInfo.openid,
        '全部字段': Object.keys(loginStatus.userInfo)
      })

      // 获取真实用户ID，优先使用_id，如果没有则使用其他可能的字段
      let realUserId = loginStatus.userInfo._id ||
                       loginStatus.userInfo.id ||
                       loginStatus.userInfo.userId

      if (!realUserId) {
        console.error('❌ 无法获取真实用户ID，用户信息:', loginStatus.userInfo)
        throw new Error('无法获取用户ID')
      }

      // 准备反馈数据，包含真实用户ID
      const feedbackData = {
        type: this.data.feedbackType,
        content: this.data.feedbackContent.trim(),
        contactInfo: this.data.contactInfo.trim(),
        userAgent: wx.getSystemInfoSync().platform,
        version: '2.0.0',
        userId: realUserId  // 传递真实用户ID
      }

      console.log('✅ 提交反馈数据:', {
        userId: feedbackData.userId,
        userIdLength: feedbackData.userId?.length,
        userIdType: typeof feedbackData.userId,
        type: feedbackData.type,
        contentLength: feedbackData.content.length
      })

      // 提交到云数据库
      const result = await CloudApi.submitFeedback(feedbackData)

      wx.hideLoading()

      if (result.success) {
        // 同时保存到本地历史（作为备份和离线查看）
        this.saveFeedbackToHistory()

        wx.showToast({
          title: '反馈提交成功',
          icon: 'success'
        })

        // 清空表单
        this.setData({
          feedbackContent: '',
          contactInfo: '',
          canSubmit: false
        })

        // 重新加载历史记录
        this.loadFeedbackHistory()
      } else {
        throw new Error(result.error || '提交失败')
      }
    } catch (error) {
      wx.hideLoading()
      console.error('提交反馈失败:', error)

      // 如果云端提交失败，仍然保存到本地
      this.saveFeedbackToHistory()

      wx.showToast({
        title: '提交失败，已保存到本地',
        icon: 'none',
        duration: 2000
      })

      // 清空表单
      this.setData({
        feedbackContent: '',
        contactInfo: '',
        canSubmit: false
      })

      // 重新加载历史记录
      this.loadFeedbackHistory()
    }
  },

  // 保存反馈到历史记录
  saveFeedbackToHistory() {
    try {
      const feedback = {
        id: Date.now(),
        type: this.data.feedbackType,
        typeText: this.getFeedbackTypeText(this.data.feedbackType),
        content: this.data.feedbackContent,
        contactInfo: this.data.contactInfo,
        date: this.formatDate(new Date()),
        status: 'pending',
        statusText: '处理中'
      }

      const history = wx.getStorageSync('feedback_history') || []
      history.unshift(feedback)
      
      // 只保留最近10条记录
      if (history.length > 10) {
        history.splice(10)
      }

      wx.setStorageSync('feedback_history', history)
    } catch (error) {
      console.error('保存反馈历史失败:', error)
    }
  },

  // 加载反馈历史
  async loadFeedbackHistory() {
    try {
      // 优先从云端加载
      const cloudResult = await CloudApi.getFeedbackHistory(1, 10)

      if (cloudResult.success && cloudResult.data) {
        const processedHistory = this.processHistoryData(cloudResult.data)
        this.setData({ feedbackHistory: processedHistory })

        // 同时更新本地缓存
        wx.setStorageSync('feedback_history', cloudResult.data)
      } else {
        // 云端加载失败，使用本地缓存
        const localHistory = wx.getStorageSync('feedback_history') || []
        const processedHistory = this.processHistoryData(localHistory)
        this.setData({ feedbackHistory: processedHistory })
      }
    } catch (error) {
      console.error('加载反馈历史失败:', error)

      // 出错时使用本地缓存
      try {
        const localHistory = wx.getStorageSync('feedback_history') || []
        const processedHistory = this.processHistoryData(localHistory)
        this.setData({ feedbackHistory: processedHistory })
      } catch (localError) {
        console.error('加载本地反馈历史失败:', localError)
        this.setData({ feedbackHistory: [] })
      }
    }
  },

  // 处理历史数据，添加显示内容和展开标识
  processHistoryData(historyData) {
    return historyData.map(item => {
      const content = item.content || ''
      const maxLength = 15

      if (content.length <= maxLength) {
        return {
          ...item,
          displayContent: content,
          hasMore: false
        }
      } else {
        return {
          ...item,
          displayContent: content.substring(0, maxLength),
          hasMore: true
        }
      }
    })
  },

  // 显示完整内容
  showFullContent(e) {
    const content = e.currentTarget.dataset.content
    wx.showModal({
      title: '完整反馈内容',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 获取反馈类型文本
  getFeedbackTypeText(type) {
    const typeMap = {
      'bug': '🐛 Bug反馈',
      'feature': '💡 功能建议',
      'other': '💬 其他'
    }
    return typeMap[type] || '💬 其他'
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}`
  },

  // 复制联系方式
  copyContact(e) {
    const text = e.currentTarget.dataset.text
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        })
      }
    })
  },

  // 加入用户群
  joinGroup() {
    wx.showModal({
      title: '加入用户群',
      content: '请添加微信号：augmentcode，备注"备考助手"，我们会邀请您加入用户交流群。',
      showCancel: false,
      confirmText: '知道了'
    })
  }
})
