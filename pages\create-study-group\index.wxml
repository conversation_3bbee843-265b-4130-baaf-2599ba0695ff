<!-- pages/create-study-group/index.wxml -->
<view class="container">
  <!-- 页面引导 -->
  <view class="page-header">
    <view class="header-desc">创建备考小组，邀请备考搭子组队更有劲！</view>
  </view>

  <!-- 选择考试 -->
  <van-card 
    title="选择考试"
    custom-class="section-card"
    >
    <van-cell-group slot="desc" custom-class="exam-list" wx:if="{{examList.length > 0}}">
      <van-cell 
        wx:for="{{examList}}" 
        wx:key="id"
        title="{{item.name}}"
        label="{{item.subject}}"
        value="{{item.date}}"
        border="{{false}}"
        clickable
        custom-class="exam-item {{selectedExam && selectedExam.id === item.id ? 'selected' : ''}}"
        bind:click="onSelectExam"
        data-exam="{{item}}"
        >
        <van-icon 
          wx:if="{{selectedExam && selectedExam.id === item.id}}"
          slot="right-icon" 
          name="success" 
          color="#1890ff" 
          size="20px"
          />
      </van-cell>
    </van-cell-group>
    
    <van-empty 
      slot="desc"
      wx:if="{{examList.length === 0}}"
      image="search"
      description="还没有考试计划"
      >
      <van-button 
        slot="description"
        type="default" 
        size="small"
        bind:click="addExam"
        custom-class="add-exam-btn">
        添加考试
      </van-button>
    </van-empty>
  </van-card>

  <!-- 小组名称 -->
  <van-card 
    title="小组名称（可选）"
    custom-class="section-card"
    >
    <van-cell-group slot="desc">
      <van-field 
        model:value="{{groupName}}"
        placeholder="{{selectedExam ? selectedExam.name + '搭子小组' : '请先选择考试'}}"
        disabled="{{!selectedExam}}"
        border="{{false}}"
        bind:change="onGroupNameInput"
        />
    </van-cell-group>
    <view slot="footer" class="input-tip">
      <van-tag type="default" size="medium">不填写将使用默认名称</van-tag>
    </view>
  </van-card>

  <!-- 小组说明 -->
  <van-card 
    title="小组功能特色"
    custom-class="section-card"
    >
    <view slot="desc" class="group-info">
      <view class="info-grid">
        <view class="info-item">
          <view class="info-icon">👥</view>
          <text class="info-text">最多3人小组</text>
        </view>
        <view class="info-item">
          <view class="info-icon">📋</view>
          <text class="info-text">分享复习计划</text>
        </view>
        <view class="info-item">
          <view class="info-icon">📊</view>
          <text class="info-text">查看彼此进度</text>
        </view>
        <view class="info-item">
          <view class="info-icon">👍</view>
          <text class="info-text">互相点赞鼓励</text>
        </view>
      </view>
    </view>
  </van-card>

  <!-- 创建按钮 -->
  <view class="bottom-actions">
    <van-button 
      type="primary"
      size="large"
      block
      round
      bind:click="onCreateGroup"
      disabled="{{!selectedExam || loading}}"
      loading="{{loading}}"
      loading-text="创建中..."
      custom-class="create-btn"
      >
      {{loading ? '' : '创建搭子小组'}}
    </van-button>
  </view>
</view>
