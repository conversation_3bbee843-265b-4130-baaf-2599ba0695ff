# 通知功能测试验证指南

## 概述
本文档提供了完整的通知功能测试流程，确保任务提醒、考试提醒和小组动态汇总功能正常工作。

## 前置条件

### 1. 模板配置
- ✅ 确保已在微信公众平台申请并配置了3个订阅消息模板
- ✅ 确保模板ID已正确替换项目中的占位符
- ✅ 确保模板状态为"已通过"

### 2. 云函数部署
- ✅ 确保notificationManager云函数已部署最新版本
- ✅ 确保taskManager和studyGroupManager云函数已更新
- ✅ 确保定时触发器已配置并启用

### 3. 数据库准备
- ✅ 确保相关数据库集合存在：user_subscriptions、notification_logs、exam_reminders、group_activities
- ✅ 确保用户已登录并有有效的openid

## 测试流程

### 测试1：任务提醒功能

#### 1.1 创建任务测试
**步骤**：
1. 打开小程序，进入"添加任务"页面
2. 填写任务信息：
   - 任务名称：测试任务提醒
   - 截止日期：明天
   - 截止时间：09:00
   - 提醒设置：开启，选择"1小时前"
3. 保存任务

**预期结果**：
- ✅ 任务创建成功
- ✅ 弹出订阅消息授权弹窗（如果用户未订阅）
- ✅ 用户同意后，订阅状态更新

#### 1.2 手动触发任务提醒测试
**步骤**：
1. 在云开发控制台进入notificationManager云函数
2. 在测试面板中输入：
```json
{
  "action": "sendBatchReminders"
}
```
3. 点击"测试"执行

**预期结果**：
- ✅ 云函数执行成功
- ✅ 返回发送统计信息
- ✅ notification_logs表中有发送记录
- ✅ 用户收到任务提醒消息（如果任务符合提醒条件）

#### 1.3 验证任务提醒内容
**检查项**：
- ✅ 消息模板格式正确
- ✅ 任务名称显示正确
- ✅ 截止时间显示正确
- ✅ 点击消息能跳转到任务详情页

### 测试2：考试提醒功能

#### 2.1 创建考试测试
**步骤**：
1. 进入"添加考试"页面
2. 填写考试信息：
   - 考试名称：测试考试提醒
   - 考试日期：3天后
   - 考试时间：14:00
   - 提醒设置：选择"考试前1天"和"考试前3天"
3. 保存考试

**预期结果**：
- ✅ 考试创建成功
- ✅ exam_reminders表中创建了提醒记录
- ✅ 弹出订阅消息授权弹窗
- ✅ 用户同意后，订阅状态更新

#### 2.2 检查考试提醒记录
**步骤**：
1. 在云开发控制台查看exam_reminders集合
2. 找到刚创建的考试提醒记录

**预期结果**：
- ✅ 记录包含正确的考试信息
- ✅ reminderDate计算正确（考试日期减去提醒天数）
- ✅ sent字段为false

#### 2.3 手动触发考试提醒测试
**步骤**：
1. 修改exam_reminders记录的reminderDate为今天
2. 在云函数测试面板执行sendBatchReminders
3. 检查发送结果

**预期结果**：
- ✅ 考试提醒发送成功
- ✅ 消息内容包含考试名称、时间、剩余天数
- ✅ 点击消息能跳转到考试详情页

### 测试3：小组动态汇总功能

#### 3.1 创建小组活动测试
**步骤**：
1. 创建或加入一个搭子小组
2. 在小组中进行以下活动：
   - 完成一个任务
   - 分享一个学习计划
   - 邀请新成员加入
3. 检查group_activities集合中的记录

**预期结果**：
- ✅ 每个活动都创建了对应的记录
- ✅ 记录包含notified: false字段
- ✅ 记录包含正确的活动类型和数据

#### 3.2 手动触发小组动态汇总测试
**步骤**：
1. 在云函数测试面板中输入：
```json
{
  "action": "sendDailyGroupDigest"
}
```
2. 执行测试

**预期结果**：
- ✅ 云函数执行成功
- ✅ 返回汇总统计信息
- ✅ 小组成员收到汇总通知
- ✅ 活动记录的notified字段更新为true

#### 3.3 验证汇总通知内容
**检查项**：
- ✅ 小组名称显示正确
- ✅ 活动汇总内容准确（如"昨日共有5个动态：完成任务3个，分享计划2个"）
- ✅ 活动数量统计正确
- ✅ 点击消息能跳转到小组详情页

### 测试4：订阅管理功能

#### 4.1 订阅状态检查测试
**步骤**：
1. 进入"通知设置"页面
2. 查看订阅状态显示

**预期结果**：
- ✅ 正确显示当前订阅状态
- ✅ 显示订阅的模板数量
- ✅ 显示订阅时间

#### 4.2 订阅请求测试
**步骤**：
1. 如果未订阅，点击"开启消息通知"按钮
2. 在弹出的授权弹窗中选择要订阅的模板
3. 点击"允许"

**预期结果**：
- ✅ 订阅请求发送成功
- ✅ user_subscriptions表中创建或更新记录
- ✅ 页面状态更新为已订阅

#### 4.3 通知类型设置测试
**步骤**：
1. 在通知设置页面切换不同通知类型的开关
2. 检查设置是否保存

**预期结果**：
- ✅ 开关状态正确保存
- ✅ 设置影响实际通知发送

### 测试5：定时触发器功能

#### 5.1 定时触发器配置验证
**步骤**：
1. 在云开发控制台查看notificationManager云函数的触发器
2. 确认两个触发器状态

**预期结果**：
- ✅ dailyReminders触发器：每天9:00，状态启用
- ✅ dailyGroupDigest触发器：每天20:00，状态启用

#### 5.2 定时执行日志检查
**步骤**：
1. 等待定时触发器执行时间
2. 在云函数日志中查看执行记录

**预期结果**：
- ✅ 定时触发器按时执行
- ✅ 日志显示触发源为"timer"
- ✅ 执行结果正常，无错误

### 测试6：异常情况处理

#### 6.1 未订阅用户测试
**步骤**：
1. 使用未订阅消息的用户账号
2. 触发各种通知发送

**预期结果**：
- ✅ 不会发送通知给未订阅用户
- ✅ 日志中记录"用户未订阅"信息
- ✅ 不影响其他用户的通知发送

#### 6.2 模板ID错误测试
**步骤**：
1. 临时修改一个模板ID为错误值
2. 触发通知发送

**预期结果**：
- ✅ 发送失败，返回错误信息
- ✅ 错误日志记录详细错误原因
- ✅ 不影响其他正确模板的发送

#### 6.3 网络异常测试
**步骤**：
1. 在网络不稳定环境下测试
2. 检查重试机制和错误处理

**预期结果**：
- ✅ 有适当的错误处理
- ✅ 失败的通知有记录
- ✅ 不会导致云函数崩溃

## 验证清单

### 功能完整性
- [ ] 任务提醒功能正常
- [ ] 考试提醒功能正常  
- [ ] 小组动态汇总功能正常
- [ ] 订阅管理功能正常
- [ ] 定时触发器功能正常

### 数据一致性
- [ ] notification_logs记录完整
- [ ] user_subscriptions状态正确
- [ ] exam_reminders创建正确
- [ ] group_activities标记正确

### 用户体验
- [ ] 订阅流程顺畅
- [ ] 通知内容准确
- [ ] 页面跳转正常
- [ ] 错误提示友好

### 性能稳定性
- [ ] 云函数执行稳定
- [ ] 批量处理高效
- [ ] 定时任务可靠
- [ ] 异常处理完善

## 常见问题排查

### 通知未收到
1. 检查用户订阅状态
2. 验证模板ID配置
3. 查看notification_logs错误记录
4. 确认微信订阅消息配额

### 定时任务未执行
1. 检查触发器配置和状态
2. 验证Cron表达式
3. 查看云函数执行日志
4. 确认云开发环境状态

### 订阅失败
1. 检查模板ID是否正确
2. 验证模板是否已通过审核
3. 确认用户操作流程
4. 查看云函数错误日志

## 测试完成标准

所有测试项目通过，且满足以下条件：
- ✅ 三种通知类型都能正常发送
- ✅ 定时触发器稳定运行
- ✅ 订阅管理功能完善
- ✅ 异常情况处理正确
- ✅ 用户体验流畅
- ✅ 数据记录完整准确
