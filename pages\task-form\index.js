// pages/task-form/index.js

// 引入必要的工具类
const SmartApi = require('../../utils/smartApi')
const NavigationUtils = require('../../utils/navigationUtils')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 页面模式相关
    mode: 'add', // add 或 edit
    isEditMode: false,
    pageTitle: '新增任务',
    taskId: null,

    // 基础数据结构
    taskForm: {
      title: '',
      description: '',
      examId: '',
      examName: '',
      subject: '',
      priority: 'medium',
      dueDate: '',
      dueTime: '',
      estimatedDuration: '',
      subtasks: [],
      reminderEnabled: false,
      reminderTime: ''
    },

    // 页面状态
    loading: false,
    canSave: false,

    // 选项数据
    examOptions: [],
    subjectOptions: [],
    priorityOptions: [
      { value: 'high', label: '高优先级', icon: '🔴', color: '#ff4d4f' },
      { value: 'medium', label: '中优先级', icon: '🟡', color: '#faad14' },
      { value: 'low', label: '低优先级', icon: '🟢', color: '#52c41a' }
    ],
    durationOptions: [
      '15分钟', '30分钟', '45分钟', '1小时', '1.5小时', '2小时', '3小时', '4小时', '半天', '1天'
    ],
    reminderOptions: [
      { value: 5, label: '提前5分钟' },
      { value: 15, label: '提前15分钟' },
      { value: 30, label: '提前30分钟' },
      { value: 60, label: '提前1小时' },
      { value: 120, label: '提前2小时' },
      { value: 1440, label: '提前1天' }
    ],

    // 检查点相关数据
    completedCheckpointsCount: 0,
    allSelected: false,
    showCheckpointSection: false,
    showAddCheckpointInput: false,
    newCheckpointTitle: '',

    // 拖拽相关数据
    dragIndex: -1,
    isDragging: false,
    startY: 0,
    currentY: 0,

    // 表单验证
    titleError: '',
    canSave: false,
    isSubmitting: false,

    // 弹窗状态
    showExamModal: false,
    showSubjectModal: false,
    showPriorityModal: false,
    showDatePicker: false,
    showTimePicker: false,
    showDurationPicker: false,
    showReminderPicker: false,

    // 选择器数据
    currentDate: new Date().getTime(),
    currentTime: '09:00',
    minDate: new Date().getTime(), // 最小日期为今天

    // 显示标签
    selectedPriorityLabel: '中优先级',
    reminderTimeText: '提前30分钟',

    // 选项数据
    priorityOptions: [
      { value: 'high', label: '高优先级', icon: '🔴', color: '#ff4d4f' },
      { value: 'medium', label: '中优先级', icon: '🟡', color: '#faad14' },
      { value: 'low', label: '低优先级', icon: '🟢', color: '#52c41a' }
    ],
    durationOptions: [
      '15分钟', '30分钟', '45分钟', '1小时', '1.5小时', '2小时', '3小时', '4小时', '半天', '1天'
    ],
    reminderOptions: [
      { value: 5, label: '提前5分钟' },
      { value: 15, label: '提前15分钟' },
      { value: 30, label: '提前30分钟' },
      { value: 60, label: '提前1小时' },
      { value: 120, label: '提前2小时' },
      { value: 1440, label: '提前1天' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    console.log('task-form页面加载，参数:', options)

    // 检查登录状态
    const app = getApp()
    const loginStatus = app.checkLoginStatus()

    if (!loginStatus.isLoggedIn) {
      wx.navigateTo({
        url: '/pages/login/index'
      })
      return
    }

    // 基础页面初始化
    await this.initializePage(options)
  },

  /**
   * 初始化页面
   */
  async initializePage(options) {
    const mode = options.mode || 'add'
    const isEditMode = mode === 'edit'
    const pageTitle = isEditMode ? '编辑任务' : '新增任务'
    const taskId = options.id || null
    const examId = options.examId || null

    // 设置页面基础状态
    this.setData({
      mode: mode,
      isEditMode: isEditMode,
      pageTitle: pageTitle,
      taskId: taskId,
      loading: true
    })

    // 动态设置页面标题
    wx.setNavigationBarTitle({
      title: pageTitle
    })

    console.log('页面初始化完成，模式:', mode, '任务ID:', taskId, '考试ID:', examId)

    try {
      // 根据模式进行不同的初始化
      if (isEditMode && taskId) {
        // 编辑模式：加载现有任务数据
        await this.loadTaskData(taskId)
      } else {
        // 新增模式：初始化空表单
        await this.initializeNewTask(examId)
      }
    } catch (error) {
      console.error('页面初始化失败:', error)
      wx.showToast({
        title: '页面加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('task-form页面渲染完成')
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('task-form页面显示')
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('task-form页面隐藏')
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('task-form页面卸载')
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    wx.stopPullDownRefresh()
  },

  // ==================== 数据加载和初始化方法 ====================

  /**
   * 加载现有任务数据（编辑模式）
   */
  async loadTaskData(taskId) {
    console.log('开始加载任务数据，ID:', taskId)

    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      // 使用SmartApi获取任务详情
      const result = await SmartApi.getTaskById(taskId)

      if (result.success && result.data) {
        const task = result.data
        console.log('加载的任务数据:', task)

        // 设置表单数据
        this.setData({
          taskForm: {
            title: task.title || '',
            description: task.description || '',
            examId: task.examId || '',
            examName: task.examName || '',
            subject: task.subject || '',
            priority: task.priority || 'medium',
            dueDate: task.dueDate || '',
            dueTime: task.dueTime || '',
            estimatedDuration: task.estimatedDurationText ||
              (task.estimatedDuration ?
                (typeof task.estimatedDuration === 'string' ? task.estimatedDuration : task.estimatedDuration + '分钟')
                : '30分钟'),
            subtasks: task.subtasks ? task.subtasks.map(item => ({ ...item })) : [],
            reminderEnabled: task.reminderEnabled || false,
            reminderTime: task.reminderTime || ''
          }
        })

        // 设置日期时间选择器的当前值
        if (task.dueDate) {
          // 将日期字符串转换为时间戳，避免时区问题
          const dateObj = new Date(task.dueDate + 'T00:00:00')
          console.log('任务截止日期:', task.dueDate, '转换后的Date对象:', dateObj)
          this.setData({
            currentDate: dateObj.getTime()
          })
        }

        if (task.dueTime) {
          this.setData({
            currentTime: task.dueTime
          })
        }

        // 设置优先级显示标签
        const priorityOption = this.data.priorityOptions.find(option => option.value === (task.priority || 'medium'))
        if (priorityOption) {
          this.setData({
            selectedPriorityLabel: priorityOption.label
          })
        }

        // 设置提醒时间显示标签
        if (task.reminderEnabled && task.reminderTime) {
          const reminderOption = this.data.reminderOptions.find(option => option.value === task.reminderTime)
          if (reminderOption) {
            this.setData({
              reminderTimeText: reminderOption.label
            })
          }
        }

        // 加载考试选项（编辑模式也需要加载，以便用户可以更改关联考试）
        await this.loadExamOptions()

        // 如果任务有关联考试，加载对应的科目选项
        if (task.examId) {
          console.log('加载任务关联考试的科目:', task.examId, task.examName)
          await this.loadSubjectOptions(task.examId)
        }

        // 更新检查点统计
        this.updateCheckpointStats()

        console.log('任务数据加载完成')
      } else {
        throw new Error(result.error || '获取任务数据失败')
      }
    } catch (error) {
      console.error('加载任务数据失败:', error)
      wx.showToast({
        title: '加载任务失败',
        icon: 'error'
      })
      // 返回上一页
      NavigationUtils.safeNavigateBack()
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 初始化新任务（新增模式）
   */
  async initializeNewTask(examId) {
    console.log('初始化新任务，考试ID:', examId)

    try {
      // 加载考试选项
      await this.loadExamOptions()

      // 如果有预设的examId，设置对应的考试信息
      if (examId) {
        const { examOptions } = this.data
        const selectedExam = examOptions.find(exam => exam.id === examId)
        if (selectedExam) {
          console.log('预选考试:', selectedExam)
          this.setData({
            'taskForm.examId': examId,
            'taskForm.examName': selectedExam.name
          })

          // 加载该考试的科目选项
          await this.loadSubjectOptions(examId)
        }
      }

      // 设置默认日期时间
      this.setDefaultDateTime()

      console.log('新任务初始化完成')
    } catch (error) {
      console.error('初始化新任务失败:', error)
      wx.showToast({
        title: '初始化失败',
        icon: 'error'
      })
    }
  },

  /**
   * 加载考试选项
   */
  async loadExamOptions() {
    try {
      const result = await SmartApi.getExams()

      if (result.success && result.data) {
        const examOptions = result.data.map(exam => ({
          id: exam._id,
          name: exam.name || exam.title,
          date: exam.examDate || exam.date
        }))

        console.log('加载的考试选项:', examOptions)
        this.setData({ examOptions })
      } else {
        console.error('加载考试选项失败:', result)
        this.setData({ examOptions: [] })
      }
    } catch (error) {
      console.error('加载考试选项异常:', error)
      this.setData({ examOptions: [] })
    }
  },

  /**
   * 加载科目选项
   */
  async loadSubjectOptions(examId) {
    try {
      const result = await SmartApi.getExamById(examId)

      if (result.success && result.data) {
        const exam = result.data
        let subjectOptions = []

        // 检查不同可能的科目字段
        if (exam.subjects && Array.isArray(exam.subjects)) {
          subjectOptions = exam.subjects
          console.log('使用exam.subjects:', subjectOptions)
        } else if (exam.subjectList && Array.isArray(exam.subjectList)) {
          subjectOptions = exam.subjectList
          console.log('使用exam.subjectList:', subjectOptions)
        } else if (exam.subject) {
          // 检查是否为数组
          if (Array.isArray(exam.subject)) {
            subjectOptions = exam.subject
            console.log('exam.subject是数组，直接使用:', subjectOptions)
          } else if (typeof exam.subject === 'string') {
            // 如果是JSON字符串，尝试解析
            try {
              // 先尝试直接解析
              let parsed = JSON.parse(exam.subject)
              if (Array.isArray(parsed)) {
                subjectOptions = parsed
                console.log('JSON解析成功，使用解析结果:', subjectOptions)
              } else {
                // 如果解析结果不是数组，但是对象，尝试提取值
                if (typeof parsed === 'object' && parsed !== null) {
                  const values = Object.values(parsed)
                  if (values.length > 0) {
                    subjectOptions = values
                    console.log('从对象中提取值:', subjectOptions)
                  } else {
                    subjectOptions = [exam.subject]
                    console.log('对象为空，作为单个科目:', subjectOptions)
                  }
                } else {
                  // 如果是普通字符串，作为单个科目
                  subjectOptions = [exam.subject]
                  console.log('JSON解析结果不是数组或对象，作为单个科目:', subjectOptions)
                }
              }
            } catch (parseError) {
              console.log('第一次JSON解析失败，尝试处理转义字符:', parseError.message)

              // 尝试处理可能的转义字符
              try {
                const unescaped = exam.subject.replace(/\\"/g, '"').replace(/\\\\/g, '\\')
                const parsed = JSON.parse(unescaped)
                if (Array.isArray(parsed)) {
                  subjectOptions = parsed
                  console.log('转义处理后JSON解析成功:', subjectOptions)
                } else {
                  subjectOptions = [exam.subject]
                  console.log('转义处理后仍不是数组，作为单个科目:', subjectOptions)
                }
              } catch (secondParseError) {
                // 最终解析失败，作为普通字符串处理
                subjectOptions = [exam.subject]
                console.log('所有JSON解析尝试失败，作为普通字符串:', subjectOptions)
              }
            }
          } else {
            // 其他类型，作为单个科目
            subjectOptions = [String(exam.subject)]
            console.log('其他类型转换为字符串:', subjectOptions)
          }
        } else {
          // 科目字段为空或不存在，使用默认科目
          subjectOptions = ['通用']
          console.log('科目字段为空，使用默认科目:', subjectOptions)
        }

        console.log('最终科目选项:', subjectOptions)

        // 过滤掉空值，确保科目选项有效
        subjectOptions = subjectOptions.filter(subject => subject && subject.trim() !== '')
        if (subjectOptions.length === 0) {
          subjectOptions = ['通用']
          console.log('过滤后科目为空，使用默认科目:', subjectOptions)
        }

        this.setData({ subjectOptions })
      } else {
        console.error('加载考试详情失败:', result)
        // 提供默认科目选项
        this.setData({
          subjectOptions: ['通用', '专业课', '公共课']
        })
      }
    } catch (error) {
      console.error('加载科目选项异常:', error)
      this.setData({
        subjectOptions: ['通用', '专业课', '公共课']
      })
    }
  },

  /**
   * 设置默认日期时间
   */
  setDefaultDateTime() {
    const now = new Date()
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)

    const defaultDate = tomorrow.toISOString().split('T')[0] // YYYY-MM-DD格式
    const defaultTime = '09:00'

    this.setData({
      'taskForm.dueDate': defaultDate,
      'taskForm.dueTime': defaultTime
    })
  },

  /**
   * 更新检查点统计
   */
  updateCheckpointStats() {
    const { taskForm } = this.data
    const completedCount = taskForm.subtasks.filter(item => item.completed).length
    const allSelected = taskForm.subtasks.length > 0 && completedCount === taskForm.subtasks.length

    this.setData({
      completedCheckpointsCount: completedCount,
      allSelected: allSelected
    })
  },

  // ==================== 表单处理方法 ====================

  /**
   * 更新任务标题
   */
  updateTitle(e) {
    const title = e.detail.value || e.detail
    this.setData({
      'taskForm.title': title,
      titleError: ''
    })
    this.validateForm()
  },

  /**
   * 更新任务描述
   */
  updateDescription(e) {
    const description = e.detail.value || e.detail
    this.setData({
      'taskForm.description': description
    })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { taskForm } = this.data
    let titleError = ''

    if (!taskForm.title || taskForm.title.trim().length === 0) {
      titleError = '复习内容不能为空'
    } else if (taskForm.title.trim().length > 50) {
      titleError = '复习内容不能超过50个字符'
    }

    const canSave = taskForm.title && taskForm.title.trim().length > 0 &&
                   taskForm.dueDate && taskForm.dueTime

    this.setData({
      titleError: titleError,
      canSave: canSave
    })
  },

  /**
   * 选择考试
   */
  selectExam() {
    this.setData({ showExamModal: true })
  },

  hideExamModal() {
    this.setData({ showExamModal: false })
  },

  selectExamOption(e) {
    const exam = e.currentTarget.dataset.exam
    const { taskForm } = this.data

    // 检查是否更换了考试
    const isExamChanged = taskForm.examId !== exam.id

    this.setData({
      'taskForm.examId': exam.id,
      'taskForm.examName': exam.name,
      showExamModal: false
    })

    // 如果更换了考试，清空学科选择并重新加载学科选项
    if (isExamChanged) {
      console.log('考试已更换，清空学科选择并重新加载学科列表')
      this.setData({
        'taskForm.subject': '' // 清空学科选择
      })
    }

    // 加载该考试的科目选项
    this.loadSubjectOptions(exam.id)
  },

  clearExamSelection() {
    console.log('清除考试选择，同时清空学科选择')
    this.setData({
      'taskForm.examId': '',
      'taskForm.examName': '',
      'taskForm.subject': '', // 清空学科选择
      showExamModal: false,
      subjectOptions: [] // 清空学科选项列表
    })
  },

  /**
   * 选择学科
   */
  selectSubject() {
    const { taskForm, subjectOptions } = this.data

    // 检查是否已选择考试
    if (!taskForm.examId) {
      wx.showToast({
        title: '请先选择考试',
        icon: 'none'
      })
      return
    }

    // 检查是否有学科选项
    if (!subjectOptions || subjectOptions.length === 0) {
      wx.showToast({
        title: '该考试暂无学科信息',
        icon: 'none'
      })
      return
    }

    this.setData({ showSubjectModal: true })
  },

  hideSubjectModal() {
    this.setData({ showSubjectModal: false })
  },

  selectSubjectOption(e) {
    const subject = e.currentTarget.dataset.subject
    this.setData({
      'taskForm.subject': subject,
      showSubjectModal: false
    })
  },

  /**
   * 选择优先级
   */
  selectPriority() {
    this.setData({ showPriorityModal: true })
  },

  hidePriorityModal() {
    this.setData({ showPriorityModal: false })
  },

  selectPriorityOption(e) {
    const priority = e.currentTarget.dataset.priority
    this.setData({
      'taskForm.priority': priority.value,
      selectedPriorityLabel: priority.label,
      showPriorityModal: false
    })
  },

  /**
   * 日期时间选择
   */
  showDatePicker() {
    // 如果任务已有截止日期，使用任务的日期；否则使用今天
    const { taskForm } = this.data
    let currentDate = new Date().getTime() // 默认今天

    if (taskForm.dueDate) {
      // 避免时区问题，在日期字符串后添加时间部分
      const dateObj = new Date(taskForm.dueDate + 'T00:00:00')
      // 确保日期不早于今天
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      if (dateObj >= today) {
        currentDate = dateObj.getTime()
      }
      console.log('打开日期选择器，任务日期:', taskForm.dueDate, '转换后:', dateObj, '时间戳:', currentDate)
    }

    this.setData({
      showDatePicker: true,
      currentDate: currentDate
    })
  },

  hideDatePicker() {
    this.setData({ showDatePicker: false })
  },

  confirmDate(e) {
    const date = new Date(e.detail)
    // 使用本地时区格式化日期，避免时区转换问题
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const dateStr = `${year}-${month}-${day}`

    console.log('选择的日期:', date, '格式化后:', dateStr)

    this.setData({
      'taskForm.dueDate': dateStr,
      currentDate: e.detail,
      showDatePicker: false
    })
    this.validateForm()
  },

  showTimePicker() {
    // 如果任务已有截止时间，使用任务的时间；否则使用默认时间
    const { taskForm } = this.data
    let currentTime = '09:00' // 默认时间

    if (taskForm.dueTime) {
      currentTime = taskForm.dueTime
    }

    this.setData({
      showTimePicker: true,
      currentTime: currentTime
    })
  },

  hideTimePicker() {
    this.setData({ showTimePicker: false })
  },

  confirmTime(e) {
    this.setData({
      'taskForm.dueTime': e.detail,
      currentTime: e.detail,
      showTimePicker: false
    })
    this.validateForm()
  },

  /**
   * 预计时长选择
   */
  selectDuration() {
    this.setData({ showDurationPicker: true })
  },

  hideDurationPicker() {
    this.setData({ showDurationPicker: false })
  },

  selectDurationOption(e) {
    const duration = e.currentTarget.dataset.duration
    this.setData({
      'taskForm.estimatedDuration': duration,
      showDurationPicker: false
    })
  },

  /**
   * 提醒设置
   */
  toggleReminder(e) {
    const enabled = e.detail
    this.setData({
      'taskForm.reminderEnabled': enabled
    })

    if (!enabled) {
      this.setData({
        'taskForm.reminderTime': '',
        reminderTimeText: '提前30分钟'
      })
    }
  },

  selectReminderTime() {
    this.setData({ showReminderPicker: true })
  },

  hideReminderPicker() {
    this.setData({ showReminderPicker: false })
  },

  selectReminderOption(e) {
    const reminder = e.currentTarget.dataset.reminder
    this.setData({
      'taskForm.reminderTime': reminder.value,
      reminderTimeText: reminder.label,
      showReminderPicker: false
    })
  },

  /**
   * 提交和取消
   */
  async submitTask() {
    // 验证表单
    this.validateForm()

    if (!this.data.canSave) {
      wx.showToast({
        title: '请完善必填信息',
        icon: 'none'
      })
      return
    }

    this.setData({ isSubmitting: true })

    // 显示加载状态
    wx.showLoading({
      title: this.data.isEditMode ? '保存中...' : '创建中...',
      mask: true
    })

    try {
      const { taskForm, isEditMode, taskId } = this.data

      // 过滤空的检查点
      const filteredSubtasks = taskForm.subtasks.filter(item =>
        item.title && item.title.trim()
      )

      // 准备提交数据
      const submitData = {
        title: taskForm.title.trim(),
        description: taskForm.description.trim(),
        examId: taskForm.examId,
        examName: taskForm.examName,
        subject: taskForm.subject,
        priority: taskForm.priority,
        dueDate: taskForm.dueDate,
        dueTime: taskForm.dueTime,
        estimatedDuration: this.convertDurationToMinutes(taskForm.estimatedDuration), // 转换为分钟数
        estimatedDurationText: taskForm.estimatedDuration, // 保留原始文本用于显示
        subtasks: filteredSubtasks,
        completedSubtasks: filteredSubtasks.filter(item => item.completed).length, // 已完成的检查点数量
        totalSubtasks: filteredSubtasks.length, // 总检查点数量
        reminderEnabled: taskForm.reminderEnabled,
        reminderTime: taskForm.reminderTime
      }

      // 新增任务时添加额外字段
      if (!isEditMode) {
        submitData.completed = false
        submitData.createTime = new Date().toISOString()
        submitData.status = 'pending'
      }

      console.log('提交任务数据:', submitData)
      console.log('预计时长转换:', taskForm.estimatedDuration, '->', submitData.estimatedDuration, '分钟')
      console.log('任务模式:', isEditMode ? '编辑' : '新增', '任务ID:', taskId)

      let result
      if (isEditMode) {
        // 编辑模式：更新任务
        console.log('调用SmartApi.updateTask，参数:', taskId, submitData)
        result = await SmartApi.updateTask(taskId, submitData)
        console.log('SmartApi.updateTask返回结果:', result)
      } else {
        // 新增模式：创建任务
        console.log('调用SmartApi.addTask，参数:', submitData)
        result = await SmartApi.addTask(submitData)
        console.log('SmartApi.addTask返回结果:', result)
      }

      wx.hideLoading()

      if (result.success) {
        // 新增任务成功后处理通知订阅
        if (!isEditMode) {
          try {
            const NotificationApi = require('../../utils/notificationApi')
            await NotificationApi.onTaskCreated(submitData)
          } catch (notificationError) {
            console.error('通知订阅处理失败:', notificationError)
            // 不影响主流程，继续执行
          }
        }

        this.setData({
          isSubmitting: false
        })

        wx.showToast({
          title: isEditMode ? '任务更新成功' : '复习计划创建成功',
          icon: 'success'
        })

        // 延迟返回，让用户看到成功提示
        setTimeout(() => {
          NavigationUtils.safeNavigateBack()
        }, 1500)
      } else {
        this.setData({
          isSubmitting: false
        })

        wx.showToast({
          title: result.error || '保存失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      this.setData({
        isSubmitting: false
      })

      console.error('提交任务失败:', error)
      wx.showToast({
        title: error.message || '保存失败，请重试',
        icon: 'none'
      })
    }
  },

  cancelTask() {
    NavigationUtils.safeNavigateBack()
  },

  /**
   * 将预计时长转换为分钟数
   */
  convertDurationToMinutes(duration) {
    if (!duration) return 0

    // 匹配各种时长格式
    const patterns = [
      { regex: /^(\d+)分钟$/, multiplier: 1 },
      { regex: /^(\d+)小时$/, multiplier: 60 },
      { regex: /^(\d+)\.5小时$/, multiplier: 90 }, // 1.5小时 = 90分钟
      { regex: /^(\d+)小时(\d+)分钟$/, handler: (match) => parseInt(match[1]) * 60 + parseInt(match[2]) },
      { regex: /^半天$/, multiplier: 240 }, // 半天 = 4小时 = 240分钟
      { regex: /^(\d+)天$/, multiplier: 480 } // 1天 = 8小时 = 480分钟
    ]

    for (const pattern of patterns) {
      const match = duration.match(pattern.regex)
      if (match) {
        if (pattern.handler) {
          return pattern.handler(match)
        } else {
          return parseInt(match[1]) * pattern.multiplier
        }
      }
    }

    // 如果无法识别格式，返回30分钟作为默认值
    console.warn('无法识别的时长格式:', duration)
    return 30
  },

  // ==================== 检查点管理方法 ====================

  // ==================== 拖拽排序方法 ====================

  /**
   * 开始拖拽
   */
  startDrag(e) {
    const index = parseInt(e.currentTarget.dataset.index)
    console.log('开始拖拽检查点:', index)

    this.setData({
      dragIndex: index,
      isDragging: true
    })
  },

  /**
   * 触摸开始
   */
  onTouchStart(e) {
    if (!this.data.isDragging) return

    this.setData({
      startY: e.touches[0].clientY,
      currentY: e.touches[0].clientY
    })
  },

  /**
   * 触摸移动
   */
  onTouchMove(e) {
    if (!this.data.isDragging) return

    const currentY = e.touches[0].clientY
    const deltaY = currentY - this.data.startY

    this.setData({
      currentY: currentY
    })

    // 计算目标位置
    const itemHeight = 80 // 估算每个检查点项目的高度
    const moveIndex = Math.floor(deltaY / itemHeight)
    const targetIndex = Math.max(0, Math.min(this.data.taskForm.subtasks.length - 1, this.data.dragIndex + moveIndex))

    // 如果位置发生变化，执行排序
    if (targetIndex !== this.data.dragIndex && Math.abs(deltaY) > itemHeight / 2) {
      this.moveCheckpointToIndex(this.data.dragIndex, targetIndex)
      this.setData({
        dragIndex: targetIndex,
        startY: currentY
      })
    }
  },

  /**
   * 触摸结束
   */
  onTouchEnd(e) {
    if (!this.data.isDragging) return

    console.log('拖拽结束')
    this.setData({
      dragIndex: -1,
      isDragging: false,
      startY: 0,
      currentY: 0
    })
  },

  /**
   * 移动检查点到指定位置
   */
  moveCheckpointToIndex(fromIndex, toIndex) {
    if (fromIndex === toIndex) return

    const taskForm = { ...this.data.taskForm }
    const subtasks = [...taskForm.subtasks]

    // 移动元素
    const [movedItem] = subtasks.splice(fromIndex, 1)
    subtasks.splice(toIndex, 0, movedItem)

    taskForm.subtasks = subtasks

    this.setData({
      taskForm: taskForm
    })

    console.log(`检查点从位置 ${fromIndex} 移动到位置 ${toIndex}`)
  },

  /**
   * 切换检查点编辑区域显示状态
   */
  toggleCheckpointSection() {
    const showCheckpointSection = !this.data.showCheckpointSection
    this.setData({
      showCheckpointSection
    })
  },

  /**
   * 显示添加检查点输入框
   */
  showAddCheckpointInput() {
    this.setData({
      showAddCheckpointInput: true,
      newCheckpointTitle: ''
    })
  },

  /**
   * 取消添加检查点
   */
  cancelAddCheckpoint() {
    this.setData({
      showAddCheckpointInput: false,
      newCheckpointTitle: ''
    })
  },

  /**
   * 保存新检查点
   */
  saveNewCheckpoint() {
    this.addCheckpoint()
    this.setData({
      showAddCheckpointInput: false
    })
  },

  /**
   * 更新新检查点标题
   */
  updateNewCheckpoint(e) {
    this.setData({
      newCheckpointTitle: e.detail.value || e.detail
    })
  },

  /**
   * 添加检查点
   */
  addCheckpoint() {
    const { newCheckpointTitle, taskForm } = this.data
    const title = newCheckpointTitle.trim()

    if (!title) {
      wx.showToast({
        title: '请输入检查点内容',
        icon: 'none'
      })
      return
    }

    // 检查是否重复
    const isDuplicate = taskForm.subtasks.some(item => item.title === title)
    if (isDuplicate) {
      wx.showToast({
        title: '检查点已存在',
        icon: 'none'
      })
      return
    }

    // 添加新检查点
    const newSubtask = {
      id: Date.now().toString(),
      title: title,
      completed: false
    }

    taskForm.subtasks.push(newSubtask)
    this.setData({
      taskForm: taskForm,
      newCheckpointTitle: ''
    })

    // 更新统计
    this.updateCheckpointStats()

    wx.showToast({
      title: '检查点已添加',
      icon: 'success'
    })
  },

  /**
   * 更新检查点内容
   */
  updateCheckpoint(e) {
    const index = parseInt(e.currentTarget.dataset.index)
    const newTitle = e.detail.value || e.detail

    const taskForm = { ...this.data.taskForm }
    if (taskForm.subtasks[index]) {
      taskForm.subtasks[index].title = newTitle
      this.setData({
        taskForm: taskForm
      })
    }
  },

  /**
   * 切换检查点完成状态
   */
  toggleCheckpointStatus(e) {
    const index = parseInt(e.currentTarget.dataset.index)
    const taskForm = { ...this.data.taskForm }

    if (taskForm.subtasks[index]) {
      taskForm.subtasks[index].completed = !taskForm.subtasks[index].completed

      this.setData({
        taskForm: taskForm
      })

      // 更新统计
      this.updateCheckpointStats()
    }
  },

  /**
   * 删除检查点
   */
  removeCheckpoint(e) {
    const index = e.currentTarget.dataset.index
    const { taskForm } = this.data
    const updatedSubtasks = [...taskForm.subtasks]
    updatedSubtasks.splice(index, 1)

    this.setData({
      taskForm: {
        ...taskForm,
        subtasks: updatedSubtasks
      }
    })

    // 更新统计
    this.updateCheckpointStats()

    wx.showToast({
      title: '检查点已删除',
      icon: 'success'
    })
  },


})
