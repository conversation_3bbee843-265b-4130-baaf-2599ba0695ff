<!--pages/data-center/index.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <van-cell-group custom-class="header-container">
    <van-cell 
      title="数据中心" 
      value="了解你的复习效果"
      title-class="page-title"
      value-class="page-subtitle"
      border="{{false}}"
      />
  </van-cell-group>

  <!-- 考试准备度 -->
  <van-card 
    custom-class="preparation-score-container"
    title="考试准备度"
    >
    <view slot="tags">
      <van-button 
        type="default" 
        size="mini"
        icon="info-o"
        bind:click="showScoreInfo"
        custom-class="score-info-btn">
      </van-button>
    </view>
    
    <view slot="desc" class="score-display">
      <van-circle
        value="{{preparationScore}}"
        size="160"
        layer-color="#F0F0F0"
        color="#52C41A"
        stroke-width="8"
        text="{{preparationScore}}分"
        custom-class="score-circle"
        />
      <view class="score-details">
        <text class="score-level">{{preparationLevel}}</text>
        <text class="score-description">{{preparationDescription}}</text>
      </view>
    </view>

    <view slot="footer" class="score-factors">
      <view class="factor-item" wx:for="{{scoreFactors}}" wx:key="name">
        <text class="factor-name">{{item.name}}</text>
        <view class="factor-progress">
          <van-progress 
            percentage="{{item.score}}" 
            color="{{item.color}}"
            track-color="#F0F0F0"
            stroke-width="8"
            show-pivot="{{false}}"
            custom-class="factor-bar"
            />
          <text class="factor-score">{{item.score}}</text>
        </view>
      </view>
    </view>
  </van-card>

  <!-- 复习统计 -->
  <van-card 
    title="复习统计"
    custom-class="study-stats-container"
    >
    <view slot="tags">
      <van-tabs 
        active="{{currentTimeRange}}" 
        bind:change="switchTimeRange"
        type="card"
        custom-class="time-range-selector"
        >
        <van-tab 
          wx:for="{{timeRangeOptions}}" 
          wx:key="value"
          title="{{item.label}}" 
          name="{{item.value}}"
          >
        </van-tab>
      </van-tabs>
    </view>
    
    <view slot="desc">
      <van-grid 
        column-num="2" 
        border="{{false}}"
        gutter="16"
        custom-class="stats-grid"
        >
        <van-grid-item 
          wx:for="{{studyStats}}" 
          wx:key="label"
          custom-class="stat-card"
          >
          <view class="stat-content">
            <view class="stat-icon" style="background-color: {{item.bgColor}}">
              <text>{{item.icon}}</text>
            </view>
            <text class="stat-value">{{item.value}}</text>
            <text class="stat-label">{{item.label}}</text>
            <view class="stat-trend" wx:if="{{item.trend}}">
              <van-tag 
                type="success" 
                size="mini"
                custom-class="trend-tag"
                >
                {{item.trend.icon}} {{item.trend.text}}
              </van-tag>
            </view>
          </view>
        </van-grid-item>
      </van-grid>
    </view>
  </van-card>

  <!-- 复习效率分析 -->
  <van-card 
    title="复习效率"
    custom-class="efficiency-container"
    >
    <view slot="tags">
      <van-button 
        type="default" 
        size="mini"
        plain
        bind:click="viewEfficiencyDetail"
        >
        详细分析
      </van-button>
    </view>
    
    <view slot="desc">
      <view class="efficiency-chart">
        <van-cell-group custom-class="chart-header">
          <van-cell 
            title="每日效率趋势" 
            value="最近7天"
            border="{{false}}"
            />
        </van-cell-group>
        
        <view class="chart-content">
          <view class="chart-bars">
            <view class="chart-bar" wx:for="{{efficiencyData}}" wx:key="date">
              <view class="bar-fill" style="height: {{item.efficiency}}%; background-color: {{item.efficiency >= 80 ? '#52C41A' : item.efficiency >= 60 ? '#FA8C16' : '#FF4D4F'}}"></view>
              <text class="bar-label">{{item.day}}</text>
              <text class="bar-value">{{item.efficiency}}%</text>
            </view>
          </view>
        </view>
      </view>

      <van-divider content-position="left">效率洞察</van-divider>
      <van-cell-group border="{{false}}">
        <van-cell 
          wx:for="{{efficiencyInsights}}" 
          wx:key="id"
          title="{{item.text}}"
          border="{{false}}"
          >
          <view slot="icon" class="insight-icon">{{item.icon}}</view>
        </van-cell>
      </van-cell-group>
    </view>
  </van-card>

  <!-- 科目分析 -->
  <van-card 
    title="科目分析"
    custom-class="subjects-analysis-container"
    >
    <view slot="tags">
      <van-button 
        type="default" 
        size="mini"
        plain
        bind:click="viewSubjectsDetail"
        >
        查看详情
      </van-button>
    </view>
    
    <view slot="desc">
      <van-cell-group border="{{false}}">
        <van-cell 
          wx:for="{{subjectsData}}" 
          wx:key="name"
          title="{{item.name}}"
          label="{{item.exam}}"
          border="{{false}}"
          custom-class="subject-item"
          >
          <view slot="right-icon" class="subject-score">
            <text class="score-number">{{item.score}}</text>
            <text class="score-max">/100</text>
          </view>
          
          <view slot="extra" class="subject-details">
            <view class="subject-progress">
              <van-progress 
                percentage="{{item.progress}}" 
                color="{{item.progressColor}}"
                track-color="#F0F0F0"
                stroke-width="6"
                show-pivot="{{false}}"
                custom-class="progress-bar"
                />
              <text class="progress-text">复习进度 {{item.progress}}%</text>
            </view>

            <van-grid 
              column-num="3" 
              border="{{false}}"
              gutter="8"
              custom-class="subject-stats"
              >
              <van-grid-item>
                <view class="subject-stat">
                  <text class="stat-label">复习时长</text>
                  <text class="stat-value">{{item.studyTime}}</text>
                </view>
              </van-grid-item>
              <van-grid-item>
                <view class="subject-stat">
                  <text class="stat-label">完成复习</text>
                  <text class="stat-value">{{item.completedTasks}}</text>
                </view>
              </van-grid-item>
              <van-grid-item>
                <view class="subject-stat">
                  <text class="stat-label">平均效率</text>
                  <text class="stat-value">{{item.avgEfficiency}}%</text>
                </view>
              </van-grid-item>
            </van-grid>
          </view>
        </van-cell>
      </van-cell-group>
    </view>
  </van-card>

  <!-- 备考习惯 -->
  <van-card 
    title="备考习惯"
    custom-class="habits-container"
    >
    <view slot="desc">
      <van-grid 
        column-num="2" 
        border="{{false}}"
        gutter="16"
        custom-class="habits-grid"
        >
        <van-grid-item 
          wx:for="{{studyHabits}}" 
          wx:key="title"
          custom-class="habit-card"
          >
          <view class="habit-content">
            <view class="habit-icon">
              <text>{{item.icon}}</text>
            </view>
            <text class="habit-title">{{item.title}}</text>
            <text class="habit-value">{{item.value}}</text>
            <text class="habit-description">{{item.description}}</text>
          </view>
        </van-grid-item>
      </van-grid>
    </view>
  </van-card>

  <!-- 成就展示 -->
  <van-card 
    title="最近成就"
    custom-class="achievements-container"
    >
    <view slot="tags">
      <van-button 
        type="default" 
        size="mini"
        plain
        bind:click="viewAllAchievements"
        >
        查看全部
      </van-button>
    </view>
    
    <view slot="desc">
      <van-cell-group border="{{false}}">
        <van-cell 
          wx:for="{{recentAchievements}}" 
          wx:key="id"
          title="{{item.name}}"
          label="{{item.description}}"
          value="{{item.time}}"
          border="{{false}}"
          custom-class="achievement-item"
          >
          <view slot="icon" class="achievement-icon">{{item.icon}}</view>
          <view slot="right-icon" wx:if="{{item.isNew}}">
            <van-tag type="danger" size="mini">NEW</van-tag>
          </view>
        </van-cell>
      </van-cell-group>
    </view>
  </van-card>

  <!-- 数据导出 -->
  <van-card 
    title="数据导出"
    desc="导出你的备考数据，便于分析和备份"
    custom-class="export-container"
    >
    <view slot="footer">
      <van-button 
        type="primary" 
        size="large"
        icon="description"
        bind:click="exportData"
        custom-class="export-btn"
        >
        导出数据
      </van-button>
    </view>
  </van-card>
</view>

<!-- 准备度说明弹窗 -->
<van-popup 
  show="{{showScoreInfo}}" 
  bind:close="hideScoreInfo"
  closeable
  position="center"
  custom-style="width: 80%; max-width: 600rpx; border-radius: 16rpx;"
  >
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">考试准备度说明</text>
    </view>
    <view class="modal-body">
      <text class="modal-text">考试准备度是基于以下因素综合计算的智能评分：</text>
      <van-cell-group border="{{false}}">
        <van-cell 
          wx:for="{{scoreFactorExplanations}}" 
          wx:key="name"
          title="{{item.name}}"
          label="{{item.description}}"
          border="{{false}}"
          />
      </van-cell-group>
    </view>
  </view>
</van-popup>