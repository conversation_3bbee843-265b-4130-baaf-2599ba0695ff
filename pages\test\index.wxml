<!--pages/test/index.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <text class="page-title">🧪 考试模型测试</text>
    <text class="page-subtitle">验证考试模型和API的功能</text>
  </view>

  <!-- 测试控制区 -->
  <view class="test-controls">
    <button class="test-btn primary {{isRunning ? 'disabled' : ''}}" 
            bindtap="runAllTests" 
            disabled="{{isRunning}}">
      {{isRunning ? '测试运行中...' : '🚀 运行完整测试'}}
    </button>
    
    <view class="quick-actions">
      <button class="test-btn secondary" bindtap="runQuickTest" disabled="{{isRunning}}">
        ⚡ 快速测试
      </button>
      <button class="test-btn secondary" bindtap="testDataCreation">
        📝 测试数据创建
      </button>
      <button class="test-btn secondary" bindtap="testDataValidation">
        ✅ 测试数据验证
      </button>
    </view>
  </view>

  <!-- 测试进度 -->
  <view class="test-progress" wx:if="{{isRunning}}">
    <view class="progress-info">
      <text class="progress-text">{{currentTest}}</text>
      <text class="progress-percent">{{progress}}%</text>
    </view>
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{progress}}%"></view>
    </view>
  </view>

  <!-- 测试结果摘要 -->
  <view class="test-summary" wx:if="{{testResults}}">
    <view class="summary-header">
      <text class="summary-title">📊 测试结果</text>
      <text class="summary-time">{{testResults.timestamp}}</text>
    </view>
    
    <view class="summary-stats">
      <view class="stat-item">
        <text class="stat-number">{{testResults.total}}</text>
        <text class="stat-label">总测试数</text>
      </view>
      <view class="stat-item success">
        <text class="stat-number">{{testResults.passed}}</text>
        <text class="stat-label">通过</text>
      </view>
      <view class="stat-item {{testResults.failed > 0 ? 'failed' : 'success'}}">
        <text class="stat-number">{{testResults.failed}}</text>
        <text class="stat-label">失败</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{testResults.successRate}}%</text>
        <text class="stat-label">成功率</text>
      </view>
    </view>

    <view class="summary-actions">
      <button class="action-btn" bindtap="toggleDetails">
        {{showDetails ? '隐藏详情' : '查看详情'}}
      </button>
      <button class="action-btn" bindtap="exportReport">
        📤 导出报告
      </button>
      <button class="action-btn danger" bindtap="clearResults">
        🗑️ 清除结果
      </button>
    </view>
  </view>

  <!-- 详细测试结果 -->
  <view class="test-details" wx:if="{{testResults && showDetails}}">
    <view class="details-header">
      <text class="details-title">📋 详细结果</text>
    </view>
    
    <view class="test-list">
      <view class="test-item {{item.status}}" 
            wx:for="{{testResults.results}}" 
            wx:key="name"
            bindtap="viewTestDetail"
            data-index="{{index}}">
        <view class="test-info">
          <text class="test-name">{{item.name}}</text>
          <text class="test-message">{{item.message}}</text>
        </view>
        <view class="test-status">
          <text class="status-icon">{{item.status === 'passed' ? '✅' : '❌'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 测试说明 -->
  <view class="test-info-section">
    <view class="info-header">
      <text class="info-title">ℹ️ 测试说明</text>
    </view>
    
    <view class="info-content">
      <view class="info-item">
        <text class="info-label">完整测试</text>
        <text class="info-desc">运行所有测试用例，包括ExamModel、SmartApi和集成测试</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">快速测试</text>
        <text class="info-desc">快速验证核心功能是否正常工作</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">数据创建测试</text>
        <text class="info-desc">测试默认数据创建功能</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">数据验证测试</text>
        <text class="info-desc">测试数据验证功能</text>
      </view>
    </view>
  </view>

  <!-- 测试覆盖范围 -->
  <view class="test-coverage">
    <view class="coverage-header">
      <text class="coverage-title">🎯 测试覆盖范围</text>
    </view>
    
    <view class="coverage-list">
      <view class="coverage-item">
        <text class="coverage-icon">📝</text>
        <view class="coverage-info">
          <text class="coverage-name">ExamModel 构造函数</text>
          <text class="coverage-desc">验证考试模型的创建和初始化</text>
        </view>
      </view>
      
      <view class="coverage-item">
        <text class="coverage-icon">🔍</text>
        <view class="coverage-info">
          <text class="coverage-name">数据验证</text>
          <text class="coverage-desc">验证必填字段、格式检查、枚举值验证</text>
        </view>
      </view>
      
      <view class="coverage-item">
        <text class="coverage-icon">⚙️</text>
        <view class="coverage-info">
          <text class="coverage-name">静态方法</text>
          <text class="coverage-desc">验证getDefaultData、validate、formatList等方法</text>
        </view>
      </view>
      
      <view class="coverage-item">
        <text class="coverage-icon">🔧</text>
        <view class="coverage-info">
          <text class="coverage-name">实例方法</text>
          <text class="coverage-desc">验证update、toJSON、clone等方法</text>
        </view>
      </view>
      
      <view class="coverage-item">
        <text class="coverage-icon">🔌</text>
        <view class="coverage-info">
          <text class="coverage-name">SmartApi 接口</text>
          <text class="coverage-desc">验证API接口的数据处理和验证</text>
        </view>
      </view>
      
      <view class="coverage-item">
        <text class="coverage-icon">🔗</text>
        <view class="coverage-info">
          <text class="coverage-name">集成测试</text>
          <text class="coverage-desc">验证ExamModel与SmartApi的集成</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态提示 -->
  <view class="empty-state" wx:if="{{!testResults && !isRunning}}">
    <text class="empty-icon">🧪</text>
    <text class="empty-title">准备运行测试</text>
    <text class="empty-desc">点击上方按钮开始测试考试模型功能</text>
  </view>
</view>
