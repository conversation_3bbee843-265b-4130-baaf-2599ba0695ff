// pages/personal-info/index.js
const LoginApi = require('../../utils/loginApi')
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    userInfo: {},
    originalUserInfo: {},
    avatarFileList: [],
    hasChanges: false,
    showSignatureModal: false,
    activeSignatureTab: 0,
    signatureCategories: [
      {
        name: '激励奋斗',
        signatures: [
          '今日事，今日毕！',
          '每一次努力都是成功的积累',
          '越努力，越幸运',
          '梦想不会辜负每一个努力的人',
          '坚持就是胜利',
          '不怕慢，就怕停',
          '天道酬勤，厚德载物',
          '拼搏到无能为力，努力到感动自己'
        ]
      },
      {
        name: '目标导向',
        signatures: [
          '一战上岸，绝不二战',
          '目标明确，全力以赴',
          '为了更好的自己而努力',
          '考研路上，永不言弃',
          '心中有目标，脚下有力量',
          '向着光明，勇敢前行',
          '未来可期，当下可为',
          '志存高远，脚踏实地'
        ]
      },
      {
        name: '时间管理',
        signatures: [
          '专注当下，把握每分每秒',
          '今天的努力，明天的收获',
          '时间就是金钱，效率就是生命',
          '合理规划，高效执行',
          '珍惜时间，就是珍惜生命',
          '一寸光阴一寸金，寸金难买寸光阴',
          '时间不等人，机会不等人',
          '把握今天，成就明天'
        ]
      },
      {
        name: '心态调节',
        signatures: [
          '保持平常心，发挥最佳状态',
          '相信自己，一定可以',
          '压力是动力的源泉',
          '心若向阳，无畏悲伤',
          '淡定从容，方能致远',
          '困难是成长的阶梯',
          '每一次挫折都是成长的机会',
          '保持微笑，拥抱挑战'
        ]
      }
    ]
  },

  onLoad(options) {
    this.loadUserInfo()
  },

  onShow() {
    // 页面显示时的处理
  },

  // 页面卸载时的处理
  onUnload() {
    // 如果有未保存的修改，可以在这里提示用户
    if (this.data.hasChanges) {
      // 注意：onUnload中无法使用wx.showModal，只能在onBackPress中处理
      console.log('页面卸载，有未保存的修改')
    }
  },

  // 处理页面返回
  onBackPress() {
    if (this.data.hasChanges) {
      wx.showModal({
        title: '确认离开',
        content: '您有未保存的修改，确定要离开吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack()
          }
        }
      })
      return true // 阻止默认返回行为
    }
    return false // 允许默认返回行为
  },

  // 加载用户信息
  loadUserInfo() {
    const app = getApp()
    let userInfo = app.globalData.userInfo || wx.getStorageSync('kaoba_user_info') || {}

    // 确保所有字段都有默认值
    userInfo = {
      nickName: userInfo.nickName || '考试达人',
      avatarUrl: userInfo.avatarUrl || '',
      signature: userInfo.signature || '努力备考，金榜题名！',
      ...userInfo
    }

    // 初始化头像文件列表
    const avatarFileList = []
    if (userInfo.avatarUrl && userInfo.avatarUrl !== '/images/default-avatar.png') {
      avatarFileList.push({
        url: userInfo.avatarUrl,
        name: 'avatar',
        isImage: true
      })
    }

    this.setData({
      userInfo: userInfo,
      originalUserInfo: JSON.parse(JSON.stringify(userInfo)), // 深拷贝
      avatarFileList: avatarFileList
    })
  },

  // 选择头像
  chooseAvatar() {
    const that = this
    
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        const tempFilePath = res.tempFilePaths[0]
        
        // 检查文件大小
        wx.getFileInfo({
          filePath: tempFilePath,
          success: async (fileInfo) => {
            if (fileInfo.size > 5 * 1024 * 1024) {
              wx.showToast({
                title: '头像文件不能超过5MB',
                icon: 'none'
              })
              return
            }
            
            try {
              // 显示上传进度
              wx.showLoading({
                title: '上传中...',
                mask: true
              })
              
              // 使用SmartApi上传头像到云存储
              const result = await SmartApi.uploadAvatar(tempFilePath)
              
              wx.hideLoading()
              
              if (result.success) {
                // 更新头像URL
                that.setData({
                  'userInfo.avatarUrl': result.data.avatarUrl,
                  avatarFileList: [{
                    url: result.data.avatarUrl,
                    name: 'avatar',
                    isImage: true
                  }],
                  hasChanges: true
                })
                
                // 立即保存头像到数据库
                wx.showLoading({
                  title: '保存中...',
                  mask: true
                })
                
                try {
                  const saveResult = await LoginApi.updateUserProfile({
                    avatarUrl: result.data.avatarUrl
                  })
                  
                  if (saveResult.success) {
                    // 更新全局数据
                    const app = getApp()
                    if (app.globalData.userInfo) {
                      app.globalData.userInfo.avatarUrl = result.data.avatarUrl
                      wx.setStorageSync('kaoba_user_info', app.globalData.userInfo)
                    }
                    
                    wx.hideLoading()
                    wx.showToast({
                      title: '头像保存成功',
                      icon: 'success'
                    })
                    
                    // 重置hasChanges，因为已经保存了
                    that.setData({
                      hasChanges: false,
                      originalUserInfo: JSON.parse(JSON.stringify(that.data.userInfo))
                    })
                  } else {
                    throw new Error(saveResult.error || '保存失败')
                  }
                } catch (saveError) {
                  console.error('保存头像失败:', saveError)
                  wx.hideLoading()
                  wx.showToast({
                    title: '头像上传成功，但保存失败',
                    icon: 'none',
                    duration: 2000
                  })
                }
              } else {
                console.error('头像上传失败:', result.error)
                wx.showToast({
                  title: result.error || '上传失败',
                  icon: 'none'
                })
              }
            } catch (error) {
              console.error('头像上传异常:', error)
              wx.hideLoading()
              wx.showToast({
                title: '上传失败，请重试',
                icon: 'error'
              })
            }
          }
        })
      }
    })
  },

  // 更新昵称
  updateNickname(e) {
    const nickName = e.detail.value || e.detail
    this.setData({
      'userInfo.nickName': nickName,
      hasChanges: true
    })
  },

  // 更新个性签名
  updateSignature(e) {
    const signature = e.detail.value || e.detail
    this.setData({
      'userInfo.signature': signature,
      hasChanges: true
    })
  },

  // 显示预设签名选择
  showSignatureSelector() {
    this.setData({
      showSignatureModal: true
    })
  },

  // 隐藏预设签名选择
  hideSignatureSelector() {
    this.setData({
      showSignatureModal: false
    })
  },

  // 切换签名分类标签
  onSignatureTabChange(e) {
    this.setData({
      activeSignatureTab: e.detail.index
    })
  },

  // 选择预设签名
  selectPresetSignature(e) {
    const signature = e.currentTarget.dataset.signature
    this.setData({
      'userInfo.signature': signature,
      hasChanges: true,
      showSignatureModal: false
    })

    wx.showToast({
      title: '签名已选择',
      icon: 'success',
      duration: 1000
    })
  },

  // 保存用户信息
  async saveUserInfo() {
    const { userInfo } = this.data

    // 验证必填字段
    if (!userInfo.nickName || !userInfo.nickName.trim()) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    try {
      wx.showLoading({
        title: '保存中...'
      })

      // 调试信息：打印要保存的用户信息
      console.log('个人信息页面：准备保存的用户信息:', JSON.stringify(userInfo, null, 2))
      console.log('个人信息页面：用户签名值:', userInfo.signature)

      // 调用API更新用户资料
      const result = await LoginApi.updateUserProfile(userInfo)

      console.log('个人信息页面：更新用户资料结果:', result)

      if (result.success) {
        // 更新全局数据
        const app = getApp()
        app.globalData.userInfo = userInfo

        // 保存到本地存储
        wx.setStorageSync('kaoba_user_info', userInfo)

        // 更新原始数据，标记无变化
        this.setData({
          originalUserInfo: JSON.parse(JSON.stringify(userInfo)),
          hasChanges: false
        })

        wx.hideLoading()
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        throw new Error(result.error || '保存失败')
      }
    } catch (error) {
      console.error('保存用户信息失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
  },



  // 重置信息
  resetInfo() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有修改吗？',
      success: (res) => {
        if (res.confirm) {
          const originalUserInfo = this.data.originalUserInfo
          
          // 重置头像文件列表
          const avatarFileList = []
          if (originalUserInfo.avatarUrl && originalUserInfo.avatarUrl !== '/images/default-avatar.png') {
            avatarFileList.push({
              url: originalUserInfo.avatarUrl,
              name: 'avatar',
              isImage: true
            })
          }

          this.setData({
            userInfo: JSON.parse(JSON.stringify(originalUserInfo)),
            avatarFileList: avatarFileList,
            hasChanges: false
          })

          wx.showToast({
            title: '已重置',
            icon: 'success'
          })
        }
      }
    })
  }
}) 