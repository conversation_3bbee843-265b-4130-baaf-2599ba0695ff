/* pages/achievement-system/index.wxss */

/* 页面容器 */
.container {
  background-color: #f7f8fa;
  padding: 16rpx;
  min-height: 100vh;
}

/* 卡片容器 */
.overview-container,
.recent-container,
.categories-container,
.stats-container {
  margin-bottom: 24rpx;
}

/* 成就概览 */
.overview-stats {
  margin: 16rpx 0;
}

.overview-stat {
  text-align: center;
  padding: 8rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4rpx;
  display: block;
}

.stat-label {
  font-size: 20rpx;
  color: #ffffff;
  opacity: 0.8;
  display: block;
}

.progress-ring {
  display: flex;
  justify-content: center;
  margin-top: 24rpx;
}

.completion-circle {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 最近解锁 */
.recent-achievement {
  padding: 16rpx 0;
}

.achievement-badge {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.badge-icon {
  font-size: 24rpx;
  color: #ffffff;
}

/* 成就分类 */
.category-tabs {
  font-size: 24rpx;
}

.category-progress {
  margin: 16rpx 0;
}

.category-progress-container {
  margin-top: 8rpx;
}

.category-progress-bar {
  margin-bottom: 8rpx;
}

/* 成就列表 */
.achievements-list {
  margin-top: 16rpx;
}

.achievement-item {
  padding: 24rpx 0;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.achievement-item.locked {
  opacity: 0.6;
}

.achievement-icon-container {
  position: relative;
  margin-right: 16rpx;
}

.achievement-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.achievement-icon.unlocked {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.achievement-icon.locked {
  background-color: #f5f5f5 !important;
  color: #cccccc;
}

.icon-emoji {
  font-size: 28rpx;
  color: #ffffff;
}

.achievement-icon.locked .icon-emoji {
  color: #cccccc;
}

.achievement-level-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  z-index: 1;
  background-color: #ee0a24;
  color: #ffffff;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
  border: 2rpx solid #ffffff;
  box-sizing: border-box;
}

.level-text {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 600;
}

.achievement-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.achievement-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
}

.achievement-rarity {
  font-size: 20rpx;
}

.achievement-content {
  margin-top: 12rpx;
}

.achievement-progress {
  margin-bottom: 12rpx;
}

.progress-bar {
  margin-bottom: 8rpx;
}

.progress-text {
  font-size: 22rpx;
  color: #646566;
}

.achievement-reward {
  margin-top: 8rpx;
}

.reward-text {
  font-size: 22rpx;
  color: #969799;
  font-style: italic;
}

/* 成就统计 */
.stats-grid {
  margin-top: 16rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content {
  padding: 24rpx 16rpx;
  border-radius: 12rpx;
  text-align: center;
  width: 100%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  display: block;
}

.stat-label {
  font-size: 24rpx;
  color: #646566;
  display: block;
}

/* 弹窗样式 */
.modal-content {
  padding: 32rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  min-height: 400rpx;
}

.modal-header {
  display: flex;
  justify-content: center;
  margin-bottom: 24rpx;
}

.modal-achievement-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.modal-achievement-icon-container {
  position: relative;
  display: flex;
  justify-content: center;
}

.modal-achievement-level-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  z-index: 1;
  background-color: #ee0a24;
  color: #ffffff;
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: 600;
  border: 2rpx solid #ffffff;
  box-sizing: border-box;
}

.modal-level-text {
  font-size: 22rpx;
  color: #ffffff;
  font-weight: 600;
}

.modal-body {
  text-align: center;
}

.modal-achievement-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.modal-achievement-name {
  font-size: 36rpx;
  font-weight: 700;
  color: #323233;
}

.modal-achievement-description {
  font-size: 28rpx;
  color: #646566;
  line-height: 1.5;
  margin-bottom: 24rpx;
  display: block;
}

.modal-achievement-details {
  margin: 16rpx 0;
}

.modal-progress {
  margin: 24rpx 0;
}

.modal-progress .progress-bar {
  margin: 16rpx 0 8rpx 0;
}

.modal-progress .progress-text {
  font-size: 24rpx;
  color: #646566;
  text-align: center;
}

.modal-tips {
  margin-top: 24rpx;
}

.tips-content {
  font-size: 26rpx;
  color: #969799;
  line-height: 1.4;
  font-style: italic;
  background-color: #f7f8fa;
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #1989fa;
}
