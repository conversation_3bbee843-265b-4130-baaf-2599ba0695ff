<!-- pages/test-invite/index.wxml -->
<view class="container">
  <view class="test-section">
    <text class="section-title">邀请功能测试</text>
    
    <view class="test-item">
      <text class="test-label">1. 创建测试小组</text>
      <button class="btn btn-primary" bindtap="createTestGroup">创建小组</button>
    </view>

    <view class="test-item" wx:if="{{testGroupInfo}}">
      <text class="test-label">2. 小组信息</text>
      <view class="group-info">
        <text>小组名称: {{testGroupInfo.groupName}}</text>
        <text>考试名称: {{testGroupInfo.examName}}</text>
        <text>邀请码: {{testGroupInfo.inviteCode}}</text>
      </view>
    </view>

    <view class="test-item" wx:if="{{testGroupInfo}}">
      <text class="test-label">3. 测试邀请码验证</text>
      <button class="btn btn-secondary" bindtap="testVerifyCode">验证邀请码</button>
    </view>

    <view class="test-item" wx:if="{{testGroupInfo}}">
      <text class="test-label">4. 测试邀请页面</text>
      <button class="btn btn-secondary" bindtap="openInvitePage">打开邀请页面</button>
    </view>

    <view class="test-item">
      <text class="test-label">5. 修复小组数据</text>
      <button class="btn btn-secondary" bindtap="fixGroupData">修复数据</button>
    </view>

    <view class="test-item">
      <text class="test-label">6. 清理测试数据</text>
      <button class="btn btn-danger" bindtap="cleanTestData">清理数据</button>
    </view>
  </view>

  <view class="log-section">
    <text class="section-title">测试日志</text>
    <scroll-view class="log-content" scroll-y>
      <text class="log-item" wx:for="{{logs}}" wx:key="index">{{item}}</text>
    </scroll-view>
  </view>
</view>
