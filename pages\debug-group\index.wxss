/* pages/debug-group/index.wxss */
.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.debug-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 30rpx;
}

.debug-item {
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.debug-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.debug-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.debug-input {
  width: 100%;
  height: 70rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  margin-bottom: 15rpx;
  box-sizing: border-box;
}

.btn {
  width: 100%;
  height: 70rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: #1890ff;
  color: white;
}

.btn-secondary {
  background: #f0f0f0;
  color: #666;
}

.debug-content {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
}

.debug-content text {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.debug-content text:last-child {
  margin-bottom: 0;
}

.debug-raw {
  height: 300rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
  font-family: monospace;
}

.debug-raw text {
  font-size: 20rpx;
  color: #666;
  line-height: 1.3;
  white-space: pre-wrap;
}
