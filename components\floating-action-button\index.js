// components/floating-action-button/index.js
const { touch } = require('@vant/weapp/mixins/touch')

Component({
  behaviors: [touch],
  
  properties: {
    // 展开方式: 'fan' | 'vertical' | 'horizontal'
    expandMode: {
      type: String,
      value: 'fan'
    },
    
    // 子按钮配置数组
    buttons: {
      type: Array,
      value: [],
      observer: 'onButtonsChange'
    },
    
    // 是否可拖动
    draggable: {
      type: Boolean,
      value: true
    },
    
    // 主按钮样式配置
    mainButtonStyle: {
      type: Object,
      value: {
        size: 112,
        color: '#1890ff',
        icon: '+'
      }
    },
    
    // 初始位置
    initialPosition: {
      type: Object,
      value: { x: 300, y: 500 }
    },
    
    // 动画时长
    animationDuration: {
      type: Number,
      value: 300
    },
    
    // 展开半径
    expandRadius: {
      type: Number,
      value: 80
    }
  },
  
  data: {
    // 展开状态
    isExpanded: false,

    // 当前位置（绝对坐标）
    currentPosition: { x: 300, y: 500 },

    // movable-view的位置（相对坐标）
    movablePosition: { x: 244, y: 444 },

    // 是否正在拖动
    isDragging: false,

    // 子按钮位置数组
    subButtonPositions: [],

    // 触摸开始时间
    touchStartTime: 0,

    // 屏幕信息
    screenInfo: { width: 375, height: 667 }
  },

  lifetimes: {
    attached() {
      // 延迟初始化，确保properties已经设置
      wx.nextTick(() => {
        this.initComponent()
      })
    },

    ready() {
      // 组件完全准备好后再次设置位置
      setTimeout(() => {
        console.log('组件ready，重新设置位置')
        this.restorePosition()
      }, 100)
    },

    detached() {
      this.cleanup()
    }
  },
  
  methods: {
    // 初始化组件
    initComponent() {
      this.getScreenInfo()
      this.restorePosition()
      this.validateButtons()
    },
    
    // 获取屏幕信息
    getScreenInfo() {
      const systemInfo = wx.getSystemInfoSync()
      this.setData({
        screenInfo: {
          width: systemInfo.windowWidth,
          height: systemInfo.windowHeight
        }
      })
    },
    
    // 恢复保存的位置
    restorePosition() {
      try {
        // 优先使用传入的初始位置，如果没有则使用保存的位置
        const { initialPosition } = this.properties
        let targetPosition

        console.log('传入的初始位置:', initialPosition)

        if (initialPosition && (initialPosition.x !== 300 || initialPosition.y !== 500)) {
          // 如果传入了非默认的初始位置，使用传入的位置
          targetPosition = this.validatePosition(initialPosition.x, initialPosition.y)
          console.log('使用传入的初始位置:', targetPosition)
        } else {
          // 否则尝试使用保存的位置
          const savedPosition = wx.getStorageSync('fab_position')
          if (savedPosition) {
            targetPosition = this.validatePosition(savedPosition.x, savedPosition.y)
            console.log('使用保存的位置:', targetPosition)
          } else {
            // 最后使用默认位置
            targetPosition = this.validatePosition(300, 500)
            console.log('使用默认位置:', targetPosition)
          }
        }

        // 同时更新绝对位置和movable位置
        this.updatePosition(targetPosition.x, targetPosition.y)
      } catch (error) {
        console.error('恢复位置失败:', error)
      }
    },

    // 更新位置（同时更新绝对位置和movable位置）
    updatePosition(absoluteX, absoluteY) {
      const validatedPosition = this.validatePosition(absoluteX, absoluteY)

      // 计算movable-view的位置
      // 由于主按钮填充整个movable-view，所以movable位置 = 主按钮位置 - 主按钮半径偏移
      const mainButtonStyle = this.properties.mainButtonStyle || { size: 112 }
      const mainButtonSizePx = this.rpxToPx(mainButtonStyle.size || 112)
      const offset = mainButtonSizePx / 2

      const movableX = validatedPosition.x - offset
      const movableY = validatedPosition.y - offset

      console.log('updatePosition - 绝对位置:', validatedPosition)
      console.log('updatePosition - 偏移量:', offset)
      console.log('updatePosition - movable位置:', { x: movableX, y: movableY })

      this.setData({
        currentPosition: validatedPosition,
        movablePosition: { x: movableX, y: movableY }
      })

      // 强制更新movable-view位置
      wx.nextTick(() => {
        console.log('强制更新movable-view位置到:', { x: movableX, y: movableY })
      })
    },
    
    // 验证按钮配置
    validateButtons() {
      const { buttons } = this.properties
      if (buttons.length > 5) {
        console.warn('子按钮数量超过5个，建议减少以获得更好的用户体验')
      }
    },
    
    // 按钮配置变化处理
    onButtonsChange(newButtons) {
      this.validateButtons()
      if (this.data.isExpanded) {
        this.calculateSubButtonPositions()
      }
    },
    
    // 清理资源
    cleanup() {
      // 清理定时器等资源
    },

    // 位置验证
    validatePosition(x, y) {
      const { screenInfo } = this.data
      const mainButtonStyle = this.properties.mainButtonStyle || { size: 112 }
      const sizeRpx = mainButtonStyle.size || 112
      const sizePx = this.rpxToPx(sizeRpx) // 转换为px
      const margin = sizePx / 2 + 20 // 边距（px）

      const validatedX = Math.max(margin, Math.min(x, screenInfo.width - margin))
      const validatedY = Math.max(margin, Math.min(y, screenInfo.height - margin - 100)) // 减少底部限制

      console.log('validatePosition - 输入:', { x, y })
      console.log('validatePosition - 屏幕:', screenInfo)
      console.log('validatePosition - 边距:', margin)
      console.log('validatePosition - 输出:', { x: validatedX, y: validatedY })

      return {
        x: validatedX,
        y: validatedY
      }
    },

    // 保存位置
    savePosition(x, y) {
      try {
        wx.setStorageSync('fab_position', { x, y })
      } catch (error) {
        console.error('保存位置失败:', error)
      }
    },

    // 拖动位置变化处理
    onPositionChange(e) {
      if (!this.properties.draggable || this.data.isExpanded) return

      const { x, y } = e.detail
      // movable-view的坐标就是主按钮的左上角坐标（因为主按钮填充整个movable-view）
      const validatedPosition = this.validatePosition(x, y)

      // 更新位置数据
      this.setData({
        currentPosition: validatedPosition,
        movablePosition: { x, y } // 保持movable-view的原始坐标
      })

      // 保存位置
      this.savePosition(validatedPosition.x, validatedPosition.y)

      // 触发位置变化事件
      this.triggerEvent('positionchange', validatedPosition)
    },

    // 触摸开始
    onTouchStart(e) {
      this.touchStart(e)
      this.touchStartTime = Date.now()

      this.setData({
        isDragging: false
      })
    },

    // 触摸结束
    onTouchEnd(e) {
      const duration = Date.now() - this.touchStartTime
      const { offsetX, offsetY } = this

      // 判断是点击还是拖动
      const isClick = duration < 200 && offsetX < 10 && offsetY < 10

      if (isClick) {
        // 点击事件 - 切换展开状态
        this.toggleExpand()
      } else {
        // 拖动结束
        this.setData({
          isDragging: false
        })
      }
    },

    // 切换展开状态
    toggleExpand() {
      const newExpandedState = !this.data.isExpanded

      this.setData({
        isExpanded: newExpandedState
      })

      if (newExpandedState) {
        this.calculateSubButtonPositions()
      }

      // 触发展开状态变化事件
      this.triggerEvent('expandchange', { expanded: newExpandedState })
    },

    // rpx转px
    rpxToPx(rpx) {
      const { screenInfo } = this.data
      return rpx * screenInfo.width / 750
    },

    // 计算子按钮位置
    calculateSubButtonPositions() {
      const { draggable, expandMode, buttons, expandRadius, mainButtonStyle } = this.properties
      const { currentPosition, movablePosition } = this.data

      // 根据模式获取主按钮的实际位置
      let mainButtonLeft, mainButtonTop

      if (draggable) {
        // 拖动模式：需要找到正确的偏移量
        // movable-view的坐标 + 偏移 = 主按钮实际位置
        const offset = this.rpxToPx(112) / 2 // 使用主按钮半径作为偏移
        mainButtonLeft = movablePosition.x + offset
        mainButtonTop = movablePosition.y + offset
        console.log('拖动模式 - movablePosition:', movablePosition)
        console.log('计算的偏移量:', offset, 'px')
        console.log('主按钮位置:', { x: mainButtonLeft, y: mainButtonTop })
      } else {
        // 非拖动模式：直接使用currentPosition
        mainButtonLeft = currentPosition.x
        mainButtonTop = currentPosition.y
        console.log('非拖动模式 - currentPosition:', currentPosition)
      }

      // 计算主按钮的中心点位置
      const mainButtonSizeRpx = (mainButtonStyle && mainButtonStyle.size) || 112
      const mainButtonSizePx = this.rpxToPx(mainButtonSizeRpx)
      const mainButtonCenterX = mainButtonLeft + (mainButtonSizePx / 2)
      const mainButtonCenterY = mainButtonTop + (mainButtonSizePx / 2)

      console.log('主按钮尺寸:', mainButtonSizeRpx, 'rpx =', mainButtonSizePx, 'px')
      console.log('主按钮中心:', { x: mainButtonCenterX, y: mainButtonCenterY })
      console.log('展开模式:', expandMode, '展开半径:', expandRadius)

      let positions = []

      switch (expandMode) {
        case 'fan':
          positions = this.calculateFanPositions(mainButtonCenterX, mainButtonCenterY, expandRadius)
          break
        case 'vertical':
          positions = this.calculateVerticalPositions(mainButtonCenterX, mainButtonCenterY, 70)
          break
        case 'horizontal':
          positions = this.calculateHorizontalPositions(mainButtonCenterX, mainButtonCenterY, 70)
          break
        default:
          positions = this.calculateFanPositions(mainButtonCenterX, mainButtonCenterY, expandRadius)
      }

      console.log('计算出的子按钮位置:', positions)

      this.setData({
        subButtonPositions: positions
      })
    },

    // 智能扇形展开位置计算
    calculateFanPositions(centerX, centerY, radius) {
      const { buttons } = this.properties
      const { screenInfo } = this.data
      const buttonCount = Math.min(buttons.length, 5)

      // 根据主按钮位置智能选择展开方向
      const expandDirection = this.getOptimalExpandDirection(centerX, centerY)
      console.log('智能展开方向:', expandDirection)

      return buttons.slice(0, 5).map((button, index) => {
        // 根据展开方向计算角度
        let baseAngle
        switch (expandDirection) {
          case 'top-right':
            baseAngle = -90 // 从上方开始，向右展开
            break
          case 'bottom-right':
            baseAngle = 0 // 从右方开始，向下展开
            break
          case 'bottom-left':
            baseAngle = 90 // 从下方开始，向左展开
            break
          case 'top-left':
            baseAngle = 180 // 从左方开始，向上展开
            break
          default:
            baseAngle = -90 // 默认向上
        }

        const angle = baseAngle + (index * 45) // 每个按钮间隔45度
        const radian = (angle * Math.PI) / 180

        // 计算相对于主按钮中心的位置
        const subButtonCenterX = centerX + Math.cos(radian) * radius
        const subButtonCenterY = centerY + Math.sin(radian) * radius

        // 转换为子按钮左上角的位置
        const subButtonX = subButtonCenterX - 44
        const subButtonY = subButtonCenterY - 44

        console.log(`子按钮${index}: 角度${angle}° -> 中心(${subButtonCenterX}, ${subButtonCenterY}) -> 左上角(${subButtonX}, ${subButtonY})`)

        return {
          x: subButtonX,
          y: subButtonY,
          index: index
        }
      })
    },

    // 获取最佳展开方向
    getOptimalExpandDirection(centerX, centerY) {
      const { screenInfo } = this.data
      const { expandRadius } = this.properties

      // 计算到屏幕边缘的距离
      const distanceToTop = centerY
      const distanceToBottom = screenInfo.height - centerY
      const distanceToLeft = centerX
      const distanceToRight = screenInfo.width - centerX

      // 需要的最小空间（展开半径 + 子按钮半径 + 安全边距）
      const requiredSpace = expandRadius + 44 + 20

      console.log('屏幕边距:', {
        top: distanceToTop,
        bottom: distanceToBottom,
        left: distanceToLeft,
        right: distanceToRight,
        required: requiredSpace
      })

      // 检查各个方向的可用空间
      const canExpandTop = distanceToTop >= requiredSpace
      const canExpandBottom = distanceToBottom >= requiredSpace
      const canExpandLeft = distanceToLeft >= requiredSpace
      const canExpandRight = distanceToRight >= requiredSpace

      // 智能选择展开方向
      if (canExpandTop && canExpandRight) {
        return 'top-right' // 优先选择右上方
      } else if (canExpandBottom && canExpandRight) {
        return 'bottom-right' // 右下方
      } else if (canExpandBottom && canExpandLeft) {
        return 'bottom-left' // 左下方
      } else if (canExpandTop && canExpandLeft) {
        return 'top-left' // 左上方
      } else if (canExpandRight) {
        return 'bottom-right' // 只能向右
      } else if (canExpandLeft) {
        return 'bottom-left' // 只能向左
      } else if (canExpandBottom) {
        return 'bottom-right' // 只能向下
      } else {
        return 'top-right' // 默认向上（可能会超出边界）
      }
    },

    // 垂直展开位置计算
    calculateVerticalPositions(centerX, centerY, spacing) {
      const { buttons } = this.properties

      return buttons.slice(0, 5).map((button, index) => ({
        x: centerX - 44, // 减去子按钮半径偏移
        y: centerY - (index + 1) * spacing - 44,
        index: index
      }))
    },

    // 水平展开位置计算
    calculateHorizontalPositions(centerX, centerY, spacing) {
      const { buttons } = this.properties

      return buttons.slice(0, 5).map((button, index) => ({
        x: centerX - (index + 1) * spacing - 44, // 减去子按钮半径偏移
        y: centerY - 44,
        index: index
      }))
    },

    // 子按钮点击事件
    onSubButtonClick(e) {
      const { index } = e.currentTarget.dataset
      const button = this.properties.buttons[index]

      if (button) {
        // 触发按钮点击事件
        this.triggerEvent('buttonclick', {
          index: index,
          button: button,
          action: button.action || button.id
        })

        // 点击后收起
        this.setData({
          isExpanded: false
        })

        // 触发展开状态变化事件
        this.triggerEvent('expandchange', { expanded: false })
      }
    }
  }
})
