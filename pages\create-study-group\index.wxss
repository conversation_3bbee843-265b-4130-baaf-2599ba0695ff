/* pages/create-study-group/index.wxss */

/* 页面容器 */
.container {
  background-color: #f7f8fa;
  padding: 16rpx;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

/* 卡片容器 */
.page-header,
.section-card {
  margin-bottom: 24rpx;
}

/* 页面引导 */
.page-header {
  text-align: center;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx 20rpx; /* 进一步减小内边距 */
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.header-desc {
  font-size: 28rpx;
  font-weight: 500;
  color: #1890ff;
  line-height: 1.4;
}

/* 考试列表 */
.exam-list {
  margin-top: 16rpx;
}

.exam-item {
  transition: all 0.3s ease;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
}

.exam-item.selected {
  background-color: #e3f2fd !important;
  border: 1rpx solid #1890ff !important;
}

.exam-item:last-child {
  margin-bottom: 0;
}

/* 空状态 */
.add-exam-btn {
  margin-top: 16rpx;
  background-color: #1890ff !important;
  color: #ffffff !important;
  border: none !important;
}

/* 输入框提示 */
.input-tip {
  text-align: center;
  margin-top: 16rpx;
}

/* 小组信息网格 */
.group-info {
  margin-top: 16rpx;
}

.info-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  justify-content: space-between;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  height: 120rpx;
  width: calc(50% - 8rpx); /* 2列布局，减去间距的一半 */
  box-sizing: border-box;
}

.info-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
  display: block;
  line-height: 1;
  color: #1890ff;
}

.info-text {
  font-size: 24rpx;
  color: #646566;
  text-align: center;
  font-weight: 500;
  line-height: 1.3;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #ebedf0;
  z-index: 100;
}

.create-btn {
  height: 88rpx !important;
  line-height: 88rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  background-color: #1890ff !important;
  border: none !important;
}

.create-btn[disabled] {
  background-color: #f2f3f5 !important;
  color: #c8c9cc !important;
}

/* 自定义van-card样式 */
.page-header,
.section-card {
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 自定义van-cell样式 */
.exam-item {
  background-color: #f8f9fa;
  margin: 8rpx 0;
  border-radius: 12rpx;
  border: 1rpx solid transparent;
}

.exam-item:active {
  background-color: #f2f3f5 !important;
}

/* 自定义van-field样式 */
.van-field {
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

/* 自定义van-button样式 */
.van-button {
  border-radius: 12rpx;
}

/* 自定义van-empty样式 */
.van-empty {
  padding: 60rpx 20rpx;
}

/* 自定义van-tag样式 */
.van-tag {
  border-radius: 8rpx;
}

/* 自定义van-grid样式 */
.van-grid {
  background-color: transparent;
}

.van-grid-item {
  margin-bottom: 16rpx;
}

/* 确保小组功能特色正常显示 */
.group-info .van-grid-item {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .info-item {
    height: 100rpx;
    padding: 12rpx;
  }
  
  .info-icon {
    font-size: 32rpx;
  }
  
  .info-text {
    font-size: 22rpx;
  }
}

/* 动画效果 */
.exam-item {
  transition: all 0.3s ease;
}

.exam-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

/* 加载状态 */
.van-loading {
  margin-right: 8rpx;
}

/* 选中状态动画 */
.exam-item.selected {
  animation: selectBounce 0.3s ease-out;
}

@keyframes selectBounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}
