// 通知管理API
class NotificationApi {
  
  // 订阅消息模板ID配置
  // 注意：以下模板ID需要在微信公众平台申请获得真实ID后替换
  static TEMPLATE_IDS = {
    TASK_REMINDER: 'TASK_REMINDER_TEMPLATE_ID_PLACEHOLDER', // 任务提醒模板ID - 需要申请
    EXAM_REMINDER: 'EXAM_REMINDER_TEMPLATE_ID_PLACEHOLDER', // 考试提醒模板ID - 需要申请
    GROUP_DIGEST: 'GROUP_DIGEST_TEMPLATE_ID_PLACEHOLDER'    // 小组动态汇总模板ID - 需要申请
  }

  // 请求订阅消息权限
  static async requestSubscription(templateIds = null) {
    try {
      // 如果没有指定模板，使用所有可用模板
      const tmplIds = templateIds || Object.values(this.TEMPLATE_IDS)
      
      console.log('请求订阅消息权限:', tmplIds)
      
      // 调用微信API请求订阅
      const result = await new Promise((resolve, reject) => {
        wx.requestSubscribeMessage({
          tmplIds: tmplIds,
          success: (res) => {
            console.log('订阅消息请求结果:', res)
            resolve(res)
          },
          fail: (err) => {
            console.error('订阅消息请求失败:', err)
            reject(err)
          }
        })
      })

      // 分析订阅结果
      const subscribedTemplates = []
      const rejectedTemplates = []
      
      Object.keys(result).forEach(templateId => {
        if (result[templateId] === 'accept') {
          subscribedTemplates.push(templateId)
        } else {
          rejectedTemplates.push(templateId)
        }
      })

      // 保存订阅状态到云端
      if (subscribedTemplates.length > 0) {
        await this.saveSubscriptionStatus(subscribedTemplates, {
          subscriptionTime: new Date().toISOString(),
          rejectedTemplates: rejectedTemplates
        })
      }

      return {
        success: true,
        data: {
          subscribedTemplates,
          rejectedTemplates,
          totalRequested: tmplIds.length,
          totalSubscribed: subscribedTemplates.length
        }
      }
    } catch (error) {
      console.error('请求订阅消息失败:', error)
      return {
        success: false,
        error: error.message || '请求订阅消息失败'
      }
    }
  }

  // 保存订阅状态到云端
  static async saveSubscriptionStatus(templateIds, subscriptionData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'notificationManager',
        data: {
          action: 'requestSubscription',
          data: {
            templateIds: templateIds,
            subscriptionData: subscriptionData
          }
        }
      })

      return result.result
    } catch (error) {
      console.error('保存订阅状态失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 发送任务提醒
  static async sendTaskReminder(taskData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'notificationManager',
        data: {
          action: 'sendTaskReminder',
          data: {
            userId: taskData.userId,
            taskId: taskData.taskId,
            taskTitle: taskData.title,
            dueDate: taskData.dueDate,
            templateId: this.TEMPLATE_IDS.TASK_REMINDER
          }
        }
      })

      return result.result
    } catch (error) {
      console.error('发送任务提醒失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 发送考试提醒
  static async sendExamReminder(examData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'notificationManager',
        data: {
          action: 'sendExamReminder',
          data: {
            userId: examData.userId,
            examId: examData.examId,
            examTitle: examData.title,
            examDate: examData.examDate,
            reminderDays: examData.reminderDays,
            templateId: this.TEMPLATE_IDS.EXAM_REMINDER
          }
        }
      })

      return result.result
    } catch (error) {
      console.error('发送考试提醒失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 获取用户订阅状态
  static async getUserSubscriptions() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'notificationManager',
        data: {
          action: 'getUserSubscriptions'
        }
      })

      return result.result
    } catch (error) {
      console.error('获取用户订阅状态失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 更新订阅状态
  static async updateSubscriptionStatus(status, preferences = {}) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'notificationManager',
        data: {
          action: 'updateSubscriptionStatus',
          data: {
            status: status,
            preferences: preferences
          }
        }
      })

      return result.result
    } catch (error) {
      console.error('更新订阅状态失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 检查是否需要请求订阅权限
  static async checkSubscriptionStatus() {
    try {
      const subscriptions = await this.getUserSubscriptions()
      
      if (!subscriptions.success || !subscriptions.data) {
        return { needSubscription: true, reason: '未找到订阅记录' }
      }

      const subscription = subscriptions.data
      
      // 检查订阅是否过期（微信订阅消息有效期为一次性）
      const subscriptionTime = new Date(subscription.subscriptionTime)
      const now = new Date()
      const daysDiff = (now - subscriptionTime) / (1000 * 60 * 60 * 24)
      
      if (daysDiff > 30) { // 30天后重新请求订阅
        return { needSubscription: true, reason: '订阅已过期' }
      }

      if (subscription.status !== 'active') {
        return { needSubscription: true, reason: '订阅状态非活跃' }
      }

      return { 
        needSubscription: false, 
        subscription: subscription,
        subscribedTemplates: subscription.templateIds || []
      }
    } catch (error) {
      console.error('检查订阅状态失败:', error)
      return { needSubscription: true, reason: '检查失败' }
    }
  }

  // 智能请求订阅（根据用户行为判断时机）
  static async smartRequestSubscription(context = 'general') {
    try {
      // 检查当前订阅状态
      const status = await this.checkSubscriptionStatus()
      
      if (!status.needSubscription) {
        console.log('用户已订阅，无需重复请求')
        return { success: true, alreadySubscribed: true }
      }

      // 根据上下文选择合适的模板
      let templateIds = []
      let message = ''
      
      switch (context) {
        case 'task_create':
          templateIds = [this.TEMPLATE_IDS.TASK_REMINDER]
          message = '开启任务提醒，不错过重要复习'
          break
        case 'exam_create':
          templateIds = [this.TEMPLATE_IDS.EXAM_REMINDER]
          message = '开启考试提醒，及时准备考试'
          break
        case 'settings':
          templateIds = Object.values(this.TEMPLATE_IDS)
          message = '开启通知提醒，获得更好的学习体验'
          break
        default:
          templateIds = [this.TEMPLATE_IDS.TASK_REMINDER, this.TEMPLATE_IDS.EXAM_REMINDER]
          message = '开启学习提醒，保持高效复习'
      }

      // 显示订阅引导
      const userChoice = await new Promise((resolve) => {
        wx.showModal({
          title: '开启消息通知',
          content: message,
          confirmText: '开启',
          cancelText: '暂不',
          success: (res) => {
            resolve(res.confirm)
          },
          fail: () => {
            resolve(false)
          }
        })
      })

      if (!userChoice) {
        return { success: false, userCancelled: true }
      }

      // 请求订阅
      return await this.requestSubscription(templateIds)
    } catch (error) {
      console.error('智能请求订阅失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 发送本地通知（应用内通知）
  static showLocalNotification(title, content, options = {}) {
    const defaultOptions = {
      icon: 'success',
      duration: 3000,
      mask: false
    }

    const finalOptions = { ...defaultOptions, ...options }

    if (finalOptions.type === 'toast') {
      wx.showToast({
        title: title,
        icon: finalOptions.icon,
        duration: finalOptions.duration,
        mask: finalOptions.mask
      })
    } else {
      wx.showModal({
        title: title,
        content: content,
        showCancel: false,
        confirmText: '知道了'
      })
    }
  }

  // 创建任务时自动请求订阅
  static async onTaskCreated(taskData) {
    try {
      // 检查是否需要订阅
      const status = await this.checkSubscriptionStatus()
      
      if (status.needSubscription) {
        // 延迟请求，避免打断用户操作流程
        setTimeout(async () => {
          await this.smartRequestSubscription('task_create')
        }, 2000)
      }

      // 如果任务有截止时间且在未来，可以考虑设置本地提醒
      if (taskData.dueDate) {
        const dueDate = new Date(taskData.dueDate)
        const now = new Date()
        
        if (dueDate > now) {
          console.log('任务创建成功，已设置提醒')
        }
      }
    } catch (error) {
      console.error('任务创建后处理失败:', error)
    }
  }

  // 创建考试时自动请求订阅
  static async onExamCreated(examData) {
    try {
      const status = await this.checkSubscriptionStatus()

      if (status.needSubscription) {
        // 对于考试，需要特别说明多次提醒的订阅需求
        const reminderCount = examData.reminderSettings ? examData.reminderSettings.length : 1

        setTimeout(async () => {
          await this.requestExamSubscriptionWithWarning(reminderCount)
        }, 2000)
      }

      console.log('考试创建成功，已设置提醒')
    } catch (error) {
      console.error('考试创建后处理失败:', error)
    }
  }

  // 考试订阅特殊处理（说明多次提醒需求）
  static async requestExamSubscriptionWithWarning(reminderCount) {
    try {
      const message = reminderCount > 1
        ? `您设置了${reminderCount}个提醒时间点。由于微信限制，每次订阅只能发送一次通知，后续提醒需要重新订阅。我们会在合适时机提醒您重新开启。`
        : '开启考试提醒，及时准备考试'

      const userChoice = await new Promise((resolve) => {
        wx.showModal({
          title: '开启考试提醒',
          content: message,
          confirmText: '开启',
          cancelText: '暂不',
          success: (res) => {
            resolve(res.confirm)
          },
          fail: () => {
            resolve(false)
          }
        })
      })

      if (userChoice) {
        return await this.requestSubscription([this.TEMPLATE_IDS.EXAM_REMINDER])
      }

      return { success: false, reason: '用户取消订阅' }
    } catch (error) {
      console.error('请求考试订阅失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 检查是否需要重新订阅
  static async checkResubscriptionNeed() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'notificationManager',
        data: {
          action: 'checkResubscriptionNeed'
        }
      })

      if (result.result.success && result.result.data) {
        const { examTitle, pendingRemindersCount } = result.result.data

        // 显示重新订阅提醒
        const userChoice = await new Promise((resolve) => {
          wx.showModal({
            title: '继续接收考试提醒',
            content: `您的"${examTitle}"还有${pendingRemindersCount}个提醒待发送，需要重新开启通知权限才能继续接收提醒。`,
            confirmText: '重新开启',
            cancelText: '暂不',
            success: (res) => {
              resolve(res.confirm)
            }
          })
        })

        if (userChoice) {
          return await this.requestSubscription([this.TEMPLATE_IDS.EXAM_REMINDER])
        }
      }

      return result.result
    } catch (error) {
      console.error('检查重新订阅需求失败:', error)
      return { success: false, error: error.message }
    }
  }
}

module.exports = NotificationApi
