/**
 * 测试运行器
 * 在小程序环境中运行测试用例
 */

const ExamModel = require('../models/ExamModel')
const SmartApi = require('./smartApi')

class TestRunner {
  constructor() {
    this.testResults = []
    this.totalTests = 0
    this.passedTests = 0
    this.failedTests = 0
  }

  /**
   * 运行所有测试
   * @returns {Object} 测试结果
   */
  async runAllTests() {
    console.log('🚀 开始运行考试模型测试...')
    
    this.resetResults()
    
    // 运行ExamModel测试
    await this.runExamModelTests()
    
    // 运行SmartApi测试
    await this.runSmartApiTests()
    
    // 运行集成测试
    await this.runIntegrationTests()
    
    const summary = this.generateSummary()
    console.log('📊 测试完成:', summary)
    
    return summary
  }

  /**
   * 运行ExamModel测试
   */
  async runExamModelTests() {
    console.log('📝 运行 ExamModel 测试...')
    
    // 测试1: 构造函数基本功能
    await this.test('ExamModel构造函数 - 基本功能', () => {
      const examData = {
        title: '期末考试',
        subject: '数学',
        examDate: '2024-01-15',
        examTime: '09:00',
        location: '教学楼A101',
        type: 'final',
        importance: 'high'
      }
      
      const exam = new ExamModel(examData)
      
      this.assert(exam.data.title === '期末考试', '标题应该正确设置')
      this.assert(exam.data.subject === '数学', '科目应该正确设置')
      this.assert(exam.data.examDate === '2024-01-15', '日期应该正确设置')
      this.assert(exam.data.type === 'final', '类型应该正确设置')
    })

    // 测试2: 默认值设置
    await this.test('ExamModel构造函数 - 默认值', () => {
      const examData = {
        title: '测试考试',
        examDate: '2024-01-15',
        examTime: '09:00'
      }
      
      const exam = new ExamModel(examData)
      
      this.assert(exam.data.type === 'final', '应该设置默认类型')
      this.assert(exam.data.importance === 'medium', '应该设置默认重要程度')
      this.assert(exam.data.status === 'upcoming', '应该设置默认状态')
      this.assert(Array.isArray(exam.data.reminderSettings), '应该设置默认提醒设置')
    })

    // 测试3: 数据验证
    await this.test('ExamModel构造函数 - 数据验证', () => {
      // 测试必填字段
      this.assertThrows(() => {
        new ExamModel({})
      }, '缺少必填字段应该抛出错误')

      this.assertThrows(() => {
        new ExamModel({ title: '', examDate: '2024-01-15', examTime: '09:00' })
      }, '空标题应该抛出错误')

      this.assertThrows(() => {
        new ExamModel({ title: '测试', examDate: 'invalid', examTime: '09:00' })
      }, '无效日期应该抛出错误')
    })

    // 测试4: 静态方法
    await this.test('ExamModel静态方法', () => {
      const defaultData = ExamModel.getDefaultData()
      this.assert(typeof defaultData === 'object', 'getDefaultData应该返回对象')
      this.assert(defaultData.title === '', '默认标题应该为空')

      const validation = ExamModel.validate({
        title: '测试',
        examDate: '2024-01-15',
        examTime: '09:00'
      })
      this.assert(validation.isValid === true, '有效数据应该通过验证')

      const invalidValidation = ExamModel.validate({})
      this.assert(invalidValidation.isValid === false, '无效数据应该验证失败')
    })

    // 测试5: 实例方法
    await this.test('ExamModel实例方法', () => {
      const exam = new ExamModel({
        title: '测试考试',
        examDate: '2024-01-15',
        examTime: '09:00'
      })

      // 测试update方法
      exam.update({ title: '更新后的考试' })
      this.assert(exam.data.title === '更新后的考试', 'update应该正确更新数据')

      // 测试toJSON方法
      const json = exam.toJSON()
      this.assert(typeof json === 'object', 'toJSON应该返回对象')
      this.assert(json.title === '更新后的考试', 'JSON应该包含正确数据')

      // 测试clone方法
      const cloned = exam.clone()
      this.assert(cloned.data.title === exam.data.title, 'clone应该复制数据')
      this.assert(cloned.data !== exam.data, 'clone应该创建新对象')
    })
  }

  /**
   * 运行SmartApi测试
   */
  async runSmartApiTests() {
    console.log('🔌 运行 SmartApi 测试...')

    // 测试1: 默认数据获取
    await this.test('SmartApi.getDefaultExamData', () => {
      const defaultData = SmartApi.getDefaultExamData()
      this.assert(typeof defaultData === 'object', '应该返回对象')
      this.assert(defaultData.hasOwnProperty('title'), '应该包含title字段')
      this.assert(defaultData.hasOwnProperty('examDate'), '应该包含examDate字段')
      this.assert(defaultData.hasOwnProperty('examTime'), '应该包含examTime字段')
    })

    // 测试2: 数据验证
    await this.test('SmartApi.validateExamData', () => {
      const validData = {
        title: '测试考试',
        examDate: '2024-01-15',
        examTime: '09:00'
      }
      
      const validation = SmartApi.validateExamData(validData)
      this.assert(validation.isValid === true, '有效数据应该通过验证')
      this.assert(Array.isArray(validation.errors), '应该返回错误数组')

      const invalidValidation = SmartApi.validateExamData({})
      this.assert(invalidValidation.isValid === false, '无效数据应该验证失败')
      this.assert(invalidValidation.errors.length > 0, '应该包含错误信息')
    })
  }

  /**
   * 运行集成测试
   */
  async runIntegrationTests() {
    console.log('🔗 运行集成测试...')

    // 测试1: ExamModel与SmartApi集成
    await this.test('ExamModel与SmartApi集成', () => {
      const defaultData = SmartApi.getDefaultExamData()
      
      // 使用SmartApi的默认数据创建ExamModel
      const exam = new ExamModel({
        ...defaultData,
        title: '集成测试考试',
        examDate: '2024-01-15',
        examTime: '09:00'
      })
      
      this.assert(exam.data.title === '集成测试考试', '应该正确创建考试模型')
      
      // 验证创建的模型数据
      const validation = SmartApi.validateExamData(exam.data)
      this.assert(validation.isValid === true, '模型数据应该通过API验证')
    })

    // 测试2: 数据格式一致性
    await this.test('数据格式一致性', () => {
      const examData = {
        title: '一致性测试',
        subject: '测试科目',
        examDate: '2024-01-15',
        examTime: '09:00',
        location: '测试地点',
        description: '测试描述',
        type: 'final',
        importance: 'high',
        status: 'upcoming',
        reminderSettings: ['1day', '3days']
      }

      // 通过ExamModel创建
      const exam = new ExamModel(examData)
      
      // 验证所有字段都正确设置
      Object.keys(examData).forEach(key => {
        this.assert(
          JSON.stringify(exam.data[key]) === JSON.stringify(examData[key]),
          `字段 ${key} 应该正确设置`
        )
      })
    })
  }

  /**
   * 执行单个测试
   * @param {string} name - 测试名称
   * @param {Function} testFn - 测试函数
   */
  async test(name, testFn) {
    this.totalTests++
    
    try {
      await testFn()
      this.passedTests++
      this.testResults.push({
        name,
        status: 'passed',
        message: '✅ 通过'
      })
      console.log(`✅ ${name}`)
    } catch (error) {
      this.failedTests++
      this.testResults.push({
        name,
        status: 'failed',
        message: `❌ 失败: ${error.message}`,
        error: error
      })
      console.error(`❌ ${name}: ${error.message}`)
    }
  }

  /**
   * 断言函数
   * @param {boolean} condition - 条件
   * @param {string} message - 错误信息
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message)
    }
  }

  /**
   * 断言抛出异常
   * @param {Function} fn - 要执行的函数
   * @param {string} message - 错误信息
   */
  assertThrows(fn, message) {
    try {
      fn()
      throw new Error(message)
    } catch (error) {
      if (error.message === message) {
        throw error
      }
      // 如果抛出了其他异常，说明测试通过
    }
  }

  /**
   * 生成测试摘要
   * @returns {Object} 测试摘要
   */
  generateSummary() {
    const successRate = this.totalTests > 0 ? 
      Math.round((this.passedTests / this.totalTests) * 100) : 0

    return {
      total: this.totalTests,
      passed: this.passedTests,
      failed: this.failedTests,
      successRate: successRate,
      results: this.testResults,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 重置测试结果
   */
  resetResults() {
    this.testResults = []
    this.totalTests = 0
    this.passedTests = 0
    this.failedTests = 0
  }

  /**
   * 导出测试报告
   * @returns {string} 测试报告
   */
  exportReport() {
    const summary = this.generateSummary()
    
    let report = `# 考试模型测试报告\n\n`
    report += `**测试时间**: ${summary.timestamp}\n`
    report += `**总测试数**: ${summary.total}\n`
    report += `**通过数**: ${summary.passed}\n`
    report += `**失败数**: ${summary.failed}\n`
    report += `**成功率**: ${summary.successRate}%\n\n`
    
    report += `## 详细结果\n\n`
    
    summary.results.forEach(result => {
      report += `### ${result.name}\n`
      report += `**状态**: ${result.status}\n`
      report += `**结果**: ${result.message}\n\n`
    })
    
    return report
  }
}

module.exports = TestRunner
