/* pages/profile/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息容器 */
.user-info-container {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.user-profile {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.user-avatar {
  margin-right: 24rpx;
  background-color: #F5F5F5;
}

.avatar-container {
  position: relative;
  margin-right: 24rpx;
}

.avatar-edit-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.avatar-container:hover .avatar-edit-overlay {
  opacity: 1;
}

.avatar-edit-overlay:active {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.5);
}

.edit-icon {
  font-size: 32rpx;
  color: #ffffff;
}

.user-details {
  flex: 1;
  cursor: pointer;
  position: relative;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  transition: background-color 0.2s ease;
}

.user-details:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.user-signature {
  font-size: 26rpx;
  color: #666666;
  font-style: italic;
  display: block;
  line-height: 1.4;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-exam {
  font-size: 26rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
}

.login-prompt {
  opacity: 0.7;
}

.login-prompt .user-details {
  color: #999;
}

.login-btn {
  --van-button-small-height: 64rpx;
  --van-button-small-padding: 0 16rpx;
  --van-button-small-font-size: 24rpx;
  --van-button-border-radius: 8rpx;
}

.login-btn {
  --van-button-primary-background-color: #52c41a;
  --van-button-primary-border-color: #52c41a;
}

/* 用户统计 */
.user-stats-container {
  display: flex;
  margin-top: 16rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 700;
  color: #1890FF;
  display: block;
  margin-bottom: 4rpx;
}

/* 未登录时的统计数据样式 */
.stat-value.empty {
  color: #d9d9d9;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
}

.stat-trend {
  font-size: 20rpx;
  color: #52c41a;
  margin-top: 4rpx;
  padding: 2rpx 8rpx;
  background-color: #f6ffed;
  border-radius: 8rpx;
  border: 1rpx solid #b7eb8f;
}

/* 成就容器 */
.achievements-container {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.achievements-container.not-logged-in {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.achievements-container.not-logged-in .section-title {
  color: white;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.achievements-grid {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16rpx;
}

.achievement-item {
  width: 25%;
  text-align: center;
  padding: 20rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.achievement-icon {
  font-size: 32rpx;
  display: block;
  margin-bottom: 8rpx;
}

.achievement-name {
  font-size: 20rpx;
  color: #333333;
}

/* 未登录状态样式 */
.achievements-placeholder {
  text-align: center;
  padding: 40rpx 0;
}

.placeholder-content {
  margin-bottom: 32rpx;
}

.placeholder-icon {
  font-size: 64rpx;
  display: block;
  margin-bottom: 16rpx;
}

.placeholder-title {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  display: block;
  margin-bottom: 12rpx;
}

.placeholder-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  display: block;
}

.preview-achievements {
  display: flex;
  justify-content: space-around;
  margin-top: 24rpx;
}

.preview-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.6;
}

.preview-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  filter: grayscale(50%);
}

.preview-name {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

.preview-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
}

/* 登录状态成就样式增强 */
.achievement-item {
  width: 25%;
  text-align: center;
  padding: 20rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  position: relative;
  transition: all 0.3s ease;
}

.achievement-item.unlocked {
  transform: scale(1.05);
}

.achievement-item.locked {
  opacity: 0.6;
}

.achievement-item.locked .achievement-icon {
  filter: grayscale(50%);
}

.achievement-new-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: #ff4757;
  color: white;
  font-size: 16rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 600;
}

.achievement-progress {
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 16rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.progress-text {
  font-size: 16rpx;
}

/* 菜单容器 */
.menu-container {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.menu-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.menu-section-title {
  padding: 20rpx 32rpx 12rpx;
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

.menu-items {
  padding: 0 32rpx;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-left {
  display: flex;
  align-items: center;
}

.menu-icon {
  font-size: 24rpx;
  width: 32rpx;
  text-align: center;
  margin-right: 16rpx;
}

.menu-text {
  font-size: 28rpx;
  color: #333333;
}

.menu-right {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

/* 数据统计卡片 */
.data-cards-container {
  margin-bottom: 24rpx;
}

.data-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.data-card {
  width: calc(50% - 8rpx);
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.card-title {
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

.card-icon {
  font-size: 32rpx;
}

.card-body {
  flex: 1;
  margin-bottom: 12rpx;
}

.card-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.card-description {
  font-size: 20rpx;
  color: #999999;
  display: block;
}

.card-trend {
  margin-top: auto;
}

/* 快捷操作 */
.quick-actions-container {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.quick-actions-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

.quick-actions-grid {
  display: flex;
  flex-wrap: wrap;
}

.quick-action-item {
  width: 25%;
  text-align: center;
  padding: 20rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.action-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 22rpx;
  color: #333333;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 16rpx;
  margin-bottom: 40rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.version-info text {
  font-size: 22rpx;
  color: #999;
}

/* 编辑弹窗 */
.edit-modal {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.modal-header {
  padding: 32rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-body {
  flex: 1;
  padding: 32rpx 0;
  overflow-y: auto;
}

.avatar-cell {
  --van-cell-vertical-padding: 24rpx;
}

.modal-footer {
  padding: 32rpx;
  display: flex;
  gap: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  --van-button-large-height: 88rpx;
  --van-button-large-font-size: 32rpx;
  --van-button-border-radius: 12rpx;
}

.cancel-btn {
  --van-button-default-background-color: #f8f9fa;
  --van-button-default-border-color: #e8e8e8;
  --van-button-default-color: #666;
}

.confirm-btn {
  --van-button-primary-background-color: #1890ff;
  --van-button-primary-border-color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 16rpx;
  }
  
  .user-info-container,
  .achievements-container,
  .menu-section,
  .quick-actions-container {
    padding: 24rpx;
  }
  
  .user-name {
    font-size: 28rpx;
  }
  
  .user-exam {
    font-size: 24rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }
  
  .user-info-container,
  .achievements-container,
  .menu-section,
  .quick-actions-container {
    background-color: #2a2a2a;
  }
  
  .user-name {
    color: #ffffff;
  }
  
  .user-exam {
    color: #cccccc;
  }
  
  .menu-text {
    color: #ffffff;
  }
  
  .card-title {
    color: #cccccc;
  }
  
  .card-value {
    color: #ffffff;
  }
  
  .action-text {
    color: #ffffff;
  }
}


