// pages/notification-settings/index.js
const NotificationApi = require('../../utils/notificationApi')
const VantToast = require('../../miniprogram_npm/@vant/weapp/toast/toast')

Page({
  data: {
    loading: true,
    requesting: false,
    
    // 订阅状态
    subscriptionStatus: {
      isActive: false,
      subscriptionTime: '',
      templateCount: 0
    },
    
    // 通知类型设置
    notificationTypes: [
      {
        type: 'task_reminder',
        name: '任务提醒',
        description: '任务截止前提醒您及时完成',
        enabled: true
      },
      {
        type: 'exam_reminder',
        name: '考试提醒',
        description: '考试前提醒您做好准备',
        enabled: true
      },
      {
        type: 'group_digest',
        name: '小组动态汇总',
        description: '每日汇总搭子小组的活动动态（每天20:00发送）',
        enabled: true
      }
    ],
    
    // 提醒时间设置
    taskReminderTime: '当天 09:00',
    examReminderAdvance: '提前3天',
    
    // 免打扰设置
    quietMode: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00'
    },
    
    // 时间选择器
    showTimePicker: false,
    showExamPicker: false,
    currentTime: '09:00',
    timePickerType: '', // 'task' | 'quiet_start' | 'quiet_end'
    
    // 考试提醒选项
    examReminderOptions: [
      { text: '提前1天', value: '1day' },
      { text: '提前3天', value: '3days' },
      { text: '提前1周', value: '1week' },
      { text: '提前2周', value: '2weeks' }
    ],
    
    // 帮助折叠面板
    activeCollapse: []
  },

  onLoad() {
    this.loadNotificationSettings()
  },

  // 返回上一页
  onBack() {
    wx.navigateBack()
  },

  // 加载通知设置
  async loadNotificationSettings() {
    try {
      this.setData({ loading: true })
      
      // 检查订阅状态
      const subscriptionResult = await NotificationApi.checkSubscriptionStatus()
      
      if (subscriptionResult.needSubscription) {
        this.setData({
          subscriptionStatus: {
            isActive: false,
            subscriptionTime: '',
            templateCount: 0
          }
        })
      } else {
        const subscription = subscriptionResult.subscription
        this.setData({
          subscriptionStatus: {
            isActive: true,
            subscriptionTime: this.formatDate(subscription.subscriptionTime),
            templateCount: subscription.templateIds?.length || 0
          }
        })
      }
      
      // 加载本地设置
      this.loadLocalSettings()
      
    } catch (error) {
      console.error('加载通知设置失败:', error)
      VantToast.fail('加载设置失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载本地设置
  loadLocalSettings() {
    try {
      const settings = wx.getStorageSync('notification_settings') || {}
      
      if (settings.notificationTypes) {
        this.setData({ notificationTypes: settings.notificationTypes })
      }
      
      if (settings.taskReminderTime) {
        this.setData({ taskReminderTime: settings.taskReminderTime })
      }
      
      if (settings.examReminderAdvance) {
        this.setData({ examReminderAdvance: settings.examReminderAdvance })
      }
      
      if (settings.quietMode) {
        this.setData({ quietMode: settings.quietMode })
      }
    } catch (error) {
      console.error('加载本地设置失败:', error)
    }
  },

  // 保存本地设置
  saveLocalSettings() {
    try {
      const settings = {
        notificationTypes: this.data.notificationTypes,
        taskReminderTime: this.data.taskReminderTime,
        examReminderAdvance: this.data.examReminderAdvance,
        quietMode: this.data.quietMode
      }
      
      wx.setStorageSync('notification_settings', settings)
    } catch (error) {
      console.error('保存本地设置失败:', error)
    }
  },

  // 请求订阅消息
  async requestSubscription() {
    try {
      this.setData({ requesting: true })
      
      const result = await NotificationApi.smartRequestSubscription('settings')
      
      if (result.success && !result.userCancelled) {
        VantToast.success('订阅成功')
        
        // 更新订阅状态
        this.setData({
          subscriptionStatus: {
            isActive: true,
            subscriptionTime: this.formatDate(new Date()),
            templateCount: result.data?.subscribedTemplates?.length || 0
          }
        })
      } else if (result.userCancelled) {
        VantToast.info('已取消订阅')
      } else {
        VantToast.fail(result.error || '订阅失败')
      }
    } catch (error) {
      console.error('请求订阅失败:', error)
      VantToast.fail('订阅失败')
    } finally {
      this.setData({ requesting: false })
    }
  },

  // 重新订阅
  async resubscribe() {
    try {
      this.setData({ requesting: true })
      
      const result = await NotificationApi.requestSubscription()
      
      if (result.success) {
        VantToast.success('重新订阅成功')
        
        this.setData({
          subscriptionStatus: {
            isActive: true,
            subscriptionTime: this.formatDate(new Date()),
            templateCount: result.data?.subscribedTemplates?.length || 0
          }
        })
      } else {
        VantToast.fail(result.error || '重新订阅失败')
      }
    } catch (error) {
      console.error('重新订阅失败:', error)
      VantToast.fail('重新订阅失败')
    } finally {
      this.setData({ requesting: false })
    }
  },

  // 切换通知类型
  toggleNotificationType(e) {
    const type = e.currentTarget.dataset.type
    const enabled = e.detail
    
    const notificationTypes = this.data.notificationTypes.map(item => {
      if (item.type === type) {
        return { ...item, enabled }
      }
      return item
    })
    
    this.setData({ notificationTypes })
    this.saveLocalSettings()
    
    VantToast.success(enabled ? '已开启' : '已关闭')
  },

  // 切换免打扰模式
  toggleQuietMode(e) {
    const enabled = e.detail
    
    this.setData({
      'quietMode.enabled': enabled
    })
    
    this.saveLocalSettings()
    VantToast.success(enabled ? '免打扰已开启' : '免打扰已关闭')
  },

  // 显示任务提醒时间选择器
  showTaskReminderPicker() {
    this.setData({
      showTimePicker: true,
      timePickerType: 'task',
      currentTime: this.extractTime(this.data.taskReminderTime)
    })
  },

  // 显示考试提醒选择器
  showExamReminderPicker() {
    this.setData({ showExamPicker: true })
  },

  // 显示免打扰时间选择器
  showQuietTimePicker() {
    wx.showActionSheet({
      itemList: ['设置开始时间', '设置结束时间'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.setData({
            showTimePicker: true,
            timePickerType: 'quiet_start',
            currentTime: this.data.quietMode.startTime
          })
        } else {
          this.setData({
            showTimePicker: true,
            timePickerType: 'quiet_end',
            currentTime: this.data.quietMode.endTime
          })
        }
      }
    })
  },

  // 隐藏时间选择器
  hideTimePicker() {
    this.setData({ showTimePicker: false })
  },

  // 隐藏考试提醒选择器
  hideExamPicker() {
    this.setData({ showExamPicker: false })
  },

  // 时间确认
  onTimeConfirm(e) {
    const time = e.detail
    const timePickerType = this.data.timePickerType
    
    if (timePickerType === 'task') {
      this.setData({ taskReminderTime: `当天 ${time}` })
    } else if (timePickerType === 'quiet_start') {
      this.setData({ 'quietMode.startTime': time })
    } else if (timePickerType === 'quiet_end') {
      this.setData({ 'quietMode.endTime': time })
    }
    
    this.hideTimePicker()
    this.saveLocalSettings()
    VantToast.success('设置已保存')
  },

  // 考试提醒确认
  onExamReminderConfirm(e) {
    const { value } = e.detail
    const option = this.data.examReminderOptions.find(opt => opt.value === value)
    
    if (option) {
      this.setData({ examReminderAdvance: option.text })
      this.saveLocalSettings()
      VantToast.success('设置已保存')
    }
    
    this.hideExamPicker()
  },

  // 发送测试通知
  async sendTestNotification() {
    try {
      VantToast.loading('发送中...')
      
      // 发送本地通知作为测试
      setTimeout(() => {
        VantToast.clear()
        NotificationApi.showLocalNotification(
          '测试通知',
          '这是一条测试消息，说明通知功能正常工作',
          { type: 'modal' }
        )
      }, 1000)
      
    } catch (error) {
      console.error('发送测试通知失败:', error)
      VantToast.fail('发送失败')
    }
  },

  // 折叠面板变化
  onCollapseChange(e) {
    this.setData({ activeCollapse: e.detail })
  },

  // 格式化日期
  formatDate(date) {
    const d = new Date(date)
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`
  },

  // 提取时间
  extractTime(timeString) {
    const match = timeString.match(/(\d{2}:\d{2})/)
    return match ? match[1] : '09:00'
  }
})
