/**
 * 考试数据迁移工具
 * 处理新旧数据格式的兼容性
 */

const ExamModel = require('../models/ExamModel')

class ExamDataMigration {
  /**
   * 迁移旧格式考试数据到新格式
   * @param {Object} oldExamData - 旧格式的考试数据
   * @returns {Object} 新格式的考试数据
   */
  static migrateOldFormat(oldExamData) {
    if (!oldExamData) return null

    try {
      // 字段映射规则
      const fieldMapping = {
        'name': 'title',           // 考试名称字段统一
        'date': 'examDate',        // 日期字段统一
        'time': 'examTime',        // 时间字段统一
        'notes': 'description',    // 描述字段统一
        'subjects': 'subject'      // 科目字段统一（数组转单个）
      }

      const migratedData = { ...oldExamData }

      // 执行字段重命名
      Object.keys(fieldMapping).forEach(oldField => {
        if (oldExamData[oldField] !== undefined) {
          migratedData[fieldMapping[oldField]] = oldExamData[oldField]
          delete migratedData[oldField]
        }
      })

      // 特殊处理：科目字段（数组转单个）
      if (oldExamData.subjects) {
        if (Array.isArray(oldExamData.subjects)) {
          // 取第一个科目，如果数组为空则为空字符串
          migratedData.subject = oldExamData.subjects.length > 0 ? oldExamData.subjects[0] : ''
        } else if (typeof oldExamData.subjects === 'string') {
          migratedData.subject = oldExamData.subjects
        }
        delete migratedData.subjects
      }

      // 特殊处理：时间字段合并
      if (oldExamData.startTime && !migratedData.examTime) {
        migratedData.examTime = oldExamData.startTime
        delete migratedData.startTime
        delete migratedData.endTime
      }

      // 特殊处理：提醒设置
      if (oldExamData.reminderEnabled !== undefined || oldExamData.reminderFrequency !== undefined) {
        if (oldExamData.reminderEnabled) {
          const frequency = oldExamData.reminderFrequency || 'daily'
          migratedData.reminderSettings = frequency === 'daily' ? ['1day'] : ['3days']
        } else {
          migratedData.reminderSettings = []
        }
        delete migratedData.reminderEnabled
        delete migratedData.reminderFrequency
      }

      // 移除不再使用的字段
      const deprecatedFields = [
        'targetScore', 'isActive', 'totalScore', 'currentScore',
        'studyPlan', 'studyProgress', 'studyMaterials'
      ]
      deprecatedFields.forEach(field => {
        delete migratedData[field]
      })

      // 设置默认值（确保所有必要字段都有值）
      const defaults = {
        title: migratedData.title || '',
        subject: migratedData.subject || '',
        examDate: migratedData.examDate || '',
        examTime: migratedData.examTime || '',
        location: migratedData.location || '',
        description: migratedData.description || '',
        type: migratedData.type || 'final',
        importance: migratedData.importance || 'medium',
        status: migratedData.status || 'upcoming',
        reminderSettings: migratedData.reminderSettings || ['1day', '3days']
      }

      // 应用默认值
      Object.keys(defaults).forEach(field => {
        migratedData[field] = defaults[field]
      })

      return migratedData
    } catch (error) {
      console.error('迁移考试数据失败:', error)
      return oldExamData
    }
  }

  /**
   * 批量迁移考试数据
   * @param {Array} examList - 考试数据列表
   * @returns {Array} 迁移后的考试数据列表
   */
  static migrateExamList(examList) {
    if (!Array.isArray(examList)) return []

    return examList.map(exam => {
      try {
        const migratedData = this.migrateOldFormat(exam)
        // 使用ExamModel验证迁移后的数据
        const examModel = new ExamModel(migratedData)
        return examModel.data
      } catch (error) {
        console.error('迁移考试数据失败:', error, exam)
        // 如果迁移失败，返回原始数据
        return exam
      }
    })
  }

  /**
   * 检查数据是否需要迁移
   * @param {Object} examData - 考试数据
   * @returns {boolean} 是否需要迁移
   */
  static needsMigration(examData) {
    if (!examData) return false

    // 检查是否包含旧字段名
    const oldFields = ['name', 'date', 'time', 'notes', 'subjects', 'startTime', 'endTime', 'reminderEnabled']
    return oldFields.some(field => examData.hasOwnProperty(field))
  }

  /**
   * 智能处理考试数据（自动检测是否需要迁移）
   * @param {Object} examData - 考试数据
   * @returns {Object} 处理后的考试数据
   */
  static smartMigrate(examData) {
    if (!examData) return null

    if (this.needsMigration(examData)) {
      console.log('检测到旧格式数据，执行迁移:', examData)
      return this.migrateOldFormat(examData)
    }

    return examData
  }

  /**
   * 验证迁移结果
   * @param {Object} originalData - 原始数据
   * @param {Object} migratedData - 迁移后数据
   * @returns {Object} 验证结果
   */
  static validateMigration(originalData, migratedData) {
    const validation = {
      success: true,
      warnings: [],
      errors: []
    }

    try {
      // 使用ExamModel验证迁移后的数据
      const examModel = new ExamModel(migratedData)
      
      // 检查关键字段是否正确迁移
      if (originalData.name && !migratedData.title) {
        validation.warnings.push('考试名称迁移可能有问题')
      }

      if (originalData.date && !migratedData.examDate) {
        validation.warnings.push('考试日期迁移可能有问题')
      }

      if (originalData.subjects && Array.isArray(originalData.subjects) && originalData.subjects.length > 1) {
        validation.warnings.push('多个科目已合并为单个科目，可能需要手动调整')
      }

    } catch (error) {
      validation.success = false
      validation.errors.push(error.message)
    }

    return validation
  }

  /**
   * 生成迁移报告
   * @param {Array} originalList - 原始数据列表
   * @param {Array} migratedList - 迁移后数据列表
   * @returns {Object} 迁移报告
   */
  static generateMigrationReport(originalList, migratedList) {
    const report = {
      totalCount: originalList.length,
      migratedCount: 0,
      successCount: 0,
      warningCount: 0,
      errorCount: 0,
      details: []
    }

    originalList.forEach((original, index) => {
      const migrated = migratedList[index]
      
      if (this.needsMigration(original)) {
        report.migratedCount++
        
        const validation = this.validateMigration(original, migrated)
        
        if (validation.success) {
          report.successCount++
        }
        
        if (validation.warnings.length > 0) {
          report.warningCount++
        }
        
        if (validation.errors.length > 0) {
          report.errorCount++
        }

        report.details.push({
          index,
          originalTitle: original.name || original.title,
          migratedTitle: migrated.title,
          validation
        })
      }
    })

    return report
  }
}

module.exports = ExamDataMigration
