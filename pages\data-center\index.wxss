/* pages/data-center/index.wxss */

/* 页面容器 */
.container {
  background-color: #f7f8fa;
  padding: 16rpx;
  min-height: 100vh;
}

/* 页面头部 */
.header-container {
  margin-bottom: 24rpx;
}

.page-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #323233;
  text-align: center;
}

.page-subtitle {
  font-size: 26rpx;
  color: #969799;
  text-align: center;
  margin-top: 8rpx;
}

/* 卡片容器 */
.preparation-score-container,
.study-stats-container,
.efficiency-container,
.subjects-analysis-container,
.habits-container,
.achievements-container,
.export-container {
  margin-bottom: 24rpx;
}

/* 考试准备度 */
.score-info-btn {
  background-color: transparent !important;
  color: #969799 !important;
  border: 1rpx solid #ebedf0 !important;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 32rpx;
  margin: 24rpx 0;
}

.score-circle {
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-details {
  flex: 1;
}

.score-level {
  font-size: 36rpx;
  font-weight: 600;
  color: #52c41a;
  margin-bottom: 8rpx;
}

.score-description {
  font-size: 26rpx;
  color: #969799;
  line-height: 1.4;
}

.score-factors {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.factor-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.factor-name {
  font-size: 26rpx;
  color: #323233;
  min-width: 120rpx;
}

.factor-progress {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.factor-bar {
  flex: 1;
}

.factor-score {
  font-size: 24rpx;
  color: #646566;
  font-weight: 500;
  min-width: 40rpx;
}

/* 复习统计 */
.time-range-selector {
  font-size: 24rpx;
}

.stats-grid {
  margin-top: 16rpx;
}

.stat-card {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  border-radius: 12rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-bottom: 12rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #969799;
  margin-bottom: 8rpx;
}

.stat-trend {
  margin-top: 8rpx;
}

.trend-tag {
  font-size: 20rpx;
}

/* 效率分析 */
.efficiency-chart {
  margin-top: 16rpx;
}

.chart-header {
  margin-bottom: 16rpx;
}

.chart-content {
  padding: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 240rpx;
  margin-bottom: 16rpx;
}

.chart-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 8rpx;
}

.bar-fill {
  width: 32rpx;
  background-color: #1989fa;
  border-radius: 4rpx 4rpx 0 0;
  margin-bottom: 12rpx;
  transition: height 0.3s ease;
}

.bar-label {
  font-size: 22rpx;
  color: #969799;
  margin-bottom: 4rpx;
}

.bar-value {
  font-size: 20rpx;
  color: #646566;
  font-weight: 500;
}

.insight-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

/* 科目分析 */
.subject-item {
  padding: 24rpx 0;
}

.subject-score {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.score-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.score-max {
  font-size: 24rpx;
  color: #969799;
}

.subject-details {
  margin-top: 16rpx;
}

.subject-progress {
  margin-bottom: 16rpx;
}

.progress-bar {
  margin-bottom: 8rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #646566;
}

.subject-stats {
  margin-top: 16rpx;
}

.subject-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-label {
  font-size: 22rpx;
  color: #969799;
  margin-bottom: 4rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
}

/* 备考习惯 */
.habits-grid {
  margin-top: 16rpx;
}

.habit-card {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  border-radius: 12rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.habit-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.habit-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.habit-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8rpx;
}

.habit-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1989fa;
  margin-bottom: 8rpx;
}

.habit-description {
  font-size: 22rpx;
  color: #969799;
  line-height: 1.4;
}

/* 成就展示 */
.achievement-item {
  padding: 16rpx 0;
}

.achievement-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
}

/* 数据导出 */
.export-btn {
  width: 100%;
  margin-top: 16rpx;
}

/* 弹窗样式 */
.modal-content {
  padding: 32rpx;
  border-radius: 16rpx;
  background-color: #fff;
}

.modal-header {
  text-align: center;
  margin-bottom: 24rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.modal-body {
  line-height: 1.5;
}

.modal-text {
  font-size: 28rpx;
  color: #646566;
  margin-bottom: 16rpx;
}
