/* components/avatar-upload/index.wxss */
.avatar-upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 头像容器 */
.avatar-wrapper {
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid #f0f0f0;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.avatar-wrapper.editable {
  cursor: pointer;
  border-color: #1890ff;
}

.avatar-wrapper.editable:hover {
  border-color: #40a9ff;
  transform: scale(1.05);
}

/* 头像图片 */
.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #f5f5f5;
}

/* 上传中遮罩 */
.upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
}

.loading-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  animation: rotate 1s linear infinite;
}

.loading-text {
  font-size: 24rpx;
  opacity: 0.9;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 编辑按钮 */
.edit-button {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 48rpx;
  height: 48rpx;
  background-color: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.edit-icon {
  font-size: 24rpx;
  color: white;
}

/* 编辑提示 */
.edit-hint {
  position: absolute;
  bottom: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  white-space: nowrap;
}

.avatar-wrapper.editable:hover .edit-hint {
  opacity: 1;
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.upload-btn {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
}

.upload-btn.button-disabled {
  background: #d9d9d9;
  color: #999;
}

.delete-btn {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: white;
}

.delete-btn.button-disabled {
  background: #d9d9d9;
  color: #999;
}

.action-btn.button-active {
  transform: scale(0.95);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .action-btn {
    width: 100%;
  }
}
