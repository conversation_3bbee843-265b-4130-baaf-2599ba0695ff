// 数据库初始化工具
class DatabaseInit {
  // 获取数据库实例
  static getDatabase() {
    return wx.cloud.database()
  }
  // 初始化数据库集合
  static async initCollections() {
    try {
      console.log('开始初始化数据库集合...')
      
      // 创建示例数据
      await this.createSampleTasks()
      await this.createSampleExams()
      await this.createSampleStudySessions()
      
      console.log('数据库初始化完成')
      return { success: true }
    } catch (error) {
      console.error('数据库初始化失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 创建示例任务
  static async createSampleTasks() {
    const db = this.getDatabase()
    const sampleTasks = [
      {
        title: '数学高数第一章复习',
        subject: '数学',
        priority: 'high',
        estimatedTime: 120,
        dueDate: new Date(),
        description: '复习极限与连续性相关内容',
        completed: false,
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        title: '英语单词背诵',
        subject: '英语',
        priority: 'medium',
        estimatedTime: 30,
        dueDate: new Date(),
        description: '背诵考研核心词汇100个',
        completed: true,
        createTime: new Date(),
        updateTime: new Date(),
        completedTime: new Date()
      },
      {
        title: '政治马原理论复习',
        subject: '政治',
        priority: 'low',
        estimatedTime: 60,
        dueDate: new Date(),
        description: '复习马克思主义基本原理',
        completed: false,
        createTime: new Date(),
        updateTime: new Date()
      }
    ]

    for (const task of sampleTasks) {
      try {
        await db.collection('tasks').add({
          data: task
        })
        console.log('创建示例任务:', task.title)
      } catch (error) {
        console.log('任务已存在或创建失败:', task.title)
      }
    }
  }

  // 创建示例考试
  static async createSampleExams() {
    const db = this.getDatabase()
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + 30)

    const sampleExams = [
      {
        title: '高等数学期末考试',
        subject: '数学',
        examDate: futureDate,
        examTime: '09:00',
        location: '教学楼A101',
        description: '涵盖微积分、线性代数等内容',
        reminderDays: 3,
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        title: '大学英语四级',
        subject: '英语',
        examDate: new Date(futureDate.getTime() + 7 * 24 * 60 * 60 * 1000),
        examTime: '09:00',
        location: '教学楼B201',
        description: '英语四级考试',
        reminderDays: 7,
        createTime: new Date(),
        updateTime: new Date()
      }
    ]

    for (const exam of sampleExams) {
      try {
        await db.collection('exams').add({
          data: exam
        })
        console.log('创建示例考试:', exam.title)
      } catch (error) {
        console.log('考试已存在或创建失败:', exam.title)
      }
    }
  }

  // 创建示例复习会话
  static async createSampleStudySessions() {
    const db = this.getDatabase()
    const today = new Date()
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)

    const sampleSessions = [
      {
        subject: '数学',
        taskId: null,
        startTime: yesterday,
        endTime: new Date(yesterday.getTime() + 90 * 60 * 1000), // 90分钟
        totalDuration: 90,
        status: 'completed',
        notes: '复习了极限的概念和性质',
        createTime: yesterday
      },
      {
        subject: '英语',
        taskId: null,
        startTime: new Date(today.getTime() - 2 * 60 * 60 * 1000), // 2小时前
        endTime: new Date(today.getTime() - 90 * 60 * 1000), // 90分钟前
        totalDuration: 30,
        status: 'completed',
        notes: '背诵了30个单词',
        createTime: new Date(today.getTime() - 2 * 60 * 60 * 1000)
      }
    ]

    for (const session of sampleSessions) {
      try {
        await db.collection('study_sessions').add({
          data: session
        })
        console.log('创建示例复习会话:', session.subject)
      } catch (error) {
        console.log('复习会话已存在或创建失败:', session.subject)
      }
    }
  }

  // 创建示例番茄钟会话
  static async createSamplePomodoroSessions() {
    const db = this.getDatabase()
    const today = new Date()
    
    const samplePomodoros = [
      {
        subject: '数学',
        taskId: null,
        duration: 25,
        completed: true,
        startTime: new Date(today.getTime() - 3 * 60 * 60 * 1000),
        endTime: new Date(today.getTime() - 3 * 60 * 60 * 1000 + 25 * 60 * 1000),
        createTime: new Date(today.getTime() - 3 * 60 * 60 * 1000)
      },
      {
        subject: '英语',
        taskId: null,
        duration: 25,
        completed: true,
        startTime: new Date(today.getTime() - 2 * 60 * 60 * 1000),
        endTime: new Date(today.getTime() - 2 * 60 * 60 * 1000 + 25 * 60 * 1000),
        createTime: new Date(today.getTime() - 2 * 60 * 60 * 1000)
      }
    ]

    for (const pomodoro of samplePomodoros) {
      try {
        await db.collection('pomodoro_sessions').add({
          data: pomodoro
        })
        console.log('创建示例番茄钟会话:', pomodoro.subject)
      } catch (error) {
        console.log('番茄钟会话已存在或创建失败:', pomodoro.subject)
      }
    }
  }

  // 检查是否需要初始化
  static async checkAndInit() {
    try {
      const db = this.getDatabase()

      // 尝试检查tasks集合是否存在
      try {
        const tasksResult = await db.collection('tasks').limit(1).get()

        if (tasksResult.data.length === 0) {
          // 集合存在但没有数据，进行初始化
          console.log('检测到空数据库，开始初始化...')
          return await this.initCollections()
        } else {
          console.log('数据库已有数据，跳过初始化')
          return { success: true, message: '数据库已有数据' }
        }
      } catch (collectionError) {
        // 集合不存在，需要创建并初始化
        if (collectionError.errCode === -502005) {
          console.log('数据库集合不存在，开始创建和初始化...')
          return await this.initCollections()
        } else {
          throw collectionError
        }
      }
    } catch (error) {
      console.error('检查数据库状态失败:', error)
      return { success: false, error: error.message }
    }
  }
}

module.exports = DatabaseInit
