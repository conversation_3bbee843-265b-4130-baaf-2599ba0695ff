// pages/admin/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    userStats: null,
    cleanupResult: null,
    loading: false
  },

  async onLoad() {
    // 检查是否是管理员（这里简单检查，实际应该有更严格的权限控制）
    const app = getApp()
    if (!app.globalData.userInfo) {
      wx.navigateTo({
        url: '/pages/login/index'
      })
      return
    }

    this.loadUserData()
  },

  // 加载用户数据
  async loadUserData() {
    wx.showLoading({
      title: '加载中...'
    })

    try {
      const result = await SmartApi.getUserData()
      
      if (result.success) {
        this.setData({
          userStats: result.data
        })
      } else {
        wx.showToast({
          title: result.error || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载用户数据失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 清理重复用户
  async cleanDuplicateUsers() {
    const result = await wx.showModal({
      title: '确认清理',
      content: '这将删除重复的用户记录，只保留最新的记录。确定继续吗？',
      confirmText: '确定',
      cancelText: '取消'
    })

    if (!result.confirm) return

    this.setData({ loading: true })
    wx.showLoading({
      title: '清理中...',
      mask: true
    })

    try {
      const cleanResult = await SmartApi.cleanDuplicateUsers()
      
      if (cleanResult.success) {
        this.setData({
          cleanupResult: cleanResult.data
        })
        
        wx.showToast({
          title: '清理完成',
          icon: 'success'
        })
        
        // 重新加载用户数据
        setTimeout(() => {
          this.loadUserData()
        }, 1500)
      } else {
        wx.showToast({
          title: cleanResult.error || '清理失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('清理重复用户失败:', error)
      wx.showToast({
        title: '清理失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
      this.setData({ loading: false })
    }
  },

  // 删除当前用户数据
  async deleteUserData() {
    const result = await wx.showModal({
      title: '危险操作',
      content: '这将删除您的所有数据，包括任务、考试、番茄钟记录等。此操作不可恢复！确定继续吗？',
      confirmText: '确定删除',
      cancelText: '取消',
      confirmColor: '#ff4757'
    })

    if (!result.confirm) return

    // 二次确认
    const secondConfirm = await wx.showModal({
      title: '最后确认',
      content: '您真的要删除所有数据吗？此操作不可恢复！',
      confirmText: '确定删除',
      cancelText: '取消',
      confirmColor: '#ff4757'
    })

    if (!secondConfirm.confirm) return

    this.setData({ loading: true })
    wx.showLoading({
      title: '删除中...',
      mask: true
    })

    try {
      const deleteResult = await SmartApi.deleteUserData()
      
      if (deleteResult.success) {
        wx.showToast({
          title: '数据已删除',
          icon: 'success'
        })
        
        // 清除本地数据
        const app = getApp()
        app.logout()
        
        // 跳转到登录页
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/login/index'
          })
        }, 1500)
      } else {
        wx.showToast({
          title: deleteResult.error || '删除失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('删除用户数据失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
      this.setData({ loading: false })
    }
  },

  // 刷新数据
  onPullDownRefresh() {
    this.loadUserData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  }
})
