<!--pages/login/index.wxml-->
<view class="container">
  <!-- 导航栏 -->
  <van-nav-bar
    title="登录"
    left-text="返回"
    left-arrow
    bind:click-left="onClickLeft"
  />
  
  <view class="logo-container">
    <image class="logo" src="/images/default-avatar.png" mode="aspectFit"></image>
    <view class="title">备考助手</view>
    <view class="subtitle">让每一次复习都更高效</view>
  </view>

  <view class="login-container">
    <van-tabs active="{{ activeTab }}" bind:change="onTabChange" color="#1989fa">
      <van-tab title="微信一键登录" name="wechat">
        <view class="wechat-login-container">
          <view class="wechat-prompt">使用微信账号快速登录</view>
          <view class="wechat-tips">首次登录将创建新账号，老用户直接登录</view>
          <view class="wechat-icon">
            <van-icon name="wechat" size="80rpx" color="#07c160" />
          </view>
          <view class="button-group">
            <van-button
              type="primary"
              size="large"
              block
              loading="{{ loading }}"
              bind:click="wechatLogin"
              color="#07c160"
              wx:if="{{ !needUserAuth }}"
            >
              <van-icon name="wechat" size="20" custom-style="margin-right: 10rpx;" />
              微信一键登录
            </van-button>
            
            <!-- 新用户授权按钮 -->
            <button
              wx:if="{{ needUserAuth }}"
              class="auth-button"
              open-type="getUserInfo"
              bind:getuserinfo="onGetUserInfo"
            >
              <van-icon name="wechat" size="20" custom-style="margin-right: 10rpx;" />
              获取微信信息完成注册
            </button>
          </view>
        </view>
      </van-tab>

      <van-tab title="账号密码登录" name="username">
        <view class="form-container">
          <van-field
            value="{{ loginForm.username }}"
            bind:input="onUsernameChange"
            label="用户名"
            placeholder="请输入用户名"
            clearable
            left-icon="user-o"
          />
          <van-field
            value="{{ loginForm.password }}"
            bind:input="onPasswordChange"
            type="password"
            label="密码"
            placeholder="请输入密码"
            clearable
            left-icon="lock"
          />
          <view class="button-group">
            <van-button
              type="primary"
              size="large"
              block
              loading="{{ loading }}"
              bind:click="usernameLogin"
            >
              登录
            </van-button>
          </view>
          <view class="links">
            <text class="link" bind:tap="showRegister">注册账号</text>
          </view>
        </view>
      </van-tab>
    </van-tabs>


  </view>

  <!-- 注册弹窗 -->
  <van-popup 
    show="{{ showRegisterModal }}" 
    position="bottom" 
    custom-style="height: 70%; border-radius: 20rpx 20rpx 0 0;" 
    bind:close="hideRegister" 
    closeable
    safe-area-inset-bottom
  >
    <view class="register-modal">
      <view class="modal-title">注册新用户</view>
      
      <view class="form-section">
        <van-field
          value="{{ registerForm.username }}"
          bind:input="onRegUsernameChange"
          label="用户名"
          placeholder="至少3个字符"
          required
          clearable
          left-icon="user-o"
          error-message="{{ registerForm.username && registerForm.username.length < 3 ? '用户名至少需要3个字符' : '' }}"
        />
        
        <van-field
          value="{{ registerForm.password }}"
          bind:input="onRegPasswordChange"
          type="password"
          label="密码"
          placeholder="至少6个字符"
          required
          clearable
          left-icon="lock"
          error-message="{{ registerForm.password && registerForm.password.length < 6 ? '密码至少需要6个字符' : '' }}"
        />
        
        <van-field
          value="{{ registerForm.confirmPassword }}"
          bind:input="onRegConfirmPasswordChange"
          type="password"
          label="确认密码"
          placeholder="请再次输入密码"
          required
          clearable
          left-icon="lock"
          error-message="{{ registerForm.confirmPassword && registerForm.password !== registerForm.confirmPassword ? '两次输入的密码不一致' : '' }}"
        />
      </view>
      
      <view class="button-group-modal">
        <van-button
          type="primary"
          size="large"
          block
          loading="{{ registerLoading }}"
          bind:click="submitRegister"
          disabled="{{ !registerForm.username || !registerForm.password || !registerForm.confirmPassword }}"
        >
          立即注册
        </van-button>
        
        <van-button
          size="large"
          block
          bind:click="hideRegister"
          custom-style="margin-top: 20rpx; background-color: #f7f8fa; color: #969799;"
        >
          取消
        </van-button>
      </view>
    </view>
  </van-popup>

</view>
