// 检查点智能建议模块
// 为不同类型的任务提供智能检查点建议

class CheckpointSuggestions {
  
  // 基础学习检查点模板
  static baseTemplates = [
    '理解基本概念',
    '完成课后练习', 
    '制作学习笔记',
    '进行自我测试',
    '复习重点内容',
    '整理知识框架'
  ]

  // 科目专门模板
  static subjectTemplates = {
    '数学': [
      '掌握公式定理',
      '完成习题练习',
      '总结解题方法',
      '错题整理分析',
      '证明过程理解',
      '计算技巧练习'
    ],
    '英语': [
      '背诵重点单词',
      '练习语法知识',
      '完成阅读理解',
      '写作练习',
      '听力训练',
      '口语表达练习'
    ],
    '政治': [
      '背诵重点知识',
      '理解理论概念',
      '练习材料分析',
      '时事热点关注',
      '答题技巧掌握',
      '知识点串联'
    ],
    '专业课': [
      '复习课本内容',
      '整理重点笔记',
      '练习专业题目',
      '查阅相关资料',
      '案例分析练习',
      '实践操作训练'
    ],
    '语文': [
      '文言文翻译',
      '诗词鉴赏',
      '作文素材积累',
      '阅读理解训练',
      '基础知识巩固',
      '写作技巧练习'
    ],
    '物理': [
      '公式推导理解',
      '实验原理掌握',
      '计算题练习',
      '概念辨析',
      '图像分析',
      '应用题训练'
    ],
    '化学': [
      '化学方程式',
      '实验操作',
      '元素周期律',
      '有机化学',
      '计算题练习',
      '实验现象分析'
    ],
    '生物': [
      '生物过程理解',
      '实验设计',
      '图表分析',
      '概念记忆',
      '生态关系',
      '遗传规律'
    ],
    '历史': [
      '时间线梳理',
      '历史事件分析',
      '人物事迹',
      '因果关系',
      '史料分析',
      '答题模板'
    ],
    '地理': [
      '地图识读',
      '地理过程',
      '区域特征',
      '环境问题',
      '地理计算',
      '案例分析'
    ]
  }

  // 考试类型模板
  static examTemplates = {
    '考研': [
      '制定复习计划',
      '真题练习',
      '模拟考试',
      '查漏补缺',
      '重点突破',
      '心理调适'
    ],
    '四级': [
      '词汇积累',
      '听力练习',
      '阅读训练',
      '写作提升',
      '翻译练习',
      '模拟测试'
    ],
    '六级': [
      '高级词汇',
      '长篇阅读',
      '翻译练习',
      '综合训练',
      '听力提升',
      '写作进阶'
    ],
    '高考': [
      '基础知识巩固',
      '专题训练',
      '综合练习',
      '应试技巧',
      '心理准备',
      '时间管理'
    ],
    '公务员': [
      '行测练习',
      '申论写作',
      '时政热点',
      '模拟考试',
      '答题技巧',
      '知识积累'
    ]
  }

  /**
   * 根据任务信息获取智能建议
   * @param {Object} taskInfo - 任务信息
   * @param {string} taskInfo.subject - 科目
   * @param {string} taskInfo.examName - 考试名称
   * @param {string} taskInfo.title - 任务标题
   * @param {Array} taskInfo.existingCheckpoints - 已有检查点
   * @returns {Array} 建议的检查点列表
   */
  static getSuggestions(taskInfo = {}) {
    const { subject, examName, title, existingCheckpoints = [] } = taskInfo
    let suggestions = []

    // 根据科目添加专门建议
    if (subject && this.subjectTemplates[subject]) {
      suggestions.push(...this.subjectTemplates[subject])
    }

    // 根据考试名称添加建议
    if (examName) {
      for (const [key, templates] of Object.entries(this.examTemplates)) {
        if (examName.includes(key)) {
          suggestions.push(...templates)
          break
        }
      }
    }

    // 添加基础模板
    suggestions.push(...this.baseTemplates)

    // 根据任务标题进行智能匹配
    if (title) {
      const titleSuggestions = this.getByTitle(title)
      suggestions.unshift(...titleSuggestions)
    }

    // 去重
    suggestions = [...new Set(suggestions)]

    // 过滤已存在的检查点
    const existingTitles = existingCheckpoints.map(item => item.title)
    suggestions = suggestions.filter(suggestion => !existingTitles.includes(suggestion))

    // 限制数量并返回
    return suggestions.slice(0, 6)
  }

  /**
   * 根据任务标题智能匹配建议
   * @param {string} title - 任务标题
   * @returns {Array} 匹配的建议列表
   */
  static getByTitle(title) {
    const titleLower = title.toLowerCase()
    const suggestions = []

    // 关键词匹配规则
    const keywordRules = {
      '复习': ['整理知识点', '制作复习卡片', '重点内容梳理'],
      '练习': ['完成练习题', '错题分析', '解题方法总结'],
      '背诵': ['分段记忆', '重复背诵', '默写检查'],
      '阅读': ['精读文本', '做读书笔记', '总结要点'],
      '写作': ['列写作提纲', '素材收集', '修改润色'],
      '实验': ['预习实验', '操作练习', '结果分析'],
      '计算': ['公式记忆', '计算练习', '验算检查']
    }

    for (const [keyword, templates] of Object.entries(keywordRules)) {
      if (titleLower.includes(keyword)) {
        suggestions.push(...templates)
      }
    }

    return suggestions
  }

  /**
   * 获取推荐的检查点数量
   * @param {number} taskComplexity - 任务复杂度 (1-5)
   * @returns {number} 推荐的检查点数量
   */
  static getRecommendedCount(taskComplexity = 3) {
    const countMap = {
      1: 2,  // 简单任务
      2: 3,  // 较简单任务
      3: 4,  // 中等任务
      4: 5,  // 较复杂任务
      5: 6   // 复杂任务
    }
    return countMap[taskComplexity] || 4
  }
}

module.exports = CheckpointSuggestions
