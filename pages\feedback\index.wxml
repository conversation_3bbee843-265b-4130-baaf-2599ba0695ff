<!--pages/feedback/index.wxml-->
<view class="container">
  <!-- 内容区域 -->
  <view class="content">

    <!-- 问题反馈 -->
    <van-cell-group title="反馈表单" custom-class="section-group">
      <van-cell title="反馈类型" custom-class="type-cell">
        <van-radio-group value="{{feedbackType}}" bind:change="selectFeedbackType">
          <view class="feedback-types">
            <van-radio name="bug" custom-class="type-radio">🐛 Bug反馈</van-radio>
            <van-radio name="feature" custom-class="type-radio">💡 功能建议</van-radio>
            <van-radio name="other" custom-class="type-radio">💬 其他</van-radio>
          </view>
        </van-radio-group>
      </van-cell>

      <van-field
        label="问题描述"
        type="textarea"
        placeholder="请详细描述您遇到的问题或建议（至少8个字）..."
        value="{{feedbackContent}}"
        bind:change="onFeedbackInput"
        maxlength="500"
        autosize
        required
        show-word-limit
        custom-class="feedback-field"
      />

      <van-field
        label="联系方式"
        placeholder="请留下您的联系方式，方便我们回复"
        value="{{contactInfo}}"
        bind:change="onContactInput"
        maxlength="50"
        custom-class="contact-field"
      />

      <view class="submit-section">
        <van-button
          type="primary"
          size="large"
          bind:click="submitFeedback"
          disabled="{{!canSubmit}}"
          custom-class="submit-button"
        >
          提交反馈
        </van-button>
      </view>
    </van-cell-group>

    <!-- 反馈历史 -->
    <van-cell-group title="我的反馈" custom-class="section-group" wx:if="{{feedbackHistory.length > 0}}">
      <view class="history-item" wx:for="{{feedbackHistory}}" wx:key="id">
        <view class="history-header">
          <text class="history-type">{{item.typeText}}</text>
          <text class="history-date">{{item.date}}</text>
        </view>
        <view class="history-content">
          <text class="history-text">{{item.displayContent}}</text>
          <text class="expand-link" wx:if="{{item.hasMore}}" bindtap="showFullContent" data-content="{{item.content}}">查看全部</text>
        </view>
        <view class="history-status">
          <van-tag type="{{item.status === 'pending' ? 'warning' : 'success'}}" size="mini">
            {{item.statusText}}
          </van-tag>
        </view>
      </view>
    </van-cell-group>

    <!-- 联系我们 -->
    <van-cell-group title="其他联系方式" custom-class="section-group">
      <van-cell
        title="邮箱反馈"
        label="<EMAIL>"
        is-link
        bind:click="copyContact"
        data-text="<EMAIL>"
        custom-class="contact-cell"
      >
        <view slot="icon" class="contact-icon">📧</view>
        <text slot="right-icon" class="contact-action">复制</text>
      </van-cell>

      <van-cell
        title="用户群反馈"
        label="加入用户交流群"
        is-link
        bind:click="joinGroup"
        custom-class="contact-cell"
      >
        <view slot="icon" class="contact-icon">👥</view>
        <text slot="right-icon" class="contact-action">加入</text>
      </van-cell>
    </van-cell-group>

  </view>
</view>
