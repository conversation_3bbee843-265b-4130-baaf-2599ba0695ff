// pages/join-study-group/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    inviteCode: '',
    inviteInfo: null,
    loading: false,
    joining: false,
    error: '',
    inputInviteCode: ''
  },

  async onLoad(options) {
    const { inviteCode } = options
    
    if (inviteCode) {
      this.setData({ inviteCode })
      await this.verifyInviteCode(inviteCode)
    } else {
      // 没有邀请码，显示手动输入界面
      this.setData({ loading: false })
    }
  },

  // 验证邀请码
  async verifyInviteCode(code) {
    if (!code) {
      this.setData({ 
        error: '邀请码不能为空',
        loading: false 
      })
      return
    }

    this.setData({ loading: true, error: '' })

    try {
      // 这里我们需要先获取邀请信息，而不是直接加入
      // 由于当前API设计，我们需要修改云函数来支持验证邀请码
      const result = await SmartApi.verifyInviteCode(code)
      
      if (result.success && result.data) {
        this.setData({
          inviteInfo: result.data,
          loading: false
        })
      } else {
        this.setData({
          error: result.error || '邀请码无效或已过期',
          loading: false
        })
      }
    } catch (error) {
      console.error('验证邀请码失败:', error)
      this.setData({
        error: '验证失败，请检查网络连接',
        loading: false
      })
    }
  },

  // 加入小组
  async onJoinGroup() {
    if (this.data.joining) return

    this.setData({ joining: true })

    try {
      const result = await SmartApi.joinStudyGroup(this.data.inviteCode)
      
      if (result.success) {
        wx.showToast({
          title: '加入成功！',
          icon: 'success'
        })

        // 通知首页刷新搭子列表
        const pages = getCurrentPages()
        const homePage = pages.find(page => page.route === 'pages/home/<USER>')
        if (homePage && homePage.loadStudyGroups) {
          homePage.loadStudyGroups()
        }

        // 跳转到小组详情页
        setTimeout(() => {
          wx.redirectTo({
            url: `/pages/study-group-detail/index?groupId=${result.data.groupId}`
          })
        }, 1500)
      } else {
        wx.showToast({
          title: result.error || '加入失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加入小组失败:', error)
      wx.showToast({
        title: '加入失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ joining: false })
    }
  },

  // 取消加入
  onCancel() {
    wx.navigateBack({
      fail: () => {
        wx.switchTab({
          url: '/pages/home/<USER>'
        })
      }
    })
  },

  // 返回首页
  onGoHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  // 邀请码输入
  onInviteCodeInput(e) {
    const value = e.detail.value.toUpperCase()
    this.setData({ inputInviteCode: value })
  },

  // 验证手动输入的邀请码
  async onVerifyCode() {
    const code = this.data.inputInviteCode
    if (!code || code.length !== 8) {
      wx.showToast({
        title: '请输入8位邀请码',
        icon: 'none'
      })
      return
    }

    this.setData({ inviteCode: code })
    await this.verifyInviteCode(code)
  }
})
