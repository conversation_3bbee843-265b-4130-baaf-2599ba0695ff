/**
 * 考试数据迁移脚本
 * 用于将现有的考试数据从旧格式迁移到新格式
 */

const ExamDataMigration = require('../utils/examDataMigration')
const ExamModel = require('../models/ExamModel')

class ExamDataMigrationScript {
  constructor() {
    this.migrationLog = []
    this.statistics = {
      total: 0,
      migrated: 0,
      success: 0,
      warnings: 0,
      errors: 0,
      skipped: 0
    }
  }

  /**
   * 执行数据迁移
   * @param {Array} examList - 考试数据列表
   * @returns {Object} 迁移结果
   */
  async migrate(examList) {
    console.log('开始执行考试数据迁移...')
    this.statistics.total = examList.length

    const migratedList = []
    
    for (let i = 0; i < examList.length; i++) {
      const exam = examList[i]
      const result = await this.migrateExam(exam, i)
      migratedList.push(result.data)
      
      // 更新统计信息
      if (result.migrated) {
        this.statistics.migrated++
        if (result.success) {
          this.statistics.success++
        }
        if (result.warnings.length > 0) {
          this.statistics.warnings++
        }
        if (result.errors.length > 0) {
          this.statistics.errors++
        }
      } else {
        this.statistics.skipped++
      }
    }

    const report = this.generateReport()
    console.log('数据迁移完成:', report)

    return {
      success: true,
      data: migratedList,
      report: report,
      log: this.migrationLog
    }
  }

  /**
   * 迁移单个考试数据
   * @param {Object} exam - 考试数据
   * @param {number} index - 索引
   * @returns {Object} 迁移结果
   */
  async migrateExam(exam, index) {
    const logEntry = {
      index,
      originalId: exam._id || exam.id,
      originalTitle: exam.name || exam.title,
      migrated: false,
      success: false,
      warnings: [],
      errors: [],
      timestamp: new Date().toISOString()
    }

    try {
      // 检查是否需要迁移
      if (!ExamDataMigration.needsMigration(exam)) {
        logEntry.message = '数据已是新格式，无需迁移'
        this.migrationLog.push(logEntry)
        return {
          data: exam,
          migrated: false,
          success: true,
          warnings: [],
          errors: []
        }
      }

      logEntry.migrated = true
      console.log(`迁移考试 ${index + 1}/${this.statistics.total}: ${exam.name || exam.title}`)

      // 执行迁移
      const migratedData = ExamDataMigration.migrateOldFormat(exam)
      
      // 验证迁移结果
      const validation = ExamDataMigration.validateMigration(exam, migratedData)
      logEntry.warnings = validation.warnings
      logEntry.errors = validation.errors
      logEntry.success = validation.success

      if (validation.success) {
        // 使用ExamModel进一步验证
        try {
          const examModel = new ExamModel(migratedData)
          logEntry.migratedTitle = examModel.data.title
          logEntry.message = '迁移成功'
          
          this.migrationLog.push(logEntry)
          return {
            data: examModel.data,
            migrated: true,
            success: true,
            warnings: validation.warnings,
            errors: validation.errors
          }
        } catch (modelError) {
          logEntry.errors.push(`ExamModel验证失败: ${modelError.message}`)
          logEntry.success = false
          logEntry.message = 'ExamModel验证失败，使用原始数据'
        }
      }

      // 如果验证失败，返回原始数据
      logEntry.message = '迁移失败，使用原始数据'
      this.migrationLog.push(logEntry)
      return {
        data: exam,
        migrated: true,
        success: false,
        warnings: validation.warnings,
        errors: validation.errors
      }

    } catch (error) {
      logEntry.errors.push(`迁移异常: ${error.message}`)
      logEntry.success = false
      logEntry.message = '迁移异常，使用原始数据'
      this.migrationLog.push(logEntry)

      return {
        data: exam,
        migrated: true,
        success: false,
        warnings: [],
        errors: [error.message]
      }
    }
  }

  /**
   * 生成迁移报告
   * @returns {Object} 迁移报告
   */
  generateReport() {
    const report = {
      summary: {
        ...this.statistics,
        successRate: this.statistics.migrated > 0 ? 
          Math.round((this.statistics.success / this.statistics.migrated) * 100) : 0
      },
      details: {
        migratedExams: this.migrationLog.filter(log => log.migrated),
        successfulMigrations: this.migrationLog.filter(log => log.success),
        failedMigrations: this.migrationLog.filter(log => log.migrated && !log.success),
        warningMigrations: this.migrationLog.filter(log => log.warnings.length > 0),
        errorMigrations: this.migrationLog.filter(log => log.errors.length > 0)
      },
      recommendations: this.generateRecommendations()
    }

    return report
  }

  /**
   * 生成迁移建议
   * @returns {Array} 建议列表
   */
  generateRecommendations() {
    const recommendations = []

    if (this.statistics.errors > 0) {
      recommendations.push({
        type: 'error',
        message: `有 ${this.statistics.errors} 个考试迁移失败，建议检查数据格式`,
        action: '查看错误日志，手动修正数据'
      })
    }

    if (this.statistics.warnings > 0) {
      recommendations.push({
        type: 'warning',
        message: `有 ${this.statistics.warnings} 个考试迁移有警告，建议检查`,
        action: '查看警告信息，确认数据正确性'
      })
    }

    const multiSubjectMigrations = this.migrationLog.filter(log => 
      log.warnings.some(warning => warning.includes('多个科目'))
    )

    if (multiSubjectMigrations.length > 0) {
      recommendations.push({
        type: 'info',
        message: `有 ${multiSubjectMigrations.length} 个考试的多个科目已合并为单个科目`,
        action: '如需保留多个科目，请手动调整'
      })
    }

    if (this.statistics.success === this.statistics.migrated) {
      recommendations.push({
        type: 'success',
        message: '所有需要迁移的考试都已成功迁移',
        action: '可以安全使用新的考试模型'
      })
    }

    return recommendations
  }

  /**
   * 导出迁移日志
   * @param {string} format - 导出格式 ('json' | 'csv')
   * @returns {string} 导出内容
   */
  exportLog(format = 'json') {
    if (format === 'json') {
      return JSON.stringify({
        timestamp: new Date().toISOString(),
        statistics: this.statistics,
        log: this.migrationLog
      }, null, 2)
    }

    if (format === 'csv') {
      const headers = ['Index', 'Original ID', 'Original Title', 'Migrated Title', 'Success', 'Warnings', 'Errors', 'Message']
      const rows = this.migrationLog.map(log => [
        log.index,
        log.originalId,
        log.originalTitle,
        log.migratedTitle || '',
        log.success,
        log.warnings.join('; '),
        log.errors.join('; '),
        log.message
      ])

      return [headers, ...rows].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n')
    }

    return ''
  }

  /**
   * 验证迁移完整性
   * @param {Array} originalList - 原始数据
   * @param {Array} migratedList - 迁移后数据
   * @returns {Object} 验证结果
   */
  validateIntegrity(originalList, migratedList) {
    const validation = {
      success: true,
      issues: []
    }

    // 检查数量一致性
    if (originalList.length !== migratedList.length) {
      validation.success = false
      validation.issues.push(`数据数量不一致: 原始${originalList.length}条，迁移后${migratedList.length}条`)
    }

    // 检查ID一致性
    for (let i = 0; i < Math.min(originalList.length, migratedList.length); i++) {
      const originalId = originalList[i]._id || originalList[i].id
      const migratedId = migratedList[i]._id || migratedList[i].id

      if (originalId !== migratedId) {
        validation.issues.push(`第${i + 1}条数据ID不一致: ${originalId} -> ${migratedId}`)
      }
    }

    // 检查必要字段
    migratedList.forEach((exam, index) => {
      if (!exam.title) {
        validation.success = false
        validation.issues.push(`第${index + 1}条数据缺少title字段`)
      }
      if (!exam.examDate) {
        validation.success = false
        validation.issues.push(`第${index + 1}条数据缺少examDate字段`)
      }
    })

    return validation
  }
}

module.exports = ExamDataMigrationScript
