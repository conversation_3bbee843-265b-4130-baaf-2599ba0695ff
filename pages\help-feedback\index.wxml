<!--pages/help-feedback/index.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <text class="page-title">💡 帮助与反馈</text>
    <text class="page-subtitle">使用帮助和问题反馈</text>
  </view>

  <!-- 内容区域 -->
  <view class="content">

    <!-- 常见问题 -->
    <view class="section">
      <text class="section-title">❓ 常见问题</text>

      <view class="faq-list">
        <view class="faq-item" wx:for="{{faqList}}" wx:key="id" bindtap="toggleFaq" data-id="{{item.id}}">
          <view class="faq-question">
            <text class="question-text">{{item.question}}</text>
            <text class="expand-icon {{item.expanded ? 'expanded' : ''}}">▼</text>
          </view>
          <view class="faq-answer {{item.expanded ? 'show' : ''}}">
            <text class="answer-text">{{item.answer}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用指南 -->
    <view class="section">
      <text class="section-title">📖 使用指南</text>

      <view class="guide-list">
        <view class="guide-item" wx:for="{{guideList}}" wx:key="id" bindtap="openGuide" data-guide="{{item}}">
          <view class="guide-icon">{{item.icon}}</view>
          <view class="guide-content">
            <text class="guide-title">{{item.title}}</text>
            <text class="guide-desc">{{item.description}}</text>
          </view>
          <text class="guide-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 问题反馈 -->
    <view class="section">
      <text class="section-title">📝 问题反馈</text>

      <view class="feedback-form">
        <view class="form-group">
          <text class="form-label">反馈类型</text>
          <view class="feedback-types">
            <button class="type-btn {{feedbackType === 'bug' ? 'active' : ''}}"
                    bindtap="selectFeedbackType"
                    data-type="bug">
              🐛 Bug反馈
            </button>
            <button class="type-btn {{feedbackType === 'feature' ? 'active' : ''}}"
                    bindtap="selectFeedbackType"
                    data-type="feature">
              💡 功能建议
            </button>
            <button class="type-btn {{feedbackType === 'other' ? 'active' : ''}}"
                    bindtap="selectFeedbackType"
                    data-type="other">
              💬 其他
            </button>
          </view>
        </view>

        <view class="form-group">
          <text class="form-label">问题描述 *</text>
          <textarea class="feedback-textarea"
                    placeholder="请详细描述您遇到的问题或建议..."
                    value="{{feedbackContent}}"
                    bindinput="onFeedbackInput"
                    maxlength="500"
                    auto-height/>
          <text class="char-count">{{feedbackContent.length}}/500</text>
        </view>

        <view class="form-group">
          <text class="form-label">联系方式（可选）</text>
          <input class="feedback-input"
                 placeholder="请留下您的联系方式，方便我们回复"
                 value="{{contactInfo}}"
                 bindinput="onContactInput"
                 maxlength="50"/>
        </view>

        <button class="submit-btn {{canSubmit ? 'enabled' : 'disabled'}}"
                bindtap="submitFeedback"
                disabled="{{!canSubmit}}">
          提交反馈
        </button>
      </view>
    </view>

    <!-- 联系我们 -->
    <view class="section">
      <text class="section-title">📞 联系我们</text>

      <view class="contact-list">
        <view class="contact-item" bindtap="copyContact" data-text="<EMAIL>">
          <view class="contact-icon">📧</view>
          <view class="contact-content">
            <text class="contact-title">邮箱支持</text>
            <text class="contact-desc"><EMAIL></text>
          </view>
          <text class="contact-action">复制</text>
        </view>

        <view class="contact-item" bindtap="joinGroup">
          <view class="contact-icon">👥</view>
          <view class="contact-content">
            <text class="contact-title">用户群</text>
            <text class="contact-desc">加入用户交流群</text>
          </view>
          <text class="contact-action">加入</text>
        </view>

        <view class="contact-item" bindtap="openGithub">
          <view class="contact-icon">💻</view>
          <view class="contact-content">
            <text class="contact-title">开源项目</text>
            <text class="contact-desc">GitHub 项目地址</text>
          </view>
          <text class="contact-action">访问</text>
        </view>
      </view>
    </view>

    <!-- 应用信息 -->
    <view class="section">
      <text class="section-title">ℹ️ 应用信息</text>

      <view class="app-info">
        <view class="info-item">
          <text class="info-label">应用版本</text>
          <text class="info-value">v2.0.0</text>
        </view>
        <view class="info-item">
          <text class="info-label">开发团队</text>
          <text class="info-value">Augment Code</text>
        </view>
        <view class="info-item">
          <text class="info-label">更新时间</text>
          <text class="info-value">2024-06-28</text>
        </view>
      </view>
    </view>

  </view>
</view>

<!-- 使用指南弹窗 -->
<view class="guide-modal" wx:if="{{showGuideModal}}" bindtap="closeGuideModal">
  <view class="guide-modal-content" catchtap="stopPropagation">
    <view class="guide-modal-header">
      <text class="guide-modal-title">{{currentGuide.title}}</text>
      <text class="guide-modal-close" bindtap="closeGuideModal">×</text>
    </view>
    <view class="guide-modal-body">
      <text class="guide-modal-text">{{currentGuide.content}}</text>
    </view>
  </view>
</view>