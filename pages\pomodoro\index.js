// pages/pomodoro/index.js - Vant重构版本（清理版）
const SmartApi = require('../../utils/smartApi')

// 简化的Vant API助手
const VantToast = {
  success: (msg) => wx.showToast({ title: msg, icon: 'success' }),
  error: (msg) => wx.showToast({ title: msg, icon: 'error' }),
  info: (msg) => wx.showToast({ title: msg, icon: 'none' })
}

const VantDialog = {
  alert: ({ title, content, onConfirm }) => wx.showModal({
    title, content, showCancel: false,
    success: (res) => res.confirm && onConfirm && onConfirm()
  }),
  confirm: ({ title, content, onConfirm, onCancel }) => wx.showModal({
    title, content,
    success: (res) => {
      if (res.confirm && onConfirm) {
        onConfirm()
      } else if (res.cancel && onCancel) {
        onCancel()
      }
    }
  })
}

const VantActionSheet = {
  showSheet: ({ titleText, buttons, onItemClick }) => {
    const itemList = buttons.map(btn => btn.text)
    wx.showActionSheet({
      itemList,
      success: (res) => onItemClick && onItemClick(buttons[res.tapIndex], res.tapIndex)
    })
  }
}

Page({
  data: {
    // 模式状态
    focusMode: false,
    studyMode: 'quick', // quick, task

    // 计时器状态
    timerState: 'work', // work, shortBreak, longBreak
    timerStateText: '专注时间',
    sessionTypeText: '专注',
    sessionIndicator: '●●●●○○○○',
    isRunning: false,
    isPaused: false,

    // 时间设置
    workDuration: 25, // 分钟
    shortBreakDuration: 5,
    longBreakDuration: 15,

    // 当前时间
    currentTime: 25 * 60, // 秒
    totalTime: 25 * 60,
    displayTime: '25:00',
    progressDegree: 0,
    timerColor: '#FF6B6B',
    timerColorVar: '--timer-work-color',

    // 手势交互
    touchStartY: 0,
    touchStartTime: 0,
    isGesturing: false,
    showGestureHint: false,
    gestureHintText: '',
    timeNodeHighlight: false,

    // 时间节点
    timeNodes: [
      { value: 5, angle: -90 },
      { value: 10, angle: -45 },
      { value: 15, angle: 0 },
      { value: 20, angle: 45 },
      { value: 25, angle: 90 },
      { value: 30, angle: 135 },
      { value: 45, angle: 180 },
      { value: 60, angle: 225 }
    ],

    // 任务相关
    selectedTask: null,
    availableTasks: [],
    taskOptions: [], // 用于action-sheet的任务选项

    // 统计数据
    completedSessions: 0,
    todayFocusTime: 0,
    todayCompletedTasks: 0,

    // 扩展统计数据
    currentStatsTab: 'today', // today, week, month
    statsData: {
      today: {
        focusTime: 0,
        sessions: 0,
        tasks: 0,
        efficiency: 0
      },
      yesterday: {
        focusTime: 0,
        sessions: 0,
        tasks: 0,
        efficiency: 0
      },
      week: {
        focusTime: 0,
        sessions: 0,
        tasks: 0,
        efficiency: 0
      }
    },

    // 统计卡片数据
    statCards: [
      {
        id: 'focusTime',
        icon: '⏰',
        label: '专注时长',
        value: '0分钟',
        bgColor: '#FF6B6B',
        trend: null,
        clickable: true
      },
      {
        id: 'sessions',
        icon: '🍅',
        label: '完成番茄',
        value: '0个',
        bgColor: '#52C41A',
        trend: null,
        clickable: true
      },
      {
        id: 'tasks',
        icon: '📝',
        label: '完成任务',
        value: '0个',
        bgColor: '#1890FF',
        trend: null,
        clickable: true
      },
      {
        id: 'efficiency',
        icon: '📊',
        label: '学习效率',
        value: '0%',
        bgColor: '#722ED1',
        trend: null,
        clickable: true
      }
    ],

    // 趋势图表数据
    trendData: [],
    showTrendChart: false,

    // 专注模式增强
    lastTapTime: 0,
    focusMotivation: '',
    focusTaskDesc: '专注复习中...',
    taskProgress: 0,
    taskProgressText: '',
    currentSessionTime: 0,
    showMilestone: false,
    milestoneText: '',
    milestoneAnimation: false,
    showCelebration: false,
    celebrationTitle: '',
    celebrationMessage: '',
    celebrationParticles: [],
    progressRings: [],

    // 激励文案库
    motivationTexts: [
      '专注当下，成就未来 ✨',
      '每一分钟的专注都在积累成功 🌟',
      '保持专注，你正在变得更强 💪',
      '专注是通往成功的唯一道路 🎯',
      '深度专注，深度成长 🌱',
      '专注让时间更有价值 ⏰',
      '在专注中找到内心的平静 🧘‍♀️',
      '专注是最好的投资 💎'
    ],

    // 里程碑配置
    milestones: [
      { time: 5, title: '起步里程碑', message: '很好！已经专注5分钟了' },
      { time: 10, title: '坚持里程碑', message: '太棒了！坚持了10分钟' },
      { time: 15, title: '专注里程碑', message: '优秀！已经专注15分钟' },
      { time: 20, title: '深度里程碑', message: '惊人！深度专注20分钟' },
      { time: 25, title: '完美里程碑', message: '完美！完成一个完整番茄钟' },
      { time: 30, title: '超越里程碑', message: '超越自我！专注30分钟' },
      { time: 45, title: '卓越里程碑', message: '卓越表现！专注45分钟' },
      { time: 60, title: '大师里程碑', message: '大师级专注！整整1小时' }
    ],

    // 音频设置
    selectedBgSound: 'silent',
    bgVolume: 50,
    currentSoundLabel: '静音模式',
    backgroundSounds: [
      { id: 'silent', name: '静音', icon: '🔇', description: '无背景音', category: 'basic' },
      { id: 'rain', name: '雨声', icon: '🌧️', description: '适合深度思考', category: 'nature' },
      { id: 'ocean', name: '海浪', icon: '🌊', description: '适合放松复习', category: 'nature' },
      { id: 'forest', name: '森林', icon: '🌲', description: '适合长时间专注', category: 'nature' },
      { id: 'thunder', name: '雷雨', icon: '⛈️', description: '激发专注力', category: 'nature' },
      { id: 'birds', name: '鸟鸣', icon: '🐦', description: '清晨专注', category: 'nature' },
      { id: 'cafe', name: '咖啡厅', icon: '☕', description: '适合轻松复习', category: 'ambient' },
      { id: 'library', name: '图书馆', icon: '📚', description: '学习氛围', category: 'ambient' },
      { id: 'fireplace', name: '壁炉', icon: '🔥', description: '温暖舒适', category: 'ambient' },
      { id: 'city', name: '城市', icon: '🏙️', description: '都市节奏', category: 'ambient' },
      { id: 'whitenoise', name: '白噪音', icon: '🎵', description: '屏蔽外界干扰', category: 'focus' },
      { id: 'pinknoise', name: '粉红噪音', icon: '🎶', description: '深度专注', category: 'focus' },
      { id: 'brownnoise', name: '棕色噪音', icon: '🎼', description: '放松心情', category: 'focus' },
      { id: 'binaural', name: '双耳节拍', icon: '🧠', description: '提升专注力', category: 'focus' }
    ],

    // 音频分类
    soundCategories: [
      { id: 'basic', name: '基础', icon: '🔇' },
      { id: 'nature', name: '自然', icon: '🌿' },
      { id: 'ambient', name: '环境', icon: '🏠' },
      { id: 'focus', name: '专注', icon: '🎯' }
    ],

    notificationSounds: [
      { id: 'start', name: '开始提示', enabled: true },
      { id: 'pause', name: '暂停提示', enabled: true },
      { id: 'complete', name: '完成提示', enabled: true },
      { id: 'warning', name: '时间警告', enabled: true }
    ],

    // 其他设置
    enableVibration: true,
    enableNotification: true,
    autoStartBreak: false,
    autoStartWork: false,
    longBreakInterval: 4,
    currentSession: 1,

    // 弹窗状态
    showSoundModal: false,
    showSettingsModal: false,
    showTaskSelector: false,
    showDropdown: false, // 控制下拉菜单显示

    // 设置界面状态
    activeSettingsTab: ['time'], // van-collapse需要数组格式
    showPresetModal: false,
    showRecommendation: false,
    recommendationText: '',

    // 配置预设方案
    presetConfigs: [
      {
        id: 'student',
        name: '学生模式',
        icon: '🎓',
        description: '适合学生学习，短时高效',
        config: {
          workDuration: 25,
          shortBreakDuration: 5,
          longBreakDuration: 15,
          longBreakInterval: 4,
          enableVibration: true,
          enableNotification: true,
          autoStartBreak: false,
          autoStartWork: false,
          selectedBgSound: 'forest'
        }
      },
      {
        id: 'work',
        name: '工作模式',
        icon: '💼',
        description: '适合职场工作，长时专注',
        config: {
          workDuration: 45,
          shortBreakDuration: 10,
          longBreakDuration: 30,
          longBreakInterval: 3,
          enableVibration: false,
          enableNotification: true,
          autoStartBreak: true,
          autoStartWork: false,
          selectedBgSound: 'whitenoise'
        }
      },
      {
        id: 'deep',
        name: '深度专注',
        icon: '🧠',
        description: '适合深度思考，超长专注',
        config: {
          workDuration: 60,
          shortBreakDuration: 15,
          longBreakDuration: 45,
          longBreakInterval: 2,
          enableVibration: false,
          enableNotification: false,
          autoStartBreak: false,
          autoStartWork: false,
          selectedBgSound: 'binaural'
        }
      },
      {
        id: 'relax',
        name: '轻松模式',
        icon: '😌',
        description: '适合轻松学习，灵活安排',
        config: {
          workDuration: 20,
          shortBreakDuration: 8,
          longBreakDuration: 20,
          longBreakInterval: 3,
          enableVibration: true,
          enableNotification: true,
          autoStartBreak: true,
          autoStartWork: true,
          selectedBgSound: 'ocean'
        }
      }
    ],

    // 使用统计（用于智能推荐）
    usageStats: {
      totalSessions: 0,
      averageWorkDuration: 25,
      preferredBreakDuration: 5,
      mostUsedSound: 'silent',
      peakHours: [],
      weeklyPattern: []
    },



    // 用户信息
    userInfo: {},
    userSignature: ''
  },

  async onLoad() {
    // 检查登录状态
    const app = getApp()
    if (!app.globalData.userInfo || !app.globalData.openid) {
      console.log('用户未登录，跳转到登录页面')
      wx.navigateTo({
        url: '/pages/login/index'
      })
      return
    }

    this.initPage()
    this.loadAvailableTasks()
    this.loadUserSettings()
    this.loadUserSignature()
    this.initAudioSystem()
    this.initStatsData()
  },

  onShow() {
    // 更新TabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      })
    }

    this.refreshTaskList()
    this.checkAndLoadFromCache()

    // 重新加载用户签名（从设置页面返回时更新）
    this.loadUserSignature()
  },



  onHide() {
    this.pauseBackgroundAudio()
  },

  onUnload() {
    this.cleanup()
  },

  // 初始化页面
  initPage() {
    this.setData({
      displayTime: this.formatTime(this.data.currentTime),
      sessionTypeText: '专注',
      progressDegree: 0
    })
  },

  // 更新声音标签
  updateSoundLabel() {
    const sound = this.data.backgroundSounds.find(s => s.id === this.data.selectedBgSound)
    this.setData({
      currentSoundLabel: sound ? sound.name : '静音模式'
    })
  },

  // 加载用户设置
  loadUserSettings() {
    try {
      const settings = wx.getStorageSync('pomodoroSettings')
      if (settings) {
        this.setData({
          workDuration: settings.workDuration || 25,
          shortBreakDuration: settings.shortBreakDuration || 5,
          longBreakDuration: settings.longBreakDuration || 15,
          selectedBgSound: settings.selectedBgSound || 'silent',
          bgVolume: settings.bgVolume || 50,
          enableVibration: settings.enableVibration !== false,
          enableNotification: settings.enableNotification !== false
        })
        this.updateCurrentTime()
        this.updateSoundLabel()
      }
    } catch (error) {
      console.error('加载用户设置失败:', error)
    }
  },

  // 保存用户设置
  saveUserSettings() {
    try {
      const settings = {
        workDuration: this.data.workDuration,
        shortBreakDuration: this.data.shortBreakDuration,
        longBreakDuration: this.data.longBreakDuration,
        selectedBgSound: this.data.selectedBgSound,
        bgVolume: this.data.bgVolume,
        enableVibration: this.data.enableVibration,
        enableNotification: this.data.enableNotification
      }
      wx.setStorageSync('pomodoroSettings', settings)
    } catch (error) {
      console.error('保存用户设置失败:', error)
    }
  },

  // 加载用户签名
  loadUserSignature() {
    try {
      const app = getApp()
      const userInfo = app.globalData.userInfo || wx.getStorageSync('kaoba_user_info') || {}

      this.setData({
        userInfo: userInfo,
        userSignature: userInfo.signature || ''
      })
    } catch (error) {
      console.error('加载用户签名失败:', error)
    }
  },

  // 加载可用任务
  async loadAvailableTasks() {
    try {
      const result = await SmartApi.getTasks()
      if (result.success) {
        // 只获取未完成的任务
        const incompleteTasks = result.data.filter(task => !task.completed)
        
        // 按照时间进行排序，时间越早的排在越前面
        const sortedTasks = incompleteTasks.sort((a, b) => {
          // 构造完整的时间字符串用于比较
          const timeA = this.getTaskDateTime(a)
          const timeB = this.getTaskDateTime(b)
          
          // 按时间升序排列（早的在前）
          return timeA - timeB
        })
        
        this.setData({ availableTasks: sortedTasks })
        console.log('加载并排序任务:', sortedTasks.length, '个任务')
      }
    } catch (error) {
      console.error('加载任务失败:', error)
    }
  },

  // 获取任务的完整日期时间用于排序
  getTaskDateTime(task) {
    try {
      // 使用截止日期和时间
      const dateStr = task.dueDate || task.date || new Date().toISOString().split('T')[0]
      const timeStr = task.dueTime || task.time || '23:59'
      
      // 构造完整的日期时间字符串
      const dateTimeStr = `${dateStr}T${timeStr}:00`
      const dateTime = new Date(dateTimeStr)
      
      // 如果解析失败，返回一个很远的未来时间
      if (isNaN(dateTime.getTime())) {
        return new Date('2099-12-31T23:59:59')
      }
      
      return dateTime
    } catch (error) {
      console.error('解析任务时间失败:', error, task)
      return new Date('2099-12-31T23:59:59')
    }
  },

  // 刷新任务列表
  refreshTaskList() {
    this.loadAvailableTasks()
  },

  // 检查并加载缓存中的任务信息
  checkAndLoadFromCache() {
    try {
      const cachedTaskId = wx.getStorageSync('currentTaskId')
      const cachedExamId = wx.getStorageSync('currentExamId')
      const cachedTaskTitle = wx.getStorageSync('currentTaskTitle')
      const cachedExamName = wx.getStorageSync('currentExamName')

      if (cachedTaskId && cachedTaskTitle) {
        // 构造任务对象
        const cachedTask = {
          id: cachedTaskId,
          title: cachedTaskTitle,
          examId: cachedExamId,
          examName: cachedExamName
        }

        // 自动选择这个任务
        this.setData({
          selectedTask: cachedTask,
          studyMode: 'task'
        })

        VantToast.success(`已自动选择任务：${cachedTaskTitle}`)

        // 清除缓存，避免重复使用
        wx.removeStorageSync('currentTaskId')
        wx.removeStorageSync('currentExamId')
        wx.removeStorageSync('currentTaskTitle')
        wx.removeStorageSync('currentExamName')

        console.log('已从缓存加载任务:', cachedTask)
      }
    } catch (error) {
      console.error('加载缓存任务失败:', error)
    }
  },

  // 初始化音频系统
  initAudioSystem() {
    try {
      this.backgroundAudio = wx.createInnerAudioContext()
      this.backgroundAudio.loop = true
      this.backgroundAudio.volume = this.data.bgVolume / 100

      this.backgroundAudio.onError((res) => {
        console.error('背景音频播放失败:', res)
      })
    } catch (error) {
      console.error('初始化音频系统失败', error)
    }
  },

  // 显示任务选择器
  showTaskSelector() {
    // 隐藏自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().hide()
    }

    this.setData({
      showTaskSelector: true
    })
  },

  // 隐藏任务选择器
  hideTaskSelector() {
    this.setData({
      showTaskSelector: false
    })
    // 显示自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().show()
    }
  },

  // 快速模式切换
  toggleQuickMode(e) {
    const isQuick = e.detail
    if (isQuick) {
      this.setData({
        selectedTask: null,
        studyMode: 'quick'
      })
      VantToast.success('已切换到快速专注模式')
    } else {
      // 如果有可用任务，选择第一个；否则保持快速模式
      if (this.data.availableTasks.length > 0) {
        const firstTask = this.data.availableTasks[0]
        this.setData({
          selectedTask: firstTask,
          studyMode: 'task'
        })
        VantToast.success(`已选择任务：${firstTask.title}`)
      } else {
        // 没有可用任务，保持快速模式
        this.setData({
          studyMode: 'quick'
        })
        VantToast.info('暂无可用任务，保持快速专注模式')
      }
    }
  },

  // 选择快速专注模式
  selectQuickMode() {
    this.setData({
      selectedTask: null,
      studyMode: 'quick',
      showTaskSelector: false
    })
    // 显示自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().show()
    }
    VantToast.success('已选择快速专注模式')
  },

  // 选择具体任务
  selectTaskItem(e) {
    const task = e.currentTarget.dataset.task
    if (task) {
      this.setData({
        selectedTask: task,
        studyMode: 'task',
        showTaskSelector: false
      })
      // 显示自定义tabBar
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().show()
      }
      VantToast.success(`已选择任务：${task.title}`)
    }
  },

  // 跳转到任务中心
  goToTaskCenter() {
    this.hideTaskSelector()
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  // 选择任务选项
  selectTaskOption(task) {
    if (!task) {
      // 快速专注模式
      this.setData({
        selectedTask: null,
        studyMode: 'quick'
      })
      VantToast.success('已选择快速专注模式')
    } else {
      // 任务模式
      this.setData({
        selectedTask: task,
        studyMode: 'task'
      })
      VantToast.success(`已选择任务：${task.title}`)
    }
  },

  // 进入专注模式
  enterFocusMode() {
    // 设置屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: true
    })

    // 随机选择激励文案
    const randomMotivation = this.data.motivationTexts[Math.floor(Math.random() * this.data.motivationTexts.length)]

    // 初始化专注模式数据
    this.setData({
      focusMode: true,
      focusMotivation: randomMotivation,
      currentSessionTime: 0,
      taskProgress: 0,
      taskProgressText: '开始专注复习',
      progressRings: this.generateProgressRings()
    })

    // 更新任务描述
    this.updateFocusTaskDesc()

    VantToast.success('已进入深度专注模式')
    wx.vibrateShort()
  },

  // 退出专注模式
  exitFocusMode() {
    // 取消屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })

    // 重置专注模式状态
    this.setData({
      focusMode: false,
      showMilestone: false,
      milestoneAnimation: false,
      showCelebration: false,
      focusMotivation: ''
    })

    VantToast.success('已退出专注模式')
  },

  // 专注模式点击处理（双击退出）
  onFocusModeTap() {
    const currentTime = Date.now()
    const timeDiff = currentTime - this.data.lastTapTime

    if (timeDiff < 300) { // 300ms内的双击
      // 双击退出确认
      VantDialog.confirm({
        title: '退出专注模式',
        content: '确定要退出深度专注模式吗？',
        onConfirm: () => {
          this.exitFocusMode()
        },
        onCancel: () => {
          // 用户选择继续专注
          VantToast.info('继续保持专注！')
        }
      })
    }

    this.setData({
      lastTapTime: currentTime
    })
  },

  // 更新专注任务描述
  updateFocusTaskDesc() {
    if (!this.data.selectedTask) return

    const progress = Math.floor((this.data.totalTime - this.data.currentTime) / this.data.totalTime * 100)
    const descriptions = [
      '深度专注复习中...',
      '专注学习，收获成长',
      '保持专注，追求卓越',
      '专注当下，成就未来'
    ]

    const randomDesc = descriptions[Math.floor(Math.random() * descriptions.length)]

    this.setData({
      focusTaskDesc: randomDesc,
      taskProgress: progress,
      taskProgressText: `进度 ${progress}%`
    })
  },

  // 生成进度环效果
  generateProgressRings() {
    const rings = []
    for (let i = 0; i < 3; i++) {
      rings.push({
        delay: i * 0.5,
        opacity: 1 - i * 0.3
      })
    }
    return rings
  },

  // 计时器核心功能
  toggleTimer() {
    if (this.data.isRunning) {
      this.pauseTimer()
    } else {
      this.startTimer()
    }
  },

  // 开始计时器
  startTimer() {
    this.setData({
      isRunning: true,
      isPaused: false
    })

    VantToast.success('开始专注')

    // 开始背景音
    if (this.data.selectedBgSound !== 'silent') {
      this.playBackgroundSound(this.data.selectedBgSound)
    }

    // 启动计时器
    this.timer = setInterval(() => {
      this.updateTimer()
    }, 1000)

    // 保持屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: true
    })
  },

  // 暂停计时器
  pauseTimer() {
    this.setData({
      isRunning: false,
      isPaused: true
    })

    VantToast.info('已暂停')

    // 暂停背景音
    this.pauseBackgroundAudio()

    // 清除计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 取消屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })
  },

  // 停止计时器
  stopTimer() {
    VantDialog.confirm({
      title: '确认停止',
      content: '确定要停止当前的专注会话吗？',
      onConfirm: () => {
        this.resetTimer()
        VantToast.info('已停止专注')
      }
    })
  },

  // 重置计时器
  resetTimer() {
    this.setData({
      isRunning: false,
      isPaused: false
    })

    // 停止背景音
    this.stopBackgroundAudio()

    // 清除计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 重置时间显示
    this.updateCurrentTime()
    this.setData({
      displayTime: this.formatTime(this.data.currentTime),
      progressDegree: 0
    })

    // 取消屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })

    // 退出专注模式
    if (this.data.focusMode) {
      this.exitFocusMode()
    }

    // 如果有选中的任务，询问是否要标记为完成
    if (this.data.selectedTask && this.data.studyMode === 'task') {
      VantDialog.confirm({
        title: '任务完成',
        content: '您已结束专注，是否要标记任务为完成？',
        onConfirm: () => {
          this.updateTaskProgress()
        }
      })
    }
  },

  // 更新计时器
  updateTimer() {
    let currentTime = this.data.currentTime - 1

    if (currentTime <= 0) {
      // 时间到了
      this.completeSession()
      return
    }

    // 计算已专注时间
    const elapsedTime = this.data.totalTime - currentTime
    const elapsedMinutes = Math.floor(elapsedTime / 60)
    const minutes = Math.floor(currentTime / 60)
    const seconds = currentTime % 60

    // 更新当前会话时长
    this.setData({
      currentSessionTime: elapsedMinutes
    })

    // 里程碑检测
    this.checkMilestones(elapsedMinutes)

    // 时间节点提示（5分钟、10分钟、15分钟等关键节点）
    if (seconds === 0 && [5, 10, 15, 20, 25, 30].includes(minutes)) {
      // 时间节点高亮效果
      this.setData({ timeNodeHighlight: true })
      setTimeout(() => {
        this.setData({ timeNodeHighlight: false })
      }, 500)
      wx.vibrateShort()
    }

    // 时间警告（最后1分钟）
    if (currentTime === 60) {
      wx.vibrateShort()
    }

    // 更新显示
    const progress = ((this.data.totalTime - currentTime) / this.data.totalTime) * 360
    this.setData({
      currentTime: currentTime,
      displayTime: this.formatTime(currentTime),
      progressDegree: progress
    })

    // 更新专注模式任务进度
    if (this.data.focusMode) {
      this.updateFocusTaskDesc()
    }
  },

  // 检查里程碑
  checkMilestones(elapsedMinutes) {
    const milestone = this.data.milestones.find(m => m.time === elapsedMinutes)

    if (milestone && !this.data.showMilestone) {
      this.triggerMilestone(milestone)
    }
  },

  // 触发里程碑庆祝
  triggerMilestone(milestone) {
    // 显示里程碑提示
    this.setData({
      showMilestone: true,
      milestoneText: milestone.message,
      milestoneAnimation: true
    })

    // 触发庆祝动画
    this.showCelebrationAnimation(milestone.title, milestone.message)

    // 震动反馈
    wx.vibrateShort()

    // 3秒后隐藏里程碑提示
    setTimeout(() => {
      this.setData({
        showMilestone: false,
        milestoneAnimation: false
      })
    }, 3000)
  },

  // 显示庆祝动画
  showCelebrationAnimation(title, message) {
    // 生成粒子效果
    const particles = []
    for (let i = 0; i < 20; i++) {
      particles.push({
        x: Math.random() * 100,
        y: Math.random() * 100,
        delay: Math.random() * 2
      })
    }

    this.setData({
      showCelebration: true,
      celebrationTitle: title,
      celebrationMessage: message,
      celebrationParticles: particles
    })

    // 5秒后隐藏庆祝动画
    setTimeout(() => {
      this.setData({
        showCelebration: false
      })
    }, 5000)
  },

  // 音量控制
  onVolumeChange(e) {
    const volume = e.detail
    this.setData({
      bgVolume: volume
    })

    // 更新背景音音量
    if (this.backgroundAudio) {
      this.backgroundAudio.volume = volume / 100
    }

    this.saveUserSettings()
  },

  // 显示音效选择器
  showSoundSelector() {
    this.setData({
      showSoundModal: true
    })
  },

  // 选择背景音效
  selectBackgroundSound(e) {
    const soundId = e.currentTarget.dataset.sound
    const sound = this.data.backgroundSounds.find(s => s.id === soundId)

    if (sound) {
      this.setData({
        selectedBgSound: soundId,
        currentSoundLabel: sound.name,
        showSoundModal: false
      })

      // 如果正在播放，切换音效
      if (this.data.isRunning && soundId !== 'silent') {
        this.stopBackgroundAudio()
        setTimeout(() => {
          this.playBackgroundSound(soundId)
        }, 100)
      }

      this.saveUserSettings()
      VantToast.success(`已切换到${sound.name}`)
    }
  },

  // 增强的背景音播放
  playBackgroundSound(soundId) {
    if (soundId === 'silent') return

    try {
      // 停止当前播放的音频
      this.stopBackgroundAudio()

      // 创建新的音频实例
      this.backgroundAudio = wx.createInnerAudioContext()
      this.backgroundAudio.src = `/sounds/${soundId}.mp3`
      this.backgroundAudio.loop = true
      this.backgroundAudio.volume = this.data.bgVolume / 100

      // 音频事件监听
      this.backgroundAudio.onPlay(() => {
        console.log(`开始播放背景音: ${soundId}`)
      })

      this.backgroundAudio.onError((error) => {
        console.error('背景音播放失败:', error)
        VantToast.fail('音效播放失败，请检查网络')
      })

      this.backgroundAudio.play()

    } catch (error) {
      console.error('创建音频实例失败:', error)
      VantToast.fail('音效初始化失败')
    }
  },

  // 完成会话
  completeSession() {
    // 停止计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    this.setData({
      isRunning: false,
      isPaused: false
    })

    // 停止背景音
    this.stopBackgroundAudio()

    // 震动提醒
    wx.vibrateShort()

    // 播放完成音效
    wx.playBackgroundAudio()

    // 更新任务进度
    this.updateTaskProgress()

    // 显示完成提示
    VantDialog.alert({
      title: '🎉 专注完成！',
      content: `恭喜完成一个${this.data.workDuration}分钟的专注时段！`,
      onConfirm: () => {
        // 可以跳转到完成页面
        if (this.data.focusMode) {
          this.exitFocusMode()
        }
      }
    })

    // 保存会话记录
    this.savePomodoroSession()

    // 更新使用统计
    this.updateUsageStats()
  },

  // 格式化时间显示
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  },

  // 更新当前时间显示
  updateCurrentTime() {
    const duration = this.data.timerState === 'work' ? this.data.workDuration :
                    this.data.timerState === 'shortBreak' ? this.data.shortBreakDuration :
                    this.data.longBreakDuration

    const timeInSeconds = duration * 60
    this.setData({
      currentTime: timeInSeconds,
      totalTime: timeInSeconds,
      displayTime: this.formatTime(timeInSeconds),
      progressDegree: 0
    })
    this.updateTimerColor()
  },

  // 更新计时器颜色
  updateTimerColor() {
    let colorVar = '--timer-work-color'
    let color = '#FF6B6B'

    switch (this.data.timerState) {
      case 'work':
        colorVar = '--timer-work-color'
        color = '#FF6B6B'
        break
      case 'shortBreak':
        colorVar = '--timer-break-color'
        color = '#52C41A'
        break
      case 'longBreak':
        colorVar = '--timer-long-break-color'
        color = '#1890FF'
        break
    }

    this.setData({
      timerColorVar: colorVar,
      timerColor: color
    })
  },

  // 计时器触摸开始
  onTimerTouchStart(e) {
    if (this.data.isRunning) return

    const touch = e.touches[0]
    this.setData({
      touchStartY: touch.clientY,
      touchStartTime: Date.now(),
      isGesturing: false
    })
  },

  // 计时器触摸移动
  onTimerTouchMove(e) {
    if (this.data.isRunning) return

    const touch = e.touches[0]
    const deltaY = this.data.touchStartY - touch.clientY
    const deltaTime = Date.now() - this.data.touchStartTime

    // 防止与长按冲突，设置时间阈值
    if (deltaTime < 200) return

    // 设置移动阈值，避免误触
    if (Math.abs(deltaY) < 20) return

    if (!this.data.isGesturing) {
      this.setData({
        isGesturing: true,
        showGestureHint: true
      })
    }

    // 根据滑动方向显示提示
    if (deltaY > 0) {
      this.setData({
        gestureHintText: '上滑增加时间'
      })
    } else {
      this.setData({
        gestureHintText: '下滑减少时间'
      })
    }
  },

  // 计时器触摸结束
  onTimerTouchEnd(e) {
    if (this.data.isRunning || !this.data.isGesturing) {
      this.setData({
        showGestureHint: false,
        isGesturing: false
      })
      return
    }

    const touch = e.changedTouches[0]
    const deltaY = this.data.touchStartY - touch.clientY
    const deltaTime = Date.now() - this.data.touchStartTime

    // 防止与长按冲突
    if (deltaTime < 200) {
      this.setData({
        showGestureHint: false,
        isGesturing: false
      })
      return
    }

    // 设置移动阈值
    if (Math.abs(deltaY) < 30) {
      this.setData({
        showGestureHint: false,
        isGesturing: false
      })
      return
    }

    // 计算时间调整
    const timeAdjustment = Math.floor(Math.abs(deltaY) / 50) * 5 // 每50px调整5分钟
    const currentMinutes = Math.floor(this.data.currentTime / 60)
    let newMinutes

    if (deltaY > 0) {
      // 上滑增加时间
      newMinutes = Math.min(currentMinutes + timeAdjustment, 60)
    } else {
      // 下滑减少时间
      newMinutes = Math.max(currentMinutes - timeAdjustment, 5)
    }

    if (newMinutes !== currentMinutes) {
      this.adjustTimerDuration(newMinutes)
      wx.vibrateShort() // 触觉反馈
    }

    this.setData({
      showGestureHint: false,
      isGesturing: false
    })
  },

  // 调整计时器时长
  adjustTimerDuration(minutes) {
    const timeInSeconds = minutes * 60
    this.setData({
      currentTime: timeInSeconds,
      totalTime: timeInSeconds,
      displayTime: this.formatTime(timeInSeconds),
      progressDegree: 0
    })

    // 更新对应的时长设置
    if (this.data.timerState === 'work') {
      this.setData({ workDuration: minutes })
    } else if (this.data.timerState === 'shortBreak') {
      this.setData({ shortBreakDuration: minutes })
    } else if (this.data.timerState === 'longBreak') {
      this.setData({ longBreakDuration: minutes })
    }

    this.saveUserSettings()
  },

  // 设置计时器时长（点击时间节点）
  setTimerDuration(e) {
    if (this.data.isRunning) return

    const minutes = e.currentTarget.dataset.time
    this.adjustTimerDuration(minutes)
    wx.vibrateShort()

    // 高亮效果
    this.setData({ timeNodeHighlight: true })
    setTimeout(() => {
      this.setData({ timeNodeHighlight: false })
    }, 300)
  },

  // 统计标签页切换
  onStatsTabChange(e) {
    const tab = e.detail.name
    this.setData({
      currentStatsTab: tab,
      showTrendChart: false
    })
    this.updateStatsData()
  },

  // 统计卡片点击事件
  onStatCardTap(e) {
    const type = e.currentTarget.dataset.type
    const cardElement = e.currentTarget

    // 添加点击效果
    this.addStatCardClickEffect(cardElement)

    switch (type) {
      case 'focusTime':
        this.showTrendChart()
        break
      case 'sessions':
        this.showSessionDetail()
        break
      case 'tasks':
        this.showTaskDetail()
        break
      case 'efficiency':
        this.showEfficiencyDetail()
        break
    }
  },

  // 添加统计卡片点击效果
  addStatCardClickEffect(element) {
    // 添加点击动画效果
    element.style.transform = 'scale(0.95)'
    element.style.transition = 'transform 0.1s ease'

    setTimeout(() => {
      element.style.transform = 'scale(1)'
    }, 100)

    wx.vibrateShort()
  },

  // 显示趋势图表
  showTrendChart() {
    this.generateTrendData()
    this.setData({
      showTrendChart: true
    })
    wx.showToast({
      title: '查看专注趋势',
      icon: 'success',
      duration: 1500
    })
  },

  // 隐藏趋势图表
  hideTrendChart() {
    this.setData({
      showTrendChart: false
    })
  },

  // 显示会话详情
  showSessionDetail() {
    const currentStats = this.data.statsData[this.data.currentStatsTab]
    wx.showModal({
      title: '番茄钟详情',
      content: `${this.getStatsTabLabel()}完成了 ${currentStats.sessions} 个番茄钟\n平均每个番茄钟 ${Math.round(currentStats.focusTime / Math.max(currentStats.sessions, 1))} 分钟`,
      showCancel: false
    })
  },

  // 显示任务详情
  showTaskDetail() {
    const currentStats = this.data.statsData[this.data.currentStatsTab]
    wx.showModal({
      title: '任务完成详情',
      content: `${this.getStatsTabLabel()}完成了 ${currentStats.tasks} 个任务\n任务完成率 ${Math.round((currentStats.tasks / Math.max(currentStats.sessions, 1)) * 100)}%`,
      showCancel: false
    })
  },

  // 显示效率详情
  showEfficiencyDetail() {
    const currentStats = this.data.statsData[this.data.currentStatsTab]
    wx.showModal({
      title: '学习效率详情',
      content: `${this.getStatsTabLabel()}学习效率 ${currentStats.efficiency}%\n基于专注时长和任务完成情况计算`,
      showCancel: false
    })
  },

  // 获取统计标签页标签
  getStatsTabLabel() {
    const labels = {
      today: '今日',
      week: '本周',
      month: '本月'
    }
    const tab = Array.isArray(this.data.currentStatsTab) ? this.data.currentStatsTab[0] : this.data.currentStatsTab || 'today'
    return labels[tab] || '今日'
  },

  // 更新统计数据
  updateStatsData() {
    const tab = Array.isArray(this.data.currentStatsTab) ? this.data.currentStatsTab[0] : this.data.currentStatsTab || 'today'
    const currentStats = this.data.statsData[tab] || this.data.statsData.today
    const yesterdayStats = this.data.statsData.yesterday

    // 计算趋势
    const trends = this.calculateTrends(currentStats, yesterdayStats)

    // 更新统计卡片
    const statCards = this.data.statCards.map(card => {
      const trend = trends[card.id]
      return {
        ...card,
        value: this.formatStatValue(card.id, currentStats[card.id === 'focusTime' ? 'focusTime' : card.id === 'sessions' ? 'sessions' : card.id === 'tasks' ? 'tasks' : 'efficiency']),
        trend: trend
      }
    })

    this.setData({
      statCards: statCards
    })
  },

  // 计算趋势
  calculateTrends(current, yesterday) {
    const trends = {}

    Object.keys(current).forEach(key => {
      const currentValue = current[key] || 0
      const yesterdayValue = yesterday[key] || 0
      const change = currentValue - yesterdayValue
      const changePercent = yesterdayValue > 0 ? Math.round((change / yesterdayValue) * 100) : 0

      if (Math.abs(changePercent) >= 5) { // 只显示变化超过5%的趋势
        trends[key] = {
          type: change > 0 ? 'success' : 'danger',
          icon: change > 0 ? '↗' : '↘',
          text: `${Math.abs(changePercent)}%`
        }
      }
    })

    return trends
  },

  // 格式化统计值
  formatStatValue(type, value) {
    switch (type) {
      case 'focusTime':
        return `${value || 0}分钟`
      case 'sessions':
        return `${value || 0}个`
      case 'tasks':
        return `${value || 0}个`
      case 'efficiency':
        return `${value || 0}%`
      default:
        return `${value || 0}`
    }
  },

  // 生成趋势图表数据
  generateTrendData() {
    const tab = Array.isArray(this.data.currentStatsTab) ? this.data.currentStatsTab[0] : this.data.currentStatsTab || 'today'
    let data = []

    if (tab === 'today') {
      // 今日每小时数据
      data = this.generateHourlyData()
    } else if (tab === 'week') {
      // 本周每日数据
      data = this.generateWeeklyData()
    } else if (tab === 'month') {
      // 本月每周数据
      data = this.generateMonthlyData()
    }

    this.setData({
      trendData: data
    })
  },

  // 生成每小时数据
  generateHourlyData() {
    const hours = ['9', '10', '11', '12', '14', '15', '16', '17']
    return hours.map((hour, index) => ({
      label: `${hour}时`,
      focusHeight: Math.random() * 80 + 10, // 模拟数据
      color: '#FF6B6B',
      date: `hour_${hour}`
    }))
  },

  // 生成每日数据
  generateWeeklyData() {
    const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    return days.map((day, index) => ({
      label: day,
      focusHeight: Math.random() * 80 + 10, // 模拟数据
      color: '#52C41A',
      date: `day_${index}`
    }))
  },

  // 生成每周数据
  generateMonthlyData() {
    const weeks = ['第1周', '第2周', '第3周', '第4周']
    return weeks.map((week, index) => ({
      label: week,
      focusHeight: Math.random() * 80 + 10, // 模拟数据
      color: '#1890FF',
      date: `week_${index}`
    }))
  },

  // 初始化统计数据
  initStatsData() {
    // 模拟加载统计数据
    const mockStatsData = {
      today: {
        focusTime: this.data.todayFocusTime || 0,
        sessions: this.data.completedSessions || 0,
        tasks: this.data.todayCompletedTasks || 0,
        efficiency: 85
      },
      yesterday: {
        focusTime: 45,
        sessions: 3,
        tasks: 2,
        efficiency: 78
      },
      week: {
        focusTime: 180,
        sessions: 12,
        tasks: 8,
        efficiency: 82
      }
    }

    this.setData({
      statsData: mockStatsData
    })

    this.updateStatsData()
    this.loadUsageStats()
  },

  // 加载使用统计
  loadUsageStats() {
    try {
      const stats = wx.getStorageSync('pomodoroUsageStats')
      if (stats) {
        this.setData({
          usageStats: {
            ...this.data.usageStats,
            ...stats
          }
        })
      }
    } catch (error) {
      console.error('加载使用统计失败:', error)
    }
  },

  // 保存使用统计
  saveUsageStats() {
    try {
      wx.setStorageSync('pomodoroUsageStats', this.data.usageStats)
    } catch (error) {
      console.error('保存使用统计失败:', error)
    }
  },

  // 更新使用统计
  updateUsageStats() {
    const stats = this.data.usageStats
    stats.totalSessions += 1
    stats.averageWorkDuration = Math.round((stats.averageWorkDuration * (stats.totalSessions - 1) + this.data.workDuration) / stats.totalSessions)
    stats.mostUsedSound = this.data.selectedBgSound

    this.setData({
      usageStats: stats
    })

    this.saveUsageStats()
  },

  // 播放背景音
  playBackgroundSound(soundId) {
    if (!this.backgroundAudio || soundId === 'silent') return

    try {
      this.stopBackgroundAudio()
      
      // 这里应该设置实际的音频文件路径
      const soundUrls = {
        rain: '/sounds/rain.mp3',
        ocean: '/sounds/ocean.mp3',
        cafe: '/sounds/cafe.mp3',
        forest: '/sounds/forest.mp3',
        whitenoise: '/sounds/whitenoise.mp3'
      }

      if (soundUrls[soundId]) {
        this.backgroundAudio.src = soundUrls[soundId]
        this.backgroundAudio.play()
      }
    } catch (error) {
      console.error('播放背景音失败', error)
      VantToast.error('播放背景音失败')
    }
  },

  // 停止背景音
  stopBackgroundAudio() {
    if (this.backgroundAudio) {
      this.backgroundAudio.stop()
    }
  },

  // 暂停背景音
  pauseBackgroundAudio() {
    if (this.backgroundAudio) {
      this.backgroundAudio.pause()
    }
  },

  // 清理资源
  cleanup() {
    // 停止计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 停止背景音
    if (this.backgroundAudio) {
      try {
        this.backgroundAudio.stop()
        this.backgroundAudio.destroy()
      } catch (error) {
        console.log('清理音频资源失败:', error)
      }
    }

    // 取消屏幕常亮
    try {
      wx.setKeepScreenOn({
        keepScreenOn: false
      })
    } catch (error) {
      console.log('取消屏幕常亮失败:', error)
    }
  },

  // === Vant组件适配方法 ===



  // 显示设置弹窗
  showSettings() {
    // 隐藏自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().hide()
    }

    // 生成智能推荐
    this.generateSmartRecommendation()

    this.setData({
      showSettingsModal: true,
      activeSettingsTab: ['time']
    })
  },

  // 隐藏设置弹窗
  hideSettings() {
    this.setData({
      showSettingsModal: false,
      showRecommendation: false
    })
    // 显示自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().show()
    }
  },

  // 设置标签页切换
  onSettingsTabChange(e) {
    this.setData({
      activeSettingsTab: e.detail
    })
  },

  // 显示预设配置
  showPresetConfigs() {
    this.setData({
      showPresetModal: true
    })
  },

  // 隐藏预设配置弹窗
  hidePresetModal() {
    this.setData({
      showPresetModal: false
    })
  },

  // 应用预设配置
  applyPresetConfig(e) {
    const presetId = e.currentTarget.dataset.preset
    const preset = this.data.presetConfigs.find(p => p.id === presetId)

    if (preset) {
      wx.showModal({
        title: '应用预设配置',
        content: `确定要应用"${preset.name}"配置吗？这将覆盖当前设置。`,
        confirmText: '应用',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 应用配置
            this.setData({
              ...preset.config,
              showPresetModal: false
            })

            // 更新音效标签
            this.updateSoundLabel()

            // 更新当前时间显示
            this.updateCurrentTime()

            // 保存设置
            this.saveUserSettings()

            VantToast.success(`已应用${preset.name}`)
          }
        }
      })
    }
  },

  // 显示音效分类选择器
  showSoundCategorySelector() {
    this.setData({
      showSoundCategoryModal: true,
      activeSoundCategory: 'basic',
      filteredSounds: this.data.backgroundSounds.filter(s => s.category === 'basic')
    })
  },

  // 隐藏音效分类选择弹窗
  hideSoundCategoryModal() {
    this.setData({
      showSoundCategoryModal: false
    })
  },

  // 音效分类切换
  onSoundCategoryChange(e) {
    const category = e.detail.name
    const filteredSounds = this.data.backgroundSounds.filter(s => s.category === category)

    this.setData({
      activeSoundCategory: category,
      filteredSounds: filteredSounds
    })
  },

  // 长休息间隔变更
  changeLongBreakInterval(e) {
    const interval = e.detail
    this.setData({
      longBreakInterval: interval
    })
    this.saveUserSettings()
  },

  // 生成智能推荐
  generateSmartRecommendation() {
    const stats = this.data.usageStats
    let recommendation = ''

    // 基于使用统计生成推荐
    if (stats.totalSessions > 10) {
      if (stats.averageWorkDuration < 20) {
        recommendation = '💡 建议：您的平均专注时长较短，可以尝试逐步增加工作时长到25分钟'
      } else if (stats.averageWorkDuration > 45) {
        recommendation = '💡 建议：长时间专注可能导致疲劳，建议适当缩短工作时长并增加休息'
      } else if (stats.mostUsedSound === 'silent') {
        recommendation = '💡 建议：尝试使用背景音效，如森林或白噪音，可能有助于提升专注力'
      }
    } else {
      // 新用户推荐
      recommendation = '💡 新手建议：推荐使用学生模式(25-5-15)开始您的专注之旅'
    }

    if (recommendation) {
      this.setData({
        showRecommendation: true,
        recommendationText: recommendation
      })
    }
  },

  // 隐藏推荐
  hideRecommendation() {
    this.setData({
      showRecommendation: false
    })
  },

  // 重置设置
  resetSettings() {
    VantDialog.confirm({
      title: '重置设置',
      content: '确定要重置所有设置到默认值吗？此操作不可撤销。',
      onConfirm: () => {
        // 重置到默认值
        this.setData({
          workDuration: 25,
          shortBreakDuration: 5,
          longBreakDuration: 15,
          longBreakInterval: 4,
          enableVibration: true,
          enableNotification: true,
          autoStartBreak: false,
          autoStartWork: false,
          selectedBgSound: 'silent',
          bgVolume: 50
        })

        this.updateSoundLabel()
        this.updateCurrentTime()
        this.saveUserSettings()

        VantToast.success('设置已重置')
      }
    })
  },

  // 导出设置
  exportSettings() {
    const settings = {
      workDuration: this.data.workDuration,
      shortBreakDuration: this.data.shortBreakDuration,
      longBreakDuration: this.data.longBreakDuration,
      longBreakInterval: this.data.longBreakInterval,
      enableVibration: this.data.enableVibration,
      enableNotification: this.data.enableNotification,
      autoStartBreak: this.data.autoStartBreak,
      autoStartWork: this.data.autoStartWork,
      selectedBgSound: this.data.selectedBgSound,
      bgVolume: this.data.bgVolume,
      exportTime: new Date().toISOString()
    }

    const settingsStr = JSON.stringify(settings, null, 2)

    wx.setClipboardData({
      data: settingsStr,
      success: () => {
        VantToast.success('设置已复制到剪贴板')
      },
      fail: () => {
        VantToast.fail('导出失败')
      }
    })
  },

  // 导入设置
  importSettings() {
    wx.getClipboardData({
      success: (res) => {
        try {
          const settings = JSON.parse(res.data)

          // 验证设置格式
          if (this.validateSettingsFormat(settings)) {
            VantDialog.confirm({
              title: '导入设置',
              content: '确定要导入剪贴板中的设置吗？这将覆盖当前设置。',
              onConfirm: () => {
                // 应用导入的设置
                this.setData({
                  workDuration: settings.workDuration || 25,
                  shortBreakDuration: settings.shortBreakDuration || 5,
                  longBreakDuration: settings.longBreakDuration || 15,
                  longBreakInterval: settings.longBreakInterval || 4,
                  enableVibration: settings.enableVibration !== false,
                  enableNotification: settings.enableNotification !== false,
                  autoStartBreak: settings.autoStartBreak || false,
                  autoStartWork: settings.autoStartWork || false,
                  selectedBgSound: settings.selectedBgSound || 'silent',
                  bgVolume: settings.bgVolume || 50
                })

                this.updateSoundLabel()
                this.updateCurrentTime()
                this.saveUserSettings()

                VantToast.success('设置导入成功')
              }
            })
          } else {
            VantToast.fail('设置格式不正确')
          }
        } catch (error) {
          VantToast.fail('剪贴板内容不是有效的设置格式')
        }
      },
      fail: () => {
        VantToast.fail('读取剪贴板失败')
      }
    })
  },

  // 验证设置格式
  validateSettingsFormat(settings) {
    const requiredFields = ['workDuration', 'shortBreakDuration', 'longBreakDuration']
    return requiredFields.every(field => typeof settings[field] === 'number')
  },



  // 添加按钮点击动画
  addButtonClickAnimation(element) {
    if (!element) return

    element.style.animation = 'buttonPress 0.15s ease-out'
    setTimeout(() => {
      element.style.animation = ''
    }, 150)
  },

  // 添加卡片滑入动画
  addCardSlideAnimation() {
    const cards = this.selectAllComponents('.stat-card')
    cards.forEach((card, index) => {
      setTimeout(() => {
        card.setData({
          animation: 'cardSlideIn 0.3s ease-out'
        })
      }, index * 100)
    })
  },

  // 添加统计数字动画
  animateStatsNumbers() {
    const statElements = [
      { key: 'completedSessions', duration: 1000 },
      { key: 'todayFocusTime', duration: 1200 },
      { key: 'todayCompletedTasks', duration: 800 }
    ]

    statElements.forEach(stat => {
      this.animateNumber(stat.key, this.data[stat.key], stat.duration)
    })
  },

  // 数字动画
  animateNumber(key, targetValue, duration) {
    const startValue = 0
    const startTime = Date.now()

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)

      // 使用缓动函数
      const easeOutQuart = 1 - Math.pow(1 - progress, 4)
      const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart)

      this.setData({
        [key]: currentValue
      })

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }

    animate()
  },

  // 显示声音设置
  showSoundSettings() {
    // 隐藏自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().hide()
    }
    this.setData({
      showSoundModal: true
    })
  },

  // 隐藏声音设置
  hideSoundModal() {
    this.setData({
      showSoundModal: false
    })
    // 显示自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().show()
    }
  },

  // 选择背景音
  selectBackgroundSound(e) {
    const soundId = e.currentTarget.dataset.sound
    this.setData({
      selectedBgSound: soundId
    })
    this.updateSoundLabel()
    this.saveUserSettings()
    
    // 如果正在播放，切换音乐
    if (this.data.isRunning && soundId !== 'silent') {
      this.playBackgroundSound(soundId)
    }
  },

  // 调整背景音量
  adjustBgVolume(e) {
    const volume = e.detail.value
    this.setData({
      bgVolume: volume
    })
    
    if (this.backgroundAudio) {
      this.backgroundAudio.volume = volume / 100
    }
    
    this.saveUserSettings()
  },

  // 工作时长变更
  changeWorkDuration(e) {
    const duration = e.detail
    this.setData({
      workDuration: duration
    })
    
    if (this.data.timerState === 'work') {
      this.updateCurrentTime()
    }
    
    this.saveUserSettings()
  },

  // 短休息时长变更
  changeShortBreakDuration(e) {
    const duration = e.detail
    this.setData({
      shortBreakDuration: duration
    })
    
    if (this.data.timerState === 'shortBreak') {
      this.updateCurrentTime()
    }
    
    this.saveUserSettings()
  },

  // 长休息时长变更
  changeLongBreakDuration(e) {
    const duration = e.detail
    this.setData({
      longBreakDuration: duration
    })
    
    if (this.data.timerState === 'longBreak') {
      this.updateCurrentTime()
    }
    
    this.saveUserSettings()
  },

  // 切换震动
  toggleVibration(e) {
    const enabled = e.detail
    this.setData({
      enableVibration: enabled
    })
    this.saveUserSettings()
  },

  // 切换通知
  toggleNotification(e) {
    const enabled = e.detail
    this.setData({
      enableNotification: enabled
    })
    this.saveUserSettings()
  },

  // 切换自动开始休息
  toggleAutoStartBreak(e) {
    const enabled = e.detail
    this.setData({
      autoStartBreak: enabled
    })
    this.saveUserSettings()
  },

  // 切换自动开始工作
  toggleAutoStartWork(e) {
    const enabled = e.detail
    this.setData({
      autoStartWork: enabled
    })
    this.saveUserSettings()
  },

  // 查看统计
  viewStatistics() {
    wx.navigateTo({
      url: '/pages/statistics-detail/index'
    })
  },

  // 更新任务进度
  async updateTaskProgress() {
    if (this.data.selectedTask && this.data.studyMode === 'task') {
      try {
        console.log('更新任务状态为完成:', this.data.selectedTask.id)
        
        // 调用API更新任务状态为完成
        const result = await SmartApi.completeTask(this.data.selectedTask.id, true)
        
        if (result.success) {
          console.log('任务状态更新成功')
          VantToast.success('任务已完成！')
          
          // 清空选中的任务
          this.setData({
            selectedTask: null,
            studyMode: 'quick'
          })
        } else {
          console.error('任务状态更新失败:', result.error)
          VantToast.error('任务状态更新失败')
        }
      } catch (error) {
        console.error('更新任务状态失败:', error)
        VantToast.error('更新任务状态失败')
      }
    }
  },

  // 保存番茄钟会话
  savePomodoroSession() {
    const session = {
      taskId: this.data.selectedTask ? this.data.selectedTask.id : null,
      taskTitle: this.data.selectedTask ? this.data.selectedTask.title : '快速专注',
      duration: this.data.workDuration,
      completedAt: new Date().toISOString(),
      sessionType: this.data.timerState
    }

    try {
      const sessions = wx.getStorageSync('pomodoroSessions') || []
      sessions.push(session)
      wx.setStorageSync('pomodoroSessions', sessions)

      // 更新统计
      this.setData({
        completedSessions: this.data.completedSessions + 1,
        todayFocusTime: this.data.todayFocusTime + this.data.workDuration
      })
    } catch (error) {
      console.error('保存会话失败:', error)
    }
  }
})

