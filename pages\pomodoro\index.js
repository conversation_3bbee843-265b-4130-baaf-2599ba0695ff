// pages/pomodoro/index.js - Vant重构版本（清理版）
const SmartApi = require('../../utils/smartApi')

// 简化的Vant API助手
const VantToast = {
  success: (msg) => wx.showToast({ title: msg, icon: 'success' }),
  error: (msg) => wx.showToast({ title: msg, icon: 'error' }),
  info: (msg) => wx.showToast({ title: msg, icon: 'none' })
}

const VantDialog = {
  alert: ({ title, content, onConfirm }) => wx.showModal({
    title, content, showCancel: false,
    success: (res) => res.confirm && onConfirm && onConfirm()
  }),
  confirm: ({ title, content, onConfirm, onCancel }) => wx.showModal({
    title, content,
    success: (res) => {
      if (res.confirm && onConfirm) {
        onConfirm()
      } else if (res.cancel && onCancel) {
        onCancel()
      }
    }
  })
}

const VantActionSheet = {
  showSheet: ({ titleText, buttons, onItemClick }) => {
    const itemList = buttons.map(btn => btn.text)
    wx.showActionSheet({
      itemList,
      success: (res) => onItemClick && onItemClick(buttons[res.tapIndex], res.tapIndex)
    })
  }
}

Page({
  data: {
    // 模式状态
    focusMode: false,
    studyMode: 'quick', // quick, task

    // 计时器状态
    timerState: 'work', // work, shortBreak, longBreak
    timerStateText: '专注时间',
    sessionTypeText: '专注',
    sessionIndicator: '●●●●○○○○',
    isRunning: false,
    isPaused: false,

    // 时间设置
    workDuration: 25, // 分钟
    shortBreakDuration: 5,
    longBreakDuration: 15,

    // 当前时间
    currentTime: 25 * 60, // 秒
    totalTime: 25 * 60,
    displayTime: '25:00',
    progressDegree: 0,
    timerColor: '#FF6B6B',
    timerColorVar: '--timer-work-color',

    // 手势交互
    touchStartY: 0,
    touchStartTime: 0,
    isGesturing: false,
    showGestureHint: false,
    gestureHintText: '',
    timeNodeHighlight: false,

    // 时间节点
    timeNodes: [
      { value: 5, angle: -90 },
      { value: 10, angle: -45 },
      { value: 15, angle: 0 },
      { value: 20, angle: 45 },
      { value: 25, angle: 90 },
      { value: 30, angle: 135 },
      { value: 45, angle: 180 },
      { value: 60, angle: 225 }
    ],

    // 任务相关
    selectedTask: null,
    availableTasks: [],
    taskOptions: [], // 用于action-sheet的任务选项

    // 统计数据
    completedSessions: 0,
    todayFocusTime: 0,
    todayCompletedTasks: 0,

    // 统计卡片数据（参考任务中心实现）
    pomodoroStats: [
      { label: '专注时长', value: '0分钟' },
      { label: '完成番茄', value: '0个' },
      { label: '完成任务', value: '0个' },
      { label: '学习效率', value: '0%' }
    ],

    // 可视化增强数据
    dailyGoal: 5,
    goalProgress: 0,
    comparisonPeriod: 'daily', // daily 或 weekly
    comparisonData: [],
    chartTimeRange: 'week', // week 或 month

    // 扩展统计数据
    currentStatsTab: 'today', // today, week, month
    statsData: {
      today: {
        focusTime: 0,
        sessions: 0,
        tasks: 0,
        efficiency: 0
      },
      yesterday: {
        focusTime: 0,
        sessions: 0,
        tasks: 0,
        efficiency: 0
      },
      week: {
        focusTime: 0,
        sessions: 0,
        tasks: 0,
        efficiency: 0
      }
    },

    // 统计卡片数据
    statCards: [
      {
        id: 'focusTime',
        icon: '⏰',
        label: '专注时长',
        value: '0分钟',
        bgColor: '#FF6B6B',
        trend: null,
        clickable: true
      },
      {
        id: 'sessions',
        icon: '🍅',
        label: '完成番茄',
        value: '0个',
        bgColor: '#52C41A',
        trend: null,
        clickable: true
      },
      {
        id: 'tasks',
        icon: '📝',
        label: '完成任务',
        value: '0个',
        bgColor: '#1890FF',
        trend: null,
        clickable: true
      },
      {
        id: 'efficiency',
        icon: '📊',
        label: '学习效率',
        value: '0%',
        bgColor: '#722ED1',
        trend: null,
        clickable: true
      }
    ],

    // 趋势图表数据
    trendData: [],
    showTrendChart: false,

    // 专注模式增强
    lastTapTime: 0,
    focusMotivation: '',
    focusTaskDesc: '专注复习中...',
    taskProgress: 0,
    taskProgressText: '',
    currentSessionTime: 0,
    showMilestone: false,
    milestoneText: '',
    milestoneAnimation: false,
    showCelebration: false,
    celebrationTitle: '',
    celebrationMessage: '',
    celebrationParticles: [],
    progressRings: [],

    // 激励文案库
    motivationTexts: [
      '专注当下，成就未来 ✨',
      '每一分钟的专注都在积累成功 🌟',
      '保持专注，你正在变得更强 💪',
      '专注是通往成功的唯一道路 🎯',
      '深度专注，深度成长 🌱',
      '专注让时间更有价值 ⏰',
      '在专注中找到内心的平静 🧘‍♀️',
      '专注是最好的投资 💎'
    ],

    // 里程碑配置
    milestones: [
      { time: 5, title: '起步里程碑', message: '很好！已经专注5分钟了' },
      { time: 10, title: '坚持里程碑', message: '太棒了！坚持了10分钟' },
      { time: 15, title: '专注里程碑', message: '优秀！已经专注15分钟' },
      { time: 20, title: '深度里程碑', message: '惊人！深度专注20分钟' },
      { time: 25, title: '完美里程碑', message: '完美！完成一个完整番茄钟' },
      { time: 30, title: '超越里程碑', message: '超越自我！专注30分钟' },
      { time: 45, title: '卓越里程碑', message: '卓越表现！专注45分钟' },
      { time: 60, title: '大师里程碑', message: '大师级专注！整整1小时' }
    ],

    // 音频设置
    selectedBgSound: 'silent',
    bgVolume: 50,
    currentSoundLabel: '静音模式',
    backgroundSounds: [
      { id: 'silent', name: '静音', icon: '🔇', description: '无背景音', category: 'basic' },
      { id: 'rain', name: '雨声', icon: '🌧️', description: '适合深度思考', category: 'nature' },
      { id: 'ocean', name: '海浪', icon: '🌊', description: '适合放松复习', category: 'nature' },
      { id: 'forest', name: '森林', icon: '🌲', description: '适合长时间专注', category: 'nature' },
      { id: 'thunder', name: '雷雨', icon: '⛈️', description: '激发专注力', category: 'nature' },
      { id: 'birds', name: '鸟鸣', icon: '🐦', description: '清晨专注', category: 'nature' },
      { id: 'cafe', name: '咖啡厅', icon: '☕', description: '适合轻松复习', category: 'ambient' },
      { id: 'library', name: '图书馆', icon: '📚', description: '学习氛围', category: 'ambient' },
      { id: 'fireplace', name: '壁炉', icon: '🔥', description: '温暖舒适', category: 'ambient' },
      { id: 'city', name: '城市', icon: '🏙️', description: '都市节奏', category: 'ambient' },
      { id: 'whitenoise', name: '白噪音', icon: '🎵', description: '屏蔽外界干扰', category: 'focus' },
      { id: 'pinknoise', name: '粉红噪音', icon: '🎶', description: '深度专注', category: 'focus' },
      { id: 'brownnoise', name: '棕色噪音', icon: '🎼', description: '放松心情', category: 'focus' },
      { id: 'binaural', name: '双耳节拍', icon: '🧠', description: '提升专注力', category: 'focus' }
    ],

    // 音频分类
    soundCategories: [
      { id: 'basic', name: '基础', icon: '🔇' },
      { id: 'nature', name: '自然', icon: '🌿' },
      { id: 'ambient', name: '环境', icon: '🏠' },
      { id: 'focus', name: '专注', icon: '🎯' }
    ],

    notificationSounds: [
      { id: 'start', name: '开始提示', enabled: true },
      { id: 'pause', name: '暂停提示', enabled: true },
      { id: 'complete', name: '完成提示', enabled: true },
      { id: 'warning', name: '时间警告', enabled: true }
    ],

    // 其他设置
    enableVibration: true,
    enableNotification: true,
    autoStartBreak: false,
    autoStartWork: false,
    longBreakInterval: 4,
    currentSession: 1,

    // 弹窗状态
    showSoundModal: false,
    showSettingsModal: false,
    showTaskSelector: false,
    showDropdown: false, // 控制下拉菜单显示

    // 设置界面状态
    activeSettingsTab: ['time'], // van-collapse需要数组格式
    showPresetModal: false,
    showRecommendation: false,
    recommendationText: '',

    // 配置预设方案
    presetConfigs: [
      {
        id: 'student',
        name: '学生模式',
        icon: '🎓',
        description: '适合学生学习，短时高效',
        config: {
          workDuration: 25,
          shortBreakDuration: 5,
          longBreakDuration: 15,
          longBreakInterval: 4,
          enableVibration: true,
          enableNotification: true,
          autoStartBreak: false,
          autoStartWork: false,
          selectedBgSound: 'forest'
        }
      },
      {
        id: 'work',
        name: '工作模式',
        icon: '💼',
        description: '适合职场工作，长时专注',
        config: {
          workDuration: 45,
          shortBreakDuration: 10,
          longBreakDuration: 30,
          longBreakInterval: 3,
          enableVibration: false,
          enableNotification: true,
          autoStartBreak: true,
          autoStartWork: false,
          selectedBgSound: 'whitenoise'
        }
      },
      {
        id: 'deep',
        name: '深度专注',
        icon: '🧠',
        description: '适合深度思考，超长专注',
        config: {
          workDuration: 60,
          shortBreakDuration: 15,
          longBreakDuration: 45,
          longBreakInterval: 2,
          enableVibration: false,
          enableNotification: false,
          autoStartBreak: false,
          autoStartWork: false,
          selectedBgSound: 'binaural'
        }
      },
      {
        id: 'relax',
        name: '轻松模式',
        icon: '😌',
        description: '适合轻松学习，灵活安排',
        config: {
          workDuration: 20,
          shortBreakDuration: 8,
          longBreakDuration: 20,
          longBreakInterval: 3,
          enableVibration: true,
          enableNotification: true,
          autoStartBreak: true,
          autoStartWork: true,
          selectedBgSound: 'ocean'
        }
      }
    ],

    // 使用统计（用于智能推荐）
    usageStats: {
      totalSessions: 0,
      averageWorkDuration: 25,
      preferredBreakDuration: 5,
      mostUsedSound: 'silent',
      peakHours: [],
      weeklyPattern: []
    },



    // 用户信息
    userInfo: {},
    userSignature: ''
  },

  async onLoad() {
    // 检查登录状态
    const app = getApp()
    if (!app.globalData.userInfo || !app.globalData.openid) {
      console.log('用户未登录，跳转到登录页面')
      wx.navigateTo({
        url: '/pages/login/index'
      })
      return
    }

    // 检查并执行数据迁移
    this.dataVersionManager.checkAndMigrate()

    // 检查数据完整性
    this.dataIntegrityChecker.checkAllData()

    this.initPage()
    this.loadAvailableTasks()
    this.loadUserSettings()
    this.loadUserSignature()
    this.initAudioSystem()
    this.initStatsData()
  },

  onShow() {
    // 更新TabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      })
    }

    this.refreshTaskList()
    this.checkAndLoadFromCache()

    // 重新加载用户签名（从设置页面返回时更新）
    this.loadUserSignature()

    // 确保统计数据被初始化和同步
    this.checkNetworkAndSync() // 检查网络状态并同步数据
  },



  onHide() {
    this.pauseBackgroundAudio()
  },

  onUnload() {
    this.cleanup()
  },

  // 初始化页面
  initPage() {
    this.setData({
      displayTime: this.formatTime(this.data.currentTime),
      sessionTypeText: '专注',
      progressDegree: 0
    })
  },

  // 更新声音标签
  updateSoundLabel() {
    const sound = this.data.backgroundSounds.find(s => s.id === this.data.selectedBgSound)
    this.setData({
      currentSoundLabel: sound ? sound.name : '静音模式'
    })
  },

  // 加载用户设置
  loadUserSettings() {
    try {
      const settings = wx.getStorageSync('kaoba_pomodoro_settings')
      if (settings) {
        this.setData({
          workDuration: settings.workDuration || 25,
          shortBreakDuration: settings.shortBreakDuration || 5,
          longBreakDuration: settings.longBreakDuration || 15,
          selectedBgSound: settings.selectedBgSound || 'silent',
          bgVolume: settings.bgVolume || 50,
          enableVibration: settings.enableVibration !== false,
          enableNotification: settings.enableNotification !== false
        })
        this.updateCurrentTime()
        this.updateSoundLabel()
      }
    } catch (error) {
      console.error('加载用户设置失败:', error)
    }
  },

  // 保存用户设置
  saveUserSettings() {
    try {
      const settings = {
        workDuration: this.data.workDuration,
        shortBreakDuration: this.data.shortBreakDuration,
        longBreakDuration: this.data.longBreakDuration,
        selectedBgSound: this.data.selectedBgSound,
        bgVolume: this.data.bgVolume,
        enableVibration: this.data.enableVibration,
        enableNotification: this.data.enableNotification
      }
      wx.setStorageSync('kaoba_pomodoro_settings', settings)
    } catch (error) {
      console.error('保存用户设置失败:', error)
    }
  },

  // 加载用户签名
  loadUserSignature() {
    try {
      const app = getApp()
      const userInfo = app.globalData.userInfo || wx.getStorageSync('kaoba_user_info') || {}

      this.setData({
        userInfo: userInfo,
        userSignature: userInfo.signature || ''
      })
    } catch (error) {
      console.error('加载用户签名失败:', error)
    }
  },

  // 加载可用任务
  async loadAvailableTasks() {
    try {
      const result = await SmartApi.getTasks()
      if (result.success) {
        // 只获取未完成的任务
        const incompleteTasks = result.data.filter(task => !task.completed)
        
        // 按照时间进行排序，时间越早的排在越前面
        const sortedTasks = incompleteTasks.sort((a, b) => {
          // 构造完整的时间字符串用于比较
          const timeA = this.getTaskDateTime(a)
          const timeB = this.getTaskDateTime(b)
          
          // 按时间升序排列（早的在前）
          return timeA - timeB
        })
        
        this.setData({ availableTasks: sortedTasks })
        console.log('加载并排序任务:', sortedTasks.length, '个任务')
      }
    } catch (error) {
      console.error('加载任务失败:', error)
    }
  },

  // 获取任务的完整日期时间用于排序
  getTaskDateTime(task) {
    try {
      // 使用截止日期和时间
      const dateStr = task.dueDate || task.date || new Date().toISOString().split('T')[0]
      const timeStr = task.dueTime || task.time || '23:59'
      
      // 构造完整的日期时间字符串
      const dateTimeStr = `${dateStr}T${timeStr}:00`
      const dateTime = new Date(dateTimeStr)
      
      // 如果解析失败，返回一个很远的未来时间
      if (isNaN(dateTime.getTime())) {
        return new Date('2099-12-31T23:59:59')
      }
      
      return dateTime
    } catch (error) {
      console.error('解析任务时间失败:', error, task)
      return new Date('2099-12-31T23:59:59')
    }
  },

  // 刷新任务列表
  refreshTaskList() {
    this.loadAvailableTasks()
  },

  // 检查并加载缓存中的任务信息
  checkAndLoadFromCache() {
    try {
      const cachedTaskId = wx.getStorageSync('currentTaskId')
      const cachedExamId = wx.getStorageSync('currentExamId')
      const cachedTaskTitle = wx.getStorageSync('currentTaskTitle')
      const cachedExamName = wx.getStorageSync('currentExamName')

      if (cachedTaskId && cachedTaskTitle) {
        // 构造任务对象
        const cachedTask = {
          id: cachedTaskId,
          title: cachedTaskTitle,
          examId: cachedExamId,
          examName: cachedExamName
        }

        // 自动选择这个任务
        this.setData({
          selectedTask: cachedTask,
          studyMode: 'task'
        })

        VantToast.success(`已自动选择任务：${cachedTaskTitle}`)

        // 清除缓存，避免重复使用
        wx.removeStorageSync('currentTaskId')
        wx.removeStorageSync('currentExamId')
        wx.removeStorageSync('currentTaskTitle')
        wx.removeStorageSync('currentExamName')

        console.log('已从缓存加载任务:', cachedTask)
      }
    } catch (error) {
      console.error('加载缓存任务失败:', error)
    }
  },

  // 初始化音频系统
  initAudioSystem() {
    try {
      this.backgroundAudio = wx.createInnerAudioContext()
      this.backgroundAudio.loop = true
      this.backgroundAudio.volume = this.data.bgVolume / 100

      this.backgroundAudio.onError((res) => {
        console.error('背景音频播放失败:', res)
      })
    } catch (error) {
      console.error('初始化音频系统失败', error)
    }
  },

  // 显示任务选择器
  showTaskSelector() {
    // 隐藏自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().hide()
    }

    this.setData({
      showTaskSelector: true
    })
  },

  // 隐藏任务选择器
  hideTaskSelector() {
    this.setData({
      showTaskSelector: false
    })
    // 显示自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().show()
    }
  },

  // 快速模式切换
  toggleQuickMode(e) {
    const isQuick = e.detail
    if (isQuick) {
      this.setData({
        selectedTask: null,
        studyMode: 'quick'
      })
      VantToast.success('已切换到快速专注模式')
    } else {
      // 如果有可用任务，选择第一个；否则保持快速模式
      if (this.data.availableTasks.length > 0) {
        const firstTask = this.data.availableTasks[0]
        this.setData({
          selectedTask: firstTask,
          studyMode: 'task'
        })
        VantToast.success(`已选择任务：${firstTask.title}`)
      } else {
        // 没有可用任务，保持快速模式
        this.setData({
          studyMode: 'quick'
        })
        VantToast.info('暂无可用任务，保持快速专注模式')
      }
    }
  },

  // 选择快速专注模式
  selectQuickMode() {
    this.setData({
      selectedTask: null,
      studyMode: 'quick',
      showTaskSelector: false
    })
    // 显示自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().show()
    }
    VantToast.success('已选择快速专注模式')
  },

  // 选择具体任务
  selectTaskItem(e) {
    const task = e.currentTarget.dataset.task
    if (task) {
      this.setData({
        selectedTask: task,
        studyMode: 'task',
        showTaskSelector: false
      })
      // 显示自定义tabBar
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().show()
      }
      VantToast.success(`已选择任务：${task.title}`)
    }
  },

  // 跳转到任务中心
  goToTaskCenter() {
    this.hideTaskSelector()
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  // 选择任务选项
  selectTaskOption(task) {
    if (!task) {
      // 快速专注模式
      this.setData({
        selectedTask: null,
        studyMode: 'quick'
      })
      VantToast.success('已选择快速专注模式')
    } else {
      // 任务模式
      this.setData({
        selectedTask: task,
        studyMode: 'task'
      })
      VantToast.success(`已选择任务：${task.title}`)
    }
  },

  // 进入专注模式
  enterFocusMode() {
    // 设置屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: true
    })

    // 随机选择激励文案
    const randomMotivation = this.data.motivationTexts[Math.floor(Math.random() * this.data.motivationTexts.length)]

    // 初始化专注模式数据
    this.setData({
      focusMode: true,
      focusMotivation: randomMotivation,
      currentSessionTime: 0,
      taskProgress: 0,
      taskProgressText: '开始专注复习',
      progressRings: this.generateProgressRings()
    })

    // 更新任务描述
    this.updateFocusTaskDesc()

    VantToast.success('已进入深度专注模式')
    wx.vibrateShort()
  },

  // 退出专注模式
  exitFocusMode() {
    // 取消屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })

    // 重置专注模式状态
    this.setData({
      focusMode: false,
      showMilestone: false,
      milestoneAnimation: false,
      showCelebration: false,
      focusMotivation: ''
    })

    VantToast.success('已退出专注模式')
  },

  // 专注模式点击处理（双击退出）
  onFocusModeTap() {
    const currentTime = Date.now()
    const timeDiff = currentTime - this.data.lastTapTime

    if (timeDiff < 300) { // 300ms内的双击
      // 双击退出确认
      VantDialog.confirm({
        title: '退出专注模式',
        content: '确定要退出深度专注模式吗？',
        onConfirm: () => {
          this.exitFocusMode()
        },
        onCancel: () => {
          // 用户选择继续专注
          VantToast.info('继续保持专注！')
        }
      })
    }

    this.setData({
      lastTapTime: currentTime
    })
  },

  // 更新专注任务描述
  updateFocusTaskDesc() {
    if (!this.data.selectedTask) return

    const progress = Math.floor((this.data.totalTime - this.data.currentTime) / this.data.totalTime * 100)
    const descriptions = [
      '深度专注复习中...',
      '专注学习，收获成长',
      '保持专注，追求卓越',
      '专注当下，成就未来'
    ]

    const randomDesc = descriptions[Math.floor(Math.random() * descriptions.length)]

    this.setData({
      focusTaskDesc: randomDesc,
      taskProgress: progress,
      taskProgressText: `进度 ${progress}%`
    })
  },

  // 生成进度环效果
  generateProgressRings() {
    const rings = []
    for (let i = 0; i < 3; i++) {
      rings.push({
        delay: i * 0.5,
        opacity: 1 - i * 0.3
      })
    }
    return rings
  },

  // 计时器核心功能
  toggleTimer() {
    if (this.data.isRunning) {
      this.pauseTimer()
    } else {
      this.startTimer()
    }
  },

  // 开始计时器
  startTimer() {
    this.setData({
      isRunning: true,
      isPaused: false
    })

    VantToast.success('开始专注')

    // 开始背景音
    if (this.data.selectedBgSound !== 'silent') {
      this.playBackgroundSound(this.data.selectedBgSound)
    }

    // 启动计时器
    this.timer = setInterval(() => {
      this.updateTimer()
    }, 1000)

    // 保持屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: true
    })
  },

  // 暂停计时器
  pauseTimer() {
    this.setData({
      isRunning: false,
      isPaused: true
    })

    VantToast.info('已暂停')

    // 暂停背景音
    this.pauseBackgroundAudio()

    // 清除计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 取消屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })
  },

  // 停止计时器
  stopTimer() {
    VantDialog.confirm({
      title: '确认停止',
      content: '确定要停止当前的专注会话吗？',
      onConfirm: () => {
        this.resetTimer()
        VantToast.info('已停止专注')
      }
    })
  },

  // 重置计时器
  resetTimer() {
    this.setData({
      isRunning: false,
      isPaused: false
    })

    // 停止背景音
    this.stopBackgroundAudio()

    // 清除计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 重置时间显示
    this.updateCurrentTime()
    this.setData({
      displayTime: this.formatTime(this.data.currentTime),
      progressDegree: 0
    })

    // 取消屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })

    // 退出专注模式
    if (this.data.focusMode) {
      this.exitFocusMode()
    }

    // 如果有选中的任务，询问是否要标记为完成
    if (this.data.selectedTask && this.data.studyMode === 'task') {
      VantDialog.confirm({
        title: '任务完成',
        content: '您已结束专注，是否要标记任务为完成？',
        onConfirm: () => {
          this.updateTaskProgress()
        }
      })
    }
  },

  // 更新计时器
  updateTimer() {
    let currentTime = this.data.currentTime - 1

    if (currentTime <= 0) {
      // 时间到了
      this.completeSession()
      return
    }

    // 计算已专注时间
    const elapsedTime = this.data.totalTime - currentTime
    const elapsedMinutes = Math.floor(elapsedTime / 60)
    const minutes = Math.floor(currentTime / 60)
    const seconds = currentTime % 60

    // 更新当前会话时长
    this.setData({
      currentSessionTime: elapsedMinutes
    })

    // 里程碑检测
    this.checkMilestones(elapsedMinutes)

    // 时间节点提示（5分钟、10分钟、15分钟等关键节点）
    if (seconds === 0 && [5, 10, 15, 20, 25, 30].includes(minutes)) {
      // 时间节点高亮效果
      this.setData({ timeNodeHighlight: true })
      setTimeout(() => {
        this.setData({ timeNodeHighlight: false })
      }, 500)
      wx.vibrateShort()
    }

    // 时间警告（最后1分钟）
    if (currentTime === 60) {
      wx.vibrateShort()
    }

    // 更新显示
    const progress = ((this.data.totalTime - currentTime) / this.data.totalTime) * 360
    this.setData({
      currentTime: currentTime,
      displayTime: this.formatTime(currentTime),
      progressDegree: progress
    })

    // 更新专注模式任务进度
    if (this.data.focusMode) {
      this.updateFocusTaskDesc()
    }
  },

  // 检查里程碑
  checkMilestones(elapsedMinutes) {
    const milestone = this.data.milestones.find(m => m.time === elapsedMinutes)

    if (milestone && !this.data.showMilestone) {
      this.triggerMilestone(milestone)
    }
  },

  // 触发里程碑庆祝
  triggerMilestone(milestone) {
    // 显示里程碑提示
    this.setData({
      showMilestone: true,
      milestoneText: milestone.message,
      milestoneAnimation: true
    })

    // 触发庆祝动画
    this.showCelebrationAnimation(milestone.title, milestone.message)

    // 震动反馈
    wx.vibrateShort()

    // 3秒后隐藏里程碑提示
    setTimeout(() => {
      this.setData({
        showMilestone: false,
        milestoneAnimation: false
      })
    }, 3000)
  },

  // 显示庆祝动画
  showCelebrationAnimation(title, message) {
    // 生成粒子效果
    const particles = []
    for (let i = 0; i < 20; i++) {
      particles.push({
        x: Math.random() * 100,
        y: Math.random() * 100,
        delay: Math.random() * 2
      })
    }

    this.setData({
      showCelebration: true,
      celebrationTitle: title,
      celebrationMessage: message,
      celebrationParticles: particles
    })

    // 5秒后隐藏庆祝动画
    setTimeout(() => {
      this.setData({
        showCelebration: false
      })
    }, 5000)
  },

  // 音量控制
  onVolumeChange(e) {
    const volume = e.detail
    this.setData({
      bgVolume: volume
    })

    // 更新背景音音量
    if (this.backgroundAudio) {
      this.backgroundAudio.volume = volume / 100
    }

    this.saveUserSettings()
  },

  // 显示音效选择器
  showSoundSelector() {
    this.setData({
      showSoundModal: true
    })
  },

  // 选择背景音效
  selectBackgroundSound(e) {
    const soundId = e.currentTarget.dataset.sound
    const sound = this.data.backgroundSounds.find(s => s.id === soundId)

    if (sound) {
      this.setData({
        selectedBgSound: soundId,
        currentSoundLabel: sound.name,
        showSoundModal: false
      })

      // 如果正在播放，切换音效
      if (this.data.isRunning && soundId !== 'silent') {
        this.stopBackgroundAudio()
        setTimeout(() => {
          this.playBackgroundSound(soundId)
        }, 100)
      }

      this.saveUserSettings()
      VantToast.success(`已切换到${sound.name}`)
    }
  },

  // 增强的背景音播放
  playBackgroundSound(soundId) {
    if (soundId === 'silent') return

    try {
      // 停止当前播放的音频
      this.stopBackgroundAudio()

      // 创建新的音频实例
      this.backgroundAudio = wx.createInnerAudioContext()
      this.backgroundAudio.src = `/sounds/${soundId}.mp3`
      this.backgroundAudio.loop = true
      this.backgroundAudio.volume = this.data.bgVolume / 100

      // 音频事件监听
      this.backgroundAudio.onPlay(() => {
        console.log(`开始播放背景音: ${soundId}`)
      })

      this.backgroundAudio.onError((error) => {
        console.error('背景音播放失败:', error)
        VantToast.fail('音效播放失败，请检查网络')
      })

      this.backgroundAudio.play()

    } catch (error) {
      console.error('创建音频实例失败:', error)
      VantToast.fail('音效初始化失败')
    }
  },

  // 完成会话
  completeSession() {
    // 停止计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    this.setData({
      isRunning: false,
      isPaused: false
    })

    // 停止背景音
    this.stopBackgroundAudio()

    // 震动提醒
    wx.vibrateShort()

    // 播放完成音效
    wx.playBackgroundAudio()

    // 更新任务进度
    this.updateTaskProgress()

    // 显示完成提示
    VantDialog.alert({
      title: '🎉 专注完成！',
      content: `恭喜完成一个${this.data.workDuration}分钟的专注时段！`,
      onConfirm: () => {
        // 可以跳转到完成页面
        if (this.data.focusMode) {
          this.exitFocusMode()
        }
      }
    })

    // 保存会话记录
    this.savePomodoroSession()

    // 更新使用统计
    this.updateUsageStats()

    // 同步到云端统计系统
    this.syncToCloudStats()

    // 增量数据同步
    this.incrementalSyncManager.markDataChanged('session')

    // 更新统计显示
    this.updatePomodoroStats()
  },

  // 格式化时间显示
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  },

  // 更新当前时间显示
  updateCurrentTime() {
    const duration = this.data.timerState === 'work' ? this.data.workDuration :
                    this.data.timerState === 'shortBreak' ? this.data.shortBreakDuration :
                    this.data.longBreakDuration

    const timeInSeconds = duration * 60
    this.setData({
      currentTime: timeInSeconds,
      totalTime: timeInSeconds,
      displayTime: this.formatTime(timeInSeconds),
      progressDegree: 0
    })
    this.updateTimerColor()
  },

  // 更新计时器颜色
  updateTimerColor() {
    let colorVar = '--timer-work-color'
    let color = '#FF6B6B'

    switch (this.data.timerState) {
      case 'work':
        colorVar = '--timer-work-color'
        color = '#FF6B6B'
        break
      case 'shortBreak':
        colorVar = '--timer-break-color'
        color = '#52C41A'
        break
      case 'longBreak':
        colorVar = '--timer-long-break-color'
        color = '#1890FF'
        break
    }

    this.setData({
      timerColorVar: colorVar,
      timerColor: color
    })
  },

  // 计时器触摸开始
  onTimerTouchStart(e) {
    if (this.data.isRunning) return

    const touch = e.touches[0]
    this.setData({
      touchStartY: touch.clientY,
      touchStartTime: Date.now(),
      isGesturing: false
    })
  },

  // 计时器触摸移动
  onTimerTouchMove(e) {
    if (this.data.isRunning) return

    const touch = e.touches[0]
    const deltaY = this.data.touchStartY - touch.clientY
    const deltaTime = Date.now() - this.data.touchStartTime

    // 防止与长按冲突，设置时间阈值
    if (deltaTime < 200) return

    // 设置移动阈值，避免误触
    if (Math.abs(deltaY) < 20) return

    if (!this.data.isGesturing) {
      this.setData({
        isGesturing: true,
        showGestureHint: true
      })
    }

    // 根据滑动方向显示提示
    if (deltaY > 0) {
      this.setData({
        gestureHintText: '上滑增加时间'
      })
    } else {
      this.setData({
        gestureHintText: '下滑减少时间'
      })
    }
  },

  // 计时器触摸结束
  onTimerTouchEnd(e) {
    if (this.data.isRunning || !this.data.isGesturing) {
      this.setData({
        showGestureHint: false,
        isGesturing: false
      })
      return
    }

    const touch = e.changedTouches[0]
    const deltaY = this.data.touchStartY - touch.clientY
    const deltaTime = Date.now() - this.data.touchStartTime

    // 防止与长按冲突
    if (deltaTime < 200) {
      this.setData({
        showGestureHint: false,
        isGesturing: false
      })
      return
    }

    // 设置移动阈值
    if (Math.abs(deltaY) < 30) {
      this.setData({
        showGestureHint: false,
        isGesturing: false
      })
      return
    }

    // 计算时间调整
    const timeAdjustment = Math.floor(Math.abs(deltaY) / 50) * 5 // 每50px调整5分钟
    const currentMinutes = Math.floor(this.data.currentTime / 60)
    let newMinutes

    if (deltaY > 0) {
      // 上滑增加时间
      newMinutes = Math.min(currentMinutes + timeAdjustment, 60)
    } else {
      // 下滑减少时间
      newMinutes = Math.max(currentMinutes - timeAdjustment, 5)
    }

    if (newMinutes !== currentMinutes) {
      this.adjustTimerDuration(newMinutes)
      wx.vibrateShort() // 触觉反馈
    }

    this.setData({
      showGestureHint: false,
      isGesturing: false
    })
  },

  // 调整计时器时长
  adjustTimerDuration(minutes) {
    const timeInSeconds = minutes * 60
    this.setData({
      currentTime: timeInSeconds,
      totalTime: timeInSeconds,
      displayTime: this.formatTime(timeInSeconds),
      progressDegree: 0
    })

    // 更新对应的时长设置
    if (this.data.timerState === 'work') {
      this.setData({ workDuration: minutes })
    } else if (this.data.timerState === 'shortBreak') {
      this.setData({ shortBreakDuration: minutes })
    } else if (this.data.timerState === 'longBreak') {
      this.setData({ longBreakDuration: minutes })
    }

    this.saveUserSettings()
  },

  // 设置计时器时长（点击时间节点）
  setTimerDuration(e) {
    if (this.data.isRunning) return

    const minutes = e.currentTarget.dataset.time
    this.adjustTimerDuration(minutes)
    wx.vibrateShort()

    // 高亮效果
    this.setData({ timeNodeHighlight: true })
    setTimeout(() => {
      this.setData({ timeNodeHighlight: false })
    }, 300)
  },

  // 统计标签页切换
  onStatsTabChange(e) {
    const tab = e.detail.name
    this.setData({
      currentStatsTab: tab,
      showTrendChart: false
    })
    this.updateStatsData()
  },

  // 统计卡片点击事件
  onStatCardTap(e) {
    const type = e.currentTarget.dataset.type
    const cardElement = e.currentTarget

    // 添加点击效果
    this.addStatCardClickEffect(cardElement)

    switch (type) {
      case 'focusTime':
        this.showTrendChart()
        break
      case 'sessions':
        this.showSessionDetail()
        break
      case 'tasks':
        this.showTaskDetail()
        break
      case 'efficiency':
        this.showEfficiencyDetail()
        break
    }
  },

  // 添加统计卡片点击效果
  addStatCardClickEffect(element) {
    // 添加点击动画效果
    element.style.transform = 'scale(0.95)'
    element.style.transition = 'transform 0.1s ease'

    setTimeout(() => {
      element.style.transform = 'scale(1)'
    }, 100)

    wx.vibrateShort()
  },

  // 显示趋势图表
  showTrendChart() {
    this.generateTrendData()
    this.setData({
      showTrendChart: true
    })
    wx.showToast({
      title: '查看专注趋势',
      icon: 'success',
      duration: 1500
    })
  },

  // 隐藏趋势图表
  hideTrendChart() {
    this.setData({
      showTrendChart: false
    })
  },

  // 显示会话详情
  showSessionDetail() {
    const currentStats = this.data.statsData[this.data.currentStatsTab]
    wx.showModal({
      title: '番茄钟详情',
      content: `${this.getStatsTabLabel()}完成了 ${currentStats.sessions} 个番茄钟\n平均每个番茄钟 ${Math.round(currentStats.focusTime / Math.max(currentStats.sessions, 1))} 分钟`,
      showCancel: false
    })
  },

  // 显示任务详情
  showTaskDetail() {
    const currentStats = this.data.statsData[this.data.currentStatsTab]
    wx.showModal({
      title: '任务完成详情',
      content: `${this.getStatsTabLabel()}完成了 ${currentStats.tasks} 个任务\n任务完成率 ${Math.round((currentStats.tasks / Math.max(currentStats.sessions, 1)) * 100)}%`,
      showCancel: false
    })
  },

  // 显示效率详情
  showEfficiencyDetail() {
    const currentStats = this.data.statsData[this.data.currentStatsTab]
    wx.showModal({
      title: '学习效率详情',
      content: `${this.getStatsTabLabel()}学习效率 ${currentStats.efficiency}%\n基于专注时长和任务完成情况计算`,
      showCancel: false
    })
  },

  // 获取统计标签页标签
  getStatsTabLabel() {
    const labels = {
      today: '今日',
      week: '本周',
      month: '本月'
    }
    const tab = Array.isArray(this.data.currentStatsTab) ? this.data.currentStatsTab[0] : this.data.currentStatsTab || 'today'
    return labels[tab] || '今日'
  },

  // 更新统计数据
  updateStatsData() {
    this.updatePomodoroStats()
  },

  // 计算趋势
  calculateTrends(current, yesterday) {
    const trends = {}

    Object.keys(current).forEach(key => {
      const currentValue = current[key] || 0
      const yesterdayValue = yesterday[key] || 0
      const change = currentValue - yesterdayValue
      const changePercent = yesterdayValue > 0 ? Math.round((change / yesterdayValue) * 100) : 0

      if (Math.abs(changePercent) >= 5) { // 只显示变化超过5%的趋势
        trends[key] = {
          type: change > 0 ? 'success' : 'danger',
          icon: change > 0 ? '↗' : '↘',
          text: `${Math.abs(changePercent)}%`
        }
      }
    })

    return trends
  },

  // 格式化统计值
  formatStatValue(type, value) {
    switch (type) {
      case 'focusTime':
        return `${value || 0}分钟`
      case 'sessions':
        return `${value || 0}个`
      case 'tasks':
        return `${value || 0}个`
      case 'efficiency':
        return `${value || 0}%`
      default:
        return `${value || 0}`
    }
  },

  // 生成趋势图表数据
  generateTrendData() {
    const tab = Array.isArray(this.data.currentStatsTab) ? this.data.currentStatsTab[0] : this.data.currentStatsTab || 'today'
    let data = []

    if (tab === 'today') {
      // 今日每小时数据
      data = this.generateHourlyData()
    } else if (tab === 'week') {
      // 本周每日数据
      data = this.generateWeeklyData()
    } else if (tab === 'month') {
      // 本月每周数据
      data = this.generateMonthlyData()
    }

    this.setData({
      trendData: data
    })
  },

  // 生成每小时数据
  generateHourlyData() {
    const hours = ['9', '10', '11', '12', '14', '15', '16', '17']
    return hours.map((hour, index) => ({
      label: `${hour}时`,
      focusHeight: Math.random() * 80 + 10, // 模拟数据
      color: '#FF6B6B',
      date: `hour_${hour}`
    }))
  },

  // 生成每日数据
  generateWeeklyData() {
    const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    return days.map((day, index) => ({
      label: day,
      focusHeight: Math.random() * 80 + 10, // 模拟数据
      color: '#52C41A',
      date: `day_${index}`
    }))
  },

  // 生成每周数据
  generateMonthlyData() {
    const weeks = ['第1周', '第2周', '第3周', '第4周']
    return weeks.map((week, index) => ({
      label: week,
      focusHeight: Math.random() * 80 + 10, // 模拟数据
      color: '#1890FF',
      date: `week_${index}`
    }))
  },

  // 初始化统计数据
  initStatsData() {
    this.loadTodayStats()
    this.updatePomodoroStats()
  },

  // 加载今日统计数据（统一数据源，支持离线和版本控制）
  loadTodayStats() {
    try {
      const today = new Date().toISOString().split('T')[0]
      const todayStats = wx.getStorageSync('kaoba_today_stats') || {
        date: today,
        completedPomodoros: 0,
        totalMinutes: 0,
        completedTasks: 0,
        goalProgress: 0,
        lastCloudSync: 0,
        version: '1.0.0'
      }

      // 如果是新的一天，重置统计
      if (todayStats.date !== today) {
        console.log('检测到新的一天，重置统计数据')
        const newStats = {
          date: today,
          completedPomodoros: 0,
          totalMinutes: 0,
          completedTasks: 0,
          goalProgress: 0,
          lastCloudSync: 0,
          version: '1.0.0'
        }
        wx.setStorageSync('kaoba_today_stats', newStats)

        // 更新页面数据
        this.setData({
          completedSessions: 0,
          todayFocusTime: 0,
          todayCompletedTasks: 0
        })
      } else {
        // 更新页面数据
        this.setData({
          completedSessions: todayStats.completedPomodoros || 0,
          todayFocusTime: todayStats.totalMinutes || 0,
          todayCompletedTasks: todayStats.completedTasks || 0
        })
      }

      // 检查数据新鲜度
      const lastSync = todayStats.lastCloudSync || 0
      const syncAge = Date.now() - lastSync
      const isDataFresh = syncAge < 10 * 60 * 1000 // 10分钟内的数据认为是新鲜的

      console.log('加载今日统计:', {
        ...todayStats,
        dataAge: Math.round(syncAge / 1000) + '秒',
        isFresh: isDataFresh
      })
    } catch (error) {
      console.error('加载今日统计失败:', error)
      // 错误时设置默认值
      this.setData({
        completedSessions: 0,
        todayFocusTime: 0,
        todayCompletedTasks: 0
      })
    }
  },

  // 保存今日统计数据（统一数据源）
  saveTodayStats() {
    try {
      const today = new Date().toISOString().split('T')[0]
      const todayStats = {
        date: today,
        completedPomodoros: this.data.completedSessions,
        totalMinutes: this.data.todayFocusTime,
        completedTasks: this.data.todayCompletedTasks,
        goalProgress: this.calculateGoalProgress()
      }

      wx.setStorageSync('kaoba_today_stats', todayStats)
      console.log('保存今日统计:', todayStats)
    } catch (error) {
      console.error('保存今日统计失败:', error)
    }
  },

  // 计算目标进度（支持用户自定义目标）
  calculateGoalProgress() {
    try {
      // 获取用户自定义的每日目标，默认为5个番茄钟
      const userSettings = wx.getStorageSync('kaoba_pomodoro_settings') || {}
      const dailyGoal = userSettings.dailyGoal || 5

      const currentSessions = this.data.completedSessions || 0
      const progress = Math.min(100, (currentSessions / dailyGoal) * 100)

      console.log('目标进度计算:', {
        当前完成: currentSessions,
        每日目标: dailyGoal,
        完成进度: Math.round(progress) + '%'
      })

      return Math.round(progress)
    } catch (error) {
      console.error('计算目标进度失败:', error)
      return 0
    }
  },

  // 获取用户每日目标设置
  getDailyGoal() {
    try {
      const userSettings = wx.getStorageSync('kaoba_pomodoro_settings') || {}
      return userSettings.dailyGoal || 5
    } catch (error) {
      console.error('获取每日目标失败:', error)
      return 5
    }
  },

  // 设置用户每日目标
  setDailyGoal(goal) {
    try {
      const userSettings = wx.getStorageSync('kaoba_pomodoro_settings') || {}
      userSettings.dailyGoal = Math.max(1, Math.min(20, goal)) // 限制在1-20之间
      wx.setStorageSync('kaoba_pomodoro_settings', userSettings)

      // 重新计算进度
      this.updatePomodoroStats()

      console.log('每日目标已设置为:', userSettings.dailyGoal)
      return true
    } catch (error) {
      console.error('设置每日目标失败:', error)
      return false
    }
  },

  // 验证统计数据的有效性
  validateStatsData() {
    try {
      const stats = {
        completedSessions: this.data.completedSessions || 0,
        todayFocusTime: this.data.todayFocusTime || 0,
        todayCompletedTasks: this.data.todayCompletedTasks || 0
      }

      // 验证数据范围
      const validations = {
        sessionsValid: stats.completedSessions >= 0 && stats.completedSessions <= 50, // 一天最多50个番茄钟
        timeValid: stats.todayFocusTime >= 0 && stats.todayFocusTime <= 1440, // 一天最多1440分钟
        tasksValid: stats.todayCompletedTasks >= 0 && stats.todayCompletedTasks <= 100 // 一天最多100个任务
      }

      // 验证数据逻辑一致性
      const logicValidations = {
        timeSessionRatio: stats.completedSessions === 0 ||
          (stats.todayFocusTime / stats.completedSessions) >= 5 &&
          (stats.todayFocusTime / stats.completedSessions) <= 120, // 每个番茄钟5-120分钟
        taskSessionRatio: stats.completedSessions === 0 ||
          stats.todayCompletedTasks <= stats.completedSessions * 2 // 任务数不超过番茄钟数的2倍
      }

      const allValid = Object.values(validations).every(v => v) &&
                      Object.values(logicValidations).every(v => v)

      if (!allValid) {
        console.warn('统计数据验证失败:', {
          数据: stats,
          范围验证: validations,
          逻辑验证: logicValidations
        })

        // 修复异常数据
        this.fixInvalidStatsData(stats, validations, logicValidations)
      }

      return allValid
    } catch (error) {
      console.error('验证统计数据失败:', error)
      return false
    }
  },

  // 修复无效的统计数据
  fixInvalidStatsData(stats, validations, logicValidations) {
    try {
      let fixedStats = { ...stats }

      // 修复范围异常
      if (!validations.sessionsValid) {
        fixedStats.completedSessions = Math.max(0, Math.min(50, stats.completedSessions))
      }
      if (!validations.timeValid) {
        fixedStats.todayFocusTime = Math.max(0, Math.min(1440, stats.todayFocusTime))
      }
      if (!validations.tasksValid) {
        fixedStats.todayCompletedTasks = Math.max(0, Math.min(100, stats.todayCompletedTasks))
      }

      // 修复逻辑异常
      if (!logicValidations.timeSessionRatio && fixedStats.completedSessions > 0) {
        // 如果时间/会话比例异常，调整时间
        const avgTime = fixedStats.todayFocusTime / fixedStats.completedSessions
        if (avgTime < 5) {
          fixedStats.todayFocusTime = fixedStats.completedSessions * 25 // 默认25分钟
        } else if (avgTime > 120) {
          fixedStats.todayFocusTime = fixedStats.completedSessions * 60 // 最多60分钟
        }
      }

      if (!logicValidations.taskSessionRatio) {
        // 如果任务/会话比例异常，调整任务数
        fixedStats.todayCompletedTasks = Math.min(
          fixedStats.todayCompletedTasks,
          fixedStats.completedSessions * 2
        )
      }

      // 更新修复后的数据
      this.setData(fixedStats)
      this.saveTodayStats()

      console.log('统计数据已修复:', fixedStats)
    } catch (error) {
      console.error('修复统计数据失败:', error)
    }
  },

  // 更新番茄钟统计数据（优化版，集成验证和趋势）
  updatePomodoroStats() {
    // 验证统计数据的有效性
    this.validateStatsData()

    // 获取统计数据，确保数据类型正确
    const focusTime = this.data.todayFocusTime || 0
    const sessions = this.data.completedSessions || 0
    const tasks = this.data.todayCompletedTasks || 0
    const efficiency = this.calculateTodayEfficiency()

    // 计算效率趋势
    const efficiencyTrend = this.calculateEfficiencyTrend()

    // 计算目标进度
    const goalProgress = this.calculateGoalProgress()

    // 按照任务中心的格式标准化数据显示
    const pomodoroStats = [
      {
        label: '专注时长',
        value: focusTime > 0 ? `${focusTime}分钟` : '0分钟',
        trend: focusTime > 0 ? this.getTimeTrend(focusTime) : null
      },
      {
        label: '完成番茄',
        value: sessions.toString() + '个',
        progress: goalProgress,
        goal: this.getDailyGoal()
      },
      {
        label: '完成任务',
        value: tasks.toString() + '个',
        ratio: sessions > 0 ? Math.round((tasks / sessions) * 100) : 0
      },
      {
        label: '学习效率',
        value: efficiency > 0 ? `${efficiency}%` : '0%',
        trend: efficiencyTrend.text,
        trendType: efficiencyTrend.trend
      }
    ]

    this.setData({ pomodoroStats })

    // 更新可视化组件
    this.updateProgressRing()
    this.generateComparisonData()
    this.generateTrendData()

    console.log('番茄钟统计数据已更新:', pomodoroStats)
    console.log('详细统计分析:', {
      专注时长: focusTime,
      完成番茄: sessions,
      完成任务: tasks,
      学习效率: efficiency,
      效率趋势: efficiencyTrend,
      目标进度: goalProgress + '%',
      任务效率: sessions > 0 ? `${Math.round((tasks / sessions) * 100)}%` : '0%'
    })
  },

  // 获取专注时长趋势
  getTimeTrend(currentTime) {
    try {
      const sessions = wx.getStorageSync('kaoba_pomodoro_sessions') || []
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayStr = yesterday.toISOString().split('T')[0]

      // 计算昨日专注时长
      const yesterdaySessions = sessions.filter(session => {
        const sessionDate = new Date(session.completedAt).toISOString().split('T')[0]
        return sessionDate === yesterdayStr
      })

      const yesterdayTime = yesterdaySessions.reduce((sum, session) => sum + (session.duration || 25), 0)

      if (yesterdayTime === 0) {
        return '新记录'
      }

      const change = currentTime - yesterdayTime
      const changePercent = Math.round((change / yesterdayTime) * 100)

      if (change > 10) {
        return `+${changePercent}%`
      } else if (change < -10) {
        return `${changePercent}%`
      } else {
        return '持平'
      }
    } catch (error) {
      console.error('计算时长趋势失败:', error)
      return '暂无数据'
    }
  },

  // 可视化增强方法
  // 更新进度环形图
  updateProgressRing() {
    const dailyGoal = this.getDailyGoal()
    const currentSessions = this.data.completedSessions || 0
    const progress = Math.min(100, Math.round((currentSessions / dailyGoal) * 100))

    this.setData({
      dailyGoal: dailyGoal,
      goalProgress: progress
    })

    console.log('进度环形图已更新:', {
      当前完成: currentSessions,
      每日目标: dailyGoal,
      完成进度: progress + '%'
    })
  },

  // 生成数据对比
  generateComparisonData() {
    try {
      const sessions = wx.getStorageSync('kaoba_pomodoro_sessions') || []
      const today = new Date()

      let comparisonData = []

      if (this.data.comparisonPeriod === 'daily') {
        comparisonData = this.generateDailyComparison(sessions, today)
      } else {
        comparisonData = this.generateWeeklyComparison(sessions, today)
      }

      this.setData({ comparisonData })
      console.log('数据对比已生成:', comparisonData)
    } catch (error) {
      console.error('生成数据对比失败:', error)
    }
  },

  // 计算统计数据
  calculateDayStats(sessions) {
    const focusTime = sessions.reduce((sum, session) => sum + (session.duration || 25), 0)
    const sessionCount = sessions.length
    const efficiency = sessionCount > 0 ?
      Math.round(sessions.reduce((sum, session) => sum + (session.efficiency || 75), 0) / sessionCount) : 0

    return {
      focusTime: focusTime,
      sessions: sessionCount,
      efficiency: efficiency
    }
  },

  // 获取趋势方向
  getTrendDirection(current, previous) {
    if (previous === 0) return 'stable'
    const change = ((current - previous) / previous) * 100
    if (change > 5) return 'up'
    if (change < -5) return 'down'
    return 'stable'
  },

  // 获取变化文本
  getChangeText(current, previous, unit) {
    if (previous === 0) {
      return current > 0 ? '新记录' : '无数据'
    }

    const change = current - previous
    const changePercent = Math.round((change / previous) * 100)

    if (Math.abs(changePercent) < 5) {
      return '基本持平'
    }

    return changePercent > 0 ? `+${changePercent}%` : `${changePercent}%`
  },

  // 生成每日对比数据
  generateDailyComparison(sessions, today) {
    const todayStr = today.toISOString().split('T')[0]
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    const yesterdayStr = yesterday.toISOString().split('T')[0]

    const todaySessions = sessions.filter(session => {
      const sessionDate = new Date(session.completedAt).toISOString().split('T')[0]
      return sessionDate === todayStr
    })

    const yesterdaySessions = sessions.filter(session => {
      const sessionDate = new Date(session.completedAt).toISOString().split('T')[0]
      return sessionDate === yesterdayStr
    })

    const todayStats = this.calculateDayStats(todaySessions)
    const yesterdayStats = this.calculateDayStats(yesterdaySessions)

    return [
      {
        label: '专注时长',
        current: todayStats.focusTime + '分钟',
        previous: yesterdayStats.focusTime + '分钟',
        trend: this.getTrendDirection(todayStats.focusTime, yesterdayStats.focusTime),
        changeText: this.getChangeText(todayStats.focusTime, yesterdayStats.focusTime, '分钟')
      },
      {
        label: '完成番茄',
        current: todayStats.sessions + '个',
        previous: yesterdayStats.sessions + '个',
        trend: this.getTrendDirection(todayStats.sessions, yesterdayStats.sessions),
        changeText: this.getChangeText(todayStats.sessions, yesterdayStats.sessions, '个')
      },
      {
        label: '学习效率',
        current: todayStats.efficiency + '%',
        previous: yesterdayStats.efficiency + '%',
        trend: this.getTrendDirection(todayStats.efficiency, yesterdayStats.efficiency),
        changeText: this.getChangeText(todayStats.efficiency, yesterdayStats.efficiency, '%')
      }
    ]
  },

  // 生成每周对比数据
  generateWeeklyComparison(sessions, today) {
    const thisWeekSessions = this.getWeekSessions(sessions, today, 0)
    const lastWeekSessions = this.getWeekSessions(sessions, today, 1)

    const thisWeekStats = this.calculateDayStats(thisWeekSessions)
    const lastWeekStats = this.calculateDayStats(lastWeekSessions)

    return [
      {
        label: '专注时长',
        current: thisWeekStats.focusTime + '分钟',
        previous: lastWeekStats.focusTime + '分钟',
        trend: this.getTrendDirection(thisWeekStats.focusTime, lastWeekStats.focusTime),
        changeText: this.getChangeText(thisWeekStats.focusTime, lastWeekStats.focusTime, '分钟')
      },
      {
        label: '完成番茄',
        current: thisWeekStats.sessions + '个',
        previous: lastWeekStats.sessions + '个',
        trend: this.getTrendDirection(thisWeekStats.sessions, lastWeekStats.sessions),
        changeText: this.getChangeText(thisWeekStats.sessions, lastWeekStats.sessions, '个')
      },
      {
        label: '学习效率',
        current: thisWeekStats.efficiency + '%',
        previous: lastWeekStats.efficiency + '%',
        trend: this.getTrendDirection(thisWeekStats.efficiency, lastWeekStats.efficiency),
        changeText: this.getChangeText(thisWeekStats.efficiency, lastWeekStats.efficiency, '%')
      }
    ]
  },

  // 获取指定周的会话数据
  getWeekSessions(sessions, referenceDate, weeksAgo) {
    const startOfWeek = new Date(referenceDate)
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay() - (weeksAgo * 7))
    startOfWeek.setHours(0, 0, 0, 0)

    const endOfWeek = new Date(startOfWeek)
    endOfWeek.setDate(endOfWeek.getDate() + 6)
    endOfWeek.setHours(23, 59, 59, 999)

    return sessions.filter(session => {
      const sessionDate = new Date(session.completedAt)
      return sessionDate >= startOfWeek && sessionDate <= endOfWeek
    })
  },

  // 计算今日学习效率（优化版）
  calculateTodayEfficiency() {
    try {
      const sessions = wx.getStorageSync('kaoba_pomodoro_sessions') || []
      const today = new Date().toISOString().split('T')[0]

      // 筛选今日的会话
      const todaySessions = sessions.filter(session => {
        const sessionDate = new Date(session.completedAt).toISOString().split('T')[0]
        return sessionDate === today && session.efficiency
      })

      if (todaySessions.length === 0) {
        return 0
      }

      // 计算加权平均效率（考虑时长权重）
      let totalWeightedEfficiency = 0
      let totalWeight = 0

      todaySessions.forEach(session => {
        const weight = session.duration || 25 // 使用时长作为权重
        totalWeightedEfficiency += session.efficiency * weight
        totalWeight += weight
      })

      const weightedAverage = totalWeight > 0 ? totalWeightedEfficiency / totalWeight : 0

      // 添加学习连续性加分
      const continuityBonus = this.calculateContinuityBonus(todaySessions)

      // 添加任务完成度加分
      const taskCompletionBonus = this.calculateTaskCompletionBonus(todaySessions)

      // 计算最终效率
      let finalEfficiency = weightedAverage + continuityBonus + taskCompletionBonus

      // 确保效率值在合理范围内
      finalEfficiency = Math.min(100, Math.max(0, Math.round(finalEfficiency)))

      console.log('今日效率计算详情:', {
        会话数量: todaySessions.length,
        加权平均: Math.round(weightedAverage),
        连续性加分: continuityBonus,
        任务完成加分: taskCompletionBonus,
        最终效率: finalEfficiency
      })

      return finalEfficiency
    } catch (error) {
      console.error('计算今日效率失败:', error)
      return 0
    }
  },

  // 计算学习连续性加分
  calculateContinuityBonus(todaySessions) {
    if (todaySessions.length <= 1) return 0

    // 按时间排序
    const sortedSessions = todaySessions.sort((a, b) =>
      new Date(a.completedAt) - new Date(b.completedAt)
    )

    let continuityBonus = 0
    let consecutiveCount = 1

    // 检查会话间的时间间隔
    for (let i = 1; i < sortedSessions.length; i++) {
      const prevTime = new Date(sortedSessions[i-1].completedAt)
      const currTime = new Date(sortedSessions[i].completedAt)
      const timeDiff = (currTime - prevTime) / (1000 * 60) // 分钟差

      // 如果间隔在30分钟到2小时之间，认为是连续学习
      if (timeDiff >= 30 && timeDiff <= 120) {
        consecutiveCount++
      } else {
        consecutiveCount = 1
      }
    }

    // 根据连续会话数给予加分
    if (consecutiveCount >= 4) {
      continuityBonus = 8 // 4个以上连续会话
    } else if (consecutiveCount >= 3) {
      continuityBonus = 5 // 3个连续会话
    } else if (consecutiveCount >= 2) {
      continuityBonus = 3 // 2个连续会话
    }

    return continuityBonus
  },

  // 计算任务完成度加分
  calculateTaskCompletionBonus(todaySessions) {
    const sessionsWithTask = todaySessions.filter(session => session.taskId)
    const taskCompletionRate = todaySessions.length > 0 ?
      (sessionsWithTask.length / todaySessions.length) * 100 : 0

    let bonus = 0
    if (taskCompletionRate >= 80) {
      bonus = 10 // 80%以上的会话有明确任务
    } else if (taskCompletionRate >= 60) {
      bonus = 6 // 60%以上的会话有明确任务
    } else if (taskCompletionRate >= 40) {
      bonus = 3 // 40%以上的会话有明确任务
    }

    return bonus
  },

  // 计算效率趋势（与昨日对比）
  calculateEfficiencyTrend() {
    try {
      const sessions = wx.getStorageSync('kaoba_pomodoro_sessions') || []
      const today = new Date().toISOString().split('T')[0]
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayStr = yesterday.toISOString().split('T')[0]

      // 获取今日和昨日的效率
      const todayEfficiency = this.calculateDayEfficiency(sessions, today)
      const yesterdayEfficiency = this.calculateDayEfficiency(sessions, yesterdayStr)

      if (yesterdayEfficiency === 0) {
        return { trend: 'new', change: 0, text: '开始记录' }
      }

      const change = todayEfficiency - yesterdayEfficiency
      const changePercent = Math.round((change / yesterdayEfficiency) * 100)

      let trend = 'stable'
      let text = '持平'

      if (change > 5) {
        trend = 'up'
        text = `+${changePercent}%`
      } else if (change < -5) {
        trend = 'down'
        text = `${changePercent}%`
      }

      return { trend, change, text, todayEfficiency, yesterdayEfficiency }
    } catch (error) {
      console.error('计算效率趋势失败:', error)
      return { trend: 'stable', change: 0, text: '暂无数据' }
    }
  },

  // 计算指定日期的效率
  calculateDayEfficiency(sessions, dateStr) {
    const daySessions = sessions.filter(session => {
      const sessionDate = new Date(session.completedAt).toISOString().split('T')[0]
      return sessionDate === dateStr && session.efficiency
    })

    if (daySessions.length === 0) return 0

    const totalEfficiency = daySessions.reduce((sum, session) => sum + session.efficiency, 0)
    return Math.round(totalEfficiency / daySessions.length)
  },

  // 加载使用统计
  loadUsageStats() {
    try {
      const stats = wx.getStorageSync('kaoba_pomodoro_usage_stats')
      if (stats) {
        this.setData({
          usageStats: {
            ...this.data.usageStats,
            ...stats
          }
        })
      }
    } catch (error) {
      console.error('加载使用统计失败:', error)
    }
  },

  // 保存使用统计
  saveUsageStats() {
    try {
      wx.setStorageSync('kaoba_pomodoro_usage_stats', this.data.usageStats)
    } catch (error) {
      console.error('保存使用统计失败:', error)
    }
  },

  // 更新使用统计
  updateUsageStats() {
    const stats = this.data.usageStats
    stats.totalSessions += 1
    stats.averageWorkDuration = Math.round((stats.averageWorkDuration * (stats.totalSessions - 1) + this.data.workDuration) / stats.totalSessions)
    stats.mostUsedSound = this.data.selectedBgSound

    this.setData({
      usageStats: stats
    })

    this.saveUsageStats()
  },

  // 播放背景音
  playBackgroundSound(soundId) {
    if (!this.backgroundAudio || soundId === 'silent') return

    try {
      this.stopBackgroundAudio()
      
      // 这里应该设置实际的音频文件路径
      const soundUrls = {
        rain: '/sounds/rain.mp3',
        ocean: '/sounds/ocean.mp3',
        cafe: '/sounds/cafe.mp3',
        forest: '/sounds/forest.mp3',
        whitenoise: '/sounds/whitenoise.mp3'
      }

      if (soundUrls[soundId]) {
        this.backgroundAudio.src = soundUrls[soundId]
        this.backgroundAudio.play()
      }
    } catch (error) {
      console.error('播放背景音失败', error)
      VantToast.error('播放背景音失败')
    }
  },

  // 停止背景音
  stopBackgroundAudio() {
    if (this.backgroundAudio) {
      this.backgroundAudio.stop()
    }
  },

  // 暂停背景音
  pauseBackgroundAudio() {
    if (this.backgroundAudio) {
      this.backgroundAudio.pause()
    }
  },

  // 清理资源
  cleanup() {
    // 停止计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 停止背景音
    if (this.backgroundAudio) {
      try {
        this.backgroundAudio.stop()
        this.backgroundAudio.destroy()
      } catch (error) {
        console.log('清理音频资源失败:', error)
      }
    }

    // 取消屏幕常亮
    try {
      wx.setKeepScreenOn({
        keepScreenOn: false
      })
    } catch (error) {
      console.log('取消屏幕常亮失败:', error)
    }
  },

  // === Vant组件适配方法 ===



  // 显示设置弹窗
  showSettings() {
    // 隐藏自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().hide()
    }

    // 生成智能推荐
    this.generateSmartRecommendation()

    this.setData({
      showSettingsModal: true,
      activeSettingsTab: ['time']
    })
  },

  // 隐藏设置弹窗
  hideSettings() {
    this.setData({
      showSettingsModal: false,
      showRecommendation: false
    })
    // 显示自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().show()
    }
  },

  // 设置标签页切换
  onSettingsTabChange(e) {
    this.setData({
      activeSettingsTab: e.detail
    })
  },

  // 显示预设配置
  showPresetConfigs() {
    this.setData({
      showPresetModal: true
    })
  },

  // 隐藏预设配置弹窗
  hidePresetModal() {
    this.setData({
      showPresetModal: false
    })
  },

  // 应用预设配置
  applyPresetConfig(e) {
    const presetId = e.currentTarget.dataset.preset
    const preset = this.data.presetConfigs.find(p => p.id === presetId)

    if (preset) {
      wx.showModal({
        title: '应用预设配置',
        content: `确定要应用"${preset.name}"配置吗？这将覆盖当前设置。`,
        confirmText: '应用',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 应用配置
            this.setData({
              ...preset.config,
              showPresetModal: false
            })

            // 更新音效标签
            this.updateSoundLabel()

            // 更新当前时间显示
            this.updateCurrentTime()

            // 保存设置
            this.saveUserSettings()

            VantToast.success(`已应用${preset.name}`)
          }
        }
      })
    }
  },

  // 显示音效分类选择器
  showSoundCategorySelector() {
    this.setData({
      showSoundCategoryModal: true,
      activeSoundCategory: 'basic',
      filteredSounds: this.data.backgroundSounds.filter(s => s.category === 'basic')
    })
  },

  // 隐藏音效分类选择弹窗
  hideSoundCategoryModal() {
    this.setData({
      showSoundCategoryModal: false
    })
  },

  // 音效分类切换
  onSoundCategoryChange(e) {
    const category = e.detail.name
    const filteredSounds = this.data.backgroundSounds.filter(s => s.category === category)

    this.setData({
      activeSoundCategory: category,
      filteredSounds: filteredSounds
    })
  },

  // 长休息间隔变更
  changeLongBreakInterval(e) {
    const interval = e.detail
    this.setData({
      longBreakInterval: interval
    })
    this.saveUserSettings()
  },

  // 生成智能推荐
  generateSmartRecommendation() {
    const stats = this.data.usageStats
    let recommendation = ''

    // 基于使用统计生成推荐
    if (stats.totalSessions > 10) {
      if (stats.averageWorkDuration < 20) {
        recommendation = '💡 建议：您的平均专注时长较短，可以尝试逐步增加工作时长到25分钟'
      } else if (stats.averageWorkDuration > 45) {
        recommendation = '💡 建议：长时间专注可能导致疲劳，建议适当缩短工作时长并增加休息'
      } else if (stats.mostUsedSound === 'silent') {
        recommendation = '💡 建议：尝试使用背景音效，如森林或白噪音，可能有助于提升专注力'
      }
    } else {
      // 新用户推荐
      recommendation = '💡 新手建议：推荐使用学生模式(25-5-15)开始您的专注之旅'
    }

    if (recommendation) {
      this.setData({
        showRecommendation: true,
        recommendationText: recommendation
      })
    }
  },

  // 隐藏推荐
  hideRecommendation() {
    this.setData({
      showRecommendation: false
    })
  },

  // 重置设置
  resetSettings() {
    VantDialog.confirm({
      title: '重置设置',
      content: '确定要重置所有设置到默认值吗？此操作不可撤销。',
      onConfirm: () => {
        // 重置到默认值
        this.setData({
          workDuration: 25,
          shortBreakDuration: 5,
          longBreakDuration: 15,
          longBreakInterval: 4,
          enableVibration: true,
          enableNotification: true,
          autoStartBreak: false,
          autoStartWork: false,
          selectedBgSound: 'silent',
          bgVolume: 50
        })

        this.updateSoundLabel()
        this.updateCurrentTime()
        this.saveUserSettings()

        VantToast.success('设置已重置')
      }
    })
  },

  // 导出设置
  exportSettings() {
    const settings = {
      workDuration: this.data.workDuration,
      shortBreakDuration: this.data.shortBreakDuration,
      longBreakDuration: this.data.longBreakDuration,
      longBreakInterval: this.data.longBreakInterval,
      enableVibration: this.data.enableVibration,
      enableNotification: this.data.enableNotification,
      autoStartBreak: this.data.autoStartBreak,
      autoStartWork: this.data.autoStartWork,
      selectedBgSound: this.data.selectedBgSound,
      bgVolume: this.data.bgVolume,
      exportTime: new Date().toISOString()
    }

    const settingsStr = JSON.stringify(settings, null, 2)

    wx.setClipboardData({
      data: settingsStr,
      success: () => {
        VantToast.success('设置已复制到剪贴板')
      },
      fail: () => {
        VantToast.fail('导出失败')
      }
    })
  },

  // 导入设置
  importSettings() {
    wx.getClipboardData({
      success: (res) => {
        try {
          const settings = JSON.parse(res.data)

          // 验证设置格式
          if (this.validateSettingsFormat(settings)) {
            VantDialog.confirm({
              title: '导入设置',
              content: '确定要导入剪贴板中的设置吗？这将覆盖当前设置。',
              onConfirm: () => {
                // 应用导入的设置
                this.setData({
                  workDuration: settings.workDuration || 25,
                  shortBreakDuration: settings.shortBreakDuration || 5,
                  longBreakDuration: settings.longBreakDuration || 15,
                  longBreakInterval: settings.longBreakInterval || 4,
                  enableVibration: settings.enableVibration !== false,
                  enableNotification: settings.enableNotification !== false,
                  autoStartBreak: settings.autoStartBreak || false,
                  autoStartWork: settings.autoStartWork || false,
                  selectedBgSound: settings.selectedBgSound || 'silent',
                  bgVolume: settings.bgVolume || 50
                })

                this.updateSoundLabel()
                this.updateCurrentTime()
                this.saveUserSettings()

                VantToast.success('设置导入成功')
              }
            })
          } else {
            VantToast.fail('设置格式不正确')
          }
        } catch (error) {
          VantToast.fail('剪贴板内容不是有效的设置格式')
        }
      },
      fail: () => {
        VantToast.fail('读取剪贴板失败')
      }
    })
  },

  // 验证设置格式
  validateSettingsFormat(settings) {
    const requiredFields = ['workDuration', 'shortBreakDuration', 'longBreakDuration']
    return requiredFields.every(field => typeof settings[field] === 'number')
  },



  // 添加按钮点击动画
  addButtonClickAnimation(element) {
    if (!element) return

    element.style.animation = 'buttonPress 0.15s ease-out'
    setTimeout(() => {
      element.style.animation = ''
    }, 150)
  },

  // 添加卡片滑入动画
  addCardSlideAnimation() {
    const cards = this.selectAllComponents('.stat-card')
    cards.forEach((card, index) => {
      setTimeout(() => {
        card.setData({
          animation: 'cardSlideIn 0.3s ease-out'
        })
      }, index * 100)
    })
  },

  // 添加统计数字动画
  animateStatsNumbers() {
    const statElements = [
      { key: 'completedSessions', duration: 1000 },
      { key: 'todayFocusTime', duration: 1200 },
      { key: 'todayCompletedTasks', duration: 800 }
    ]

    statElements.forEach(stat => {
      this.animateNumber(stat.key, this.data[stat.key], stat.duration)
    })
  },

  // 数字动画
  animateNumber(key, targetValue, duration) {
    const startValue = 0
    const startTime = Date.now()

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)

      // 使用缓动函数
      const easeOutQuart = 1 - Math.pow(1 - progress, 4)
      const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart)

      this.setData({
        [key]: currentValue
      })

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }

    animate()
  },

  // 显示声音设置
  showSoundSettings() {
    // 隐藏自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().hide()
    }
    this.setData({
      showSoundModal: true
    })
  },

  // 隐藏声音设置
  hideSoundModal() {
    this.setData({
      showSoundModal: false
    })
    // 显示自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().show()
    }
  },

  // 选择背景音
  selectBackgroundSound(e) {
    const soundId = e.currentTarget.dataset.sound
    this.setData({
      selectedBgSound: soundId
    })
    this.updateSoundLabel()
    this.saveUserSettings()
    
    // 如果正在播放，切换音乐
    if (this.data.isRunning && soundId !== 'silent') {
      this.playBackgroundSound(soundId)
    }
  },

  // 调整背景音量
  adjustBgVolume(e) {
    const volume = e.detail.value
    this.setData({
      bgVolume: volume
    })
    
    if (this.backgroundAudio) {
      this.backgroundAudio.volume = volume / 100
    }
    
    this.saveUserSettings()
  },

  // 工作时长变更
  changeWorkDuration(e) {
    const duration = e.detail
    this.setData({
      workDuration: duration
    })
    
    if (this.data.timerState === 'work') {
      this.updateCurrentTime()
    }
    
    this.saveUserSettings()
  },

  // 短休息时长变更
  changeShortBreakDuration(e) {
    const duration = e.detail
    this.setData({
      shortBreakDuration: duration
    })
    
    if (this.data.timerState === 'shortBreak') {
      this.updateCurrentTime()
    }
    
    this.saveUserSettings()
  },

  // 长休息时长变更
  changeLongBreakDuration(e) {
    const duration = e.detail
    this.setData({
      longBreakDuration: duration
    })
    
    if (this.data.timerState === 'longBreak') {
      this.updateCurrentTime()
    }
    
    this.saveUserSettings()
  },

  // 切换震动
  toggleVibration(e) {
    const enabled = e.detail
    this.setData({
      enableVibration: enabled
    })
    this.saveUserSettings()
  },

  // 切换通知
  toggleNotification(e) {
    const enabled = e.detail
    this.setData({
      enableNotification: enabled
    })
    this.saveUserSettings()
  },

  // 切换自动开始休息
  toggleAutoStartBreak(e) {
    const enabled = e.detail
    this.setData({
      autoStartBreak: enabled
    })
    this.saveUserSettings()
  },

  // 切换自动开始工作
  toggleAutoStartWork(e) {
    const enabled = e.detail
    this.setData({
      autoStartWork: enabled
    })
    this.saveUserSettings()
  },

  // 查看统计
  viewStatistics() {
    wx.navigateTo({
      url: '/pages/statistics-detail/index'
    })
  },

  // 更新任务进度
  async updateTaskProgress() {
    if (this.data.selectedTask && this.data.studyMode === 'task') {
      try {
        console.log('更新任务状态为完成:', this.data.selectedTask.id)
        
        // 调用API更新任务状态为完成
        const result = await SmartApi.completeTask(this.data.selectedTask.id, true)
        
        if (result.success) {
          console.log('任务状态更新成功')
          VantToast.success('任务已完成！')
          
          // 清空选中的任务
          this.setData({
            selectedTask: null,
            studyMode: 'quick'
          })
        } else {
          console.error('任务状态更新失败:', result.error)
          VantToast.error('任务状态更新失败')
        }
      } catch (error) {
        console.error('更新任务状态失败:', error)
        VantToast.error('更新任务状态失败')
      }
    }
  },

  // 保存番茄钟会话
  savePomodoroSession() {
    const session = {
      taskId: this.data.selectedTask ? this.data.selectedTask.id : null,
      taskTitle: this.data.selectedTask ? this.data.selectedTask.title : '快速专注',
      duration: this.data.workDuration,
      completedAt: new Date().toISOString(),
      sessionType: this.data.timerState,
      efficiency: this.calculateSessionEfficiency()
    }

    try {
      // 保存会话记录
      const sessions = wx.getStorageSync('kaoba_pomodoro_sessions') || []
      sessions.push(session)
      wx.setStorageSync('kaoba_pomodoro_sessions', sessions)

      // 更新统计数据（使用统一的数据管理）
      this.setData({
        completedSessions: this.data.completedSessions + 1,
        todayFocusTime: this.data.todayFocusTime + this.data.workDuration
      })

      // 如果完成的是任务相关的番茄钟，更新任务完成数
      if (this.data.selectedTask) {
        this.setData({
          todayCompletedTasks: this.data.todayCompletedTasks + 1
        })
      }

      // 保存到统一的今日统计
      this.saveTodayStats()

      console.log('番茄钟会话已保存:', session)
    } catch (error) {
      console.error('保存会话失败:', error)
    }
  },

  // 计算会话效率（基于真实用户行为数据）
  calculateSessionEfficiency() {
    try {
      let efficiency = 50 // 基础效率分数

      // 1. 基于完成时间计算效率
      const actualDuration = this.data.workDuration // 设定的专注时长
      const targetDuration = 25 // 标准番茄钟时长

      // 时长效率：接近标准时长得分更高
      if (actualDuration >= 20 && actualDuration <= 30) {
        efficiency += 25 // 标准时长范围内
      } else if (actualDuration >= 15 && actualDuration <= 35) {
        efficiency += 15 // 接近标准时长
      } else if (actualDuration >= 10) {
        efficiency += 10 // 至少有一定专注时间
      }

      // 2. 基于任务完成情况
      if (this.data.selectedTask) {
        efficiency += 15 // 有明确学习目标
      }

      // 3. 基于时间段效率（参考现有逻辑）
      const currentHour = new Date().getHours()
      if (currentHour >= 8 && currentHour <= 11) {
        efficiency += 10 // 上午时段，专注度较高
      } else if (currentHour >= 14 && currentHour <= 17) {
        efficiency += 8 // 下午时段，专注度中等
      } else if (currentHour >= 19 && currentHour <= 22) {
        efficiency += 5 // 晚上时段，专注度一般
      }

      // 4. 基于连续专注天数（从本地存储获取）
      const consecutiveDays = this.getConsecutiveStudyDays()
      if (consecutiveDays >= 7) {
        efficiency += 10 // 连续一周以上
      } else if (consecutiveDays >= 3) {
        efficiency += 5 // 连续三天以上
      }

      // 确保效率值在合理范围内
      efficiency = Math.min(100, Math.max(30, efficiency))

      console.log('会话效率计算:', {
        基础分: 50,
        时长分: actualDuration,
        任务分: this.data.selectedTask ? 15 : 0,
        时段分: this.getTimeSlotBonus(),
        连续分: consecutiveDays,
        最终效率: efficiency
      })

      return efficiency
    } catch (error) {
      console.error('计算会话效率失败:', error)
      return 75 // 默认效率值
    }
  },

  // 获取连续学习天数
  getConsecutiveStudyDays() {
    try {
      const sessions = wx.getStorageSync('kaoba_pomodoro_sessions') || []
      if (sessions.length === 0) return 0

      const today = new Date()
      let consecutiveDays = 0

      // 从今天开始往前检查连续学习天数
      for (let i = 0; i < 30; i++) { // 最多检查30天
        const checkDate = new Date(today)
        checkDate.setDate(today.getDate() - i)
        const dateStr = checkDate.toISOString().split('T')[0]

        // 检查这一天是否有学习记录
        const dayHasSessions = sessions.some(session => {
          const sessionDate = new Date(session.completedAt).toISOString().split('T')[0]
          return sessionDate === dateStr
        })

        if (dayHasSessions) {
          consecutiveDays++
        } else if (i > 0) {
          // 如果不是今天且没有记录，则中断连续计数
          break
        }
      }

      return consecutiveDays
    } catch (error) {
      console.error('获取连续学习天数失败:', error)
      return 0
    }
  },

  // 获取时间段加分
  getTimeSlotBonus() {
    const currentHour = new Date().getHours()
    if (currentHour >= 8 && currentHour <= 11) {
      return 10 // 上午黄金时段
    } else if (currentHour >= 14 && currentHour <= 17) {
      return 8 // 下午时段
    } else if (currentHour >= 19 && currentHour <= 22) {
      return 5 // 晚上时段
    }
    return 0 // 其他时段
  },

  // 同步统计数据到云端系统
  async syncToCloudStats() {
    try {
      // 检查是否有选中的任务，确定科目信息
      const subject = this.data.selectedTask ?
        (this.data.selectedTask.subject || this.data.selectedTask.title) :
        '专注练习'

      // 准备统计数据，格式符合statsManager云函数要求
      const statsData = {
        studyTime: this.data.workDuration * 60, // 转换为秒，符合现有API格式
        completedTasks: this.data.selectedTask ? 1 : 0, // 只有选择任务时才计算任务完成
        pomodoroSessions: 1, // 完成一个番茄钟
        efficiency: this.calculateSessionEfficiency(), // 计算本次会话效率
        subject: subject // 科目信息
      }

      console.log('准备同步统计数据到云端:', statsData)

      // 调用现有的SmartApi更新统计
      const result = await SmartApi.updateDailyStats(statsData)

      if (result.success) {
        console.log('统计数据同步成功')
      } else {
        console.error('统计数据同步失败:', result.error)
      }
    } catch (error) {
      console.error('同步统计数据到云端失败:', error)
      // 云端同步失败不影响本地功能，只记录错误
    }
  },

  // 数据版本控制和迁移管理
  dataVersionManager: {
    currentVersion: '2.0.0',

    // 检查并执行数据迁移
    checkAndMigrate() {
      try {
        const storedVersion = wx.getStorageSync('kaoba_data_version') || '1.0.0'

        if (storedVersion !== this.currentVersion) {
          console.log(`检测到数据版本变化: ${storedVersion} -> ${this.currentVersion}`)
          this.migrateData(storedVersion, this.currentVersion)
          wx.setStorageSync('kaoba_data_version', this.currentVersion)
        }
      } catch (error) {
        console.error('数据版本检查失败:', error)
      }
    },

    // 执行数据迁移
    migrateData(fromVersion, toVersion) {
      console.log(`开始数据迁移: ${fromVersion} -> ${toVersion}`)

      // 从1.0.0迁移到2.0.0：统一存储key命名
      if (fromVersion === '1.0.0' && toVersion === '2.0.0') {
        this.migrateFrom1To2()
      }

      console.log('数据迁移完成')
    },

    // 从1.0.0迁移到2.0.0
    migrateFrom1To2() {
      const migrations = [
        { old: 'todayStats', new: 'kaoba_today_stats' },
        { old: 'pomodoroSettings', new: 'kaoba_pomodoro_settings' },
        { old: 'pomodoroSessions', new: 'kaoba_pomodoro_sessions' },
        { old: 'pomodoroUsageStats', new: 'kaoba_pomodoro_usage_stats' },
        { old: 'statsLastSyncTime', new: 'kaoba_stats_last_sync_time' }
      ]

      migrations.forEach(migration => {
        try {
          const oldData = wx.getStorageSync(migration.old)
          if (oldData) {
            wx.setStorageSync(migration.new, oldData)
            wx.removeStorageSync(migration.old)
            console.log(`迁移存储key: ${migration.old} -> ${migration.new}`)
          }
        } catch (error) {
          console.error(`迁移${migration.old}失败:`, error)
        }
      })
    }
  },

  // 数据完整性检查和修复
  dataIntegrityChecker: {
    // 检查所有番茄钟相关数据的完整性
    checkAllData() {
      console.log('开始数据完整性检查...')

      const results = {
        todayStats: this.checkTodayStats(),
        sessions: this.checkSessions(),
        settings: this.checkSettings(),
        usageStats: this.checkUsageStats()
      }

      const hasErrors = Object.values(results).some(result => !result.valid)

      if (hasErrors) {
        console.warn('发现数据完整性问题:', results)
        this.repairData(results)
      } else {
        console.log('数据完整性检查通过')
      }

      return results
    },

    // 检查今日统计数据
    checkTodayStats() {
      try {
        const data = wx.getStorageSync('kaoba_today_stats')
        if (!data) {
          return { valid: false, error: '数据不存在', data: null }
        }

        const required = ['date', 'completedPomodoros', 'totalMinutes', 'completedTasks']
        const missing = required.filter(field => data[field] === undefined)

        if (missing.length > 0) {
          return { valid: false, error: '缺少必要字段', missing, data }
        }

        // 检查数据类型和范围
        if (typeof data.completedPomodoros !== 'number' || data.completedPomodoros < 0) {
          return { valid: false, error: '番茄钟数量无效', data }
        }

        if (typeof data.totalMinutes !== 'number' || data.totalMinutes < 0) {
          return { valid: false, error: '总时长无效', data }
        }

        return { valid: true, data }
      } catch (error) {
        return { valid: false, error: error.message, data: null }
      }
    },

    // 检查会话数据
    checkSessions() {
      try {
        const sessions = wx.getStorageSync('kaoba_pomodoro_sessions') || []

        if (!Array.isArray(sessions)) {
          return { valid: false, error: '会话数据格式错误', data: sessions }
        }

        const invalidSessions = sessions.filter(session => {
          return !session.completedAt || !session.duration ||
                 typeof session.duration !== 'number' || session.duration <= 0
        })

        if (invalidSessions.length > 0) {
          return { valid: false, error: '存在无效会话', invalidSessions, data: sessions }
        }

        return { valid: true, data: sessions }
      } catch (error) {
        return { valid: false, error: error.message, data: null }
      }
    },

    // 检查设置数据
    checkSettings() {
      try {
        const settings = wx.getStorageSync('kaoba_pomodoro_settings') || {}

        // 检查关键设置的有效性
        if (settings.workDuration && (settings.workDuration < 1 || settings.workDuration > 120)) {
          return { valid: false, error: '工作时长设置无效', data: settings }
        }

        if (settings.dailyGoal && (settings.dailyGoal < 1 || settings.dailyGoal > 50)) {
          return { valid: false, error: '每日目标设置无效', data: settings }
        }

        return { valid: true, data: settings }
      } catch (error) {
        return { valid: false, error: error.message, data: null }
      }
    },

    // 检查使用统计数据
    checkUsageStats() {
      try {
        const stats = wx.getStorageSync('kaoba_pomodoro_usage_stats') || {}
        return { valid: true, data: stats }
      } catch (error) {
        return { valid: false, error: error.message, data: null }
      }
    },

    // 修复损坏的数据
    repairData(checkResults) {
      console.log('开始修复损坏的数据...')

      // 修复今日统计
      if (!checkResults.todayStats.valid) {
        this.repairTodayStats()
      }

      // 修复会话数据
      if (!checkResults.sessions.valid) {
        this.repairSessions(checkResults.sessions.data)
      }

      // 修复设置数据
      if (!checkResults.settings.valid) {
        this.repairSettings(checkResults.settings.data)
      }

      console.log('数据修复完成')
    },

    // 修复今日统计数据
    repairTodayStats() {
      const today = new Date().toISOString().split('T')[0]
      const defaultStats = {
        date: today,
        completedPomodoros: 0,
        totalMinutes: 0,
        completedTasks: 0,
        goalProgress: 0,
        lastCloudSync: 0,
        version: '2.0.0'
      }

      wx.setStorageSync('kaoba_today_stats', defaultStats)
      console.log('今日统计数据已修复')
    },

    // 修复会话数据
    repairSessions(sessions) {
      if (!Array.isArray(sessions)) {
        wx.setStorageSync('kaoba_pomodoro_sessions', [])
        console.log('会话数据已重置')
        return
      }

      // 过滤掉无效的会话
      const validSessions = sessions.filter(session => {
        return session.completedAt && session.duration &&
               typeof session.duration === 'number' && session.duration > 0
      })

      wx.setStorageSync('kaoba_pomodoro_sessions', validSessions)
      console.log(`会话数据已修复，保留${validSessions.length}个有效会话`)
    },

    // 修复设置数据
    repairSettings(settings) {
      const defaultSettings = {
        workDuration: 25,
        shortBreakDuration: 5,
        longBreakDuration: 15,
        dailyGoal: 5,
        enableNotification: true,
        enableVibration: true
      }

      const repairedSettings = { ...defaultSettings, ...settings }

      // 修正无效值
      if (repairedSettings.workDuration < 1 || repairedSettings.workDuration > 120) {
        repairedSettings.workDuration = 25
      }
      if (repairedSettings.dailyGoal < 1 || repairedSettings.dailyGoal > 50) {
        repairedSettings.dailyGoal = 5
      }

      wx.setStorageSync('kaoba_pomodoro_settings', repairedSettings)
      console.log('设置数据已修复')
    }
  },

  // 数据导出和导入管理
  dataBackupManager: {
    // 导出所有番茄钟数据
    exportAllData() {
      try {
        const exportData = {
          metadata: {
            exportTime: new Date().toISOString(),
            version: '2.0.0',
            dataType: 'pomodoro_backup'
          },
          data: {
            todayStats: wx.getStorageSync('kaoba_today_stats') || {},
            sessions: wx.getStorageSync('kaoba_pomodoro_sessions') || [],
            settings: wx.getStorageSync('kaoba_pomodoro_settings') || {},
            usageStats: wx.getStorageSync('kaoba_pomodoro_usage_stats') || {},
            lastSyncTime: wx.getStorageSync('kaoba_stats_last_sync_time') || 0
          }
        }

        // 计算数据校验和
        exportData.metadata.checksum = this.calculateChecksum(exportData.data)

        console.log('数据导出完成:', {
          会话数量: exportData.data.sessions.length,
          导出时间: exportData.metadata.exportTime,
          校验和: exportData.metadata.checksum
        })

        return {
          success: true,
          data: exportData,
          message: '数据导出成功'
        }
      } catch (error) {
        console.error('数据导出失败:', error)
        return {
          success: false,
          error: error.message,
          message: '数据导出失败'
        }
      }
    },

    // 导入番茄钟数据
    importData(importData) {
      try {
        // 验证导入数据格式
        if (!this.validateImportData(importData)) {
          return {
            success: false,
            message: '导入数据格式无效'
          }
        }

        // 备份当前数据
        const backupResult = this.createBackupBeforeImport()
        if (!backupResult.success) {
          return {
            success: false,
            message: '创建备份失败，导入中止'
          }
        }

        // 导入数据
        const data = importData.data

        if (data.todayStats) {
          wx.setStorageSync('kaoba_today_stats', data.todayStats)
        }
        if (data.sessions) {
          wx.setStorageSync('kaoba_pomodoro_sessions', data.sessions)
        }
        if (data.settings) {
          wx.setStorageSync('kaoba_pomodoro_settings', data.settings)
        }
        if (data.usageStats) {
          wx.setStorageSync('kaoba_pomodoro_usage_stats', data.usageStats)
        }
        if (data.lastSyncTime) {
          wx.setStorageSync('kaoba_stats_last_sync_time', data.lastSyncTime)
        }

        console.log('数据导入完成:', {
          会话数量: data.sessions?.length || 0,
          导入时间: new Date().toISOString(),
          备份文件: backupResult.backupKey
        })

        return {
          success: true,
          message: '数据导入成功',
          backupKey: backupResult.backupKey
        }
      } catch (error) {
        console.error('数据导入失败:', error)
        return {
          success: false,
          error: error.message,
          message: '数据导入失败'
        }
      }
    },

    // 验证导入数据
    validateImportData(importData) {
      if (!importData || typeof importData !== 'object') {
        return false
      }

      if (!importData.metadata || !importData.data) {
        return false
      }

      if (importData.metadata.dataType !== 'pomodoro_backup') {
        return false
      }

      // 验证校验和
      const calculatedChecksum = this.calculateChecksum(importData.data)
      if (importData.metadata.checksum !== calculatedChecksum) {
        console.warn('数据校验和不匹配，可能存在数据损坏')
        return false
      }

      return true
    },

    // 导入前创建备份
    createBackupBeforeImport() {
      try {
        const timestamp = new Date().toISOString()
        const backupKey = `kaoba_backup_before_import_${timestamp}`

        const currentData = this.exportAllData()
        if (!currentData.success) {
          return { success: false, error: '无法创建当前数据备份' }
        }

        wx.setStorageSync(backupKey, currentData.data)

        return {
          success: true,
          backupKey: backupKey,
          message: '导入前备份创建成功'
        }
      } catch (error) {
        return {
          success: false,
          error: error.message
        }
      }
    },

    // 计算数据校验和
    calculateChecksum(data) {
      try {
        const jsonString = JSON.stringify(data)
        let hash = 0
        for (let i = 0; i < jsonString.length; i++) {
          const char = jsonString.charCodeAt(i)
          hash = ((hash << 5) - hash) + char
          hash = hash & hash // 转换为32位整数
        }
        return Math.abs(hash).toString(16)
      } catch (error) {
        console.error('计算校验和失败:', error)
        return '0'
      }
    },

    // 清理旧备份文件
    cleanupOldBackups() {
      try {
        const storageInfo = wx.getStorageInfoSync()
        const backupKeys = storageInfo.keys.filter(key =>
          key.startsWith('kaoba_backup_before_import_')
        )

        // 只保留最近5个备份
        if (backupKeys.length > 5) {
          const sortedKeys = backupKeys.sort().slice(0, -5)
          sortedKeys.forEach(key => {
            wx.removeStorageSync(key)
          })
          console.log(`清理了${sortedKeys.length}个旧备份文件`)
        }
      } catch (error) {
        console.error('清理旧备份失败:', error)
      }
    }
  },

  // 增量数据同步管理
  incrementalSyncManager: {
    // 标记数据变化
    markDataChanged(dataType) {
      try {
        const changes = wx.getStorageSync('kaoba_pending_changes') || {}
        const timestamp = Date.now()

        if (!changes[dataType]) {
          changes[dataType] = []
        }

        changes[dataType].push({
          timestamp: timestamp,
          type: 'update',
          synced: false
        })

        // 只保留最近100个变化记录
        if (changes[dataType].length > 100) {
          changes[dataType] = changes[dataType].slice(-100)
        }

        wx.setStorageSync('kaoba_pending_changes', changes)
        console.log(`标记数据变化: ${dataType} at ${new Date(timestamp).toISOString()}`)

        // 如果有网络连接，尝试立即同步
        this.tryImmediateSync()
      } catch (error) {
        console.error('标记数据变化失败:', error)
      }
    },

    // 尝试立即同步
    tryImmediateSync() {
      wx.getNetworkType({
        success: (res) => {
          if (res.networkType !== 'none') {
            // 延迟1秒后同步，避免频繁调用
            setTimeout(() => {
              this.syncPendingChanges()
            }, 1000)
          }
        }
      })
    },

    // 同步待处理的变化
    async syncPendingChanges() {
      try {
        const changes = wx.getStorageSync('kaoba_pending_changes') || {}
        const hasChanges = Object.keys(changes).some(key =>
          changes[key].some(change => !change.synced)
        )

        if (!hasChanges) {
          console.log('没有待同步的数据变化')
          return
        }

        console.log('开始同步待处理的数据变化...')

        // 获取最新的统计数据
        const todayStats = wx.getStorageSync('kaoba_today_stats') || {}
        const sessions = wx.getStorageSync('kaoba_pomodoro_sessions') || []

        // 计算需要同步的增量数据
        const incrementalData = this.calculateIncrementalData(changes, todayStats, sessions)

        if (incrementalData.hasChanges) {
          // 调用云端API同步增量数据
          const SmartApi = require('../../utils/smartApi.js')
          const result = await SmartApi.updateDailyStats(incrementalData.statsData)

          if (result.success) {
            // 标记变化为已同步
            this.markChangesAsSynced(changes)
            console.log('增量数据同步成功')
          } else {
            console.error('增量数据同步失败:', result.error)
          }
        }
      } catch (error) {
        console.error('同步待处理变化失败:', error)
      }
    },

    // 计算增量数据
    calculateIncrementalData(changes, todayStats, sessions) {
      const lastSyncTime = wx.getStorageSync('kaoba_stats_last_sync_time') || 0
      const now = Date.now()

      // 获取自上次同步以来的新会话
      const newSessions = sessions.filter(session => {
        const sessionTime = new Date(session.completedAt).getTime()
        return sessionTime > lastSyncTime
      })

      if (newSessions.length === 0) {
        return { hasChanges: false }
      }

      // 计算增量统计数据
      const incrementalStats = {
        studyTime: newSessions.reduce((sum, session) => sum + (session.duration || 25), 0) * 60, // 转换为秒
        completedTasks: newSessions.filter(session => session.taskId).length,
        pomodoroSessions: newSessions.length,
        efficiency: this.calculateAverageEfficiency(newSessions),
        subject: this.getMostFrequentSubject(newSessions)
      }

      console.log('计算增量数据:', {
        新会话数: newSessions.length,
        增量时长: incrementalStats.studyTime / 60 + '分钟',
        增量任务: incrementalStats.completedTasks
      })

      return {
        hasChanges: true,
        statsData: incrementalStats,
        newSessions: newSessions
      }
    },

    // 计算平均效率
    calculateAverageEfficiency(sessions) {
      if (sessions.length === 0) return 0

      const validSessions = sessions.filter(session => session.efficiency)
      if (validSessions.length === 0) return 75 // 默认效率

      const totalEfficiency = validSessions.reduce((sum, session) => sum + session.efficiency, 0)
      return Math.round(totalEfficiency / validSessions.length)
    },

    // 获取最频繁的科目
    getMostFrequentSubject(sessions) {
      const subjects = {}
      sessions.forEach(session => {
        const subject = session.taskTitle || '专注练习'
        subjects[subject] = (subjects[subject] || 0) + 1
      })

      let mostFrequent = '专注练习'
      let maxCount = 0
      Object.keys(subjects).forEach(subject => {
        if (subjects[subject] > maxCount) {
          maxCount = subjects[subject]
          mostFrequent = subject
        }
      })

      return mostFrequent
    },

    // 标记变化为已同步
    markChangesAsSynced(changes) {
      try {
        Object.keys(changes).forEach(dataType => {
          changes[dataType].forEach(change => {
            change.synced = true
          })
        })

        wx.setStorageSync('kaoba_pending_changes', changes)

        // 清理已同步的旧记录（保留最近24小时的记录）
        this.cleanupSyncedChanges()
      } catch (error) {
        console.error('标记变化为已同步失败:', error)
      }
    },

    // 清理已同步的旧记录
    cleanupSyncedChanges() {
      try {
        const changes = wx.getStorageSync('kaoba_pending_changes') || {}
        const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000

        Object.keys(changes).forEach(dataType => {
          changes[dataType] = changes[dataType].filter(change => {
            return !change.synced || change.timestamp > oneDayAgo
          })
        })

        wx.setStorageSync('kaoba_pending_changes', changes)
      } catch (error) {
        console.error('清理已同步记录失败:', error)
      }
    }
  },

  // 从云端同步统计数据（跨页面数据同步机制）
  async syncStatsFromCloud() {
    try {
      // 检查缓存是否需要更新（避免频繁请求）
      const lastSyncTime = wx.getStorageSync('kaoba_stats_last_sync_time') || 0
      const now = Date.now()
      const syncInterval = 5 * 60 * 1000 // 5分钟缓存间隔

      // 如果距离上次同步时间小于间隔，且不是强制刷新，则使用缓存数据
      if (now - lastSyncTime < syncInterval) {
        console.log('使用缓存的统计数据，距离上次同步:', Math.round((now - lastSyncTime) / 1000), '秒')
        this.loadTodayStats()
        this.updatePomodoroStats()
        return
      }

      console.log('开始从云端同步统计数据...')

      // 使用现有的getUserRealStats API获取综合统计数据
      const result = await SmartApi.getUserRealStats('week')

      if (result.success && result.data) {
        const cloudStats = result.data
        console.log('云端统计数据获取成功:', cloudStats)

        // 更新本地统计数据
        this.updateLocalStatsFromCloud(cloudStats)

        // 更新同步时间
        wx.setStorageSync('kaoba_stats_last_sync_time', now)

        // 更新显示
        this.updatePomodoroStats()

        console.log('统计数据同步完成')
      } else {
        console.log('云端统计数据获取失败，使用本地缓存数据')
        this.loadTodayStats()
        this.updatePomodoroStats()
      }
    } catch (error) {
      console.error('从云端同步统计数据失败:', error)
      // 网络异常时使用本地缓存数据
      this.loadTodayStats()
      this.updatePomodoroStats()
    }
  },

  // 将云端统计数据更新到本地缓存
  updateLocalStatsFromCloud(cloudStats) {
    try {
      const today = new Date().toISOString().split('T')[0]

      // 从云端数据中提取番茄钟相关统计
      const pomodoroCount = cloudStats.pomodoroCount || 0
      const studyTime = cloudStats.studyTime || 0
      const completedTasks = cloudStats.completedTasks || 0

      // 转换时长单位（云端可能是小时，本地需要分钟）
      let focusTimeMinutes = 0
      if (typeof studyTime === 'string') {
        // 处理 "2.5h" 格式
        const match = studyTime.match(/(\d+\.?\d*)h/)
        if (match) {
          focusTimeMinutes = Math.round(parseFloat(match[1]) * 60)
        }
      } else if (typeof studyTime === 'number') {
        // 如果是数字，假设是分钟
        focusTimeMinutes = studyTime
      }

      // 更新本地统计数据
      this.setData({
        completedSessions: pomodoroCount,
        todayFocusTime: focusTimeMinutes,
        todayCompletedTasks: completedTasks
      })

      // 更新统一的今日统计存储
      const todayStats = {
        date: today,
        completedPomodoros: pomodoroCount,
        totalMinutes: focusTimeMinutes,
        completedTasks: completedTasks,
        goalProgress: this.calculateGoalProgress(),
        lastCloudSync: Date.now() // 记录云端同步时间
      }

      wx.setStorageSync('kaoba_today_stats', todayStats)
      console.log('本地统计数据已更新:', todayStats)
    } catch (error) {
      console.error('更新本地统计数据失败:', error)
    }
  },

  // 强制刷新统计数据（忽略缓存）
  async forceRefreshStats() {
    try {
      console.log('强制刷新统计数据...')

      // 清除缓存时间，强制从云端获取
      wx.removeStorageSync('kaoba_stats_last_sync_time')

      // 显示加载提示
      wx.showLoading({
        title: '同步数据中...',
        mask: true
      })

      // 从云端同步数据
      await this.syncStatsFromCloud()

      wx.hideLoading()

      wx.showToast({
        title: '数据已更新',
        icon: 'success'
      })
    } catch (error) {
      wx.hideLoading()
      console.error('强制刷新统计数据失败:', error)
      wx.showToast({
        title: '刷新失败',
        icon: 'none'
      })
    }
  },

  // 检查网络状态并决定数据同步策略
  checkNetworkAndSync() {
    wx.getNetworkType({
      success: (res) => {
        const networkType = res.networkType
        console.log('当前网络类型:', networkType)

        if (networkType === 'none') {
          console.log('无网络连接，使用离线数据')
          this.loadTodayStats()
          this.updatePomodoroStats()
        } else {
          console.log('有网络连接，尝试同步云端数据')
          this.syncStatsFromCloud()
        }
      },
      fail: () => {
        console.log('获取网络状态失败，使用离线数据')
        this.loadTodayStats()
        this.updatePomodoroStats()
      }
    })
  },

  // 可视化交互方法
  // 调整每日目标
  adjustDailyGoal() {
    const currentGoal = this.getDailyGoal()

    wx.showModal({
      title: '设置每日目标',
      content: `当前目标：${currentGoal}个番茄钟`,
      editable: true,
      placeholderText: '请输入新的每日目标（1-20）',
      success: (res) => {
        if (res.confirm && res.content) {
          const newGoal = parseInt(res.content)
          if (newGoal >= 1 && newGoal <= 20) {
            this.setDailyGoal(newGoal)
            this.updateProgressRing()
            this.updatePomodoroStats()

            wx.showToast({
              title: `目标已设为${newGoal}个`,
              icon: 'success'
            })
          } else {
            wx.showToast({
              title: '请输入1-20之间的数字',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 切换对比周期
  switchComparisonPeriod() {
    const newPeriod = this.data.comparisonPeriod === 'daily' ? 'weekly' : 'daily'
    this.setData({ comparisonPeriod: newPeriod })
    this.generateComparisonData()

    wx.showToast({
      title: newPeriod === 'daily' ? '切换到日对比' : '切换到周对比',
      icon: 'none'
    })
  },

  // 设置图表时间范围
  setChartTimeRange(e) {
    const range = e.currentTarget.dataset.range
    this.setData({ chartTimeRange: range })
    this.generateTrendData()

    wx.showToast({
      title: range === 'week' ? '显示7天数据' : '显示30天数据',
      icon: 'none'
    })
  },

  // 导出图表为图片
  exportChartImage() {
    wx.showLoading({
      title: '生成图片中...',
      mask: true
    })

    // 模拟图片生成过程
    setTimeout(() => {
      wx.hideLoading()

      wx.showModal({
        title: '图片已生成',
        content: '统计图表已保存到相册，可以分享给朋友了！',
        showCancel: false,
        confirmText: '好的',
        success: () => {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          })
        }
      })
    }, 1500)
  },

  // 生成趋势数据
  generateTrendData() {
    try {
      const sessions = wx.getStorageSync('kaoba_pomodoro_sessions') || []
      const days = this.data.chartTimeRange === 'week' ? 7 : 30
      const trendData = []

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        const dateStr = date.toISOString().split('T')[0]

        const daySessions = sessions.filter(session => {
          const sessionDate = new Date(session.completedAt).toISOString().split('T')[0]
          return sessionDate === dateStr
        })

        const focusTime = daySessions.reduce((sum, session) => sum + (session.duration || 25), 0)
        const maxTime = 120 // 最大时长用于计算高度百分比

        trendData.push({
          date: dateStr,
          label: this.formatDateLabel(date, i === 0),
          focusTime: focusTime,
          focusHeight: Math.min(100, (focusTime / maxTime) * 100),
          color: focusTime > 60 ? '#52C41A' : focusTime > 30 ? '#FA8C16' : '#FF4D4F'
        })
      }

      this.setData({ trendData })
      console.log('趋势数据已生成:', trendData)
    } catch (error) {
      console.error('生成趋势数据失败:', error)
    }
  },

  // 格式化日期标签
  formatDateLabel(date, isToday) {
    if (isToday) return '今日'

    const weekdays = ['日', '一', '二', '三', '四', '五', '六']
    return weekdays[date.getDay()]
  },
})

