// pages/pomodoro/index.js - Vant重构版本（清理版）
const SmartApi = require('../../utils/smartApi')

// 简化的Vant API助手
const VantToast = {
  success: (msg) => wx.showToast({ title: msg, icon: 'success' }),
  error: (msg) => wx.showToast({ title: msg, icon: 'error' }),
  info: (msg) => wx.showToast({ title: msg, icon: 'none' })
}

const VantDialog = {
  alert: ({ title, content, onConfirm }) => wx.showModal({
    title, content, showCancel: false,
    success: (res) => res.confirm && onConfirm && onConfirm()
  }),
  confirm: ({ title, content, onConfirm }) => wx.showModal({
    title, content,
    success: (res) => res.confirm && onConfirm && onConfirm()
  })
}

const VantActionSheet = {
  showSheet: ({ titleText, buttons, onItemClick }) => {
    const itemList = buttons.map(btn => btn.text)
    wx.showActionSheet({
      itemList,
      success: (res) => onItemClick && onItemClick(buttons[res.tapIndex], res.tapIndex)
    })
  }
}

Page({
  data: {
    // 模式状态
    focusMode: false,
    studyMode: 'quick', // quick, task

    // 计时器状态
    timerState: 'work', // work, shortBreak, longBreak
    timerStateText: '专注时间',
    sessionTypeText: '专注',
    sessionIndicator: '●●●●○○○○',
    isRunning: false,
    isPaused: false,

    // 时间设置
    workDuration: 25, // 分钟
    shortBreakDuration: 5,
    longBreakDuration: 15,

    // 当前时间
    currentTime: 25 * 60, // 秒
    totalTime: 25 * 60,
    displayTime: '25:00',
    progressDegree: 0,
    timerColor: '#FF6B6B',

    // 任务相关
    selectedTask: null,
    availableTasks: [],
    taskOptions: [], // 用于action-sheet的任务选项

    // 统计数据
    completedSessions: 0,
    todayFocusTime: 0,
    todayCompletedTasks: 0,

    // 音频设置
    selectedBgSound: 'silent',
    bgVolume: 50,
    currentSoundLabel: '静音模式',
    backgroundSounds: [
      { id: 'silent', name: '静音', icon: '🔇', description: '无背景音' },
      { id: 'rain', name: '雨声', icon: '🌧️', description: '适合深度思考' },
      { id: 'ocean', name: '海浪', icon: '🌊', description: '适合放松复习' },
      { id: 'cafe', name: '咖啡厅', icon: '☕', description: '适合轻松复习' },
      { id: 'forest', name: '森林', icon: '🌲', description: '适合长时间专注' },
      { id: 'whitenoise', name: '白噪音', icon: '🎵', description: '屏蔽外界干扰' }
    ],

    notificationSounds: [
      { id: 'start', name: '开始提示', enabled: true },
      { id: 'pause', name: '暂停提示', enabled: true },
      { id: 'complete', name: '完成提示', enabled: true },
      { id: 'warning', name: '时间警告', enabled: true }
    ],

    // 其他设置
    enableVibration: true,
    enableNotification: true,
    autoStartBreak: false,
    autoStartWork: false,
    longBreakInterval: 4,
    currentSession: 1,

    // 弹窗状态
    showSoundModal: false,
    showSettingsModal: false,
    showTaskSelector: false,
    showDropdown: false, // 控制下拉菜单显示

    // 用户信息
    userInfo: {},
    userSignature: ''
  },

  async onLoad() {
    // 检查登录状态
    const app = getApp()
    if (!app.globalData.userInfo || !app.globalData.openid) {
      console.log('用户未登录，跳转到登录页面')
      wx.navigateTo({
        url: '/pages/login/index'
      })
      return
    }

    this.initPage()
    this.loadAvailableTasks()
    this.loadUserSettings()
    this.loadUserSignature()
    this.initAudioSystem()
  },

  onShow() {
    // 更新TabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      })
    }

    this.refreshTaskList()
    this.checkAndLoadFromCache()

    // 重新加载用户签名（从设置页面返回时更新）
    this.loadUserSignature()
  },



  onHide() {
    this.pauseBackgroundAudio()
  },

  onUnload() {
    this.cleanup()
  },

  // 初始化页面
  initPage() {
    this.setData({
      displayTime: this.formatTime(this.data.currentTime),
      sessionTypeText: '专注',
      progressDegree: 0
    })
  },

  // 更新声音标签
  updateSoundLabel() {
    const sound = this.data.backgroundSounds.find(s => s.id === this.data.selectedBgSound)
    this.setData({
      currentSoundLabel: sound ? sound.name : '静音模式'
    })
  },

  // 加载用户设置
  loadUserSettings() {
    try {
      const settings = wx.getStorageSync('pomodoroSettings')
      if (settings) {
        this.setData({
          workDuration: settings.workDuration || 25,
          shortBreakDuration: settings.shortBreakDuration || 5,
          longBreakDuration: settings.longBreakDuration || 15,
          selectedBgSound: settings.selectedBgSound || 'silent',
          bgVolume: settings.bgVolume || 50,
          enableVibration: settings.enableVibration !== false,
          enableNotification: settings.enableNotification !== false
        })
        this.updateCurrentTime()
        this.updateSoundLabel()
      }
    } catch (error) {
      console.error('加载用户设置失败:', error)
    }
  },

  // 保存用户设置
  saveUserSettings() {
    try {
      const settings = {
        workDuration: this.data.workDuration,
        shortBreakDuration: this.data.shortBreakDuration,
        longBreakDuration: this.data.longBreakDuration,
        selectedBgSound: this.data.selectedBgSound,
        bgVolume: this.data.bgVolume,
        enableVibration: this.data.enableVibration,
        enableNotification: this.data.enableNotification
      }
      wx.setStorageSync('pomodoroSettings', settings)
    } catch (error) {
      console.error('保存用户设置失败:', error)
    }
  },

  // 加载用户签名
  loadUserSignature() {
    try {
      const app = getApp()
      const userInfo = app.globalData.userInfo || wx.getStorageSync('kaoba_user_info') || {}

      this.setData({
        userInfo: userInfo,
        userSignature: userInfo.signature || ''
      })
    } catch (error) {
      console.error('加载用户签名失败:', error)
    }
  },

  // 加载可用任务
  async loadAvailableTasks() {
    try {
      const result = await SmartApi.getTasks()
      if (result.success) {
        // 只获取未完成的任务
        const incompleteTasks = result.data.filter(task => !task.completed)
        
        // 按照时间进行排序，时间越早的排在越前面
        const sortedTasks = incompleteTasks.sort((a, b) => {
          // 构造完整的时间字符串用于比较
          const timeA = this.getTaskDateTime(a)
          const timeB = this.getTaskDateTime(b)
          
          // 按时间升序排列（早的在前）
          return timeA - timeB
        })
        
        this.setData({ availableTasks: sortedTasks })
        console.log('加载并排序任务:', sortedTasks.length, '个任务')
      }
    } catch (error) {
      console.error('加载任务失败:', error)
    }
  },

  // 获取任务的完整日期时间用于排序
  getTaskDateTime(task) {
    try {
      // 使用截止日期和时间
      const dateStr = task.dueDate || task.date || new Date().toISOString().split('T')[0]
      const timeStr = task.dueTime || task.time || '23:59'
      
      // 构造完整的日期时间字符串
      const dateTimeStr = `${dateStr}T${timeStr}:00`
      const dateTime = new Date(dateTimeStr)
      
      // 如果解析失败，返回一个很远的未来时间
      if (isNaN(dateTime.getTime())) {
        return new Date('2099-12-31T23:59:59')
      }
      
      return dateTime
    } catch (error) {
      console.error('解析任务时间失败:', error, task)
      return new Date('2099-12-31T23:59:59')
    }
  },

  // 刷新任务列表
  refreshTaskList() {
    this.loadAvailableTasks()
  },

  // 检查并加载缓存中的任务信息
  checkAndLoadFromCache() {
    try {
      const cachedTaskId = wx.getStorageSync('currentTaskId')
      const cachedExamId = wx.getStorageSync('currentExamId')
      const cachedTaskTitle = wx.getStorageSync('currentTaskTitle')
      const cachedExamName = wx.getStorageSync('currentExamName')

      if (cachedTaskId && cachedTaskTitle) {
        // 构造任务对象
        const cachedTask = {
          id: cachedTaskId,
          title: cachedTaskTitle,
          examId: cachedExamId,
          examName: cachedExamName
        }

        // 自动选择这个任务
        this.setData({
          selectedTask: cachedTask,
          studyMode: 'task'
        })

        VantToast.success(`已自动选择任务：${cachedTaskTitle}`)

        // 清除缓存，避免重复使用
        wx.removeStorageSync('currentTaskId')
        wx.removeStorageSync('currentExamId')
        wx.removeStorageSync('currentTaskTitle')
        wx.removeStorageSync('currentExamName')

        console.log('已从缓存加载任务:', cachedTask)
      }
    } catch (error) {
      console.error('加载缓存任务失败:', error)
    }
  },

  // 初始化音频系统
  initAudioSystem() {
    try {
      this.backgroundAudio = wx.createInnerAudioContext()
      this.backgroundAudio.loop = true
      this.backgroundAudio.volume = this.data.bgVolume / 100

      this.backgroundAudio.onError((res) => {
        console.error('背景音频播放失败:', res)
      })
    } catch (error) {
      console.error('初始化音频系统失败', error)
    }
  },

  // 显示任务选择器
  showTaskSelector() {
    // 隐藏自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().hide()
    }
    
    const actions = [
      {
        name: '⚡ 快速专注(25分钟)',
        value: 'quick'
      }
    ]

    // 添加可用任务
    this.data.availableTasks.forEach(task => {
      actions.push({
        name: `📝 ${task.title}`,
        value: task.id
      })
    })

    this.setData({
      taskOptions: actions,
      showTaskSelector: true
    })
  },

  // 隐藏任务选择器
  hideTaskSelector() {
    this.setData({
      showTaskSelector: false
    })
    // 显示自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().show()
    }
  },

  // 选择任务
  selectTask(e) {
    const { value } = e.detail
    
    if (value === 'quick') {
      // 快速专注模式
      this.setData({
        selectedTask: null,
        studyMode: 'quick',
        showTaskSelector: false
      })
      // 显示自定义tabBar
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().show()
      }
      VantToast.success('已选择快速专注模式')
    } else {
      // 任务模式
      const task = this.data.availableTasks.find(t => t.id === value)
      if (task) {
        this.setData({
          selectedTask: task,
          studyMode: 'task',
          showTaskSelector: false
        })
        // 显示自定义tabBar
        if (typeof this.getTabBar === 'function' && this.getTabBar()) {
          this.getTabBar().show()
        }
        VantToast.success(`已选择任务：${task.title}`)
      }
    }
  },

  // 选择任务选项
  selectTaskOption(task) {
    if (!task) {
      // 快速专注模式
      this.setData({
        selectedTask: null,
        studyMode: 'quick'
      })
      VantToast.success('已选择快速专注模式')
    } else {
      // 任务模式
      this.setData({
        selectedTask: task,
        studyMode: 'task'
      })
      VantToast.success(`已选择任务：${task.title}`)
    }
  },

  // 进入专注模式
  enterFocusMode() {
    this.setData({ focusMode: true })
    VantToast.success('已进入专注模式')
  },

  // 退出专注模式
  exitFocusMode() {
    this.setData({ focusMode: false })
    VantToast.success('已退出专注模式')
  },

  // 计时器核心功能
  toggleTimer() {
    if (this.data.isRunning) {
      this.pauseTimer()
    } else {
      this.startTimer()
    }
  },

  // 开始计时器
  startTimer() {
    this.setData({
      isRunning: true,
      isPaused: false
    })

    VantToast.success('开始专注')

    // 开始背景音
    if (this.data.selectedBgSound !== 'silent') {
      this.playBackgroundSound(this.data.selectedBgSound)
    }

    // 启动计时器
    this.timer = setInterval(() => {
      this.updateTimer()
    }, 1000)

    // 保持屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: true
    })
  },

  // 暂停计时器
  pauseTimer() {
    this.setData({
      isRunning: false,
      isPaused: true
    })

    VantToast.info('已暂停')

    // 暂停背景音
    this.pauseBackgroundAudio()

    // 清除计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 取消屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })
  },

  // 停止计时器
  stopTimer() {
    VantDialog.confirm({
      title: '确认停止',
      content: '确定要停止当前的专注会话吗？',
      onConfirm: () => {
        this.resetTimer()
        VantToast.info('已停止专注')
      }
    })
  },

  // 重置计时器
  resetTimer() {
    this.setData({
      isRunning: false,
      isPaused: false
    })

    // 停止背景音
    this.stopBackgroundAudio()

    // 清除计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 重置时间显示
    this.updateCurrentTime()
    this.setData({
      displayTime: this.formatTime(this.data.currentTime),
      progressDegree: 0
    })

    // 取消屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })

    // 退出专注模式
    if (this.data.focusMode) {
      this.exitFocusMode()
    }

    // 如果有选中的任务，询问是否要标记为完成
    if (this.data.selectedTask && this.data.studyMode === 'task') {
      VantDialog.confirm({
        title: '任务完成',
        content: '您已结束专注，是否要标记任务为完成？',
        onConfirm: () => {
          this.updateTaskProgress()
        }
      })
    }
  },

  // 更新计时器
  updateTimer() {
    let currentTime = this.data.currentTime - 1

    if (currentTime <= 0) {
      // 时间到了
      this.completeSession()
      return
    }

    // 时间警告（最后1分钟）
    if (currentTime === 60) {
      wx.vibrateShort()
    }

    // 更新显示
    const progress = ((this.data.totalTime - currentTime) / this.data.totalTime) * 360
    this.setData({
      currentTime: currentTime,
      displayTime: this.formatTime(currentTime),
      progressDegree: progress
    })
  },

  // 完成会话
  completeSession() {
    // 停止计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    this.setData({
      isRunning: false,
      isPaused: false
    })

    // 停止背景音
    this.stopBackgroundAudio()

    // 震动提醒
    wx.vibrateShort()

    // 播放完成音效
    wx.playBackgroundAudio()

    // 更新任务进度
    this.updateTaskProgress()

    // 显示完成提示
    VantDialog.alert({
      title: '🎉 专注完成！',
      content: `恭喜完成一个${this.data.workDuration}分钟的专注时段！`,
      onConfirm: () => {
        // 可以跳转到完成页面
        if (this.data.focusMode) {
          this.exitFocusMode()
        }
      }
    })

    // 保存会话记录
    this.savePomodoroSession()
  },

  // 格式化时间显示
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  },

  // 更新当前时间显示
  updateCurrentTime() {
    const duration = this.data.timerState === 'work' ? this.data.workDuration :
                    this.data.timerState === 'shortBreak' ? this.data.shortBreakDuration :
                    this.data.longBreakDuration

    const timeInSeconds = duration * 60
    this.setData({
      currentTime: timeInSeconds,
      totalTime: timeInSeconds,
      displayTime: this.formatTime(timeInSeconds),
      progressDegree: 0
    })
  },

  // 播放背景音
  playBackgroundSound(soundId) {
    if (!this.backgroundAudio || soundId === 'silent') return

    try {
      this.stopBackgroundAudio()
      
      // 这里应该设置实际的音频文件路径
      const soundUrls = {
        rain: '/sounds/rain.mp3',
        ocean: '/sounds/ocean.mp3',
        cafe: '/sounds/cafe.mp3',
        forest: '/sounds/forest.mp3',
        whitenoise: '/sounds/whitenoise.mp3'
      }

      if (soundUrls[soundId]) {
        this.backgroundAudio.src = soundUrls[soundId]
        this.backgroundAudio.play()
      }
    } catch (error) {
      console.error('播放背景音失败', error)
      VantToast.error('播放背景音失败')
    }
  },

  // 停止背景音
  stopBackgroundAudio() {
    if (this.backgroundAudio) {
      this.backgroundAudio.stop()
    }
  },

  // 暂停背景音
  pauseBackgroundAudio() {
    if (this.backgroundAudio) {
      this.backgroundAudio.pause()
    }
  },

  // 清理资源
  cleanup() {
    // 停止计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 停止背景音
    if (this.backgroundAudio) {
      try {
        this.backgroundAudio.stop()
        this.backgroundAudio.destroy()
      } catch (error) {
        console.log('清理音频资源失败:', error)
      }
    }

    // 取消屏幕常亮
    try {
      wx.setKeepScreenOn({
        keepScreenOn: false
      })
    } catch (error) {
      console.log('取消屏幕常亮失败:', error)
    }
  },

  // === Vant组件适配方法 ===

  // 显示设置弹窗
  showSettings() {
    // 隐藏自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().hide()
    }
    this.setData({
      showSettingsModal: true
    })
  },

  // 隐藏设置弹窗
  hideSettings() {
    this.setData({
      showSettingsModal: false
    })
    // 显示自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().show()
    }
  },

  // 显示声音设置
  showSoundSettings() {
    // 隐藏自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().hide()
    }
    this.setData({
      showSoundModal: true
    })
  },

  // 隐藏声音设置
  hideSoundModal() {
    this.setData({
      showSoundModal: false
    })
    // 显示自定义tabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().show()
    }
  },

  // 选择背景音
  selectBackgroundSound(e) {
    const soundId = e.currentTarget.dataset.sound
    this.setData({
      selectedBgSound: soundId
    })
    this.updateSoundLabel()
    this.saveUserSettings()
    
    // 如果正在播放，切换音乐
    if (this.data.isRunning && soundId !== 'silent') {
      this.playBackgroundSound(soundId)
    }
  },

  // 调整背景音量
  adjustBgVolume(e) {
    const volume = e.detail.value
    this.setData({
      bgVolume: volume
    })
    
    if (this.backgroundAudio) {
      this.backgroundAudio.volume = volume / 100
    }
    
    this.saveUserSettings()
  },

  // 工作时长变更
  changeWorkDuration(e) {
    const duration = e.detail
    this.setData({
      workDuration: duration
    })
    
    if (this.data.timerState === 'work') {
      this.updateCurrentTime()
    }
    
    this.saveUserSettings()
  },

  // 短休息时长变更
  changeShortBreakDuration(e) {
    const duration = e.detail
    this.setData({
      shortBreakDuration: duration
    })
    
    if (this.data.timerState === 'shortBreak') {
      this.updateCurrentTime()
    }
    
    this.saveUserSettings()
  },

  // 长休息时长变更
  changeLongBreakDuration(e) {
    const duration = e.detail
    this.setData({
      longBreakDuration: duration
    })
    
    if (this.data.timerState === 'longBreak') {
      this.updateCurrentTime()
    }
    
    this.saveUserSettings()
  },

  // 切换震动
  toggleVibration(e) {
    const enabled = e.detail
    this.setData({
      enableVibration: enabled
    })
    this.saveUserSettings()
  },

  // 切换通知
  toggleNotification(e) {
    const enabled = e.detail
    this.setData({
      enableNotification: enabled
    })
    this.saveUserSettings()
  },

  // 切换自动开始休息
  toggleAutoStartBreak(e) {
    const enabled = e.detail
    this.setData({
      autoStartBreak: enabled
    })
    this.saveUserSettings()
  },

  // 切换自动开始工作
  toggleAutoStartWork(e) {
    const enabled = e.detail
    this.setData({
      autoStartWork: enabled
    })
    this.saveUserSettings()
  },

  // 查看统计
  viewStatistics() {
    wx.navigateTo({
      url: '/pages/statistics-detail/index'
    })
  },

  // 更新任务进度
  async updateTaskProgress() {
    if (this.data.selectedTask && this.data.studyMode === 'task') {
      try {
        console.log('更新任务状态为完成:', this.data.selectedTask.id)
        
        // 调用API更新任务状态为完成
        const result = await SmartApi.completeTask(this.data.selectedTask.id, true)
        
        if (result.success) {
          console.log('任务状态更新成功')
          VantToast.success('任务已完成！')
          
          // 清空选中的任务
          this.setData({
            selectedTask: null,
            studyMode: 'quick'
          })
        } else {
          console.error('任务状态更新失败:', result.error)
          VantToast.error('任务状态更新失败')
        }
      } catch (error) {
        console.error('更新任务状态失败:', error)
        VantToast.error('更新任务状态失败')
      }
    }
  },

  // 保存番茄钟会话
  savePomodoroSession() {
    const session = {
      taskId: this.data.selectedTask ? this.data.selectedTask.id : null,
      taskTitle: this.data.selectedTask ? this.data.selectedTask.title : '快速专注',
      duration: this.data.workDuration,
      completedAt: new Date().toISOString(),
      sessionType: this.data.timerState
    }

    try {
      const sessions = wx.getStorageSync('pomodoroSessions') || []
      sessions.push(session)
      wx.setStorageSync('pomodoroSessions', sessions)

      // 更新统计
      this.setData({
        completedSessions: this.data.completedSessions + 1,
        todayFocusTime: this.data.todayFocusTime + this.data.workDuration
      })
    } catch (error) {
      console.error('保存会话失败:', error)
    }
  }
})

