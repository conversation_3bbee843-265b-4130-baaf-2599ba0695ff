<!-- pages/join-study-group/index.wxml -->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-content">
      <text class="loading-text">正在验证邀请码...</text>
    </view>
  </view>

  <!-- 邀请信息 -->
  <view class="invite-info" wx:if="{{!loading && inviteInfo}}">
    <view class="invite-header">
      <text class="invite-icon">👥</text>
      <text class="invite-title">邀请您加入搭子小组</text>
    </view>

    <view class="group-card">
      <view class="group-info">
        <text class="group-name">{{inviteInfo.groupName}}</text>
        <text class="exam-name">{{inviteInfo.examName}}</text>
        <text class="member-count">{{inviteInfo.currentMembers}}/{{inviteInfo.maxMembers}}人</text>
      </view>

      <view class="inviter-info">
        <image src="{{inviteInfo.inviterInfo.avatarUrl || '/images/default-avatar.png'}}" class="inviter-avatar" />
        <view class="inviter-details">
          <text class="inviter-name">{{inviteInfo.inviterInfo.nickName}}</text>
          <text class="invite-time">邀请您一起备考</text>
        </view>
      </view>
    </view>

    <view class="join-actions">
      <button 
        class="btn btn-primary join-btn" 
        bindtap="onJoinGroup"
        disabled="{{joining}}"
      >
        {{joining ? '加入中...' : '加入小组'}}
      </button>
      <button class="btn btn-secondary" bindtap="onCancel">取消</button>
    </view>

    <view class="invite-tips">
      <text class="tip-text">• 加入后可以与小组成员互助备考</text>
      <text class="tip-text">• 可以分享和复制复习计划</text>
      <text class="tip-text">• 查看成员复习进度</text>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:if="{{!loading && error}}">
    <view class="error-content">
      <text class="error-icon">❌</text>
      <text class="error-title">邀请无效</text>
      <text class="error-message">{{error}}</text>
      <button class="btn btn-primary" bindtap="onGoHome">返回首页</button>
    </view>
  </view>

  <!-- 手动输入邀请码 -->
  <view class="manual-input" wx:if="{{!loading && !inviteInfo && !error}}">
    <view class="input-header">
      <text class="input-title">输入邀请码</text>
      <text class="input-desc">请输入朋友分享的邀请码</text>
    </view>

    <view class="input-section">
      <input 
        class="invite-code-input"
        placeholder="请输入8位邀请码"
        value="{{inputInviteCode}}"
        bindinput="onInviteCodeInput"
        maxlength="8"
      />
      <button 
        class="btn btn-primary verify-btn"
        bindtap="onVerifyCode"
        disabled="{{!inputInviteCode || inputInviteCode.length !== 8}}"
      >
        验证
      </button>
    </view>
  </view>
</view>
