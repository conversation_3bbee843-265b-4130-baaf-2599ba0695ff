/* 数据库调试页面样式 */
.debug-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 操作按钮 */
.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.action-btn.primary {
  background: #007aff;
  color: white;
}

.action-btn.secondary {
  background: #5856d6;
  color: white;
}

.action-btn.success {
  background: #34c759;
  color: white;
}

.action-btn.info {
  background: #17a2b8;
  color: white;
}

.action-btn.warning {
  background: #ff9500;
  color: white;
}

.action-btn.danger {
  background: #ff3b30;
  color: white;
}

.action-btn[disabled] {
  opacity: 0.6;
}

/* 状态区域 */
.status-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.status-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  min-width: 150rpx;
}

.value {
  font-size: 28rpx;
  font-weight: bold;
}

.value.success {
  color: #34c759;
}

.value.error {
  color: #ff3b30;
}

/* 统计数据 */
.stats {
  flex: 1;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  margin-bottom: 10rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.stat-item.total {
  background: #e3f2fd;
  border: 2rpx solid #2196f3;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

.stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.stat-item.total .stat-value {
  color: #2196f3;
}

/* 日志区域 */
.logs-section {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.log-actions {
  display: flex;
  gap: 10rpx;
}

.log-btn {
  background: #f0f0f0;
  color: #333;
  border: none;
  border-radius: 10rpx;
  font-size: 24rpx;
}

.logs-container {
  height: 400rpx;
  background: white;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.log-item {
  font-size: 24rpx;
  line-height: 1.5;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  color: #333;
  font-family: monospace;
}

.log-item:last-child {
  border-bottom: none;
}

.empty-logs {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 60rpx 0;
}

/* 帮助区域 */
.help-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.help-content {
  margin-top: 20rpx;
}

.help-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
}

.help-item:last-child {
  margin-bottom: 0;
}

.help-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.help-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}
