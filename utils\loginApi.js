// 移除MD5引入
// const md5 = require('./md5')

// 登录API工具类
class LoginApi {
  // 检查微信用户是否存在
  static async checkWechatUser() {
    try {
      console.log('检查微信用户是否存在...')
      
      // 调用云函数检查用户
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {
          action: 'checkWechatUser'
        }
      })

      if (result.result && result.result.success) {
        return result.result
      } else {
        console.error('检查微信用户失败:', result.result?.error)
        return {
          success: false,
          error: result.result?.error || '检查用户失败'
        }
      }
    } catch (error) {
      console.error('检查微信用户异常:', error)
      
      // 如果云函数调用失败，尝试本地检查
      return await this.localCheckWechatUser()
    }
  }

  // 本地检查微信用户（云开发不可用时的后备方案）
  static async localCheckWechatUser() {
    try {
      console.log('使用本地检查微信用户...')
      
      // 检查本地是否有用户信息
      const localUser = wx.getStorageSync('kaoba_local_user_info')
      
      if (localUser) {
        return {
          success: true,
          userExists: true,
          data: {
            openid: localUser._id,
            user: localUser
          },
          isLocal: true
        }
      } else {
        return {
          success: true,
          userExists: false,
          isLocal: true
        }
      }
    } catch (error) {
      console.error('本地检查微信用户失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 用户登录
  static async login(userInfo = null) {
    try {
      console.log('开始用户登录...')
      
      // 调用登录云函数
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {
          action: 'login',
          userInfo: userInfo
        }
      })

      if (result.result && result.result.success) {
        const loginData = result.result.data
        
        // 清除可能存在的本地用户数据，避免身份识别冲突
        wx.removeStorageSync('kaoba_local_user_info')
        wx.removeStorageSync('kaoba_local_user_id')
        console.log('微信登录成功，已清除本地用户数据')
        
        // 创建包含有效期的登录信息
        const loginTime = Date.now()
        const expireTime = loginTime + (30 * 24 * 60 * 60 * 1000) // 30天有效期
        const loginInfo = {
          userInfo: loginData.user,
          openid: loginData.openid,
          loginTime: loginTime,
          expireTime: expireTime,
          lastVerifyTime: loginTime // 最后验证时间
        }
        
        // 保存用户信息到全局数据和本地存储
        const app = getApp()
        app.globalData.userInfo = loginData.user
        app.globalData.openid = loginData.openid
        app.globalData.loginInfo = loginInfo
        
        wx.setStorageSync('kaoba_user_info', loginData.user)
        wx.setStorageSync('kaoba_openid', loginData.openid)
        wx.setStorageSync('kaoba_login_info', loginInfo)
        
        console.log('微信登录成功:', loginData.user.nickName, '有效期30天')
        
        return {
          success: true,
          data: loginData,
          isFirstLogin: loginData.isFirstLogin
        }
      } else {
        console.error('登录失败:', result.result?.error)
        return {
          success: false,
          error: result.result?.error || '登录失败'
        }
      }
    } catch (error) {
      console.error('登录异常:', error)
      
      // 如果云函数调用失败，尝试本地登录
      return await this.localLogin(userInfo)
    }
  }

  // 本地登录（云开发不可用时的后备方案）
  static async localLogin(userInfo = null) {
    try {
      console.log('使用本地登录...')
      
      // 生成本地用户ID
      let localUserId = wx.getStorageSync('kaoba_local_user_id')
      if (!localUserId) {
        localUserId = 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
        wx.setStorageSync('kaoba_local_user_id', localUserId)
      }

      // 获取或创建本地用户信息
      let localUser = wx.getStorageSync('kaoba_local_user_info')
      const now = new Date().toISOString()
      
      if (!localUser) {
        // 创建新的本地用户
        localUser = {
          _id: localUserId,
          type: 'user',
          openid: localUserId,
          nickName: userInfo?.nickName || '本地用户',
          avatarUrl: userInfo?.avatarUrl || '',
          gender: userInfo?.gender || 0,
          country: userInfo?.country || '',
          province: userInfo?.province || '',
          city: userInfo?.city || '',
          language: userInfo?.language || 'zh_CN',
          isFirstLogin: true,
          loginCount: 1,
          lastLoginTime: now,
          createTime: now,
          updateTime: now
        }
        
        wx.setStorageSync('kaoba_local_user_info', localUser)
        console.log('创建本地用户:', localUser.nickName)
      } else {
        // 老用户，直接登录，只更新登录相关信息，完全不修改个人信息
        localUser.lastLoginTime = now
        localUser.loginCount = (localUser.loginCount || 0) + 1
        localUser.updateTime = now
        localUser.isFirstLogin = false
        
        wx.setStorageSync('kaoba_local_user_info', localUser)
        console.log('本地老用户登录:', localUser.nickName)
      }

      // 保存到全局数据
      const app = getApp()
      app.globalData.userInfo = localUser
      app.globalData.openid = localUserId
      
      return {
        success: true,
        data: {
          openid: localUserId,
          user: localUser,
          isFirstLogin: localUser.isFirstLogin
        },
        isLocal: true
      }
    } catch (error) {
      console.error('本地登录失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取用户信息
  static async getUserInfo() {
    try {
      // 先尝试从云端获取
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {
          action: 'getUserInfo'
        }
      })

      if (result.result && result.result.success) {
        return result.result
      } else {
        // 从本地获取
        const localUser = wx.getStorageSync('kaoba_local_user_info')
        if (localUser) {
          return {
            success: true,
            data: localUser,
            isLocal: true
          }
        } else {
          return {
            success: false,
            error: '用户信息不存在'
          }
        }
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      
      // 从本地获取
      const localUser = wx.getStorageSync('kaoba_local_user_info')
      if (localUser) {
        return {
          success: true,
          data: localUser,
          isLocal: true
        }
      } else {
        return {
          success: false,
          error: error.message
        }
      }
    }
  }

  // 更新用户资料
  static async updateUserProfile(userInfo) {
    try {
      // 获取当前用户的_id
      const loginStatus = this.checkLoginStatus()
      if (!loginStatus.isLoggedIn || !loginStatus.userInfo || !loginStatus.userInfo._id) {
        return {
          success: false,
          error: '用户未登录或身份信息缺失'
        }
      }

      const userId = loginStatus.userInfo._id

      // 调试信息：打印传递给云函数的参数
      console.log('LoginApi：准备更新用户资料，userInfo:', JSON.stringify(userInfo, null, 2))
      console.log('LoginApi：用户签名值:', userInfo.signature)
      console.log('LoginApi：当前用户_id:', userId)

      // 先尝试云端更新，传递用户_id
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {
          action: 'updateProfile',
          userInfo: userInfo,
          userId: userId  // 传递用户_id
        }
      })

      console.log('LoginApi：云函数调用结果:', result)

      if (result.result && result.result.success) {
        // 更新本地缓存
        const app = getApp()
        if (app.globalData.userInfo) {
          Object.assign(app.globalData.userInfo, userInfo)
          wx.setStorageSync('kaoba_user_info', app.globalData.userInfo)
        }
        
        return result.result
      } else {
        throw new Error(result.result?.error || '云端更新失败')
      }
    } catch (error) {
      console.error('云端更新用户资料失败，使用本地更新:', error)
      
      // 本地更新
      const localUser = wx.getStorageSync('kaoba_local_user_info')
      if (localUser) {
        Object.assign(localUser, userInfo, {
          updateTime: new Date().toISOString()
        })
        
        wx.setStorageSync('kaoba_local_user_info', localUser)
        
        // 更新全局数据
        const app = getApp()
        app.globalData.userInfo = localUser
        
        return {
          success: true,
          data: localUser,
          isLocal: true
        }
      } else {
        return {
          success: false,
          error: '本地用户信息不存在'
        }
      }
    }
  }

  // 检查登录状态
  static checkLoginStatus() {
    try {
      const app = getApp()

      // 首先检查登录有效期
      const loginInfo = wx.getStorageSync('kaoba_login_info')
      if (loginInfo) {
        const now = Date.now()
        
        // 检查是否过期
        if (now > loginInfo.expireTime) {
          console.log('登录已过期，清除登录状态')
          this.clearExpiredLogin()
          return {
            isLoggedIn: false,
            userInfo: null,
            openid: null,
            error: 'LOGIN_EXPIRED'
          }
        }
        
        // 如果距离上次验证超过1小时，尝试验证云函数连接
        if (now - loginInfo.lastVerifyTime > 60 * 60 * 1000) {
          this.verifyCloudConnection(loginInfo)
        }
        
        // 恢复到全局数据
        if (app && app.globalData) {
          app.globalData.userInfo = loginInfo.userInfo
          app.globalData.openid = loginInfo.openid
          app.globalData.loginInfo = loginInfo
        }
        
        console.log('登录状态有效，自动延长机制处理中')

        // 调试用户信息结构
        console.log('🔍 LoginApi-checkLoginStatus用户信息检查:', {
          hasUserInfo: !!loginInfo.userInfo,
          userInfo_id: loginInfo.userInfo?._id,
          userInfoKeys: loginInfo.userInfo ? Object.keys(loginInfo.userInfo) : null,
          openid: loginInfo.openid
        })

        return {
          isLoggedIn: true,
          userInfo: loginInfo.userInfo,
          openid: loginInfo.openid,
          loginInfo: loginInfo
        }
      }

      // 检查全局数据（向下兼容）
      if (app && app.globalData && app.globalData.userInfo && app.globalData.openid) {
        // 如果有全局数据但没有登录信息，创建登录信息
        const now = Date.now()
        const newLoginInfo = {
          userInfo: app.globalData.userInfo,
          openid: app.globalData.openid,
          loginTime: now,
          expireTime: now + (30 * 24 * 60 * 60 * 1000), // 30天有效期
          lastVerifyTime: now
        }
        
        wx.setStorageSync('kaoba_login_info', newLoginInfo)
        app.globalData.loginInfo = newLoginInfo
        
        return {
          isLoggedIn: true,
          userInfo: app.globalData.userInfo,
          openid: app.globalData.openid,
          loginInfo: newLoginInfo
        }
      }

      // 检查旧版本存储（向下兼容）
      const currentUser = wx.getStorageSync('kaoba_user_info')
      const currentOpenid = wx.getStorageSync('kaoba_openid')
      
      if (currentUser && currentOpenid) {
        // 创建登录信息
        const now = Date.now()
        const legacyLoginInfo = {
          userInfo: currentUser,
          openid: currentOpenid,
          loginTime: now,
          expireTime: now + (30 * 24 * 60 * 60 * 1000), // 30天有效期
          lastVerifyTime: now
        }
        
        // 保存新的登录信息格式
        wx.setStorageSync('kaoba_login_info', legacyLoginInfo)
        
        // 恢复到全局数据
        if (app && app.globalData) {
          app.globalData.userInfo = currentUser
          app.globalData.openid = currentOpenid
          app.globalData.loginInfo = legacyLoginInfo
        }

        console.log('迁移旧版本登录状态完成')

        return {
          isLoggedIn: true,
          userInfo: currentUser,
          openid: currentOpenid,
          loginInfo: legacyLoginInfo
        }
      }

      // 检查本地用户数据（后备方案）
      const localUser = wx.getStorageSync('kaoba_local_user_info')
      const localOpenid = wx.getStorageSync('kaoba_local_user_id')

      if (localUser && localOpenid) {
        // 恢复到全局数据
        if (app && app.globalData) {
          app.globalData.userInfo = localUser
          app.globalData.openid = localOpenid
        }

        return {
          isLoggedIn: true,
          userInfo: localUser,
          openid: localOpenid,
          isLocal: true
        }
      }

      return {
        isLoggedIn: false,
        userInfo: null,
        openid: null
      }
    } catch (error) {
      console.error('检查登录状态失败:', error)

      // 错误情况下的向下兼容检查
      try {
        const currentUser = wx.getStorageSync('kaoba_user_info')
        const currentOpenid = wx.getStorageSync('kaoba_openid')
        
        if (currentUser && currentOpenid) {
          return {
            isLoggedIn: true,
            userInfo: currentUser,
            openid: currentOpenid
          }
        }

        const localUser = wx.getStorageSync('kaoba_local_user_info')
        const localOpenid = wx.getStorageSync('kaoba_local_user_id')

        if (localUser && localOpenid) {
          return {
            isLoggedIn: true,
            userInfo: localUser,
            openid: localOpenid,
            isLocal: true
          }
        }
      } catch (storageError) {
        console.error('存储访问失败:', storageError)
      }

      return {
        isLoggedIn: false,
        userInfo: null,
        openid: null
      }
    }
  }

  // 清除过期登录信息
  static clearExpiredLogin() {
    try {
      const app = getApp()
      
      // 清除全局数据
      if (app && app.globalData) {
        app.globalData.userInfo = null
        app.globalData.openid = null
        app.globalData.loginInfo = null
      }
      
      // 清除登录相关存储，保留本地用户数据作为后备
      const keysToRemove = [
        'kaoba_user_info',
        'kaoba_openid',
        'kaoba_login_info'
      ]
      
      keysToRemove.forEach(key => {
        try {
          wx.removeStorageSync(key)
        } catch (error) {
          console.warn(`清除存储 ${key} 失败:`, error)
        }
      })
      
      console.log('已清除过期登录信息')
    } catch (error) {
      console.error('清除过期登录失败:', error)
    }
  }

  // 验证云函数连接
  static async verifyCloudConnection(loginInfo) {
    try {
      // 异步验证，不阻塞主流程
      setTimeout(async () => {
        try {
          const result = await wx.cloud.callFunction({
            name: 'login',
            data: { action: 'ping' }
          })
          
          if (result.result && result.result.success) {
            // 更新最后验证时间
            loginInfo.lastVerifyTime = Date.now()
            wx.setStorageSync('kaoba_login_info', loginInfo)
            
            const app = getApp()
            if (app && app.globalData && app.globalData.loginInfo) {
              app.globalData.loginInfo.lastVerifyTime = loginInfo.lastVerifyTime
            }
            
            console.log('云函数连接验证成功')
          }
        } catch (error) {
          console.warn('云函数连接验证失败:', error)
          // 验证失败不强制退出登录，允许继续使用本地状态
        }
      }, 0)
    } catch (error) {
      console.warn('启动云函数验证失败:', error)
    }
  }

  // 延长登录有效期
  static extendLoginValidity() {
    try {
      const loginInfo = wx.getStorageSync('kaoba_login_info')
      if (loginInfo) {
        const now = Date.now()
        loginInfo.expireTime = now + (30 * 24 * 60 * 60 * 1000) // 延长30天
        loginInfo.lastVerifyTime = now
        
        wx.setStorageSync('kaoba_login_info', loginInfo)
        
        const app = getApp()
        if (app && app.globalData) {
          app.globalData.loginInfo = loginInfo
        }
        
        console.log('登录有效期已延长30天')
        return true
      }
      return false
    } catch (error) {
      console.error('延长登录有效期失败:', error)
      return false
    }
  }

  // 用户名密码登录
  static async loginWithPassword(credentials) {
    try {
      console.log('开始用户名密码登录...')
      
      // 直接传递原始密码给云函数，在云函数端进行加密
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {
          action: 'loginWithPassword',
          credentials: credentials  // 传递原始密码
        }
      })

      if (result.result && result.result.success) {
        const loginData = result.result.data
        
        // 清除可能存在的本地用户数据，避免身份识别冲突
        wx.removeStorageSync('kaoba_local_user_info')
        wx.removeStorageSync('kaoba_local_user_id')
        console.log('账号密码登录成功，已清除本地用户数据')
        
        // 创建包含有效期的登录信息
        const loginTime = Date.now()
        const expireTime = loginTime + (30 * 24 * 60 * 60 * 1000) // 30天有效期
        const loginInfo = {
          userInfo: loginData.user,
          openid: loginData.openid,
          loginTime: loginTime,
          expireTime: expireTime,
          lastVerifyTime: loginTime
        }
        
        // 保存用户信息到全局数据和本地存储
        const app = getApp()
        app.globalData.userInfo = loginData.user
        app.globalData.openid = loginData.openid
        app.globalData.loginInfo = loginInfo
        
        wx.setStorageSync('kaoba_user_info', loginData.user)
        wx.setStorageSync('kaoba_openid', loginData.openid)
        wx.setStorageSync('kaoba_login_info', loginInfo)
        
        console.log('用户名密码登录成功:', loginData.user.nickName, '有效期30天')
        
        return {
          success: true,
          data: loginData,
          isFirstLogin: loginData.isFirstLogin
        }
      } else {
        console.error('用户名密码登录失败:', result.result?.error)
        return {
          success: false,
          error: result.result?.error || '登录失败'
        }
      }
    } catch (error) {
      console.error('用户名密码登录异常:', error)
      
      // 如果云函数调用失败，尝试本地验证
      return await this.localPasswordLogin(credentials)
    }
  }

  // 本地密码登录（云开发不可用时的后备方案）
  static async localPasswordLogin(credentials) {
    try {
      console.log('使用本地密码验证...')
      
      // 从本地存储获取所有用户（包括微信用户和注册用户）
      const allUsers = wx.getStorageSync('kaoba_all_users') || {}
      
      // 查找用户（仅支持用户名登录，查找loginType为password的用户）
      const userByAccount = Object.values(allUsers).find(u => 
        u.username === credentials.username && u.loginType === 'password'
      )
      
      if (!userByAccount) {
        return {
          success: false,
          error: '用户不存在，请先注册账号'
        }
      }
      
      // 验证密码（本地存储的密码可能是明文，这里先直接比较）
      // 注意：本地存储是后备方案，建议生产环境只使用云函数
      if (userByAccount.password !== credentials.password) {
        return {
          success: false,
          error: '密码错误，请重新输入'
        }
      }
      
      const user = userByAccount

      // 更新登录信息
      const now = new Date().toISOString()
      user.lastLoginTime = now
      user.loginCount = (user.loginCount || 0) + 1
      user.updateTime = now

      // 保存更新后的用户信息
      allUsers[user._id] = user
      wx.setStorageSync('kaoba_all_users', allUsers)
      
      // 保存到全局数据和当前用户存储
      const app = getApp()
      app.globalData.userInfo = user
      app.globalData.openid = user._id
      
      wx.setStorageSync('kaoba_user_info', user)
      wx.setStorageSync('kaoba_openid', user._id)
      
      console.log('本地密码登录成功:', user.nickName)
      
      return {
        success: true,
        data: {
          openid: user._id,
          user: user,
          isFirstLogin: false
        },
        isLocal: true
      }
    } catch (error) {
      console.error('本地密码登录失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 用户注册
  static async register(userInfo) {
    try {
      console.log('开始用户注册...')
      
      // 直接传递原始密码给云函数，在云函数端进行加密
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {
          action: 'register',
          userInfo: userInfo  // 传递原始密码
        }
      })

      if (result.result && result.result.success) {
        const loginData = result.result.data
        
        // 保存用户信息到全局数据和本地存储
        const app = getApp()
        app.globalData.userInfo = loginData.user
        app.globalData.openid = loginData.openid
        
        wx.setStorageSync('kaoba_user_info', loginData.user)
        wx.setStorageSync('kaoba_openid', loginData.openid)
        
        console.log('用户注册成功:', loginData.user.nickName)
        
        return {
          success: true,
          data: loginData
        }
      } else {
        console.error('用户注册失败:', result.result?.error)
        return {
          success: false,
          error: result.result?.error || '注册失败'
        }
      }
    } catch (error) {
      console.error('用户注册异常:', error)
      
      // 如果云函数调用失败，尝试本地注册
      return await this.localRegister(userInfo)
    }
  }

  // 本地注册（云开发不可用时的后备方案）
  static async localRegister(userInfo) {
    try {
      console.log('使用本地注册...')
      
      // 从本地存储获取所有用户
      const allUsers = wx.getStorageSync('kaoba_all_users') || {}
      
      // 检查用户名是否已存在（仅检查loginType为password的用户）
      const usernameExists = Object.values(allUsers).some(u => 
        u.username === userInfo.username && u.loginType === 'password'
      )
      
      if (usernameExists) {
        return {
          success: false,
          error: '用户名已存在'
        }
      }
      
      // 创建新用户
      const now = new Date().toISOString()
      const newUser = {
        _id: 'local_user_' + Date.now(),
        loginType: 'password',
        username: userInfo.username,
        password: userInfo.password, // 本地存储明文密码
        nickName: userInfo.username,
        avatarUrl: '',
        signature: '努力备考，金榜题名！',
        gender: 0,
        country: '',
        province: '',
        city: '',
        language: 'zh_CN',
        isFirstLogin: true,
        loginCount: 0,
        lastLoginTime: null,
        createTime: now,
        updateTime: now
      }

      // 保存新用户到本地存储
      allUsers[newUser._id] = newUser
      wx.setStorageSync('kaoba_all_users', allUsers)
      
      console.log('本地用户注册成功:', newUser.nickName)
      
      return {
        success: true,
        data: {
          openid: newUser._id,
          user: newUser
        },
        isLocal: true
      }
    } catch (error) {
      console.error('本地注册失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 退出登录
  static logout() {
    try {
      const app = getApp()
      
      // 清除全局数据
      if (app && app.globalData) {
        app.globalData.userInfo = null
        app.globalData.openid = null
        app.globalData.loginInfo = null
      }
      
      // 清除所有相关的本地存储
      const keysToRemove = [
        'kaoba_user_info',
        'kaoba_openid', 
        'kaoba_login_info',
        'kaoba_local_user_info',
        'kaoba_local_user_id',
        'userInfo' // 兼容旧版本
      ]
      
      keysToRemove.forEach(key => {
        try {
          wx.removeStorageSync(key)
        } catch (error) {
          console.warn(`清除存储 ${key} 失败:`, error)
        }
      })
      
      console.log('用户已退出登录，所有数据已清除')
      
      return { success: true }
    } catch (error) {
      console.error('退出登录失败:', error)
      return { success: false, error: error.message }
    }
  }
}

module.exports = LoginApi
