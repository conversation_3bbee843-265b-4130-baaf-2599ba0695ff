// pages/achievement-system/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    overviewStats: {
      totalUnlocked: 12,
      totalAchievements: 25,
      completionRate: 48,
      totalPoints: 1250
    },

    recentAchievements: [
      {
        id: 'streak_15',
        name: '坚持达人',
        icon: '🔥',
        color: '#FF4D4F',
        unlockedTime: '2小时前',
        isNew: true
      },
      {
        id: 'task_master',
        name: '任务大师',
        icon: '🎯',
        color: '#52C41A',
        unlockedTime: '昨天',
        isNew: false
      }
    ],

    selectedCategory: 'study',
    achievementCategories: [
      { id: 'study', name: '备考成就' },
      { id: 'focus', name: '专注成就' },
      { id: 'exam', name: '考试成就' },
      { id: 'habit', name: '习惯成就' },
      { id: 'special', name: '特殊成就' }
    ],

    currentCategoryInfo: {
      name: '备考成就',
      unlockedCount: 5,
      totalCount: 10,
      progress: 50
    },

    currentCategoryAchievements: [],

    achievementStats: [
      { label: '备考成就', value: '5/10', color: '#1890FF', bgColor: '#E6F7FF' },
      { label: '专注成就', value: '3/6', color: '#52C41A', bgColor: '#F6FFED' },
      { label: '考试成就', value: '2/5', color: '#FA8C16', bgColor: '#FFF7E6' },
      { label: '习惯成就', value: '2/4', color: '#722ED1', bgColor: '#F9F0FF' }
    ],

    allAchievements: {},
    showAchievementDetail: false,
    selectedAchievement: {}
  },

  async onLoad() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      await this.initAchievements()
      await this.loadAchievementData()
      await this.updateCategoryAchievements()

      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      console.error('加载成就系统失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  },

  async onShow() {
    await this.refreshAchievementProgress()
  },

  // 初始化成就数据
  async initAchievements() {
    try {
      // 调用云函数初始化成就数据
      await wx.cloud.callFunction({
        name: 'achievementManager',
        data: {
          action: 'initAchievements',
          data: {}
        }
      })
    } catch (error) {
      console.error('初始化成就数据失败:', error)
    }
  },

  // 加载成就数据
  async loadAchievementData() {
    try {
      // 获取成就统计
      const statsResult = await SmartApi.getAchievementStats()

      if (statsResult.success && statsResult.data) {
        const achievementStats = statsResult.data.map(stat => ({
          label: stat.name,
          value: `${stat.unlocked}/${stat.total}`,
          color: this.getCategoryColor(stat.category),
          bgColor: this.getCategoryBgColor(stat.category)
        }))

        this.setData({ achievementStats })
      }

      // 获取用户成就
      const achievementsResult = await SmartApi.getUserAchievements()

      if (achievementsResult.success && achievementsResult.data) {
        // 按分类组织成就数据
        const allAchievements = {}
        const recentAchievements = []

        achievementsResult.data.forEach(achievement => {
          const category = achievement.category
          if (!allAchievements[category]) {
            allAchievements[category] = []
          }
          allAchievements[category].push(achievement)

          // 收集最近解锁的成就
          if (achievement.unlocked && achievement.isNew) {
            recentAchievements.push({
              id: achievement._id,
              name: achievement.name,
              icon: achievement.icon,
              color: achievement.color,
              unlockedTime: this.formatRelativeTime(achievement.unlockedTime),
              isNew: achievement.isNew
            })
          }
        })

        this.setData({
          allAchievements,
          recentAchievements: recentAchievements.slice(0, 3) // 只显示最近3个
        })
      }
    } catch (error) {
      console.error('加载成就数据失败:', error)
      this.setDefaultAchievementData()
    }
  },

  // 设置默认成就数据
  setDefaultAchievementData() {
    const achievements = {
      study: [
        {
          id: 'first_task',
          name: '初学者',
          description: '完成第一个复习任务',
          icon: '🌱',
          color: '#52C41A',
          category: 'study',
          categoryName: '备考成就',
          rarity: '普通',
          rarityColor: '#52C41A',
          points: 10,
          unlocked: false,
          unlockedTime: null,
          reward: '解锁复习统计功能',
          currentValue: 0,
          targetValue: 1,
          progress: 0,
          tips: '完成任何一个复习任务即可解锁'
        },
        {
          id: 'task_master',
          name: '任务大师',
          description: '累计完成100个复习任务',
          icon: '🎯',
          color: '#1890FF',
          category: 'study',
          categoryName: '备考成就',
          rarity: '稀有',
          rarityColor: '#1890FF',
          points: 50,
          unlocked: true,
          unlockedTime: '2025-01-25 16:45',
          currentValue: 100,
          targetValue: 100,
          progress: 100,
          reward: '解锁高级任务模板'
        },
        {
          id: 'knowledge_seeker',
          name: '求知者',
          description: '复习时长达到100小时',
          icon: '📚',
          color: '#722ED1',
          category: 'study',
          categoryName: '备考成就',
          rarity: '史诗',
          rarityColor: '#722ED1',
          points: 100,
          unlocked: false,
          unlockedTime: null,
          currentValue: 65,
          targetValue: 100,
          progress: 65,
          reward: '解锁专家模式',
          tips: '持续复习，累计复习时长到达100小时'
        }
      ],
      focus: [
        {
          id: 'focus_master',
          name: '专注大师',
          description: '单次专注时长达到2小时',
          icon: '🧘',
          color: '#FA8C16',
          category: 'focus',
          categoryName: '专注成就',
          rarity: '稀有',
          rarityColor: '#FA8C16',
          points: 30,
          unlocked: false,
          unlockedTime: null,
          currentValue: 85,
          targetValue: 120,
          progress: 71,
          reward: '解锁专注音效',
          tips: '在番茄钟模式下持续专注2小时不间断'
        }
      ],
      exam: [
        {
          id: 'exam_ready',
          name: '考试准备',
          description: '创建第一个考试计划',
          icon: '📝',
          color: '#52C41A',
          category: 'exam',
          categoryName: '考试成就',
          rarity: '普通',
          rarityColor: '#52C41A',
          points: 15,
          unlocked: true,
          unlockedTime: '2025-01-20 10:30',
          reward: '解锁考试统计功能'
        }
      ],
      habit: [
        {
          id: 'daily_habit',
          name: '习惯养成',
          description: '连续复习7天',
          icon: '🔥',
          color: '#FF4D4F',
          category: 'habit',
          categoryName: '习惯成就',
          rarity: '普通',
          rarityColor: '#FF4D4F',
          points: 25,
          unlocked: true,
          unlockedTime: '2025-01-23 18:20',
          reward: '解锁习惯提醒功能'
        }
      ],
      special: [
        {
          id: 'early_bird',
          name: '早起鸟儿',
          description: '在早上6点前开始复习',
          icon: '🌅',
          color: '#1989FA',
          category: 'special',
          categoryName: '特殊成就',
          rarity: '传说',
          rarityColor: '#1989FA',
          points: 200,
          unlocked: false,
          unlockedTime: null,
          currentValue: 0,
          targetValue: 1,
          progress: 0,
          reward: '解锁早起徽章',
          tips: '在早上6点前开始复习任务'
        }
      ]
    }

    this.setData({ allAchievements: achievements })
  },

  // 切换成就分类 - 适配Vant tabs组件
  switchCategory(e) {
    const category = e.detail.name
    this.setData({ selectedCategory: category })
    this.updateCategoryAchievements()
  },

  // 更新当前分类的成就
  updateCategoryAchievements() {
    const selectedCategory = this.data.selectedCategory
    const allAchievements = this.data.allAchievements
    const achievementCategories = this.data.achievementCategories

    const currentCategoryAchievements = allAchievements[selectedCategory] || []
    
    // 计算当前分类信息
    const categoryInfo = achievementCategories.find(cat => cat.id === selectedCategory)
    const unlockedCount = currentCategoryAchievements.filter(item => item.unlocked).length
    const totalCount = currentCategoryAchievements.length
    const progress = totalCount > 0 ? Math.round((unlockedCount / totalCount) * 100) : 0

    const currentCategoryInfo = {
      name: categoryInfo ? categoryInfo.name : '未知分类',
      unlockedCount,
      totalCount,
      progress
    }

    this.setData({
      currentCategoryAchievements,
      currentCategoryInfo
    })
  },

  // 查看成就详情
  viewAchievementDetail(e) {
    const achievement = e.currentTarget.dataset.achievement
    this.setData({
      selectedAchievement: achievement,
      showAchievementDetail: true
    })
  },

  // 隐藏成就详情
  hideAchievementDetail() {
    this.setData({ showAchievementDetail: false })
  },

  // 阻止冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 刷新成就进度
  async refreshAchievementProgress() {
    try {
      await this.checkNewAchievements()
      await this.calculateOverviewStats()
    } catch (error) {
      console.error('刷新成就进度失败:', error)
    }
  },

  // 检查新成就
  async checkNewAchievements() {
    try {
      // 获取用户最新的复习数据
      const studyRecords = await SmartApi.getStudyRecords()
      const todayStats = await SmartApi.getTodayStats()

      // 检查各类成就
      await this.checkStudyAchievements(studyRecords)
      await this.checkFocusAchievements(studyRecords)
      await this.checkHabitAchievements(todayStats)
    } catch (error) {
      console.error('检查新成就失败:', error)
    }
  },

  // 检查备考成就
  async checkStudyAchievements(studyRecords) {
    const completedTasks = studyRecords.completedTasks || 0
    const totalStudyTime = studyRecords.totalStudyTime || 0

    // 检查任务大师成就
    if (completedTasks >= 100) {
      await this.unlockAchievement('task_master')
    }

    // 检查求知者成就
    if (totalStudyTime >= 100) {
      await this.unlockAchievement('knowledge_seeker')
    }
  },

  // 检查专注成就
  async checkFocusAchievements(studyRecords) {
    const maxFocusTime = studyRecords.maxFocusTime || 0

    // 检查专注大师成就
    if (maxFocusTime >= 120) {
      await this.unlockAchievement('focus_master')
    }
  },

  // 检查习惯成就
  async checkHabitAchievements(todayStats) {
    const studyStreak = await this.calculateStudyStreak(todayStats)

    // 检查习惯养成成就
    if (studyStreak >= 7) {
      await this.unlockAchievement('daily_habit')
    }
  },

  // 计算复习连续天数
  async calculateStudyStreak(todayStats) {
    // 简化实现，实际应该从数据库获取
    return todayStats.studyStreak || 0
  },

  // 解锁成就
  async unlockAchievement(achievementId) {
    try {
      const result = await SmartApi.unlockAchievement(achievementId)
      
      if (result.success) {
        // 更新本地数据
        const allAchievements = this.data.allAchievements
        
        Object.keys(allAchievements).forEach(category => {
          const achievement = allAchievements[category].find(item => item.id === achievementId)
          if (achievement && !achievement.unlocked) {
            achievement.unlocked = true
            achievement.unlockedTime = new Date().toISOString()
            achievement.isNew = true
            
            // 显示解锁动画
            this.showAchievementUnlocked(achievementId)
          }
        })

        this.setData({ allAchievements })
        await this.calculateOverviewStats()
        await this.updateCategoryAchievements()
      }
    } catch (error) {
      console.error('解锁成就失败:', error)
    }
  },

  // 显示成就解锁动画
  showAchievementUnlocked(achievementId) {
    // 查找成就信息
    const allAchievements = this.data.allAchievements
    let unlockedAchievement = null

    Object.keys(allAchievements).forEach(category => {
      const achievement = allAchievements[category].find(item => item.id === achievementId)
      if (achievement) {
        unlockedAchievement = achievement
      }
    })

    if (unlockedAchievement) {
      wx.showModal({
        title: '🎉 成就解锁',
        content: `恭喜解锁成就："${unlockedAchievement.name}"！\n${unlockedAchievement.description}`,
        showCancel: false,
        confirmText: '太棒了'
      })
    }
  },

  // 计算概览统计
  async calculateOverviewStats() {
    const allAchievements = this.data.allAchievements
    let totalUnlocked = 0
    let totalAchievements = 0
    let totalPoints = 0

    Object.keys(allAchievements).forEach(category => {
      allAchievements[category].forEach(achievement => {
        totalAchievements++
        if (achievement.unlocked) {
          totalUnlocked++
          totalPoints += achievement.points || 0
        }
      })
    })

    const completionRate = totalAchievements > 0 ? Math.round((totalUnlocked / totalAchievements) * 100) : 0

    this.setData({
      overviewStats: {
        totalUnlocked,
        totalAchievements,
        completionRate,
        totalPoints
      }
    })
  },

  // 下拉刷新
  async onPullDownRefresh() {
    try {
      await this.loadAchievementData()
      await this.refreshAchievementProgress()
      wx.stopPullDownRefresh()
    } catch (error) {
      wx.stopPullDownRefresh()
      console.error('刷新失败:', error)
    }
  },

  // 分享
  onShareAppMessage() {
    const overviewStats = this.data.overviewStats
    return {
      title: `我已解锁${overviewStats.totalUnlocked}个成就，获得${overviewStats.totalPoints}点成就点数！`,
      path: '/pages/achievement-system/index'
    }
  },

  // 格式化相对时间
  formatRelativeTime(dateTimeStr) {
    if (!dateTimeStr) return ''
    
    const now = new Date()
    const date = new Date(dateTimeStr)
    const diff = now.getTime() - date.getTime()
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (minutes < 60) {
      return `${minutes}分钟前`
    } else if (hours < 24) {
      return `${hours}小时前`
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return date.toLocaleDateString()
    }
  },

  // 获取分类颜色
  getCategoryColor(category) {
    const colorMap = {
      study: '#1890FF',
      focus: '#52C41A',
      exam: '#FA8C16',
      habit: '#722ED1',
      special: '#FF4D4F'
    }
    return colorMap[category] || '#1890FF'
  },

  // 获取分类背景颜色
  getCategoryBgColor(category) {
    const bgColorMap = {
      study: '#E6F7FF',
      focus: '#F6FFED',
      exam: '#FFF7E6',
      habit: '#F9F0FF',
      special: '#FFF1F0'
    }
    return bgColorMap[category] || '#E6F7FF'
  }
})