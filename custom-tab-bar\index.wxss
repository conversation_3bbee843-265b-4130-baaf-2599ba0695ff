/* custom-tab-bar/index.wxss - Vant重构版本 */

/* 表情符号图标样式 */
.tab-emoji {
  font-size: 44rpx;
  line-height: 1;
  color: #999999;
  transition: all 0.3s ease;
  display: inline-block;
}

.tab-emoji.active {
  color: #1890FF;
  transform: scale(1.1);
}

/* 选中状态的文字样式 */
.active {
  color: #1890FF !important;
  font-weight: 600 !important;
}

/* Vant TabBar 自定义样式 */
.van-tabbar {
  border-top: 1rpx solid #E5E5E5;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 999 !important;
  position: relative;
}

/* 确保TabBar项的图标区域足够 */
.van-tabbar-item {
  padding: 8rpx 0 !important;
}

/* 标签文字样式优化 */
.van-tabbar-item__text {
  font-size: 20rpx !important;
  margin-top: 4rpx !important;
  font-weight: 500 !important;
}

/* 选中状态的标签文字颜色 */
.van-tabbar-item--active .van-tabbar-item__text {
  color: #1890FF !important;
  font-weight: 600 !important;
}

/* 表情图标容器 */
.van-tabbar-item__icon {
  margin-bottom: 6rpx !important;
}

/* 悬停效果（仅在支持的设备上） */
@media (hover: hover) {
  .van-tabbar-item:hover .tab-emoji {
    transform: scale(1.05);
  }

  .van-tabbar-item:hover .van-tabbar-item__text {
    color: #40a9ff !important;
  }
}

/* 活跃状态的动画增强 */
.van-tabbar-item--active .tab-emoji {
  animation: tabIconBounce 0.3s ease;
}

@keyframes tabIconBounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1.1); }
}

/* 适配深色模式（如果需要） */
@media (prefers-color-scheme: dark) {
  .van-tabbar {
    background-color: #1f1f1f !important;
    border-top-color: #333 !important;
  }
  
  .tab-emoji {
    filter: brightness(0.9);
  }
}
