<!-- pages/debug-group/index.wxml -->
<view class="container">
  <view class="debug-section">
    <text class="section-title">小组数据调试</text>
    
    <view class="debug-item">
      <text class="debug-label">小组ID:</text>
      <input class="debug-input" placeholder="输入小组ID" value="{{groupId}}" bindinput="onGroupIdInput" />
      <button class="btn btn-primary" bindtap="loadGroupData">加载数据</button>
    </view>

    <view class="debug-item" wx:if="{{groupInfo}}">
      <text class="debug-label">小组信息:</text>
      <view class="debug-content">
        <text>小组名称: {{groupInfo.groupName}}</text>
        <text>考试名称: {{groupInfo.examName}}</text>
        <text>当前成员数: {{groupInfo.currentMembers}}</text>
        <text>最大成员数: {{groupInfo.maxMembers}}</text>
        <text>邀请码: {{groupInfo.inviteCode}}</text>
        <text>状态: {{groupInfo.status}}</text>
        <text>创建时间: {{groupInfo.createTime}}</text>
      </view>
    </view>

    <view class="debug-item" wx:if="{{groupInfo}}">
      <text class="debug-label">按钮显示条件:</text>
      <view class="debug-content">
        <text>currentMembers: {{groupInfo.currentMembers}}</text>
        <text>maxMembers: {{groupInfo.maxMembers}}</text>
        <text>比较结果: {{shouldShowButton}}</text>
        <text>按钮应该显示: {{buttonDisplayText}}</text>
      </view>
    </view>

    <view class="debug-item" wx:if="{{groupInfo}}">
      <text class="debug-label">成员列表:</text>
      <view class="debug-content">
        <text wx:for="{{groupInfo.members}}" wx:key="userId">
          {{index + 1}}. {{item.userInfo.nickName}} ({{item.userId}})
        </text>
      </view>
    </view>

    <view class="debug-item" wx:if="{{groupInfo}}">
      <text class="debug-label">测试邀请按钮:</text>
      <button 
        class="btn btn-secondary" 
        bindtap="testInviteButton"
        wx:if="{{groupInfo.currentMembers < groupInfo.maxMembers}}"
      >
        邀请成员 (测试)
      </button>
      <text wx:else>按钮被隐藏：人数已满</text>
    </view>

    <view class="debug-item">
      <text class="debug-label">原始数据:</text>
      <scroll-view class="debug-raw" scroll-y>
        <text>{{rawData}}</text>
      </scroll-view>
    </view>
  </view>
</view>
